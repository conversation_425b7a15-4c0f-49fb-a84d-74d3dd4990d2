# 团购网开发指南

## 1. 开发环境搭建

### 1.1 环境要求

#### 基础环境
| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | 18.x+ | 前端开发环境 |
| JDK | 17+ | 后端开发环境 |
| MySQL | 8.0+ | 数据库 |
| Redis | 7.0+ | 缓存数据库 |
| Git | 2.30+ | 版本控制 |

#### 开发工具推荐
- **前端IDE**: VS Code + Vue Language Features (Volar)
- **后端IDE**: IntelliJ IDEA / Eclipse
- **数据库工具**: Navicat / DataGrip / MySQL Workbench
- **API测试**: Postman / Apifox
- **Redis客户端**: Redis Desktop Manager / Another Redis Desktop Manager

### 1.2 项目克隆和初始化

```bash
# 1. 克隆项目
git clone https://github.com/your-org/tuangou-platform.git
cd tuangou-platform

# 2. 安装前端依赖 (移动端APP)
cd APP
npm install
# 或使用 yarn
yarn install

# 3. 安装管理后台依赖
cd ../Server/smart-admin/admin-web
npm install

# 4. 后端项目导入IDE
# 使用IntelliJ IDEA打开 Server/smart-admin/admin-api 目录
# Maven会自动下载依赖
```

### 1.3 数据库初始化

```sql
-- 1. 创建数据库
CREATE DATABASE smart_admin DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 导入表结构和初始数据
-- 执行 Server/Docs/database/schema.sql
-- 执行 Server/Docs/database/init_data.sql
```

### 1.4 配置文件设置

#### 后端配置 (application-dev.yml)
```yaml
server:
  port: 8080

spring:
  datasource:
    url: *************************************************************************************************************************
    username: root
    password: your_password
    
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0

# SA-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  active-timeout: -1
  is-concurrent: false
  is-share: false
```

#### 前端配置 (vite.config.js)
```javascript
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 2. 项目结构说明

### 2.1 整体目录结构
```
团购网/
├── APP/                    # 移动端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   ├── dist/              # 构建输出
│   └── package.json       # 依赖配置
├── Server/                # 服务端
│   ├── smart-admin/       # Smart Admin框架
│   │   ├── admin-api/     # 后端API服务
│   │   └── admin-web/     # 管理后台
│   └── Docs/              # 文档
└── README.md              # 项目说明
```

### 2.2 前端项目结构

#### 移动端APP (APP/src/)
```
src/
├── api/                   # API接口定义
│   ├── index.js          # 统一导出
│   ├── standardAdapter.js # API适配器
│   └── real/             # 真实API实现
│       ├── authApi.js    # 认证相关API
│       ├── goodsApi.js   # 商品相关API
│       ├── orderApi.js   # 订单相关API
│       ├── walletApi.js  # 钱包相关API
│       └── userApi.js    # 用户相关API
├── components/           # 公共组件
│   ├── common/          # 通用组件
│   │   ├── BottomNav.vue      # 底部导航
│   │   ├── ProductCard.vue    # 商品卡片
│   │   └── NavigationHeader.vue # 导航头部
│   └── popups/          # 弹窗组件
├── views/               # 页面组件
│   ├── home/           # 首页相关
│   │   ├── HomePage.vue       # 首页
│   │   └── ActivityZonePage.vue # 活动专区
│   ├── product/        # 商品相关
│   │   ├── ProductDetailPage.vue    # 商品详情
│   │   ├── DirectBuyDetailPage.vue  # 直购详情
│   │   └── CategoryPage.vue         # 商品分类
│   ├── order/          # 订单相关
│   │   ├── ConfirmPage.vue     # 订单确认
│   │   ├── PaymentPage.vue     # 支付页面
│   │   └── WaitingPage.vue     # 等待页面
│   ├── user/           # 用户中心
│   │   ├── ProfilePage.vue     # 个人资料
│   │   ├── WalletPage.vue      # 钱包管理
│   │   ├── OrdersPage.vue      # 订单列表
│   │   └── AddressPage.vue     # 地址管理
│   └── group/          # 团购相关
│       ├── GroupPage.vue       # 团购页面
│       └── GroupWaitingPage.vue # 团购等待
├── store/              # 状态管理
│   ├── auth.js         # 认证状态
│   └── modules/        # 功能模块状态
│       ├── user.js     # 用户状态
│       └── ui.js       # UI状态
├── router/             # 路由配置
│   └── index.js        # 路由定义
├── utils/              # 工具函数
│   ├── request.js      # HTTP请求封装
│   ├── auth.js         # 认证工具
│   ├── format.js       # 格式化工具
│   └── validation.js   # 验证工具
├── assets/             # 静态资源
│   ├── images/         # 图片资源
│   └── styles/         # 样式文件
├── config/             # 配置文件
│   └── env.js          # 环境配置
└── main.js             # 应用入口
```

#### 管理后台 (admin-web/src/)
```
src/
├── api/                  # API接口
│   ├── business/        # 业务API
│   │   ├── goods-api.js      # 商品管理API
│   │   ├── orders-api.js     # 订单管理API
│   │   ├── wallets-api.js    # 钱包管理API
│   │   └── activities-api.js # 活动管理API
│   └── system/          # 系统API
│       ├── login-api.js      # 登录API
│       ├── employee-api.js   # 员工管理API
│       └── menu-api.js       # 菜单管理API
├── views/               # 页面组件
│   ├── business/        # 业务模块
│   │   ├── erp/         # ERP相关
│   │   │   ├── goods/        # 商品管理
│   │   │   ├── orders/       # 订单管理
│   │   │   ├── wallets/      # 钱包管理
│   │   │   └── activities/   # 活动管理
│   │   └── oa/          # OA相关
│   │       ├── banners/      # 横幅管理
│   │       └── popups/       # 弹窗管理
│   ├── system/          # 系统管理
│   │   ├── employee/         # 员工管理
│   │   ├── role/             # 角色管理
│   │   └── menu/             # 菜单管理
│   └── support/         # 支撑模块
│       ├── dict/             # 数据字典
│       └── config/           # 系统配置
├── components/          # 公共组件
│   ├── framework/       # 框架组件
│   └── business/        # 业务组件
├── store/               # 状态管理
│   └── modules/         # 状态模块
├── router/              # 路由配置
├── utils/               # 工具函数
└── assets/              # 静态资源
```

### 2.3 后端项目结构

#### API服务 (admin-api/src/main/java/)
```
net/lab1024/sa/admin/
├── common/                    # 公共模块
│   ├── config/               # 配置类
│   │   ├── DatabaseConfig.java    # 数据库配置
│   │   ├── RedisConfig.java       # Redis配置
│   │   └── WebConfig.java         # Web配置
│   ├── constant/             # 常量定义
│   │   ├── OrdersConst.java       # 订单常量
│   │   ├── WalletConst.java       # 钱包常量
│   │   └── UserConst.java         # 用户常量
│   ├── exception/            # 异常处理
│   │   ├── BusinessException.java # 业务异常
│   │   └── GlobalExceptionHandler.java # 全局异常处理
│   └── util/                 # 工具类
│       ├── CryptoUtil.java        # 加密工具
│       ├── DateUtil.java          # 日期工具
│       └── ValidationUtil.java    # 验证工具
├── framework/                 # 框架模块
│   ├── security/             # 安全配置
│   │   ├── SecurityConfig.java    # 安全配置
│   │   └── JwtAuthFilter.java     # JWT过滤器
│   ├── web/                  # Web配置
│   │   ├── ResponseAdvice.java    # 响应处理
│   │   └── RequestWrapper.java    # 请求包装
│   └── database/             # 数据库配置
│       ├── MybatisConfig.java     # MyBatis配置
│       └── DataSourceConfig.java  # 数据源配置
├── module/                    # 业务模块
│   ├── business/             # 业务功能
│   │   ├── goods/            # 商品管理
│   │   │   ├── controller/        # 控制器
│   │   │   ├── service/           # 服务层
│   │   │   ├── manager/           # 管理层
│   │   │   ├── dao/               # 数据访问层
│   │   │   └── domain/            # 领域模型
│   │   │       ├── entity/        # 实体类
│   │   │       ├── vo/            # 视图对象
│   │   │       └── form/          # 表单对象
│   │   ├── orders/           # 订单管理
│   │   ├── wallets/          # 钱包管理
│   │   ├── activities/       # 活动管理
│   │   └── users/            # 用户管理
│   └── system/               # 系统功能
│       ├── employee/         # 员工管理
│       ├── role/             # 角色管理
│       ├── menu/             # 菜单管理
│       └── dict/             # 数据字典
└── SmartAdminApplication.java # 启动类
```

## 3. 开发规范

### 3.1 代码规范

#### Java代码规范
```java
/**
 * 商品服务类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@Slf4j
public class GoodsService {
    
    @Autowired
    private GoodsDao goodsDao;
    
    /**
     * 查询商品详情
     * 
     * @param goodsId 商品ID
     * @return 商品详情
     */
    public ResponseDTO<GoodsDetailVO> getGoodsDetail(Long goodsId) {
        // 参数验证
        if (goodsId == null || goodsId <= 0) {
            return ResponseDTO.userErrorParam("商品ID不能为空");
        }
        
        // 业务逻辑
        GoodsDetailVO goodsDetail = goodsDao.getDetailById(goodsId);
        if (goodsDetail == null) {
            return ResponseDTO.userErrorParam("商品不存在");
        }
        
        // 返回结果
        return ResponseDTO.ok(goodsDetail);
    }
}
```

#### JavaScript代码规范
```javascript
/**
 * 商品API服务
 * <AUTHOR>
 * @since 2025-01-01
 */

import { standardApi } from '@/api/standardAdapter'

// 使用函数声明而不是箭头函数（便于hoisting）
export function getGoodsList(params) {
  return standardApi.get('/goods/list', params)
}

export function getGoodsDetail(goodsId) {
  // 参数验证
  if (!goodsId) {
    throw new Error('商品ID不能为空')
  }
  
  return standardApi.get(`/goods/detail/${goodsId}`)
}

// Vue组件规范
export default {
  name: 'GoodsList',
  
  // 属性定义
  props: {
    categoryId: {
      type: [String, Number],
      default: null
    }
  },
  
  // 响应式数据
  data() {
    return {
      goodsList: [],
      loading: false,
      error: null
    }
  },
  
  // 计算属性
  computed: {
    filteredGoods() {
      if (!this.categoryId) return this.goodsList
      return this.goodsList.filter(goods => goods.categoryId === this.categoryId)
    }
  },
  
  // 生命周期钩子
  async mounted() {
    await this.loadGoodsList()
  },
  
  // 方法定义
  methods: {
    async loadGoodsList() {
      try {
        this.loading = true
        const response = await getGoodsList()
        this.goodsList = response.data
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    }
  }
}
```

### 3.2 数据库规范

#### 表设计规范
```sql
-- 表命名：使用小写字母和下划线，以t_开头
-- 字段命名：使用小写字母和下划线
-- 注释：必须添加表和字段注释

CREATE TABLE t_goods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    status TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    KEY idx_name (name),
    KEY idx_status (status),
    KEY idx_create_time (create_time)
) COMMENT '商品表';
```

#### SQL编写规范
```sql
-- 使用明确的表别名
SELECT 
    g.id,
    g.name,
    g.price,
    c.name AS category_name
FROM t_goods g
LEFT JOIN t_categories c ON g.category_id = c.id
WHERE g.status = 1
    AND g.create_time >= '2025-01-01'
ORDER BY g.create_time DESC
LIMIT 20;

-- 避免SELECT *，明确指定字段
-- 使用适当的索引
-- 注意SQL注入防护
```

### 3.3 API设计规范

#### RESTful API设计
```
GET    /api/v1/goods           # 获取商品列表
GET    /api/v1/goods/{id}      # 获取商品详情  
POST   /api/v1/goods           # 创建商品
PUT    /api/v1/goods/{id}      # 更新商品
DELETE /api/v1/goods/{id}      # 删除商品

GET    /api/v1/orders          # 获取订单列表
POST   /api/v1/orders          # 创建订单
GET    /api/v1/orders/{id}     # 获取订单详情
PUT    /api/v1/orders/{id}/status # 更新订单状态
```

#### 响应格式规范
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "商品名称",
    "price": 99.99
  },
  "timestamp": 1672531200000
}

// 分页响应
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "size": 20
  },
  "timestamp": 1672531200000
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": 1672531200000
}
```

### 3.4 Git使用规范

#### 分支管理
```
master     # 主分支，用于生产环境
develop    # 开发分支，用于集成测试
feature/*  # 功能分支，用于新功能开发
hotfix/*   # 热修复分支，用于紧急修复
release/*  # 发布分支，用于版本发布
```

#### 提交信息规范
```bash
# 格式：<type>(<scope>): <subject>

# 示例
feat(goods): 添加商品搜索功能
fix(order): 修复订单状态更新异常
docs(api): 更新API文档
style(css): 优化商品卡片样式
refactor(auth): 重构认证逻辑
test(user): 添加用户模块单元测试
chore(deps): 升级依赖版本

# type说明
feat     # 新功能
fix      # 修复bug
docs     # 文档更新
style    # 代码格式调整
refactor # 代码重构
test     # 测试相关
chore    # 构建或辅助工具变动
```

## 4. 开发流程

### 4.1 功能开发流程

#### 1. 需求分析
- 理解需求文档和产品原型
- 评估开发工作量和技术难点
- 确定技术方案和实现路径

#### 2. 数据库设计
```sql
-- 1. 创建表结构
CREATE TABLE t_new_feature (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    -- 其他字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建索引
CREATE INDEX idx_xxx ON t_new_feature(field_name);

-- 3. 插入初始数据（如果需要）
INSERT INTO t_new_feature (...) VALUES (...);
```

#### 3. 后端开发
```java
// 1. 创建实体类
@Data
@TableName("t_new_feature")
public class NewFeatureEntity {
    @TableId
    private Long id;
    // 其他字段
}

// 2. 创建DAO层
@Mapper
public interface NewFeatureDao extends BaseMapper<NewFeatureEntity> {
    List<NewFeatureVO> queryPage(@Param("queryForm") NewFeatureQueryForm queryForm);
}

// 3. 创建Service层
@Service
public class NewFeatureService {
    public ResponseDTO<String> add(NewFeatureAddForm addForm) {
        // 业务逻辑
    }
}

// 4. 创建Controller层
@RestController
@RequestMapping("/api/v1/new-feature")
public class NewFeatureController {
    @PostMapping("/add")
    public ResponseDTO<String> add(@RequestBody @Valid NewFeatureAddForm addForm) {
        return newFeatureService.add(addForm);
    }
}
```

#### 4. 前端开发
```javascript
// 1. 创建API接口
export function addNewFeature(data) {
  return standardApi.post('/new-feature/add', data)
}

// 2. 创建页面组件
<template>
  <div class="new-feature-page">
    <!-- 页面内容 -->
  </div>
</template>

<script setup>
import { addNewFeature } from '@/api/newFeatureApi'

// 组件逻辑
</script>

// 3. 添加路由
{
  path: '/new-feature',
  name: 'NewFeature',
  component: () => import('@/views/NewFeaturePage.vue')
}
```

#### 5. 测试验证
```javascript
// 单元测试示例
describe('NewFeatureService', () => {
  test('should add new feature successfully', async () => {
    const mockData = { name: 'test feature' }
    const result = await newFeatureService.add(mockData)
    expect(result.code).toBe(200)
  })
})
```

### 4.2 调试指南

#### 前端调试
```javascript
// 1. 使用Vue DevTools调试组件状态
// 2. 使用Console进行日志调试
console.log('API Response:', response)
console.error('Error occurred:', error)

// 3. 使用断点调试
debugger; // 代码会在此处暂停

// 4. 网络请求调试
// 在浏览器开发者工具的Network面板查看请求详情
```

#### 后端调试
```java
// 1. 使用IDE断点调试
// 在代码行号左侧点击设置断点

// 2. 使用日志调试
log.debug("Processing request: {}", request);
log.info("User {} performed action {}", userId, action);
log.error("Error occurred while processing: ", exception);

// 3. 使用Spring Boot Actuator监控
// 访问 http://localhost:8080/actuator/health
// 访问 http://localhost:8080/actuator/metrics
```

#### 数据库调试
```sql
-- 1. 查看执行计划
EXPLAIN SELECT * FROM t_goods WHERE status = 1;

-- 2. 查看慢查询日志
SHOW VARIABLES LIKE 'slow_query_log%';

-- 3. 分析表状态
SHOW TABLE STATUS LIKE 't_goods';

-- 4. 查看索引使用情况
SHOW INDEX FROM t_goods;
```

### 4.3 性能优化指南

#### 前端性能优化
```javascript
// 1. 组件懒加载
const LazyComponent = defineAsyncComponent(() => import('./LazyComponent.vue'))

// 2. 图片懒加载
<img v-lazy="imageSrc" alt="description">

// 3. 防抖处理用户输入
import { debounce } from 'lodash'

const debouncedSearch = debounce((keyword) => {
  // 搜索逻辑
}, 300)

// 4. 虚拟滚动处理长列表
<virtual-list
  :data-key="'id'"
  :data-sources="longList"
  :data-component="itemComponent"
  :estimate-size="50"
/>
```

#### 后端性能优化
```java
// 1. 使用缓存
@Cacheable(value = "goods", key = "#goodsId")
public GoodsDetailVO getGoodsDetail(Long goodsId) {
    return goodsDao.getDetailById(goodsId);
}

// 2. 批量操作
public void batchUpdateStatus(List<Long> ids, String status) {
    goodsDao.batchUpdateStatus(ids, status);
}

// 3. 分页查询优化
public PageResult<GoodsVO> queryGoods(GoodsQueryForm form) {
    // 先查询ID列表（覆盖索引）
    List<Long> ids = goodsDao.queryIds(form);
    if (CollectionUtils.isEmpty(ids)) {
        return PageResult.empty();
    }
    
    // 再查询详细信息
    List<GoodsVO> goods = goodsDao.queryByIds(ids);
    return PageResult.of(goods, form.getPage(), form.getSize(), ids.size());
}

// 4. 异步处理
@Async
public CompletableFuture<Void> processAsync(Long id) {
    // 异步处理逻辑
    return CompletableFuture.completedFuture(null);
}
```

## 5. 测试指南

### 5.1 单元测试

#### 前端单元测试
```javascript
// 使用Jest + Vue Test Utils
import { mount } from '@vue/test-utils'
import GoodsCard from '@/components/common/GoodsCard.vue'

describe('GoodsCard', () => {
  test('renders goods information correctly', () => {
    const goods = {
      id: 1,
      name: '测试商品',
      price: 99.99,
      image: 'test.jpg'
    }
    
    const wrapper = mount(GoodsCard, {
      props: { goods }
    })
    
    expect(wrapper.text()).toContain('测试商品')
    expect(wrapper.text()).toContain('99.99')
  })
  
  test('emits click event when clicked', async () => {
    const wrapper = mount(GoodsCard, {
      props: { goods: { id: 1, name: 'test' } }
    })
    
    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

#### 后端单元测试
```java
@SpringBootTest
@Transactional
class GoodsServiceTest {
    
    @Autowired
    private GoodsService goodsService;
    
    @Test
    void testAddGoods() {
        // 准备测试数据
        GoodsAddForm addForm = new GoodsAddForm();
        addForm.setName("测试商品");
        addForm.setPrice(new BigDecimal("99.99"));
        
        // 执行测试
        ResponseDTO<String> result = goodsService.add(addForm);
        
        // 验证结果
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }
    
    @Test
    void testAddGoodsWithDuplicateName() {
        // 测试重复名称的情况
        GoodsAddForm addForm = new GoodsAddForm();
        addForm.setName("已存在的商品");
        
        ResponseDTO<String> result = goodsService.add(addForm);
        
        assertEquals(400, result.getCode());
        assertTrue(result.getMessage().contains("已存在"));
    }
}
```

### 5.2 集成测试

#### API集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class GoodsControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateAndQueryGoods() {
        // 创建商品
        GoodsAddForm addForm = new GoodsAddForm();
        addForm.setName("集成测试商品");
        addForm.setPrice(new BigDecimal("199.99"));
        
        ResponseEntity<ResponseDTO> createResponse = restTemplate.postForEntity(
            "/api/v1/goods", addForm, ResponseDTO.class);
        assertEquals(200, createResponse.getStatusCode().value());
        
        // 查询商品列表
        ResponseEntity<ResponseDTO> queryResponse = restTemplate.getForEntity(
            "/api/v1/goods/list", ResponseDTO.class);
        assertEquals(200, queryResponse.getStatusCode().value());
    }
}
```

### 5.3 端到端测试

#### 使用Playwright进行E2E测试
```javascript
// tests/e2e/goods.spec.js
import { test, expect } from '@playwright/test'

test.describe('商品管理', () => {
  test('用户可以浏览商品列表', async ({ page }) => {
    // 访问首页
    await page.goto('/')
    
    // 等待商品列表加载
    await page.waitForSelector('.goods-list')
    
    // 验证商品卡片存在
    const goodsCards = await page.locator('.goods-card').count()
    expect(goodsCards).toBeGreaterThan(0)
  })
  
  test('用户可以查看商品详情', async ({ page }) => {
    await page.goto('/')
    
    // 点击第一个商品
    await page.locator('.goods-card').first().click()
    
    // 验证跳转到详情页
    await expect(page).toHaveURL(/\/product\/\d+/)
    
    // 验证详情页内容
    await expect(page.locator('.goods-detail')).toBeVisible()
  })
})
```

## 6. 部署指南

### 6.1 开发环境部署

#### 前端开发服务器
```bash
# 移动端APP
cd APP
npm run dev
# 访问 http://localhost:3000

# 管理后台
cd Server/smart-admin/admin-web
npm run dev
# 访问 http://localhost:3001
```

#### 后端开发服务器
```bash
# 使用IDE运行SmartAdminApplication主类
# 或使用Maven命令
cd Server/smart-admin/admin-api
mvn spring-boot:run
# 访问 http://localhost:8080
```

### 6.2 生产环境部署

#### 前端打包
```bash
# 移动端APP
cd APP
npm run build
# 生成 dist/ 目录

# 管理后台
cd Server/smart-admin/admin-web
npm run build:prod
# 生成 dist/ 目录
```

#### 后端打包
```bash
cd Server/smart-admin/admin-api
mvn clean package -Dmaven.test.skip=true
# 生成 target/smart-admin.jar
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 移动端APP
    location / {
        root /var/www/app/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 管理后台
    location /admin {
        root /var/www/admin/dist;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 6.3 Docker部署

#### Dockerfile示例
```dockerfile
# 后端Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/smart-admin.jar app.jar

EXPOSE 8080

CMD ["java", "-jar", "app.jar"]
```

#### Docker Compose配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: smart_admin
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7.0
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 7. 常见问题

### 7.1 开发环境问题

#### Node.js版本问题
```bash
# 使用nvm管理Node.js版本
nvm install 18
nvm use 18
```

#### 依赖安装失败
```bash
# 清除缓存
npm cache clean --force
# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 端口占用
```bash
# 查看端口占用
netstat -ano | findstr :3000
# 杀死进程
taskkill /PID <进程ID> /F
```

### 7.2 运行时问题

#### 数据库连接失败
```yaml
# 检查数据库配置
spring:
  datasource:
    url: *********************************************************************************
    username: root
    password: your_password
```

#### Redis连接失败
```yaml
# 检查Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    password: your_password
```

#### 跨域问题
```java
// 后端CORS配置
@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
```

### 7.3 性能问题

#### 前端首屏加载慢
```javascript
// 1. 使用代码分割
const ProductDetail = () => import('@/views/product/ProductDetailPage.vue')

// 2. 预加载关键资源
<link rel="preload" href="/fonts/font.woff2" as="font" type="font/woff2" crossorigin>

// 3. 使用骨架屏
<template>
  <div v-if="loading" class="skeleton">
    <!-- 骨架屏内容 -->
  </div>
  <div v-else class="content">
    <!-- 实际内容 -->
  </div>
</template>
```

#### 数据库查询慢
```sql
-- 1. 添加索引
CREATE INDEX idx_create_time ON t_orders(create_time);

-- 2. 优化查询语句
-- 避免SELECT *，只查询需要的字段
SELECT id, name, price FROM t_goods WHERE status = 1;

-- 3. 使用EXPLAIN分析查询计划
EXPLAIN SELECT * FROM t_goods WHERE category_id = 1;
```

## 8. 开发工具和插件推荐

### 8.1 VS Code插件
- **Vue Language Features (Volar)**: Vue 3支持
- **TypeScript Vue Plugin (Volar)**: TypeScript支持
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Auto Rename Tag**: 自动重命名标签
- **Bracket Pair Colorizer**: 括号配对高亮
- **GitLens**: Git增强功能
- **REST Client**: API测试

### 8.2 IntelliJ IDEA插件
- **Lombok**: 简化Java代码
- **MyBatis X**: MyBatis增强
- **Spring Assistant**: Spring Boot支持
- **Alibaba Java Coding Guidelines**: 阿里巴巴代码规范
- **Translation**: 翻译插件
- **Rainbow Brackets**: 彩虹括号
- **Statistic**: 代码统计

### 8.3 开发工具
- **Postman**: API测试和文档
- **Navicat**: 数据库管理
- **Redis Desktop Manager**: Redis客户端
- **SourceTree**: Git图形化工具
- **Xmind**: 思维导图
- **ProcessOn**: 流程图和原型设计

## 9. 总结

本开发指南涵盖了团购网项目开发的各个方面，包括环境搭建、项目结构、开发规范、开发流程、测试指南和部署方法。

### 关键要点：
1. **规范化开发**: 遵循代码规范和最佳实践
2. **模块化设计**: 清晰的项目结构和模块划分
3. **自动化测试**: 完善的测试覆盖
4. **性能优化**: 前后端性能优化策略
5. **持续集成**: 标准化的开发和部署流程

开发团队应该熟练掌握这些内容，以确保项目的高质量交付和长期可维护性。

---

**文档版本**: V1.0  
**创建日期**: 2025年1月  
**维护团队**: 技术开发组  
**审核状态**: 已审核