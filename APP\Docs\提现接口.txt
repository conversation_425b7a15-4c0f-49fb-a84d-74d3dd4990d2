● 申请记录
/api/v1/withdrawals
GET提交:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "pageNum": 1,
        "pageSize": 30,
        "total": 2,
        "pages": 1,
        "list": [
            {
                "id": 4,
                "userId": 81,
                "userName": "***********",
                "amount": 2000.00,
                "fee": 0.00,
                "actualAmount": 2000.00,
                "status": "approved",
                "bankName": "越南银行",
                "bankAccount": "**********",
                "accountHolder": "布洛林",
                "processedBy": 1,
                "createTime": "2025-07-06 10:38:48",
                "processedTime": "2025-07-06 10:38:58"
            },
            {
                "id": 3,
                "userId": 81,
                "userName": "***********",
                "amount": 20000.00,
                "fee": 0.00,
                "actualAmount": 0.00,
                "status": "rejected",
                "bankName": "越南银行",
                "bankAccount": "**********",
                "accountHolder": "布洛林",
                "rejectionReason": "余额不足",
                "processedBy": 1,
                "createTime": "2025-07-06 10:15:37",
                "processedTime": "2025-07-06 10:38:21"
            }
        ],
        "emptyFlag": false
    },
    "dataType": 1
}


● 提现申请
/api/v1/withdrawalsApply
POST提交:
{
  "amount": "2000", 		//提现额
  "bankName": "越南银行", 		//提现银行名称
  "bankAccount": "**********",	//提现银行账号
  "accountHolder": "布洛林"		//提现银行户名
}
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "dataType": 1
}