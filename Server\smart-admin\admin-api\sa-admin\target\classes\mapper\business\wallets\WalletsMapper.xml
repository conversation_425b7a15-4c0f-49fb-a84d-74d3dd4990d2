<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.wallets.dao.WalletsDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_wallets.user_id,
        t_wallets.balance,
        t_wallets.experience_balance,
        t_wallets.points,
        t_wallets.total_recharge,
        t_wallets.total_withdraw,
        t_wallets.total_earnings,
        t_wallets.create_time,
        t_wallets.update_time,
        t_wallets.status
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.wallets.domain.vo.WalletsVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_wallets
        <where>
            <!--用户ID-->
            <if test="queryForm.userId != null">
                AND t_wallets.user_id = #{queryForm.userId}
            </if>
            <!--创建时间-->
            <if test="queryForm.createTimeBegin != null">
                AND t_wallets.create_time &gt;= #{queryForm.createTimeBegin}
            </if>
            <if test="queryForm.createTimeEnd != null">
                AND t_wallets.create_time &lt;= #{queryForm.createTimeEnd}
            </if>
        </where>
    </select>

    <update id="changeStatus">
        UPDATE t_wallets
        SET status = #{status},
            update_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <update id="modifyBalance">
        UPDATE t_wallets
        SET balance = balance + #{amount}
        <if test="type == 'RECHARGE'">
            , total_recharge = total_recharge + #{amount}
        </if>
        <if test="type == 'WITHDRAWAL'">
            , total_withdraw = total_withdraw - #{amount}
        </if>
        ,update_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <update id="modifyExperienceBalance">
        UPDATE t_wallets
        SET experience_balance = experience_balance + #{amount},
            update_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <update id="modifyPoints">
        UPDATE t_wallets
        SET points = points + #{amount},
            update_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <update id="updateTotalEarnings">
        UPDATE t_wallets
        SET total_earnings = total_earnings + #{amount},
            update_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

</mapper>
