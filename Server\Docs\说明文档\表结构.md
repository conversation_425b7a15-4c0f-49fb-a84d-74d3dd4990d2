mysql> SELECT TABLE_NAME, COLUMN_NAME
    -> FROM information_schema.COLUMNS
    -> WHERE TABLE_SCHEMA = 'tgw_pp';
+--------------------------+--------------------------------+
| TABLE_NAME               | COLUMN_NAME                    |
+--------------------------+--------------------------------+
| t_activities             | config_info                    |
| t_activities             | create_time                    |
| t_activities             | current_participants           |
| t_activities             | deleted_flag                   |
| t_activities             | description                    |
| t_activities             | end_time                       |
| t_activities             | force_loss_flag                |
| t_activities             | id                             |
| t_activities             | name                           |
| t_activities             | participant_limit              |
| t_activities             | remaining_time                 |
| t_activities             | return_ratio                   |
| t_activities             | start_time                     |
| t_activities             | status                         |
| t_activities             | type                           |
| t_activities_count       | high_price                     |
| t_activities_count       | high_price_count               |
| t_activities_count       | low_price                      |
| t_activities_count       | low_price_count                |
| t_activities_count       | novice                         |
| t_activities_count       | novice_count                   |
| t_activities_count       | update_time                    |
| t_activities_count       | user_id                        |
| t_banners                | create_time                    |
| t_banners                | end_time                       |
| t_banners                | id                             |
| t_banners                | image_url                      |
| t_banners                | link_type                      |
| t_banners                | link_url                       |
| t_banners                | sort_order                     |
| t_banners                | start_time                     |
| t_banners                | status                         |
| t_banners                | title                          |
| t_category               | category_id                    |
| t_category               | category_name                  |
| t_category               | category_type                  |
| t_category               | create_time                    |
| t_category               | deleted_flag                   |
| t_category               | disabled_flag                  |
| t_category               | parent_id                      |
| t_category               | remark                         |
| t_category               | sort                           |
| t_category               | update_time                    |
| t_change_log             | change_log_id                  |
| t_change_log             | content                        |
| t_change_log             | create_time                    |
| t_change_log             | link                           |
| t_change_log             | public_date                    |
| t_change_log             | publish_author                 |
| t_change_log             | type                           |
| t_change_log             | update_time                    |
| t_change_log             | update_version                 |
| t_code_generator_config  | basic                          |
| t_code_generator_config  | create_time                    |
| t_code_generator_config  | delete_info                    |
| t_code_generator_config  | detail                         |
| t_code_generator_config  | fields                         |
| t_code_generator_config  | insert_and_update              |
| t_code_generator_config  | query_fields                   |
| t_code_generator_config  | table_fields                   |
| t_code_generator_config  | table_name                     |
| t_code_generator_config  | update_time                    |
| t_config                 | config_id                      |
| t_config                 | config_key                     |
| t_config                 | config_name                    |
| t_config                 | config_value                   |
| t_config                 | create_time                    |
| t_config                 | remark                         |
| t_config                 | update_time                    |
| t_data_tracer            | content                        |
| t_data_tracer            | create_time                    |
| t_data_tracer            | data_id                        |
| t_data_tracer            | data_tracer_id                 |
| t_data_tracer            | diff_new                       |
| t_data_tracer            | diff_old                       |
| t_data_tracer            | extra_data                     |
| t_data_tracer            | ip                             |
| t_data_tracer            | ip_region                      |
| t_data_tracer            | type                           |
| t_data_tracer            | update_time                    |
| t_data_tracer            | user_agent                     |
| t_data_tracer            | user_id                        |
| t_data_tracer            | user_name                      |
| t_data_tracer            | user_type                      |
| t_department             | create_time                    |
| t_department             | department_id                  |
| t_department             | department_name                |
| t_department             | manager_id                     |
| t_department             | parent_id                      |
| t_department             | sort                           |
| t_department             | update_time                    |
| t_dict                   | create_time                    |
| t_dict                   | dict_code                      |
| t_dict                   | dict_id                        |
| t_dict                   | dict_name                      |
| t_dict                   | disabled_flag                  |
| t_dict                   | remark                         |
| t_dict                   | update_time                    |
| t_dict_data              | create_time                    |
| t_dict_data              | data_label                     |
| t_dict_data              | data_value                     |
| t_dict_data              | dict_data_id                   |
| t_dict_data              | dict_id                        |
| t_dict_data              | disabled_flag                  |
| t_dict_data              | remark                         |
| t_dict_data              | sort_order                     |
| t_dict_data              | update_time                    |
| t_employee               | actual_name                    |
| t_employee               | administrator_flag             |
| t_employee               | avatar                         |
| t_employee               | child_count                    |
| t_employee               | create_time                    |
| t_employee               | deleted_flag                   |
| t_employee               | department_id                  |
| t_employee               | disabled_flag                  |
| t_employee               | email                          |
| t_employee               | employee_id                    |
| t_employee               | gender                         |
| t_employee               | has_real                       |
| t_employee               | inviter_id                     |
| t_employee               | login_name                     |
| t_employee               | login_pwd                      |
| t_employee               | novice_count                   |
| t_employee               | phone                          |
| t_employee               | position_id                    |
| t_employee               | remark                         |
| t_employee               | risk_level                     |
| t_employee               | update_time                    |
| t_feedback               | create_time                    |
| t_feedback               | feedback_attachment            |
| t_feedback               | feedback_content               |
| t_feedback               | feedback_id                    |
| t_feedback               | update_time                    |
| t_feedback               | user_id                        |
| t_feedback               | user_name                      |
| t_feedback               | user_type                      |
| t_file                   | create_time                    |
| t_file                   | creator_id                     |
| t_file                   | creator_name                   |
| t_file                   | creator_user_type              |
| t_file                   | file_id                        |
| t_file                   | file_key                       |
| t_file                   | file_name                      |
| t_file                   | file_size                      |
| t_file                   | file_type                      |
| t_file                   | folder_type                    |
| t_file                   | update_time                    |
| t_goods                  | activity_id                    |
| t_goods                  | alone_flag                     |
| t_goods                  | alone_price                    |
| t_goods                  | category_id                    |
| t_goods                  | create_time                    |
| t_goods                  | deleted_flag                   |
| t_goods                  | description                    |
| t_goods                  | detail_images                  |
| t_goods                  | goods_currency                 |
| t_goods                  | goods_id                       |
| t_goods                  | goods_name                     |
| t_goods                  | goods_status                   |
| t_goods                  | goods_type                     |
| t_goods                  | images                         |
| t_goods                  | pay_mode                       |
| t_goods                  | place                          |
| t_goods                  | price                          |
| t_goods                  | remark                         |
| t_goods                  | shelves_flag                   |
| t_goods                  | update_time                    |
| t_goods_skus             | alone_flag                     |
| t_goods_skus             | alone_price                    |
| t_goods_skus             | attributes                     |
| t_goods_skus             | create_time                    |
| t_goods_skus             | goods_id                       |
| t_goods_skus             | id                             |
| t_goods_skus             | original_price                 |
| t_goods_skus             | price                          |
| t_goods_skus             | sales_count                    |
| t_goods_skus             | sku_code                       |
| t_goods_skus             | status                         |
| t_goods_skus             | stock                          |
| t_goods_skus             | update_time                    |
| t_heart_beat_record      | heart_beat_record_id           |
| t_heart_beat_record      | heart_beat_time                |
| t_heart_beat_record      | process_no                     |
| t_heart_beat_record      | process_start_time             |
| t_heart_beat_record      | project_path                   |
| t_heart_beat_record      | server_ip                      |
| t_help_doc               | attachment                     |
| t_help_doc               | author                         |
| t_help_doc               | content_html                   |
| t_help_doc               | content_text                   |
| t_help_doc               | create_time                    |
| t_help_doc               | help_doc_catalog_id            |
| t_help_doc               | help_doc_id                    |
| t_help_doc               | page_view_count                |
| t_help_doc               | sort                           |
| t_help_doc               | title                          |
| t_help_doc               | update_time                    |
| t_help_doc               | user_view_count                |
| t_help_doc_catalog       | create_time                    |
| t_help_doc_catalog       | help_doc_catalog_id            |
| t_help_doc_catalog       | name                           |
| t_help_doc_catalog       | parent_id                      |
| t_help_doc_catalog       | sort                           |
| t_help_doc_catalog       | update_time                    |
| t_help_doc_relation      | create_time                    |
| t_help_doc_relation      | help_doc_id                    |
| t_help_doc_relation      | relation_id                    |
| t_help_doc_relation      | relation_name                  |
| t_help_doc_relation      | update_time                    |
| t_help_doc_view_record   | create_time                    |
| t_help_doc_view_record   | first_ip                       |
| t_help_doc_view_record   | first_user_agent               |
| t_help_doc_view_record   | help_doc_id                    |
| t_help_doc_view_record   | last_ip                        |
| t_help_doc_view_record   | last_user_agent                |
| t_help_doc_view_record   | page_view_count                |
| t_help_doc_view_record   | update_time                    |
| t_help_doc_view_record   | user_id                        |
| t_help_doc_view_record   | user_name                      |
| t_invitation_records     | commission_earned              |
| t_invitation_records     | create_time                    |
| t_invitation_records     | first_order_time               |
| t_invitation_records     | first_recharge_time            |
| t_invitation_records     | id                             |
| t_invitation_records     | invitee_id                     |
| t_invitation_records     | inviter_id                     |
| t_invitation_records     | status                         |
| t_login_fail             | create_time                    |
| t_login_fail             | lock_flag                      |
| t_login_fail             | login_fail_count               |
| t_login_fail             | login_fail_id                  |
| t_login_fail             | login_lock_begin_time          |
| t_login_fail             | login_name                     |
| t_login_fail             | update_time                    |
| t_login_fail             | user_id                        |
| t_login_fail             | user_type                      |
| t_login_log              | create_time                    |
| t_login_log              | login_device                   |
| t_login_log              | login_ip                       |
| t_login_log              | login_ip_region                |
| t_login_log              | login_log_id                   |
| t_login_log              | login_result                   |
| t_login_log              | remark                         |
| t_login_log              | update_time                    |
| t_login_log              | user_agent                     |
| t_login_log              | user_id                        |
| t_login_log              | user_name                      |
| t_login_log              | user_type                      |
| t_mail_template          | create_time                    |
| t_mail_template          | disable_flag                   |
| t_mail_template          | template_code                  |
| t_mail_template          | template_content               |
| t_mail_template          | template_subject               |
| t_mail_template          | template_type                  |
| t_mail_template          | update_time                    |
| t_menu                   | api_perms                      |
| t_menu                   | cache_flag                     |
| t_menu                   | component                      |
| t_menu                   | context_menu_id                |
| t_menu                   | create_time                    |
| t_menu                   | create_user_id                 |
| t_menu                   | deleted_flag                   |
| t_menu                   | disabled_flag                  |
| t_menu                   | frame_flag                     |
| t_menu                   | frame_url                      |
| t_menu                   | icon                           |
| t_menu                   | menu_id                        |
| t_menu                   | menu_name                      |
| t_menu                   | menu_type                      |
| t_menu                   | parent_id                      |
| t_menu                   | path                           |
| t_menu                   | perms_type                     |
| t_menu                   | sort                           |
| t_menu                   | update_time                    |
| t_menu                   | update_user_id                 |
| t_menu                   | visible_flag                   |
| t_menu                   | web_perms                      |
| t_message                | content                        |
| t_message                | create_time                    |
| t_message                | data_id                        |
| t_message                | message_id                     |
| t_message                | message_type                   |
| t_message                | read_flag                      |
| t_message                | read_time                      |
| t_message                | receiver_user_id               |
| t_message                | receiver_user_type             |
| t_message                | title                          |
| t_message                | update_time                    |
| t_notice                 | all_visible_flag               |
| t_notice                 | attachment                     |
| t_notice                 | author                         |
| t_notice                 | content_html                   |
| t_notice                 | content_text                   |
| t_notice                 | create_time                    |
| t_notice                 | create_user_id                 |
| t_notice                 | deleted_flag                   |
| t_notice                 | document_number                |
| t_notice                 | notice_id                      |
| t_notice                 | notice_type_id                 |
| t_notice                 | page_view_count                |
| t_notice                 | publish_time                   |
| t_notice                 | scheduled_publish_flag         |
| t_notice                 | source                         |
| t_notice                 | title                          |
| t_notice                 | update_time                    |
| t_notice                 | user_view_count                |
| t_notice_type            | create_time                    |
| t_notice_type            | notice_type_id                 |
| t_notice_type            | notice_type_name               |
| t_notice_type            | update_time                    |
| t_notice_view_record     | create_time                    |
| t_notice_view_record     | employee_id                    |
| t_notice_view_record     | first_ip                       |
| t_notice_view_record     | first_user_agent               |
| t_notice_view_record     | last_ip                        |
| t_notice_view_record     | last_user_agent                |
| t_notice_view_record     | notice_id                      |
| t_notice_view_record     | page_view_count                |
| t_notice_view_record     | update_time                    |
| t_notice_visible_range   | create_time                    |
| t_notice_visible_range   | data_id                        |
| t_notice_visible_range   | data_type                      |
| t_notice_visible_range   | notice_id                      |
| t_oa_bank                | account_name                   |
| t_oa_bank                | account_number                 |
| t_oa_bank                | bank_id                        |
| t_oa_bank                | bank_name                      |
| t_oa_bank                | business_flag                  |
| t_oa_bank                | create_time                    |
| t_oa_bank                | create_user_id                 |
| t_oa_bank                | create_user_name               |
| t_oa_bank                | deleted_flag                   |
| t_oa_bank                | disabled_flag                  |
| t_oa_bank                | enterprise_id                  |
| t_oa_bank                | remark                         |
| t_oa_bank                | update_time                    |
| t_oa_enterprise          | address                        |
| t_oa_enterprise          | business_license               |
| t_oa_enterprise          | city                           |
| t_oa_enterprise          | city_name                      |
| t_oa_enterprise          | contact                        |
| t_oa_enterprise          | contact_phone                  |
| t_oa_enterprise          | create_time                    |
| t_oa_enterprise          | create_user_id                 |
| t_oa_enterprise          | create_user_name               |
| t_oa_enterprise          | deleted_flag                   |
| t_oa_enterprise          | disabled_flag                  |
| t_oa_enterprise          | district                       |
| t_oa_enterprise          | district_name                  |
| t_oa_enterprise          | email                          |
| t_oa_enterprise          | enterprise_id                  |
| t_oa_enterprise          | enterprise_logo                |
| t_oa_enterprise          | enterprise_name                |
| t_oa_enterprise          | province                       |
| t_oa_enterprise          | province_name                  |
| t_oa_enterprise          | type                           |
| t_oa_enterprise          | unified_social_credit_code     |
| t_oa_enterprise          | update_time                    |
| t_oa_enterprise_employee | create_time                    |
| t_oa_enterprise_employee | employee_id                    |
| t_oa_enterprise_employee | enterprise_employee_id         |
| t_oa_enterprise_employee | enterprise_id                  |
| t_oa_enterprise_employee | update_time                    |
| t_oa_invoice             | account_number                 |
| t_oa_invoice             | bank_name                      |
| t_oa_invoice             | create_time                    |
| t_oa_invoice             | create_user_id                 |
| t_oa_invoice             | create_user_name               |
| t_oa_invoice             | deleted_flag                   |
| t_oa_invoice             | disabled_flag                  |
| t_oa_invoice             | enterprise_id                  |
| t_oa_invoice             | invoice_heads                  |
| t_oa_invoice             | invoice_id                     |
| t_oa_invoice             | remark                         |
| t_oa_invoice             | taxpayer_identification_number |
| t_oa_invoice             | update_time                    |
| t_operate_log            | content                        |
| t_operate_log            | create_time                    |
| t_operate_log            | fail_reason                    |
| t_operate_log            | ip                             |
| t_operate_log            | ip_region                      |
| t_operate_log            | method                         |
| t_operate_log            | module                         |
| t_operate_log            | operate_log_id                 |
| t_operate_log            | operate_user_id                |
| t_operate_log            | operate_user_name              |
| t_operate_log            | operate_user_type              |
| t_operate_log            | param                          |
| t_operate_log            | success_flag                   |
| t_operate_log            | update_time                    |
| t_operate_log            | url                            |
| t_operate_log            | user_agent                     |
| t_order_logistics        | courier_company                |
| t_order_logistics        | create_time                    |
| t_order_logistics        | deliver_time                   |
| t_order_logistics        | id                             |
| t_order_logistics        | order_id                       |
| t_order_logistics        | shipped_time                   |
| t_order_logistics        | status                         |
| t_order_logistics        | tracking_info                  |
| t_order_logistics        | tracking_number                |
| t_orders                 | activity_id                    |
| t_orders                 | alone_flag                     |
| t_orders                 | amount_paid                    |
| t_orders                 | complete_time                  |
| t_orders                 | create_time                    |
| t_orders                 | deleted_flag                   |
| t_orders                 | draw_result                    |
| t_orders                 | draw_time                      |
| t_orders                 | experience_paid                |
| t_orders                 | goods_id                       |
| t_orders                 | id                             |
| t_orders                 | order_sn                       |
| t_orders                 | payable_amount                 |
| t_orders                 | payment_method                 |
| t_orders                 | payment_time                   |
| t_orders                 | points_paid                    |
| t_orders                 | refund_reason                  |
| t_orders                 | refund_time                    |
| t_orders                 | reward_amount                  |
| t_orders                 | settle_flag                    |
| t_orders                 | shipping_address_id            |
| t_orders                 | sku_id                         |
| t_orders                 | status                         |
| t_orders                 | subsidy_paid                   |
| t_orders                 | update_time                    |
| t_orders                 | user_id                        |
| t_orders                 | win_option                     |
| t_password_log           | create_time                    |
| t_password_log           | id                             |
| t_password_log           | new_password                   |
| t_password_log           | old_password                   |
| t_password_log           | update_time                    |
| t_password_log           | user_id                        |
| t_password_log           | user_type                      |
| t_popups                 | create_time                    |
| t_popups                 | end_time                       |
| t_popups                 | id                             |
| t_popups                 | image_url                      |
| t_popups                 | link_url                       |
| t_popups                 | start_time                     |
| t_popups                 | status                         |
| t_popups                 | title                          |
| t_popups                 | trigger_rules                  |
| t_position               | create_time                    |
| t_position               | deleted_flag                   |
| t_position               | position_id                    |
| t_position               | position_level                 |
| t_position               | position_name                  |
| t_position               | remark                         |
| t_position               | sort                           |
| t_position               | update_time                    |
| t_reload_item            | args                           |
| t_reload_item            | create_time                    |
| t_reload_item            | identification                 |
| t_reload_item            | tag                            |
| t_reload_item            | update_time                    |
| t_reload_result          | args                           |
| t_reload_result          | create_time                    |
| t_reload_result          | exception                      |
| t_reload_result          | identification                 |
| t_reload_result          | result                         |
| t_reload_result          | tag                            |
| t_role                   | create_time                    |
| t_role                   | remark                         |
| t_role                   | role_code                      |
| t_role                   | role_id                        |
| t_role                   | role_name                      |
| t_role                   | update_time                    |
| t_role_data_scope        | create_time                    |
| t_role_data_scope        | data_scope_type                |
| t_role_data_scope        | id                             |
| t_role_data_scope        | role_id                        |
| t_role_data_scope        | update_time                    |
| t_role_data_scope        | view_type                      |
| t_role_employee          | create_time                    |
| t_role_employee          | employee_id                    |
| t_role_employee          | id                             |
| t_role_employee          | role_id                        |
| t_role_employee          | update_time                    |
| t_role_menu              | create_time                    |
| t_role_menu              | menu_id                        |
| t_role_menu              | role_id                        |
| t_role_menu              | role_menu_id                   |
| t_role_menu              | update_time                   |
| t_serial_number          | business_name                  |
| t_serial_number          | create_time                    |
| t_serial_number          | format                         |
| t_serial_number          | init_number                    |
| t_serial_number          | last_number                    |
| t_serial_number          | last_time                      |
| t_serial_number          | remark                         |
| t_serial_number          | rule_type                      |
| t_serial_number          | serial_number_id               |
| t_serial_number          | step_random_range              |
| t_serial_number          | update_time                    |
| t_serial_number_record   | count                          |
| t_serial_number_record   | create_time                    |
| t_serial_number_record   | last_number                    |
| t_serial_number_record   | last_time                      |
| t_serial_number_record   | record_date                    |
| t_serial_number_record   | serial_number_id               |
| t_serial_number_record   | update_time                    |
| t_smart_job              | create_time                    |
| t_smart_job              | deleted_flag                   |
| t_smart_job              | enabled_flag                   |
| t_smart_job              | job_class                      |
| t_smart_job              | job_id                         |
| t_smart_job              | job_name                       |
| t_smart_job              | last_execute_log_id            |
| t_smart_job              | last_execute_time              |
| t_smart_job              | param                          |
| t_smart_job              | remark                         |
| t_smart_job              | sort                           |
| t_smart_job              | trigger_type                   |
| t_smart_job              | trigger_value                  |
| t_smart_job              | update_name                    |
| t_smart_job              | update_time                    |
| t_smart_job_log          | create_name                    |
| t_smart_job_log          | create_time                    |
| t_smart_job_log          | execute_end_time               |
| t_smart_job_log          | execute_result                 |
| t_smart_job_log          | execute_start_time             |
| t_smart_job_log          | execute_time_millis            |
| t_smart_job_log          | ip                             |
| t_smart_job_log          | job_id                         |
| t_smart_job_log          | job_name                       |
| t_smart_job_log          | log_id                         |
| t_smart_job_log          | param                          |
| t_smart_job_log          | process_id                     |
| t_smart_job_log          | program_path                   |
| t_smart_job_log          | success_flag                   |
| t_table_column           | columns                        |
| t_table_column           | create_time                    |
| t_table_column           | table_column_id                |
| t_table_column           | table_id                       |
| t_table_column           | update_time                    |
| t_table_column           | user_id                        |
| t_table_column           | user_type                      |
| t_team_rewards           | create_time                    |
| t_team_rewards           | id                             |
| t_team_rewards           | qualified_count                |
| t_team_rewards           | reward_amount                  |
| t_team_rewards           | reward_date                    |
| t_team_rewards           | status                         |
| t_team_rewards           | user_id                        |
| t_user_address           | address_line                   |
| t_user_address           | city                           |
| t_user_address           | create_time                    |
| t_user_address           | district                       |
| t_user_address           | id                             |
| t_user_address           | is_default                     |
| t_user_address           | phone_number                   |
| t_user_address           | province                       |
| t_user_address           | recipient_name                 |
| t_user_address           | user_id                        |
| t_wallet_transactions    | amount                         |
| t_wallet_transactions    | balance_after                  |
| t_wallet_transactions    | create_time                    |
| t_wallet_transactions    | description                    |
| t_wallet_transactions    | experience_balance_after       |
| t_wallet_transactions    | id                             |
| t_wallet_transactions    | mode                           |
| t_wallet_transactions    | points_after                   |
| t_wallet_transactions    | related_id                     |
| t_wallet_transactions    | related_user_id                |
| t_wallet_transactions    | type                           |
| t_wallet_transactions    | user_id                        |
| t_wallets                | balance                        |
| t_wallets                | create_time                    |
| t_wallets                | experience_balance             |
| t_wallets                | points                         |
| t_wallets                | status                         |
| t_wallets                | total_recharge                 |
| t_wallets                | total_withdraw                 |
| t_wallets                | update_time                    |
| t_wallets                | user_id                        |
| t_withdrawals            | account_holder                 |
| t_withdrawals            | actual_amount                  |
| t_withdrawals            | amount                         |
| t_withdrawals            | bank_account                   |
| t_withdrawals            | bank_name                      |
| t_withdrawals            | create_time                    |
| t_withdrawals            | fee                            |
| t_withdrawals            | id                             |
| t_withdrawals            | processed_by                   |
| t_withdrawals            | processed_time                 |
| t_withdrawals            | rejection_reason               |
| t_withdrawals            | status                         |
| t_withdrawals            | user_id                        |
+--------------------------+--------------------------------+
