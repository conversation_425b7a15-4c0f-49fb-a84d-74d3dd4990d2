#!/bin/bash
# Pre-commit hook - 自动记录修改信息

# 创建修改记录目录
mkdir -p .git/change-logs

# 获取当前时间戳
timestamp=$(date +"%Y%m%d_%H%M%S")

# 获取当前分支
current_branch=$(git branch --show-current)

# 创建修改记录文件
change_log_file=".git/change-logs/${timestamp}_${current_branch}.md"

echo "# 修改记录 - $(date)" > "$change_log_file"
echo "分支: $current_branch" >> "$change_log_file"
echo "" >> "$change_log_file"

# 记录修改的文件
echo "## 修改的文件" >> "$change_log_file"
git diff --cached --name-only | while read file; do
    echo "- $file" >> "$change_log_file"
done

echo "" >> "$change_log_file"

# 记录详细的修改统计
echo "## 修改统计" >> "$change_log_file"
git diff --cached --stat >> "$change_log_file"

echo "" >> "$change_log_file"

# 记录新增和删除的行数
echo "## 详细修改" >> "$change_log_file"
git diff --cached --numstat | while read added removed file; do
    echo "- $file: +$added -$removed" >> "$change_log_file"
done

echo "✅ 修改记录已保存: $change_log_file"