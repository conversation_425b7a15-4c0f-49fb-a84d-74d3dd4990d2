<!DOCTYPE html>
<html>
<head>
    <title>Image Test</title>
</head>
<body>
    <h1>Image Loading Test</h1>
    
    <h2>Direct Image Tag</h2>
    <img src="http://localhost/upload/public/common/67c558bb7e884068a0682a630d5dafe1_20250723004853.jpg" 
         alt="Test Image" 
         style="max-width: 200px;"
         onload="console.log('Image loaded successfully')" 
         onerror="console.log('Image failed to load')">
    
    <h2>JavaScript Fetch Test</h2>
    <button onclick="testFetch()">Test Fetch</button>
    <div id="result"></div>
    
    <script>
    async function testFetch() {
        try {
            console.log('Starting fetch test...');
            const response = await fetch('http://localhost/upload/public/common/67c558bb7e884068a0682a630d5dafe1_20250723004853.jpg');
            console.log('Response status:', response.status);
            console.log('Response headers:', [...response.headers.entries()]);
            
            if (response.ok) {
                const blob = await response.blob();
                console.log('Blob size:', blob.size);
                document.getElementById('result').innerHTML = `
                    <p>Fetch successful! Status: ${response.status}</p>
                    <p>Content-Type: ${response.headers.get('content-type')}</p>
                    <p>Content-Length: ${response.headers.get('content-length')}</p>
                    <p>Blob size: ${blob.size} bytes</p>
                `;
            } else {
                document.getElementById('result').innerHTML = `<p>Fetch failed: ${response.status}</p>`;
            }
        } catch (error) {
            console.error('Fetch error:', error);
            document.getElementById('result').innerHTML = `<p>Fetch error: ${error.message}</p>`;
        }
    }
    </script>
</body>
</html>