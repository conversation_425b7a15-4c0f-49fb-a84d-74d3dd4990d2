#!/usr/bin/env node
/**
 * GitHub Token权限检查脚本
 * 验证Token是否具有所需的权限
 */

const https = require('https');

class TokenPermissionChecker {
  constructor(token) {
    this.token = token;
    this.requiredScopes = [
      'repo',
      'workflow',
      'write:packages',
      'read:packages',
      'admin:repo_hook'
    ];
  }

  makeRequest(path, method = 'GET') {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'api.github.com',
        path: path,
        method: method,
        headers: {
          'Authorization': `token ${this.token}`,
          'User-Agent': 'PingTuan-Token-Checker',
          'Accept': 'application/vnd.github.v3+json'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.end();
    });
  }

  async checkUserInfo() {
    try {
      const response = await this.makeRequest('/user');
      if (response.statusCode === 200) {
        const userData = JSON.parse(response.data);
        return {
          success: true,
          user: userData.login,
          name: userData.name,
          email: userData.email
        };
      } else {
        return {
          success: false,
          error: 'Token验证失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async checkTokenScopes() {
    try {
      const response = await this.makeRequest('/user');
      const scopes = response.headers['x-oauth-scopes'];
      
      if (scopes) {
        const availableScopes = scopes.split(',').map(s => s.trim());
        return {
          success: true,
          scopes: availableScopes
        };
      } else {
        return {
          success: false,
          error: '无法获取Token权限范围'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async checkRepositoryAccess() {
    try {
      const response = await this.makeRequest('/repos/mengdong88/PingTuan');
      if (response.statusCode === 200) {
        const repoData = JSON.parse(response.data);
        return {
          success: true,
          permissions: repoData.permissions
        };
      } else {
        return {
          success: false,
          error: '无法访问仓库'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async checkWorkflowPermission() {
    try {
      // 尝试获取workflow runs来检查workflow权限
      const response = await this.makeRequest('/repos/mengdong88/PingTuan/actions/runs');
      return {
        success: response.statusCode === 200,
        message: response.statusCode === 200 ? '✅ 具有workflow权限' : '❌ 缺少workflow权限'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  printResult(title, result) {
    console.log(`\n${title}:`);
    console.log('='.repeat(50));
    
    if (result.success) {
      console.log('✅ 成功');
      Object.keys(result).forEach(key => {
        if (key !== 'success') {
          console.log(`  ${key}: ${JSON.stringify(result[key], null, 2)}`);
        }
      });
    } else {
      console.log('❌ 失败');
      console.log(`  错误: ${result.error}`);
    }
  }

  checkScopeRequirements(availableScopes) {
    console.log('\n权限检查结果:');
    console.log('='.repeat(50));
    
    let allRequiredPresent = true;
    
    this.requiredScopes.forEach(requiredScope => {
      const hasScope = availableScopes.some(scope => 
        scope === requiredScope || scope.startsWith(requiredScope + ':')
      );
      
      if (hasScope) {
        console.log(`✅ ${requiredScope}`);
      } else {
        console.log(`❌ ${requiredScope} - 缺少此权限`);
        allRequiredPresent = false;
      }
    });
    
    console.log('\n可用权限范围:');
    availableScopes.forEach(scope => {
      console.log(`  - ${scope}`);
    });
    
    return allRequiredPresent;
  }

  async run() {
    console.log('🔐 GitHub Token权限检查器');
    console.log('='.repeat(50));
    
    // 检查用户信息
    const userInfo = await this.checkUserInfo();
    this.printResult('用户信息', userInfo);
    
    if (!userInfo.success) {
      console.log('\n❌ Token验证失败，请检查Token是否正确');
      process.exit(1);
    }
    
    // 检查Token权限范围
    const scopeInfo = await this.checkTokenScopes();
    this.printResult('Token权限范围', scopeInfo);
    
    if (scopeInfo.success) {
      const hasAllRequired = this.checkScopeRequirements(scopeInfo.scopes);
      
      if (!hasAllRequired) {
        console.log('\n⚠️  缺少必需权限，请重新创建Token');
        console.log('创建Token时请确保勾选以下权限:');
        this.requiredScopes.forEach(scope => {
          console.log(`  - ${scope}`);
        });
        process.exit(1);
      }
    }
    
    // 检查仓库访问权限
    const repoAccess = await this.checkRepositoryAccess();
    this.printResult('仓库访问权限', repoAccess);
    
    // 检查workflow权限
    const workflowPermission = await this.checkWorkflowPermission();
    this.printResult('Workflow权限', workflowPermission);
    
    console.log('\n🎉 所有权限检查完成!');
    
    if (workflowPermission.success) {
      console.log('✅ Token配置正确，可以推送workflow文件');
    } else {
      console.log('❌ Token缺少workflow权限，请重新创建Token');
      console.log('创建Token时请确保勾选 "workflow" 权限');
    }
  }
}

// 运行检查
if (require.main === module) {
  const token = process.argv[2];
  
  if (!token) {
    console.log('使用方法: node check-token-permissions.js <github_token>');
    console.log('示例: node check-token-permissions.js ghp_xxxxxxxxxxxx');
    process.exit(1);
  }
  
  const checker = new TokenPermissionChecker(token);
  checker.run().catch(console.error);
}

module.exports = TokenPermissionChecker;