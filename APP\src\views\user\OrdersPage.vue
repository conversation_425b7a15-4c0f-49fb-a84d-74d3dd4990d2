<template>
  <div class="bg-gray-100 min-h-screen">
    <!-- 顶部导航 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-b">
      <iconify-icon 
        icon="material-symbols:arrow-back" 
        class="text-2xl text-gray-700 cursor-pointer" 
        @click="goBack"
      ></iconify-icon>
      <h1 class="text-lg font-bold text-gray-800">我的订单</h1>
      <iconify-icon 
        icon="material-symbols:search" 
        class="text-2xl text-gray-700 cursor-pointer" 
        @click="handleSearch"
      ></iconify-icon>
    </div>


    <!-- 订单列表 -->
    <div class="pb-20">
      <!-- 加载状态 -->
      <div v-if="loading && orderList.length === 0" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">正在加载订单...</p>
      </div>

      <!-- 订单列表 -->
      <div v-else-if="orderList.length > 0" class="px-4 py-4 space-y-4">
        <!-- 订单卡片 -->
        <div 
          v-for="order in orderList" 
          :key="order.id"
          class="bg-white rounded-2xl p-4 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
          @click="viewOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <div class="flex items-center mb-3">
            <div class="flex items-center space-x-3">
              <span class="text-xs text-gray-600">订单号：{{ order.orderSn || order.orderNo || order.id }}</span>
              <!-- 订单类型标识 -->
              <div class="flex items-center space-x-2">
                <span 
                  :class="[
                    'text-xs px-2 py-1 rounded-full font-medium',
                    getOrderTypeClass(order)
                  ]"
                >
                  {{ getOrderTypeText(order) }}
                </span>
                <!-- 拼团状态 -->
                <span 
                  v-if="!isDirectBuyOrder(order)"
                  :class="[
                    'text-xs px-2 py-1 rounded-full font-medium',
                    getGroupResultClass(order)
                  ]"
                >
                  {{ getGroupResultText(order) }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 商品信息 -->
          <div class="flex mb-3">
            <div 
              class="w-16 h-16 bg-cover bg-center rounded-lg mr-3 bg-gray-200"
              :style="getImageStyle(order)"
            ></div>
            <div class="flex-1">
              <h3 class="font-medium text-gray-800 mb-1">{{ getProductName(order) }}</h3>
              <div v-if="getProductSpec(order)" class="text-sm text-gray-500 mb-1">{{ getProductSpec(order) }}</div>
              <div class="flex items-center">
                <span 
                  v-if="!isDirectBuyOrder(order)"
                  :class="[
                    'text-white text-xs px-2 py-1 rounded mr-2',
                    getGroupTypeClass(order.activityId)
                  ]"
                >
                  {{ getGroupTypeText(order.activityId) }}
                </span>
                <span class="text-red-500 font-bold">{{ formatPrice(getOrderPrice(order)) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 订单状态信息 -->
          <div v-if="order.status === 'PENDING'" class="bg-blue-50 p-3 rounded-lg mb-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-blue-700 text-sm">
                {{ order.orderType === 'GROUP_BUY' ? '等待开奖' : '等待发货' }}
              </span>
              <span class="text-blue-700 text-sm">{{ getTimeDisplay(order) }}</span>
            </div>
            <div class="text-xs text-blue-600">
              <span v-if="order.orderType === 'GROUP_BUY'">订单已提交，等待系统开奖</span>
              <span v-else>订单已提交，商家正在处理</span>
            </div>
          </div>

          <div v-else-if="order.status === 'COMPLETED'" class="bg-green-50 p-3 rounded-lg mb-3">
            <div class="flex items-center justify-between text-green-700 text-sm">
              <div class="flex items-center">
                <iconify-icon icon="material-symbols:check-circle" class="mr-2"></iconify-icon>
                <span>{{ getCompletedResultText(order) }}</span>
              </div>
              <div class="text-xs text-green-600">
                {{ getCompletedTimeDisplay(order) }}
              </div>
            </div>
          </div>

          <div v-else-if="order.status === 'FAILED'" class="bg-gray-50 p-3 rounded-lg mb-3">
            <div class="text-gray-600 text-sm">
              <div class="mb-1">{{ getFailedResultText(order) }}</div>
              <div class="text-green-600">本金已退回</div>
            </div>
          </div>

          <div v-else-if="order.status === 'SHIPPING'" class="bg-purple-50 p-3 rounded-lg mb-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-purple-700 text-sm">物流信息</span>
              <span class="text-purple-700 text-sm cursor-pointer hover:text-purple-800" @click.stop="viewLogistics(order)">查看详情</span>
            </div>
            <div class="text-sm text-purple-600">
              <div class="mb-1">• 商品已发货</div>
              <div>• 预计3-5天送达</div>
            </div>
          </div>

          <!-- 订单金额 -->
          <div class="text-sm text-gray-600 mb-3">
            <div class="flex items-center justify-between mb-1">
              <span>共1件商品</span>
              <span>{{ getPaymentAmountLabel(order) }}：<span class="text-red-500 font-bold">{{ formatPrice(getActualPaidAmount(order)) }}</span></span>
            </div>
            <!-- 奖励返还金额 -->
            <div v-if="shouldShowRewardAmount(order)" class="flex items-center justify-end">
              <span>返还奖励：<span class="text-green-500 font-bold">{{ formatPrice(getRewardAmount(order)) }}</span></span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex justify-end">
            <div class="flex space-x-2">
              <template v-if="order.status === 'PENDING'">
                <ShareButton
                  v-if="order.orderType === 'GROUP_BUY'"
                  text="邀请好友"
                  button-class="simple-share-button"
                  success-message="邀请链接已复制，快去分享给好友吧"
                  @click.stop
                />
                <button 
                  v-else
                  class="px-4 py-2 border border-gray-400 text-gray-600 text-sm rounded-full hover:bg-gray-50 transition-colors"
                  @click.stop="contactService(order)"
                >
                  联系客服
                </button>
              </template>
              
              <template v-else-if="order.status === 'COMPLETED'">
                <button 
                  class="px-4 py-2 border border-gray-400 text-gray-600 text-sm rounded-full hover:bg-gray-50 transition-colors"
                  @click.stop="contactService(order)"
                >
                  联系客服
                </button>
                <button 
                  class="px-4 py-2 bg-blue-500 text-white text-sm rounded-full hover:bg-blue-600 transition-colors"
                  @click.stop="viewOrderDetail(order)"
                >
                  查看详情
                </button>
              </template>
              
              <template v-else-if="order.status === 'SHIPPING'">
                <button 
                  class="px-4 py-2 border border-gray-400 text-gray-600 text-sm rounded-full hover:bg-gray-50 transition-colors"
                  @click.stop="contactService(order)"
                >
                  联系客服
                </button>
                <button 
                  class="px-4 py-2 bg-blue-500 text-white text-sm rounded-full hover:bg-blue-600 transition-colors"
                  @click.stop="confirmReceipt(order)"
                >
                  确认收货
                </button>
              </template>
              
              <template v-else>
                <button 
                  class="px-4 py-2 border border-gray-400 text-gray-600 text-sm rounded-full hover:bg-gray-50 transition-colors"
                  @click.stop="buyAgain(order)"
                >
                  再次购买
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-16">
        <iconify-icon icon="material-symbols:receipt-long" class="text-gray-300 text-6xl mb-4"></iconify-icon>
        <p class="text-gray-500 text-sm mb-6">暂无订单</p>
        <button 
          class="bg-blue-500 text-white py-3 px-6 rounded-full font-medium text-sm hover:bg-blue-600 transition-colors"
          @click="goShopping"
        >
          去逛逛
        </button>
      </div>

      <!-- 加载更多 -->
      <div v-if="loading && orderList.length > 0" class="text-center py-4">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">加载中...</p>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <BottomNav current="user" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError } from '@/utils/message.js'
import BottomNav from '@/components/common/BottomNav.vue'
import { requireAuth } from '@/composables/usePageAuth'

import ShareButton from '@/components/common/ShareButton.vue'

// 启用页面级登录检查
requireAuth({
  errorMessage: '访问订单页面需要先登录'
})
import { getImageUrl } from '@/config/image'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { formatPrice, getGroupTypeText } from '@/utils/format'
import { useAuthGuard } from '@/composables/useAuthGuard'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 登录状态检查
const {
  isAuthenticated,
  isLoading: authLoading,
  authError,
  canAccess,
  requireAuth,
  recheckAuth
} = useAuthGuard({
  redirectPath: '/login',
  showMessages: true,
  autoRedirect: true
})

// 页面状态
const loading = ref(false)
const allOrders = ref([])
const productsMap = ref(new Map()) // 商品信息映射表

// API实例
let apiAdapter = null

// 初始化API适配器
const initApiAdapter = async () => {
  if (!apiAdapter) {
    apiAdapter = new StandardApiAdapter()
  }
}

// 加载商品缓存数据
const loadProductsFromCache = () => {
  try {
    const homeData = localStorage.getItem('homeData')
    if (homeData) {
      const data = JSON.parse(homeData)
      if (data.products && Array.isArray(data.products)) {
        // 将商品数组转换为Map，以goodsId为key
        const map = new Map()
        data.products.forEach(product => {
          if (product.goodsId) {
            map.set(product.goodsId, product)
          }
        })
        productsMap.value = map
        console.log('✅ 从缓存加载商品数据成功:', map.size, '个商品')
        return true
      }
    }
  } catch (error) {
    console.warn('⚠️ 加载商品缓存失败:', error)
  }
  return false
}

// 加载订单数据
const loadOrders = async () => {
  try {
    // 检查登录状态
    if (!requireAuth('查看订单需要先登录')) {
      return
    }
    
    loading.value = true
    await initApiAdapter()
    
    console.log('🔄 开始加载订单数据')
    const response = await apiAdapter.request('GET', '/orders', { status: 'all' })
    
    console.log('📦 订单API响应:', response)
    
    if (response.code === 0 || response.code === 200) {
      allOrders.value = response.data?.list || []
      console.log('✅ 订单数据加载成功:', allOrders.value.length, '条订单')
      console.log('📋 订单数据详情:', allOrders.value.slice(0, 2)) // 打印前2条数据用于调试
      
      // 保存订单数据到localStorage作为缓存，供订单详情页面使用
      localStorage.setItem('allOrders', JSON.stringify(allOrders.value))
      console.log('💾 订单数据已保存到localStorage缓存')
    } else {
      throw new Error(response.message || response.msg || '加载订单失败')
    }
  } catch (error) {
    console.error('❌ 加载订单失败:', error)
    
    // 如果是认证错误，触发重新检查
    if (error.message?.includes('401') || error.message?.includes('认证') || error.message?.includes('登录')) {
      console.log('🔐 检测到认证错误，重新检查登录状态')
      await recheckAuth()
      return
    }
    
    showError('加载订单失败，请重试')
    allOrders.value = []
  } finally {
    loading.value = false
  }
}

// 判断是否为直接购买订单
const isDirectBuyOrder = (order) => {
  return order.aloneFlag === true || order.aloneFlag === 1
}

// 获取订单类型显示文本
const getOrderTypeText = (order) => {
  return isDirectBuyOrder(order) ? '直接购买' : '拼团购买'
}


// 订单列表（显示全部订单，按创建时间从新到旧排序）
const orderList = computed(() => {
  return allOrders.value.sort((a, b) => {
    const timeA = new Date(a.createTime || 0).getTime()
    const timeB = new Date(b.createTime || 0).getTime()
    return timeB - timeA // 降序排列，最新的在前面
  })
})

// 获取订单类型样式
const getOrderTypeClass = (order) => {
  return isDirectBuyOrder(order)
    ? 'bg-blue-100 text-blue-600' 
    : 'bg-orange-100 text-orange-600'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classes = {
    PENDING: 'bg-blue-100 text-blue-600',
    COMPLETED: 'bg-green-100 text-green-600',
    FAILED: 'bg-gray-100 text-gray-600',
    SHIPPING: 'bg-purple-100 text-purple-600',
    EXPIRED: 'bg-red-100 text-red-600',
    CANCELLED: 'bg-gray-100 text-gray-600',
    REFUNDED: 'bg-orange-100 text-orange-600'
  }
  return classes[status] || 'bg-gray-100 text-gray-600'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    PENDING: '待开奖',
    COMPLETED: '已完成',
    FAILED: '未中奖',
    SHIPPING: '待收货',
    EXPIRED: '已失效',
    CANCELLED: '已取消',
    REFUNDED: '已退单' // 订单列表页保留"已退单"显示
  }
  return texts[status] || '未知状态'
}

// 获取团类型样式
const getGroupTypeClass = (activityId) => {
  const classes = {
    3: 'bg-orange-500',
    4: 'bg-red-500',
    5: 'bg-purple-500',
    6: 'bg-green-500',
    7: 'bg-blue-500',
    8: 'bg-pink-500'
  }
  return classes[activityId] || 'bg-gray-500'
}

// 拼团类型文本现在使用统一的工具函数

// 获取商品名称
const getProductName = (order) => {
  // 优先从缓存的商品数据中获取
  const cachedProduct = productsMap.value.get(order.goodsId)
  if (cachedProduct) {
    return cachedProduct.goodsName || cachedProduct.name || cachedProduct.title
  }
  
  // 备用：从订单中获取
  return order.sku?.goodsName || order.goodsName || `商品ID: ${order.goodsId}`
}

// 获取商品规格
const getProductSpec = (order) => {
  // 如果是拼团订单，不显示规格信息
  if (!isDirectBuyOrder(order)) {
    return ''
  }
  return order.sku?.spec || '标准规格 x 1'
}

// 获取拼团结果状态样式
const getGroupResultClass = (order) => {
  if (isDirectBuyOrder(order)) return ''
  
  // 退单订单不显示拼团结果
  if (order.status === 'REFUNDED' || order.status === 'CANCELLED') {
    return 'bg-orange-100 text-orange-600'
  }
  
  // 根据订单状态和开奖结果判断
  if (order.status === 'COMPLETED' && order.drawResult === 1) {
    return 'bg-green-100 text-green-600'
  } else if (order.status === 'FAILED' || (order.status === 'COMPLETED' && order.drawResult === 0)) {
    return 'bg-red-100 text-red-600'
  } else if (order.status === 'PENDING') {
    return 'bg-yellow-100 text-yellow-600'
  }
  return 'bg-gray-100 text-gray-600'
}

// 获取拼团结果状态文本
const getGroupResultText = (order) => {
  if (isDirectBuyOrder(order)) return ''
  
  // 退单订单显示退单状态
  if (order.status === 'REFUNDED') {
    return '已退单'
  } else if (order.status === 'CANCELLED') {
    return '已取消'
  }
  
  // 根据订单状态和开奖结果判断
  if (order.status === 'COMPLETED' && order.drawResult === 1) {
    return '拼中'
  } else if (order.status === 'FAILED' || (order.status === 'COMPLETED' && order.drawResult === 0)) {
    return '未拼中'
  } else if (order.status === 'PENDING') {
    return '待开奖'
  }
  return '未知'
}

// 获取订单价格
const getOrderPrice = (order) => {
  // 如果是直接购买订单，使用pointsPaid
  if (isDirectBuyOrder(order)) {
    return order.pointsPaid || order.amountPaid || '0.00'
  }
  
  // 拼团订单使用原逻辑
  return order.amountPaid || order.sku?.price || order.price || '0.00'
}

// 获取实付金额（未中奖订单显示0）
const getActualPaidAmount = (order) => {
  // 如果是拼团订单且未中奖，实付金额显示为0
  if (!isDirectBuyOrder(order) && (order.status === 'FAILED' || (order.status === 'COMPLETED' && order.drawResult === 0))) {
    return '0.00'
  }
  
  return getOrderPrice(order)
}

// 获取奖励返还金额
const getRewardAmount = (order) => {
  // 只有拼团订单且未中奖才显示奖励返还金额
  if (!isDirectBuyOrder(order) && (order.status === 'FAILED' || (order.status === 'COMPLETED' && order.drawResult === 0))) {
    return order.rewardAmount || '0.00'
  }
  return null
}

// 判断是否显示奖励返还
const shouldShowRewardAmount = (order) => {
  return !isDirectBuyOrder(order) && (order.status === 'FAILED' || (order.status === 'COMPLETED' && order.drawResult === 0))
}

// 获取支付金额标签文本
const getPaymentAmountLabel = (order) => {
  // 如果是直接购买订单，需要根据实际支付金额判断
  if (isDirectBuyOrder(order)) {
    // 如果积分支付金额大于0，说明是积分支付
    if (order.pointsPaid > 0 && order.amountPaid === 0) {
      return '实付积分'
    }
  }
  return '实付'
}

// 价格格式化现在使用统一的越南盾格式化函数

// 获取图片样式
const getImageStyle = (order) => {
  // 优先从缓存的商品数据中获取图片
  const cachedProduct = productsMap.value.get(order.goodsId)
  let imageUrl = null
  
  if (cachedProduct) {
    imageUrl = cachedProduct.image || cachedProduct.imageUrl || cachedProduct.mainImage
  }
  
  // 备用：从订单中获取
  if (!imageUrl) {
    imageUrl = order.sku?.image || order.image
  }
  
  if (imageUrl) {
    const fullUrl = getImageUrl(imageUrl)
    return `background-image: url('${fullUrl}')`
  }
  return 'background-color: #f3f4f6'
}

// 获取时间显示（右上角显示，已完成订单不显示）
const getTimeDisplay = (order) => {
  // 已完成订单不在右上角显示时间
  if (order.status === 'COMPLETED') {
    return ''
  }
  
  let timeLabel = '下单时间'
  let timeValue = order.createTime
  
  // 根据订单状态显示相应的时间
  switch (order.status) {
    case 'PENDING':
      timeLabel = '下单时间'
      timeValue = order.createTime
      break
    case 'FAILED':
      if (order.drawTime) {
        timeLabel = '开奖时间'
        timeValue = order.drawTime
      }
      break
    case 'SHIPPING':
      if (order.paymentTime) {
        timeLabel = '支付时间'
        timeValue = order.paymentTime
      }
      break
    default:
      timeLabel = '下单时间'
      timeValue = order.createTime
  }
  
  if (timeValue) {
    const formattedTime = new Date(timeValue).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    return `${timeLabel}：${formattedTime}`
  }
  return ''
}

// 获取已完成订单的完成时间显示
const getCompletedTimeDisplay = (order) => {
  let timeLabel = '完成时间'
  let timeValue = order.completeTime || order.drawTime
  
  if (timeValue) {
    const formattedTime = new Date(timeValue).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    return `${timeLabel}：${formattedTime}`
  }
  return ''
}

// 获取完成状态结果文本
const getCompletedResultText = (order) => {
  if (order.orderType === 'GROUP_BUY') {
    return order.drawResult === 1 ? '恭喜中奖！' : '订单已完成'
  } else {
    return '订单已完成'
  }
}

// 获取失败状态结果文本
const getFailedResultText = (order) => {
  if (order.orderType === 'GROUP_BUY') {
    return '很遗憾未中奖'
  } else {
    return '订单已取消'
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 搜索订单
const handleSearch = () => {
  showSuccess('搜索功能开发中')
}

// 查看订单详情
const viewOrderDetail = (order) => {
  console.log('👀 查看订单详情:', order)
  const orderId = order.id || order.orderSn
  if (orderId) {
    // 多种方式传递订单数据，确保数据能够正确传递
    
    // 方式1: 存储到sessionStorage
    const storageKey = `orderDetail_${orderId}`
    sessionStorage.setItem(storageKey, JSON.stringify(order))
    console.log('💾 订单数据已存储到sessionStorage:', storageKey)
    
    // 方式2: 通过路由state传递
    try {
      router.push({
        path: `/user/orders/${orderId}`,
        state: { orderData: order }
      })
    } catch (error) {
      // 如果路由state方式失败，使用普通的push方式
      console.warn('路由state传递失败，使用普通方式:', error)
      router.push(`/user/orders/${orderId}`)
    }
  } else {
    showError('订单ID不存在')
  }
}

// 邀请好友功能已由ShareButton组件实现

// 联系客服
const contactService = (order) => {
  showSuccess('联系客服功能开发中')
}

// 确认收货
const confirmReceipt = (order) => {
  if (confirm('确认已收到商品吗？')) {
    showSuccess('收货成功')
  }
}

// 再次购买
const buyAgain = (order) => {
  const productId = order.sku?.goodsId || order.goodsId
  if (productId) {
    router.push(`/product/${productId}`)
  } else {
    showError('商品信息不完整')
  }
}

// 查看物流
const viewLogistics = (order) => {
  showSuccess('查看物流详情功能开发中')
}

// 去逛逛
const goShopping = () => {
  router.push('/')
}

// 页面初始化
onMounted(async () => {
  console.log('🎯 OrdersPage mounted')
  
  // 等待认证检查完成
  if (authLoading.value) {
    console.log('⏳ 等待认证检查完成...')
    // 等待认证状态确定
    await new Promise(resolve => {
      const unwatch = watch([isAuthenticated, authLoading], ([auth, loading]) => {
        if (!loading) {
          unwatch()
          resolve()
        }
      }, { immediate: true })
    })
  }
  
  // 如果认证成功，加载数据
  if (isAuthenticated.value) {
    // 先加载商品缓存数据
    loadProductsFromCache()
    
    // 加载订单数据
    await loadOrders()
  } else {
    console.log('❌ 认证失败，跳过数据加载')
  }
})
</script>

<style scoped>
/* 自定义样式 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 卡片悬停效果 */
.shadow-sm:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>