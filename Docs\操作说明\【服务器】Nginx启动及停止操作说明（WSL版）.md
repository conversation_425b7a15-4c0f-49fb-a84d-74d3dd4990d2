# 【服务器】Nginx启动及停止操作说明（WSL版）

## 项目信息
- **服务名称**: Nginx Web服务器
- **配置目录**: `/etc/nginx/`
- **主配置文件**: `/etc/nginx/nginx.conf`
- **站点配置目录**: `/etc/nginx/sites-available/` 和 `/etc/nginx/sites-enabled/`
- **日志目录**: `/var/log/nginx/`
- **默认端口**: 80 (HTTP), 443 (HTTPS)

## 环境检查

### 检查Nginx是否已安装
```bash
# 检查Nginx版本
nginx -v

# 检查Nginx状态
systemctl status nginx

# 检查Nginx进程
ps aux | grep nginx
```

### 检查配置文件语法
```bash
# 测试配置文件语法
sudo nginx -t

# 测试并显示配置详情
sudo nginx -T
```

## 手动启动命令

### 方式1：使用systemctl启动（推荐）
```bash
# 启动Nginx服务
sudo systemctl start nginx

# 启动并设置开机自启
sudo systemctl enable nginx
sudo systemctl start nginx

# 重启Nginx服务
sudo systemctl restart nginx

# 重新加载配置文件（不中断连接）
sudo systemctl reload nginx
```

### 方式2：直接使用nginx命令启动
```bash
# 启动Nginx
sudo nginx

# 重新加载配置
sudo nginx -s reload

# 重新打开日志文件
sudo nginx -s reopen
```

## 手动停止命令

### 方式1：使用systemctl停止（推荐）
```bash
# 停止Nginx服务
sudo systemctl stop nginx

# 停止并禁用开机自启
sudo systemctl disable nginx
sudo systemctl stop nginx
```

### 方式2：使用nginx命令停止
```bash
# 快速停止（立即终止）
sudo nginx -s stop

# 优雅停止（等待连接处理完成）
sudo nginx -s quit
```

### 方式3：使用kill命令停止
```bash
# 查找Nginx主进程PID
ps aux | grep nginx | grep master

# 优雅停止（发送QUIT信号）
sudo kill -QUIT <master_pid>

# 快速停止（发送TERM信号）
sudo kill -TERM <master_pid>

# 强制停止（发送KILL信号）
sudo kill -9 <master_pid>

# 或者使用pkill
sudo pkill nginx
```

## 检查服务状态

### 检查Nginx服务状态
```bash
# 查看服务状态
sudo systemctl status nginx

# 查看是否开机自启
sudo systemctl is-enabled nginx

# 查看服务是否运行
sudo systemctl is-active nginx
```

### 检查端口占用情况
```bash
# 检查80端口
ss -tlnp | grep :80

# 检查443端口
ss -tlnp | grep :443

# 检查所有监听端口
ss -tlnp | grep nginx
```

### 查看Nginx进程
```bash
# 查看Nginx进程
ps aux | grep nginx

# 查看进程树
pstree -p | grep nginx
```

## 配置管理

### 配置文件操作
```bash
# 编辑主配置文件
sudo nano /etc/nginx/nginx.conf

# 创建新站点配置
sudo nano /etc/nginx/sites-available/example.com

# 启用站点配置
sudo ln -s /etc/nginx/sites-available/example.com /etc/nginx/sites-enabled/

# 禁用站点配置
sudo rm /etc/nginx/sites-enabled/example.com
```

### 测试和重载配置
```bash
# 测试配置语法
sudo nginx -t

# 如果测试通过，重新加载配置
sudo nginx -s reload
# 或者
sudo systemctl reload nginx
```

## 日志管理

### 查看访问日志
```bash
# 实时查看访问日志
sudo tail -f /var/log/nginx/access.log

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 查看特定站点的日志（如果配置了）
sudo tail -f /var/log/nginx/example.com.access.log
```

### 日志轮转
```bash
# 手动轮转日志
sudo nginx -s reopen

# 或者重启logrotate
sudo logrotate -f /etc/logrotate.d/nginx
```

## 常见问题解决

### 配置语法错误
```bash
# 检查语法错误
sudo nginx -t

# 查看详细错误信息
sudo journalctl -u nginx.service
```

### 端口被占用
```bash
# 查找占用80端口的进程
sudo lsof -ti:80

# 终止占用端口的进程
sudo lsof -ti:80 | xargs kill -9

# 或者修改Nginx监听端口
```

### 权限问题
```bash
# 检查Nginx用户权限
ps aux | grep nginx

# 修改文件权限（网站根目录）
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### 服务启动失败
```bash
# 查看详细错误信息
sudo journalctl -u nginx.service -f

# 检查配置文件
sudo nginx -t

# 重置服务状态
sudo systemctl reset-failed nginx
```

## 性能监控

### 查看连接状态
```bash
# 查看当前连接数
ss -an | grep :80 | wc -l

# 查看Nginx状态（需要配置status模块）
curl http://localhost/nginx_status
```

### 监控命令
```bash
# 实时监控访问日志
sudo tail -f /var/log/nginx/access.log | grep -E "GET|POST"

# 统计访问量
sudo awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10
```

## 安全建议

### 基本安全配置
```bash
# 隐藏Nginx版本信息（在nginx.conf中）
server_tokens off;

# 设置合适的worker进程数
worker_processes auto;

# 限制请求大小
client_max_body_size 10M;
```

### 防火墙配置
```bash
# 允许HTTP和HTTPS流量
sudo ufw allow 'Nginx Full'

# 或者分别配置
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```