package net.lab1024.sa.admin.module.business.userAddress.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.userAddress.domain.entity.UserAddressEntity;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressQueryForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.vo.UserAddressVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 用户收货地址表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-28 15:09:41
 * @Copyright -
 */

@Mapper
public interface UserAddressDao extends BaseMapper<UserAddressEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<UserAddressVO> queryPage(Page page, @Param("queryForm") UserAddressQueryForm queryForm);

    void setAllIsDefaultFalse(@Param("userId") Long userId);
}
