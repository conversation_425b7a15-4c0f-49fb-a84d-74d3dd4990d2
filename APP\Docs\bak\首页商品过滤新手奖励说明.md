# 首页商品过滤新手奖励功能说明

## 功能概述
在首页"全部商品"展示中，系统会自动过滤掉商品类型为"新手奖励"的商品，确保这些特殊商品不会在主要商品展示区域显示。

## 实现位置
- 文件：`APP/src/views/home/<USER>
- 方法：`currentProducts` 计算属性

## 过滤逻辑
在商品展示的数据处理流程中，增加了专门的过滤步骤：

```javascript
// 🚫 过滤掉"新手奖励"类型的商品
const filteredProducts = uniqueProducts.filter(product => {
  const goodsType = product.goodsType
  const isNewbieReward = goodsType === '新手奖励' || 
                        goodsType === 'newbie_reward' || 
                        goodsType === 'newbie' ||
                        goodsType === 'reward' ||
                        goodsType === 'beginner_reward' ||
                        goodsType === 'NewUser'
  
  if (isNewbieReward) {
    console.log(`🚫 过滤掉新手奖励商品: ${product.goodsName} (goodsType: ${goodsType})`)
    return false
  }
  
  return true
})
```

## 支持的商品类型标识
系统会识别以下 `goodsType` 值作为新手奖励商品：
- `新手奖励` - 中文标识
- `newbie_reward` - 英文下划线格式
- `newbie` - 简化英文标识
- `reward` - 奖励标识
- `beginner_reward` - 初学者奖励标识
- `NewUser` - 新用户标识（主要使用）

## 处理流程
1. **商品去重**：首先按 `goodsId` 去重，避免重复显示
2. **新手奖励过滤**：过滤掉所有新手奖励类型的商品
3. **分类筛选**：根据用户选择的商品分类进行筛选
4. **活动筛选**：根据选择的拼团活动进行筛选

## 日志输出
系统会在控制台输出详细的过滤日志：
- `🚫 过滤掉新手奖励商品: [商品名称] (goodsType: [商品类型])`
- `🚫 新手奖励过滤结果: [原始数量]个 -> [过滤后数量]个`

## 测试验证
1. 打开首页，查看"全部商品"区域
2. 确认没有显示商品类型为"NewUser"的商品
3. 检查浏览器控制台，查看过滤日志
4. 验证其他类型商品正常显示

## 注意事项
- 此过滤仅影响首页商品展示，不影响其他页面
- 新手奖励商品仍可通过其他入口（如专门的新手页面）访问
- 过滤逻辑在客户端执行，不影响后端数据
- 支持多种商品类型标识格式，确保兼容性
- 主要的新手奖励商品类型标识为 `NewUser`

## 相关文件
- `APP/src/views/home/<USER>
- `APP/src/api/home.js` - 商品数据API接口 