import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { createAuthGuard } from '@/utils/auth'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '首页', requiresAuth: false }
  },
  {
    path: '/category',
    name: 'Category',
    component: () => import('@/views/CategoryPage.vue'),
    meta: { title: '分类', requiresAuth: false }
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: () => import('@/views/ApiTestPage.vue'),
    meta: { title: 'API测试', requiresAuth: false }
  },
  {
    path: '/activity',
    name: 'Activity',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '活动专区', requiresAuth: false }
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/product/ProductDetailPage.vue'),
    meta: { title: '商品详情', requiresAuth: false }
  },
  {
    path: '/product/direct-buy/:id',
    name: 'DirectBuyDetail',
    component: () => import('@/views/product/DirectBuyDetailPage.vue'),
    meta: { title: '直接购买', requiresAuth: false }
  },
  {
    path: '/order/confirm',
    name: 'OrderConfirm',
    component: () => import('@/views/order/ConfirmPage.vue'),
    meta: { title: '确认订单', requiresAuth: true }
  },
  {
    path: '/order/direct-buy',
    name: 'DirectBuy',
    component: () => import('@/views/order/DirectBuyPage.vue'),
    meta: { title: '直接购买', requiresAuth: true }
  },
  {
    path: '/order/direct-buy-failure',
    name: 'DirectBuyFailure',
    component: () => import('@/views/order/DirectBuyFailurePage.vue'),
    meta: { title: '购买失败', requiresAuth: true }
  },
  {
    path: '/order/group-buy-failure',
    name: 'GroupBuyFailure',
    component: () => import('@/views/order/GroupBuyFailurePage.vue'),
    meta: { title: '拼团失败', requiresAuth: true }
  },
  {
    path: '/order/cancel',
    name: 'OrderCancel',
    component: () => import('@/views/order/OrderCancelPage.vue'),
    meta: { title: '申请退单', requiresAuth: true }
  },
  {
    path: '/payment',
    name: 'Payment',
    component: () => import('@/views/order/PaymentPage.vue'),
    meta: { title: '支付', requiresAuth: true }
  },
  {
    path: '/order/waiting',
    name: 'Waiting',
    component: () => import('@/views/order/WaitingPage.vue'),
    meta: { title: '等待开奖', requiresAuth: true }
  },
  {
    path: '/settlement/success',
    name: 'SettlementSuccess',
    component: () => import('@/views/settlement/SettlementSuccessPage.vue'),
    meta: { title: '拼团结果', requiresAuth: true }
  },
  {
    path: '/settlement/failed',
    name: 'SettlementFailed',
    component: () => import('@/views/settlement/SettlementFailedPage.vue'),
    meta: { title: '拼团失败', requiresAuth: true }
  },
  {
    path: '/settlement/newuser-failed',
    name: 'NewUserSettlementFailed',
    component: () => import('@/views/settlement/NewUserSettlementFailedPage.vue'),
    meta: { title: '体验结果', requiresAuth: true }
  },
  {
    path: '/settlement/pending',
    name: 'SettlementPending',
    component: () => import('@/views/settlement/SettlementPendingPage.vue'),
    meta: { title: '等待开奖', requiresAuth: true }
  },
  {
    path: '/group',
    name: 'Group',
    component: () => import('@/views/group/GroupPage.vue'),
    meta: { title: '拼团', requiresAuth: false }
  },
  {
    path: '/group/confirm/:id',
    name: 'GroupConfirm',
    component: () => import('@/views/group/GroupConfirmPage.vue'),
    meta: { title: '拼团详情', requiresAuth: true }
  },
  {
    path: '/group-buy/waiting/:orderId',
    name: 'GroupWaiting',
    component: () => import('@/views/group/GroupWaitingPage.vue'),
    meta: { title: '拼团等待开奖', requiresAuth: true }
  },
  {
    path: '/cart',
    name: 'Cart',
    component: () => import('@/views/cart/CartPage.vue'),
    meta: { title: '购物车', requiresAuth: false }
  },
  {
    path: '/user',
    name: 'Profile',
    component: () => import('@/views/user/ProfilePage.vue'),
    meta: { title: '个人中心', requiresAuth: true }
  },
  {
    path: '/user/orders',
    name: 'Orders',
    component: () => import('@/views/user/OrdersPage.vue'),
    meta: { title: '我的订单', requiresAuth: true }
  },
  {
    path: '/user/orders/:id',
    name: 'OrderDetail',
    component: () => import('@/views/user/OrderDetailPage.vue'),
    meta: { title: '订单详情', requiresAuth: true }
  },
  {
    path: '/user/wallet',
    name: 'Wallet',
    component: () => import('@/views/user/WalletPage.vue'),
    meta: { title: '我的钱包', requiresAuth: true }
  },
  {
    path: '/user/withdraw',
    name: 'Withdraw',
    component: () => import('@/views/user/WithdrawPage.vue'),
    meta: { title: '提现', requiresAuth: true }
  },
  {
    path: '/user/withdraw/history',
    name: 'WithdrawHistory',
    component: () => import('@/views/user/WithdrawHistoryPage.vue'),
    meta: { title: '提现记录', requiresAuth: true }
  },
  {
    path: '/user/identity-verification',
    name: 'IdentityVerification',
    component: () => import('@/views/user/IdentityVerificationPage.vue'),
    meta: { title: '实名认证', requiresAuth: true }
  },
  {
    path: '/user/identity-manage',
    name: 'IdentityManage',
    component: () => import('@/views/user/IdentityManagePage.vue'),
    meta: { title: '实名认证管理', requiresAuth: true }
  },
  {
    path: '/user/team',
    name: 'Team',
    component: () => import('@/views/user/TeamPage.vue'),
    meta: { title: '我的团队', requiresAuth: true }
  },
  {
    path: '/user/address',
    name: 'Address',
    component: () => import('@/views/user/AddressPage.vue'),
    meta: { title: '收货地址', requiresAuth: true }
  },
  {
    path: '/user/address/add',
    name: 'AddAddress',
    component: () => import('@/views/user/AddAddressPage.vue'),
    meta: { title: '添加收货地址', requiresAuth: true }
  },
  {
    path: '/user/address/edit/:id',
    name: 'EditAddress',
    component: () => import('@/views/user/EditAddressPage.vue'),
    meta: { title: '编辑收货地址', requiresAuth: true }
  },
  {
    path: '/user/coupons',
    name: 'Coupons',
    component: () => import('@/views/user/CouponsPage.vue'),
    meta: { title: '我的优惠券', requiresAuth: true }
  },
  {
    path: '/user/favorites',
    name: 'Favorites',
    component: () => import('@/views/user/FavoritesPage.vue'),
    meta: { title: '我的收藏', requiresAuth: true }
  },
  {
    path: '/user/history',
    name: 'History',
    component: () => import('@/views/user/HistoryPage.vue'),
    meta: { title: '浏览足迹', requiresAuth: true }
  },
  {
    path: '/user/customer-service',
    name: 'CustomerService',
    component: () => import('@/views/user/CustomerServicePage.vue'),
    meta: { title: '客服中心', requiresAuth: false } // 允许未登录用户访问客服
  },
  {
    path: '/user/settings',
    name: 'Settings',
    component: () => import('@/views/user/SettingsPage.vue'),
    meta: { title: '设置', requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫 - 统一登录状态管理
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 使用统一的认证守卫
  const authGuard = createAuthGuard()
  await authGuard(to, from, next)
})

// 路由后置守卫 - 记录页面访问
router.afterEach((to, from) => {
  console.log(`📱 页面导航: ${from.path} → ${to.path}`)
  
  // 记录页面访问统计（可选）
  if (import.meta.env.DEV) {
    console.log(`📊 页面访问: ${to.name} (${to.path})`)
  }
})

export default router 