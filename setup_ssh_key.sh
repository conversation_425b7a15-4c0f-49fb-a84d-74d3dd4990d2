#!/bin/bash

# SSH密钥配置脚本 - 避免重复输入SSH密码
# 使用方法: ./setup_ssh_key.sh

SERVER_IP="*************"

echo "🔑 SSH密钥配置脚本"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "目标服务器: $SERVER_IP"
echo ""

# 检查是否已有SSH密钥
if [ -f ~/.ssh/id_rsa.pub ]; then
    echo "✅ 发现现有SSH密钥"
    echo "📁 公钥位置: ~/.ssh/id_rsa.pub"
    echo ""
    echo "📋 您的公钥内容:"
    cat ~/.ssh/id_rsa.pub
    echo ""
else
    echo "🔧 生成新的SSH密钥..."
    read -p "请输入您的邮箱地址: " email
    ssh-keygen -t rsa -b 4096 -C "$email" -f ~/.ssh/id_rsa -N ""
    
    if [ $? -eq 0 ]; then
        echo "✅ SSH密钥生成成功"
        echo "📁 私钥: ~/.ssh/id_rsa"
        echo "📁 公钥: ~/.ssh/id_rsa.pub"
        echo ""
        echo "📋 您的公钥内容:"
        cat ~/.ssh/id_rsa.pub
        echo ""
    else
        echo "❌ SSH密钥生成失败"
        exit 1
    fi
fi

# 复制公钥到服务器
echo "🚀 配置服务器SSH密钥认证..."
echo ">>> 请输入服务器SSH密码 (最后一次!):"

ssh-copy-id root@$SERVER_IP

if [ $? -eq 0 ]; then
    echo "✅ SSH密钥配置成功!"
    echo ""
    echo "🧪 测试无密码连接..."
    ssh -o PasswordAuthentication=no root@$SERVER_IP "echo '🎉 无密码SSH连接成功!'"
    
    if [ $? -eq 0 ]; then
        echo "✅ 配置完成! 现在可以无密码连接服务器"
        echo ""
        echo "💡 接下来使用备份脚本时，只需要输入MySQL密码，不再需要SSH密码"
    else
        echo "⚠️  SSH密钥可能未正确配置，请检查"
    fi
else
    echo "❌ SSH密钥配置失败"
    echo "请检查:"
    echo "1. 服务器IP是否正确"
    echo "2. SSH密码是否正确"
    echo "3. 服务器是否允许密钥认证"
fi

echo ""
echo "📋 使用说明:"
echo "1. 配置成功后，使用 ./optimized_backup.sh 只需输入MySQL密码"
echo "2. 如果仍需输入SSH密码，请联系服务器管理员检查SSH配置"
