-- ========================================
-- 社交拼团APP数据库建表脚本
-- 版本: V1.0
-- 日期: 2025-06-25
-- 说明: 基于数据库设计文档V1.0创建的MySQL建表脚本
-- ========================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `social_group_buy` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `social_group_buy`;

-- ========================================
-- 1. 用户管理模块
-- ========================================

-- 1.1 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `phone_number` VARCHAR(20) NOT NULL COMMENT '手机号，登录凭证',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
  `nickname` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `inviter_id` BIGINT DEFAULT NULL COMMENT '邀请人ID',
  `status` ENUM('active','frozen','deleted') NOT NULL DEFAULT 'active' COMMENT '账户状态',
  `risk_level` ENUM('normal','high','watch') NOT NULL DEFAULT 'normal' COMMENT '风控等级',
  `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_users_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 1.2 用户钱包表
DROP TABLE IF EXISTS `wallets`;
CREATE TABLE `wallets` (
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `balance` DECIMAL(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额(可提现)',
  `experience_balance` DECIMAL(15,2) NOT NULL DEFAULT '0.00' COMMENT '体验金(不可提现)',
  `points` BIGINT NOT NULL DEFAULT 0 COMMENT '用户积分',
  `total_recharge` DECIMAL(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_withdraw` DECIMAL(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现金额',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  CONSTRAINT `fk_wallets_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户钱包表';

-- 1.3 钱包流水表
DROP TABLE IF EXISTS `wallet_transactions`;
CREATE TABLE `wallet_transactions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '流水ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `amount` DECIMAL(15,2) NOT NULL COMMENT '变动金额(正数收入，负数支出)',
  `balance_after` DECIMAL(15,2) NOT NULL COMMENT '变动后余额',
  `type` ENUM('RECHARGE','WITHDRAWAL','PAYMENT','REFUND_WIN','REFUND_LOSS','SUBSIDY','COMMISSION','TEAM_REWARD','MANUAL_ADJUST','EXPERIENCE_GIFT') NOT NULL COMMENT '交易类型',
  `related_id` BIGINT DEFAULT NULL COMMENT '关联业务ID',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '交易描述',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`, `created_at`),
  KEY `idx_type` (`type`),
  KEY `idx_related_id` (`related_id`),
  CONSTRAINT `fk_wallet_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包流水表';

-- 1.4 用户收货地址表
DROP TABLE IF EXISTS `user_addresses`;
CREATE TABLE `user_addresses` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `recipient_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
  `phone_number` VARCHAR(20) NOT NULL COMMENT '收件人电话',
  `province` VARCHAR(50) NOT NULL COMMENT '省份',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `district` VARCHAR(50) NOT NULL COMMENT '区县',
  `address_line` VARCHAR(255) NOT NULL COMMENT '详细地址',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否默认地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_user_addresses_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收货地址表';

-- ========================================
-- 2. 商品管理模块
-- ========================================

-- 2.1 商品分类表
DROP TABLE IF EXISTS `product_categories`;
CREATE TABLE `product_categories` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `icon_url` VARCHAR(500) DEFAULT NULL COMMENT '分类图标',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` ENUM('active','inactive') NOT NULL DEFAULT 'active' COMMENT '分类状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 2.2 商品表(SPU)
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` VARCHAR(255) NOT NULL COMMENT '商品名称',
  `description` TEXT DEFAULT NULL COMMENT '商品描述',
  `category_id` INT DEFAULT NULL COMMENT '分类ID',
  `images` JSON DEFAULT NULL COMMENT '商品图片数组',
  `detail_images` JSON DEFAULT NULL COMMENT '详情图片数组',
  `status` ENUM('active','inactive','deleted') NOT NULL DEFAULT 'active' COMMENT '商品状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_products_category` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表(SPU)';

-- 2.3 商品规格表(SKU)
DROP TABLE IF EXISTS `product_skus`;
CREATE TABLE `product_skus` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
  `product_id` BIGINT NOT NULL COMMENT '商品ID',
  `sku_code` VARCHAR(100) NOT NULL COMMENT 'SKU编码',
  `attributes` JSON DEFAULT NULL COMMENT '规格属性',
  `price` DECIMAL(10,2) NOT NULL COMMENT '商品标价',
  `stock` INT NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales_count` INT NOT NULL DEFAULT 0 COMMENT '销售数量',
  `status` ENUM('active','inactive','deleted') NOT NULL DEFAULT 'active' COMMENT 'SKU状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_code` (`sku_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_product_skus_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格表(SKU)';

-- ========================================
-- 3. 活动管理模块
-- ========================================

-- 3.1 抽奖活动表
DROP TABLE IF EXISTS `activities`;
CREATE TABLE `activities` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `product_sku_id` BIGINT NOT NULL COMMENT '关联SKU',
  `name` VARCHAR(255) NOT NULL COMMENT '活动名称',
  `description` TEXT DEFAULT NULL COMMENT '活动描述',
  `type` ENUM('NOVICE','LOW_PRICE','HIGH_PRICE') NOT NULL COMMENT '活动类型',
  `status` ENUM('pending','active','finished','cancelled') NOT NULL DEFAULT 'pending' COMMENT '活动状态',
  `config` JSON NOT NULL COMMENT '活动配置参数',
  `force_loss_flag` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '必不中开关',
  `participant_limit` INT DEFAULT NULL COMMENT '参与人数限制',
  `current_participants` INT NOT NULL DEFAULT 0 COMMENT '当前参与人数',
  `start_time` TIMESTAMP NOT NULL COMMENT '活动开始时间',
  `end_time` TIMESTAMP NOT NULL COMMENT '活动结束时间',
  `draw_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开奖时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_sku_id` (`product_sku_id`),
  KEY `idx_type_status` (`type`, `status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  CONSTRAINT `fk_activities_sku` FOREIGN KEY (`product_sku_id`) REFERENCES `product_skus` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖活动表';

-- ========================================
-- 4. 订单管理模块
-- ========================================

-- 4.1 订单表
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_sn` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `activity_id` BIGINT NOT NULL COMMENT '活动ID',
  `status` ENUM('PENDING_PAYMENT','WON_PENDING_ACTION','LOST','COMPLETED','CANCELLED','EXPIRED') NOT NULL DEFAULT 'PENDING_PAYMENT' COMMENT '订单状态',
  `draw_result` ENUM('WON','LOST') DEFAULT NULL COMMENT '开奖结果',
  `win_option` ENUM('TAKE_GOODS','CASH_OUT','PAY_REMAINDER','FORFEIT_DEPOSIT') DEFAULT NULL COMMENT '中签后选择',
  `amount_paid` DECIMAL(10,2) NOT NULL COMMENT '用户实付金额',
  `experience_used` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '使用体验金',
  `subsidy_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '补贴金额',
  `shipping_address_id` BIGINT DEFAULT NULL COMMENT '收货地址ID',
  `payment_method` VARCHAR(50) DEFAULT NULL COMMENT '支付方式',
  `payment_time` TIMESTAMP NULL DEFAULT NULL COMMENT '支付时间',
  `draw_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开奖时间',
  `completed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sn` (`order_sn`),
  KEY `idx_user_created` (`user_id`, `created_at`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_status` (`status`),
  KEY `idx_draw_result` (`draw_result`),
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_orders_activity` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_orders_address` FOREIGN KEY (`shipping_address_id`) REFERENCES `user_addresses` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 4.2 订单物流表
DROP TABLE IF EXISTS `order_logistics`;
CREATE TABLE `order_logistics` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `tracking_number` VARCHAR(100) DEFAULT NULL COMMENT '快递单号',
  `courier_company` VARCHAR(50) DEFAULT NULL COMMENT '快递公司',
  `status` ENUM('pending','shipped','in_transit','delivered','returned') NOT NULL DEFAULT 'pending' COMMENT '物流状态',
  `tracking_info` JSON DEFAULT NULL COMMENT '物流跟踪信息',
  `shipped_at` TIMESTAMP NULL DEFAULT NULL COMMENT '发货时间',
  `delivered_at` TIMESTAMP NULL DEFAULT NULL COMMENT '签收时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_tracking_number` (`tracking_number`),
  CONSTRAINT `fk_order_logistics_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单物流表';

-- ========================================
-- 5. 裂变系统模块
-- ========================================

-- 5.1 邀请记录表
DROP TABLE IF EXISTS `invitation_records`;
CREATE TABLE `invitation_records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `inviter_id` BIGINT NOT NULL COMMENT '邀请人ID',
  `invitee_id` BIGINT NOT NULL COMMENT '被邀请人ID',
  `commission_earned` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '已获得佣金',
  `status` ENUM('pending','active','invalid') NOT NULL DEFAULT 'pending' COMMENT '邀请状态',
  `first_order_at` TIMESTAMP NULL DEFAULT NULL COMMENT '首次下单时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invitee_id` (`invitee_id`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_invitation_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invitation_invitee` FOREIGN KEY (`invitee_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请记录表';

-- 5.2 团队奖励表
DROP TABLE IF EXISTS `team_rewards`;
CREATE TABLE `team_rewards` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '奖励ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `reward_date` DATE NOT NULL COMMENT '奖励日期',
  `qualified_count` INT NOT NULL COMMENT '达标人数',
  `reward_amount` DECIMAL(10,2) NOT NULL COMMENT '奖励金额',
  `status` ENUM('pending','paid') NOT NULL DEFAULT 'pending' COMMENT '发放状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`, `reward_date`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_team_rewards_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队奖励表';

-- ========================================
-- 6. 财务管理模块
-- ========================================

-- 6.1 提现申请表
DROP TABLE IF EXISTS `withdrawals`;
CREATE TABLE `withdrawals` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `amount` DECIMAL(15,2) NOT NULL COMMENT '申请金额',
  `fee` DECIMAL(15,2) NOT NULL COMMENT '手续费',
  `actual_amount` DECIMAL(15,2) NOT NULL COMMENT '实际到账',
  `status` ENUM('pending','approved','rejected','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `bank_name` VARCHAR(100) DEFAULT NULL COMMENT '银行名称',
  `bank_account` VARCHAR(50) DEFAULT NULL COMMENT '银行账号',
  `account_holder` VARCHAR(50) DEFAULT NULL COMMENT '账户姓名',
  `rejection_reason` VARCHAR(255) DEFAULT NULL COMMENT '驳回原因',
  `processed_by` INT DEFAULT NULL COMMENT '处理人',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `processed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`, `created_at`),
  KEY `idx_status` (`status`),
  KEY `idx_processed_by` (`processed_by`),
  CONSTRAINT `fk_withdrawals_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现申请表';

-- ========================================
-- 7. 风控系统模块
-- ========================================

-- 7.1 用户风控策略表
DROP TABLE IF EXISTS `user_risk_strategies`;
CREATE TABLE `user_risk_strategies` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `strategy_type` ENUM('FORCE_WIN','FORCE_LOSS','PROBABILITY_ADJUST') NOT NULL COMMENT '策略类型',
  `effect_times` INT NOT NULL COMMENT '生效次数',
  `used_times` INT NOT NULL DEFAULT 0 COMMENT '已使用次数',
  `config_data` JSON DEFAULT NULL COMMENT '策略配置',
  `status` ENUM('active','expired','disabled') NOT NULL DEFAULT 'active' COMMENT '策略状态',
  `created_by` INT NOT NULL COMMENT '创建人',
  `expires_at` TIMESTAMP NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_strategy_type` (`strategy_type`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `fk_user_risk_strategies_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户风控策略表';

-- 7.2 风控日志表
DROP TABLE IF EXISTS `risk_control_logs`;
CREATE TABLE `risk_control_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
  `order_id` BIGINT DEFAULT NULL COMMENT '订单ID',
  `action_type` ENUM('DRAW_RESULT_OVERRIDE','PROBABILITY_ADJUST','USER_RISK_LEVEL_CHANGE','ACTIVITY_FORCE_LOSS') NOT NULL COMMENT '操作类型',
  `reason` TEXT DEFAULT NULL COMMENT '操作原因',
  `original_data` JSON DEFAULT NULL COMMENT '原始数据',
  `modified_data` JSON DEFAULT NULL COMMENT '修改后数据',
  `operated_by` INT DEFAULT NULL COMMENT '操作人',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_operated_by` (`operated_by`),
  CONSTRAINT `fk_risk_control_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_risk_control_logs_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风控日志表';

-- ========================================
-- 8. 内容管理模块
-- ========================================

-- 8.1 Banner管理表
DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'Banner ID',
  `title` VARCHAR(255) NOT NULL COMMENT 'Banner标题',
  `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
  `link_url` VARCHAR(500) DEFAULT NULL COMMENT '跳转链接',
  `link_type` ENUM('product','activity','h5','none') NOT NULL DEFAULT 'none' COMMENT '链接类型',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` ENUM('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Banner管理表';

-- 8.2 弹窗管理表
DROP TABLE IF EXISTS `popups`;
CREATE TABLE `popups` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '弹窗ID',
  `title` VARCHAR(255) NOT NULL COMMENT '弹窗标题',
  `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
  `link_url` VARCHAR(500) DEFAULT NULL COMMENT '跳转链接',
  `trigger_rules` JSON NOT NULL COMMENT '触发规则',
  `status` ENUM('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `start_time` TIMESTAMP NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹窗管理表';

-- ========================================
-- 9. 后台管理模块
-- ========================================

-- 9.1 管理员表
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `status` ENUM('active','inactive','deleted') NOT NULL DEFAULT 'active' COMMENT '状态',
  `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录',
  `last_login_ip` VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 9.2 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
  `status` ENUM('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 9.3 权限表
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `code` VARCHAR(100) NOT NULL COMMENT '权限代码',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '权限描述',
  `module` VARCHAR(50) NOT NULL COMMENT '所属模块',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_module` (`module`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 9.4 管理员角色关联表
DROP TABLE IF EXISTS `admin_role_relations`;
CREATE TABLE `admin_role_relations` (
  `admin_id` INT NOT NULL COMMENT '管理员ID',
  `role_id` INT NOT NULL COMMENT '角色ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`admin_id`, `role_id`),
  CONSTRAINT `fk_admin_role_admin` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_admin_role_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色关联表';

-- 9.5 角色权限关联表
DROP TABLE IF EXISTS `role_permission_relations`;
CREATE TABLE `role_permission_relations` (
  `role_id` INT NOT NULL COMMENT '角色ID',
  `permission_id` INT NOT NULL COMMENT '权限ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`role_id`, `permission_id`),
  CONSTRAINT `fk_role_permission_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permission_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 9.6 管理员操作日志表
DROP TABLE IF EXISTS `admin_action_logs`;
CREATE TABLE `admin_action_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` INT NOT NULL COMMENT '操作人ID',
  `action` VARCHAR(100) NOT NULL COMMENT '操作动作',
  `module` VARCHAR(50) NOT NULL COMMENT '操作模块',
  `description` TEXT DEFAULT NULL COMMENT '操作描述',
  `request_data` JSON DEFAULT NULL COMMENT '请求数据',
  `ip_address` VARCHAR(45) DEFAULT NULL COMMENT '操作IP',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_created` (`admin_id`, `created_at`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  CONSTRAINT `fk_admin_action_logs_admin` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- ========================================
-- 10. 系统配置模块
-- ========================================

-- 10.1 系统配置表
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '配置说明',
  `value_type` ENUM('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '值类型',
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_key`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ========================================
-- 11. 添加延迟外键约束
-- ========================================

-- 添加提现表的外键约束
ALTER TABLE `withdrawals` ADD CONSTRAINT `fk_withdrawals_admin` FOREIGN KEY (`processed_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

-- 添加风控策略表的外键约束
ALTER TABLE `user_risk_strategies` ADD CONSTRAINT `fk_user_risk_strategies_admin` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`) ON DELETE RESTRICT;

-- 添加风控日志表的外键约束
ALTER TABLE `risk_control_logs` ADD CONSTRAINT `fk_risk_control_logs_admin` FOREIGN KEY (`operated_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

-- ========================================
-- 12. 插入初始数据
-- ========================================

-- 插入默认系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `description`, `value_type`) VALUES
('low_price_win_rate', '0.15', '低价区总中签率', 'number'),
('high_price_win_rate', '0.08', '高价区总中签率', 'number'),
('new_user_experience_money', '20.00', '新用户注册赠送体验金', 'number'),
('commission_rate', '0.005', '邀请返佣比例', 'number'),
('team_reward_threshold', '5', '团队奖励人数阈值', 'number'),
('team_reward_per_person', '10.00', '团队奖励每人金额', 'number'),
('withdrawal_min_amount', '10.00', '最低提现金额', 'number'),
('withdrawal_fee_rate', '0.01', '提现手续费率', 'number'),
('novice_activity_limit', '1', '新手团参与次数限制', 'number');

-- 插入默认角色
INSERT INTO `roles` (`name`, `description`) VALUES
('super_admin', '超级管理员'),
('admin', '普通管理员'),
('operator', '运营人员'),
('finance', '财务人员'),
('customer_service', '客服人员');

-- 插入默认权限
INSERT INTO `permissions` (`name`, `code`, `description`, `module`) VALUES
-- 用户管理权限
('用户列表查看', 'user:list', '查看用户列表', 'user'),
('用户详情查看', 'user:detail', '查看用户详情', 'user'),
('用户余额调整', 'user:balance:adjust', '调整用户余额', 'user'),
('用户积分调整', 'user:points:adjust', '调整用户积分', 'user'),
('用户账户冻结', 'user:freeze', '冻结用户账户', 'user'),

-- 订单管理权限
('订单列表查看', 'order:list', '查看订单列表', 'order'),
('订单详情查看', 'order:detail', '查看订单详情', 'order'),
('订单售后处理', 'order:after_sale', '处理订单售后', 'order'),

-- 商品管理权限
('商品列表查看', 'product:list', '查看商品列表', 'product'),
('商品创建编辑', 'product:edit', '创建编辑商品', 'product'),
('商品删除', 'product:delete', '删除商品', 'product'),

-- 活动管理权限
('活动列表查看', 'activity:list', '查看活动列表', 'activity'),
('活动创建编辑', 'activity:edit', '创建编辑活动', 'activity'),
('活动删除', 'activity:delete', '删除活动', 'activity'),

-- 财务管理权限
('提现审核', 'finance:withdrawal:audit', '审核用户提现', 'finance'),
('资金流水查看', 'finance:transaction:list', '查看资金流水', 'finance'),
('财务报表查看', 'finance:report', '查看财务报表', 'finance'),

-- 风控管理权限
('风控策略配置', 'risk:strategy:config', '配置风控策略', 'risk'),
('风控日志查看', 'risk:log:list', '查看风控日志', 'risk'),

-- 内容管理权限
('Banner管理', 'content:banner:manage', '管理Banner', 'content'),
('弹窗管理', 'content:popup:manage', '管理弹窗', 'content'),

-- 系统管理权限
('管理员管理', 'system:admin:manage', '管理管理员', 'system'),
('角色权限管理', 'system:role:manage', '管理角色权限', 'system'),
('系统配置管理', 'system:config:manage', '管理系统配置', 'system'),
('操作日志查看', 'system:log:list', '查看操作日志', 'system');

-- 创建默认超级管理员账户 (密码: admin123)
INSERT INTO `admin_users` (`username`, `password_hash`, `real_name`, `status`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'active');

-- 为超级管理员分配角色
INSERT INTO `admin_role_relations` (`admin_id`, `role_id`) VALUES (1, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO `role_permission_relations` (`role_id`, `permission_id`)
SELECT 1, `id` FROM `permissions`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 脚本执行完成
-- ======================================== 