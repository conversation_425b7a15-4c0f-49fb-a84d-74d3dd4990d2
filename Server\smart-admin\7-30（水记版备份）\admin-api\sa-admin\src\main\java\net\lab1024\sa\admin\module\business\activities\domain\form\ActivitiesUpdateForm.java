package net.lab1024.sa.admin.module.business.activities.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 抽奖活动表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ActivitiesUpdateForm extends ActivitiesAddForm {

    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动ID 不能为空")
    private Long id;

}