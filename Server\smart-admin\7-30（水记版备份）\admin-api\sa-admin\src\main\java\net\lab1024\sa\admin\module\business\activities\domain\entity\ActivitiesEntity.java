package net.lab1024.sa.admin.module.business.activities.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Map;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

/**
 * 抽奖活动表 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */

@Data
@TableName(value = "t_activities", autoResultMap = true)
public class ActivitiesEntity {

    /**
     * 活动ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 活动类型
     */
    private String type;

    /**
     * 活动状态 0 pending, 1 active, 2 finished, 3 cancelled
     */
    private Integer status;

    /**
     * 返还比例%
     */
    private Integer returnRatio;

    /**
     * 活动配置参数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> configInfo;

    /**
     * 必不中开关
     */
    private Integer forceLossFlag;

    /**
     * 参与人数限制
     */
    private Integer participantLimit;

    /**
     * 当前参与人数
     */
    private Integer currentParticipants;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 开奖计时
     */
    private Integer remainingTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 删除标记
     */
    private Integer deletedFlag;

}
