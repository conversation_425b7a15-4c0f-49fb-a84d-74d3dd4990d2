package net.lab1024.sa.admin.module.business.orders.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsSkusVO;
import net.lab1024.sa.admin.module.business.userAddress.domain.vo.UserAddressVO;

/**
 * 订单表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Data
public class OrdersVO {


    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderSn;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "开奖结果")
    private Integer drawResult;

    @Schema(description = "返奖金额")
    private BigDecimal rewardAmount;

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "SkuID")
    private Long skuId;

    @Schema(description = "Sku")
    private GoodsSkusVO sku;

    @Schema(description = "中签后选择")
    private String winOption;

    @Schema(description = "单买标记")
    private Boolean aloneFlag;

    @Schema(description = "用户实付金额")
    private BigDecimal amountPaid;

    @Schema(description = "使用体验金")
    private BigDecimal experiencePaid;

    @Schema(description = "补贴金额")
    private BigDecimal subsidyPaid;

    @Schema(description = "使用积分")
    private Integer pointsPaid;

    @Schema(description = "应付额")
    private BigDecimal payableAmount;

    @Schema(description = "收货地址ID")
    private Long shippingAddressId;

    @Schema(description = "收货信息")
    private UserAddressVO shippingInfo;

    @Schema(description = "支付方式")
    private String paymentMethod;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "开奖时间")
    private LocalDateTime drawTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "结算标记")
    private Integer settleFlag;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "退款原因")
    private String refundReason;

//    @Schema(description = "删除标记")
//    private Integer deletedFlag;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
