# 社交拼团APP后台管理系统产品需求文档 (PRD)

| 版本   | 日期       | 作者   | 审核人 | 变更描述                                                     |
| :----- | :--------- | :----- | :----- | :----------------------------------------------------------- |
| V1.0 | 2025-06-25 | Gemini |        | 基于V2.2.2版APP PRD及V1.2版页面设计文档，创建后台PRD |

---

## 1. 引言 (Introduction)

### 1.1. 文档目的

本文档旨在明确"社交拼团APP"后台管理系统的产品需求，定义其功能模块、业务逻辑和管理权限。它将作为后台系统开发、测试和运维的统一依据，确保对前端APP的有效支撑和精细化运营。

### 1.2. 系统愿景

打造一个功能强大、操作便捷、安全可靠的中心化管理平台。该平台不仅能满足日常的商品、订单、用户管理需求，更能通过创新的**风控策略中心**和**裂变体系管理**模块，赋能运营团队，实现用户增长、风险控制和平台盈利的核心目标。

### 1.3. 目标用户

- **平台运营人员:** 负责日常活动配置、内容更新、用户沟通和数据监控。
- **平台管理人员:** 负责核心业务参数设置、财务审核、风控策略制定和团队管理。
- **客服人员:** 负责查询用户信息、订单状态，解答用户疑问和处理售后问题。
- **财务人员:** 负责处理用户提现、平台对账和财务数据分析。

---

## 2. 系统功能 (System Features)

### 2.1. 功能总览 (Feature Overview)

```mermaid
graph TD;
    A["后台管理系统"] --> B["工作台 (Dashboard)"];
    A --> C["用户管理"];
    A --> D["营销中心"];
    A --> E["商品中心"];
    A --> F["订单中心"];
    A --> G["财务中心"];
    A --> H["风控策略中心"];
    A --> I["裂变体系管理"];
    A --> J["系统管理"];

    subgraph D[营销中心]
        D1["抽奖活动管理"];
        D2["内容配置 (Banner, 弹窗)"];
    end
    
    subgraph E[商品中心]
        E1["商品库管理 (SPU/SKU)"];
        E2["商品分类管理"];
    end

    subgraph G[财务中心]
        G1["提现审核"];
        G2["资金流水"];
        G3["财务报表"];
    end

    subgraph H[风控策略中心]
        H1["全局概率配置"];
        H2["用户策略干预"];
        H3["活动策略干预"];
    end

    subgraph I[裂变体系管理]
        I1["邀请规则配置"];
        I2["团队奖励配置"];
        I3["新用户注册礼配置"];
    end
    
    subgraph J[系统管理]
        J1["角色与权限管理"];
        J2["操作日志"];
    end
```

### 2.2. 功能详述 (Detailed Features)

#### 2.2.1. 工作台 (Dashboard)

| 功能ID | 功能名称       | 功能描述                                                                                                                                                                                                                    |
| :----- | :------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| DB-001 | **核心数据总览** | 1. **实时数据**: 今日新增用户、今日活跃用户、今日订单数、今日交易额(GMV)。<br>2. **待办事项**: 待审核提现数、待处理售后订单数。<br>3. **图表展示**: 近7日用户增长趋势图、近7日交易额趋势图。 |

#### 2.2.2. 用户管理 (User Management)

| 功能ID | 功能名称         | 功能描述                                                                                                                                                                                                                                  |
| :----- | :--------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| UM-001 | **用户列表查询** | 1. **查询条件**: 支持按用户ID、手机号、昵称、注册时间、所属上级ID进行筛选和查询。<br>2. **列表展示**: 用户ID、头像、昵称、手机号、账户余额、体验金、积分、直接邀请人数、注册时间、最近登录时间、风控标签。 |
| UM-002 | **用户详情查看** | 1. **基本信息**: 同列表展示。<br>2. **团队信息**: 展示其**所属上级**和**直接下级列表**。<br>3. **资产信息**: 钱包余额、体验金、积分、账单流水。<br>4. **订单信息**: 该用户的所有订单列表。          |
| UM-003 | **用户操作**   | 1. **增/减余额**: 支持手动为用户增加或扣除账户余额，需填写事由。<br>2. **增/减积分**: 支持手动为用户增加或扣除积分。<br>3. **冻结/解冻账户**: 临时禁止用户登录和交易。                                            |

#### 2.2.3. 营销中心 (Marketing Center)

| 功能ID | 功能名称                 | 功能描述                                                                                                                                                                                                                                                                                                                                                        |
| :----- | :----------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| MK-001 | **抽奖活动管理**   | 1. **创建/编辑活动**: 设置活动名称、时间、关联商品。<br>2. **配置活动类型**: 选择`新手团`、`低价抽奖团`或`高价抽奖团`。<br>3. **设置核心参数**:<br>   - **低价团**: 配置`中签后折现比例`（如80%），`未中签补贴比例`（如3-5%）。<br>   - **高价团**: 配置`预付款比例`（如20%），`未中签补贴比例`（基于商品标价，如3-5%）。<br>4. **活动状态**: 控制活动上下线。 |
| MK-002 | **内容配置**       | 1. **Banner管理**: 上传Banner图片、配置跳转链接（可跳转至指定商品、活动专区或H5页面）、排序、上下线。<br>2. **弹窗管理**: 上传弹窗图片、配置跳转链接、设置触发规则（如新用户首次启动、指定日期范围）。                                                                                        |

#### 2.2.4. 订单中心 (Order Center)

| 功能ID | 功能名称         | 功能描述                                                                                                                                                                                                                                                           |
| :----- | :--------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OD-001 | **订单列表查询** | 1. **查询条件**: 支持按订单号、用户ID/手机号、商品名称、订单状态、下单时间进行查询。<br>2. **订单状态**: **已中签、未中签、已完成、已失效**。<br>3. **列表展示**: 订单号、用户信息、商品信息、参与金额、订单状态、下单时间。 |
| OD-002 | **订单详情查看** | 展示完整的订单信息，包括：产品信息、金额明细、支付方式、物流信息(中签要货后)、中签/未中签结果及处理方式（如补贴金额、折现金额）。                                                                                                               |
| OD-003 | **售后处理**   | 1. 对于中签选择要货后，用户申请退货的订单，进行审核和处理。<br>2. 对于其他售后场景，提供客服介入处理的接口。                                                                                                                                           |

#### 2.2.5. 风控与策略中心 (Risk Control & Strategy Center) - **核心模块**

| 功能ID | 功能名称               | 功能描述                                                                                                                                                                                                                                              |
| :----- | :--------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| RC-001 | **全局概率设置** | 1. **低价区总中签率**: 设置低价区商品当天整体的中签概率基准值。<br>2. **高价区总中签率**: 设置高价区商品当天整体的中签概率基准值。                                                                                                         |
| RC-002 | **用户策略干预** | 1. **指定用户必中/必不中**: 可按用户ID或手机号，设置其在未来N次参与中`必中`或`必不中`，并可设置生效次数。操作需记录详细日志。<br>2. **新用户保护策略**: 提供一个总开关，开启后，所有新用户的首次参与默认为`必不中`，用于体验流程。<br>3. **特殊用户风控**: 可为特定用户打上`高风险`、`重点关注`等标签，用于后续运营决策。 |
| RC-003 | **活动策略干预** | 1. **商品必不中开关**: 在`抽奖活动管理`中，为特定活动设置一个`必不中`开关。开启后，所有参与该活动的用户均为`未中签`。主要用于测试、引流或特殊运营活动。                                                                   |

#### 2.2.6. 裂变体系管理 (Viral System Management)

| 功能ID | 功能名称                 | 功能描述                                                                                                                                                                                          |
| :----- | :----------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| VS-001 | **新用户注册礼配置** | 1. **体验金配置**: 设置新用户完成注册后，系统自动赠送的`体验金`金额。<br>2. **新手团限制**: 设置每个用户可参与新手团的次数上限（如1次）。                                                         |
| VS-002 | **邀请规则配置**     | 1. **消费返佣比例**: 设置邀请人（上级）可获得的消费返佣百分比（如0.5%）。<br>2. **有效用户定义**: (未来规划)定义"有效用户"的标准，如完成首次消费。                                                   |
| VS-003 | **团队奖励配置**     | 1. **人数阈值**: 设置触发团队奖励的`当日消费直属下级人数`阈值（如5人）。<br>2. **人均奖励金额**: 设置达标后，每个人头对应的奖励金额`N`元。                                                       |

#### 2.2.7. 财务中心 (Finance Center)

| 功能ID | 功能名称         | 功能描述                                                                                                                                                                                          |
| :----- | :--------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| FI-001 | **提现管理**     | 1. **提现设置**: 配置最低提现金额、提现手续费率。<br>2. **提现审核**: 列表展示用户提现申请（申请人、金额、手续费、实际到账、申请时间），支持`通过`和`驳回`操作。                                      |
| FI-002 | **资金流水**     | 记录平台所有关键资金变动，包括：用户充值、订单支付、订单退款、各项补贴、各项返佣、用户提现等。支持按类型和时间筛选查询和导出。                                                                          |
| FI-003 | **平台对账**     | 提供平台总收入、总支出、用户总余额、总补贴等关键财务数据的统计和报表功能，便于财务对账。                                                                                                                |

#### 2.2.8. 系统管理 (System Management)

| 功能ID | 功能名称               | 功能描述                                                                                                                                            |
| :----- | :--------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------- |
| SM-001 | **角色与权限管理** | 1. **角色管理**: 创建/编辑角色（如运营、财务、客服）。<br>2. **权限分配**: 为不同角色勾选可访问的菜单和可操作的功能点。                                  |
| SM-002 | **操作日志**       | 记录所有后台用户的登录和关键操作日志（如修改金额、审核提现、变更风控策略等），确保所有敏感操作可被追溯。                                                 |

---

## 3. 非功能性需求 (Non-Functional Requirements)

| 类别       | 需求描述                                                                                                                                    |
| :--------- | :------------------------------------------------------------------------------------------------------------------------------------------ |
| **安全性** | 1. **权限控制**: 不同角色的用户严格按照权限访问数据和功能，禁止越权操作。<br>2. **操作留痕**: 所有对资金、用户、订单的敏感操作必须有详细日志记录。<br>3. **防暴力破解**: 后台登录接口需有防暴力破解机制。 |
| **性能**   | 1. 列表查询页面在数据量大的情况下（如百万级用户或订单），响应时间应在3秒以内。<br>2. 报表生成功能支持异步处理，避免长时间等待。                                 |
| **易用性** | 1. 界面布局清晰，常用功能入口明显。<br>2. 表单填写有明确提示和校验规则。                                                                     |

---

**文档结束** 