● 我的团队
/app/v1/myTeam?pageNum=1&pageSize=20
有pageNum， pageSize这两个翻页的参数
GET:
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "list": [
            {
                "id": 82, //用户ID
                "name": "13833883389", //用户名
                "childCount": 0, //用户下级数
                "noviceCount": 0, //试新数
                "hasReal": false //是否实名
                //新增
                "contribution": 3000  //对上级累计贡献积分
                "createTime":  //创建日期时间
                "consume":  //消费总金额
            },
            {
                "id": 74,
                "name": "xzh",
                "childCount": 0,
                "noviceCount": 0,
                "hasReal": false
            },
            {
                "id": 73,
                "name": "limbo",
                "childCount": 0,
                "noviceCount": 0,
                "hasReal": false
            }
        ],
        "total": 3, //下级数
        "totalTeamReward": 0.00 //所有下级的累计奖励积分
        "weekCount": 4  //本周新增成员数
    },
    "dataType": 1
}


  ✅ 页面改造总结：

  🎯 新的页面布局：

  1. 三列统计卡片：

  团队成员          累计奖励积分        本周新增成员
     8                10570              2

  2. 成员消费排行榜：

  - ✅ 标题：成员消费排行榜
  - ✅ 右上角链接：所有成员 →
  - ✅ 排名显示：1、2、3名有特殊颜色的圆形排名
  - ✅ 成员信息：ID: U003129 格式
  - ✅ 消费信息：总消费：¥45,800
  - ✅ 奖励积分：右侧大数字显示，下方"奖励积分"标签

  📊 API数据对接状态：

  已对接的数据：

  - ✅ 团队成员数量：teamData.total
  - ✅ 累计奖励积分：teamData.totalTeamReward
  - ✅ 团队成员列表：teamData.list

  注意事项：

  - 🔍 消费排行榜数据：当前/api/v1/myTeam接口只返回基本团队信息，没有消费数据
  - 🔍 排行榜显示：目前使用模拟数据展示UI效果
  - 🔍 本周新增成员：使用固定值2，实际需要基于时间计算或API提供

  🛠️ API数据分析：

  根据文档，/api/v1/myTeam返回的数据结构：
  {
    "data": {
      "list": [
        {
          "id": 82,           // 用户ID
          "name": "用户名",    // 显示为ID
          "childCount": 0,    // 下级数（未在排行榜中使用）
          "noviceCount": 0,   // 试新数（未在排行榜中使用）
          "hasReal": false    // 实名状态（未在排行榜中使用）
        }
      ],
      "total": 3,                    // ✅ 已对接：团队成员数
      "totalTeamReward": 10570.00    // ✅ 已对接：累计奖励积分
    }
  }

  🔍 需要额外API的功能：

  1. 成员消费排行榜：需要包含消费金额和奖励积分的API
  2. 本周新增成员：需要包含时间筛选的成员统计API

  📱 当前显示效果：

  现在页面会正确显示：
  - 团队成员总数（从API获取）
  - 累计奖励积分（从API获取）
  - 消费排行榜（使用模拟数据展示UI）

  页面的基础框架和API对接已完成，只需要后续补充消费数据相关的API即可完整显示所有功能！