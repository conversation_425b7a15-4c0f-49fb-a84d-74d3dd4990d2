package net.lab1024.sa.admin.module.business.goods.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsQueryForm;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsSkusQueryForm;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsSkusVO;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface GoodsSkusDao extends BaseMapper<GoodsSkusEntity> {

    List<GoodsSkusVO> query(Page page, @Param("query") GoodsSkusQueryForm query);

    @Update("UPDATE t_goods_skus SET goods_id=NULL WHERE goods_id=#{goodsId}")
    void setNullByGoodsId(@Param("goodsId") Long goodsId);

    @Update("UPDATE t_goods_skus SET stock=stock-1, sales_count=sales_count+1 WHERE id=#{id}")
    void salesCount(Long id);

    GoodsSkusVO queryById(@Param("skuId") long skuId);
}
