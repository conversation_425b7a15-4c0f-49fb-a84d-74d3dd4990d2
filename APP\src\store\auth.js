import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { standardApi } from '@/api/standardAdapter'
import { clearToken, setToken as setStorageToken } from '@/utils/request'
import { setChatraUserInfo } from '@/utils/chatra'

/**
 * 认证状态管理
 */
export const useAuthStore = defineStore('auth', () => {
  // 状态 - 兼容多种token存储方式
  const getStoredToken = () => {
    return localStorage.getItem('access_token') || 
           sessionStorage.getItem('access_token') ||
           localStorage.getItem('token') || ''
  }
  
  const getStoredRefreshToken = () => {
    return localStorage.getItem('refresh_token') || ''
  }
  
  // 获取token过期时间
  const getTokenExpireTime = () => {
    const expireTime = localStorage.getItem('token_expire_time')
    return expireTime ? parseInt(expireTime) : null
  }
  
  // 设置token过期时间
  const setTokenExpireTime = (expireTime) => {
    if (expireTime) {
      localStorage.setItem('token_expire_time', expireTime.toString())
    } else {
      localStorage.removeItem('token_expire_time')
    }
  }
  
  const token = ref(getStoredToken())
  const refreshToken = ref(getStoredRefreshToken())
  const tokenExpireTime = ref(getTokenExpireTime())
  const user = ref(null)
  
  // 增强的登录状态检查
  const isLoggedIn = computed(() => {
    // 基本检查：token和用户信息都存在
    if (!token.value || !user.value) {
      console.log('🔍 [AuthStore] isLoggedIn检查 - token:', !!token.value, 'user:', !!user.value)
      return false
    }
    
    // 检查token是否过期
    if (tokenExpireTime.value) {
      const now = Date.now()
      const expireTime = tokenExpireTime.value
      
      // 如果token已经过期，返回false
      if (now >= expireTime) {
        console.log('🔒 Token已过期，需要重新登录')
        return false
      }
      
      // 如果token即将过期（30分钟内），标记为需要刷新
      const thirtyMinutes = 30 * 60 * 1000
      if (now + thirtyMinutes >= expireTime) {
        console.log('⚠️ Token即将过期，建议刷新')
        // 这里可以触发自动刷新逻辑
      }
    }
    
    return true
  })
  
  // 检查token是否即将过期
  const isTokenExpiringSoon = computed(() => {
    if (!tokenExpireTime.value) return false
    
    const now = Date.now()
    const expireTime = tokenExpireTime.value
    const thirtyMinutes = 30 * 60 * 1000
    
    return now + thirtyMinutes >= expireTime
  })
  
  // 检查token是否已过期
  const isTokenExpired = computed(() => {
    if (!tokenExpireTime.value) return false
    
    const now = Date.now()
    const expireTime = tokenExpireTime.value
    
    return now >= expireTime
  })

  // 初始化用户信息
  const initUserInfo = () => {
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      try {
        user.value = JSON.parse(userInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  // 设置token - 更新：同时更新响应式状态和过期时间
  const setToken = (newToken, newRefreshToken, expireTime = null) => {
    token.value = newToken
    if (newRefreshToken) {
      refreshToken.value = newRefreshToken
    }
    
    // 设置过期时间
    if (expireTime) {
      tokenExpireTime.value = expireTime
      setTokenExpireTime(expireTime)
    } else {
      // 如果没有提供过期时间，默认设置为24小时后过期
      const defaultExpireTime = Date.now() + 24 * 60 * 60 * 1000
      tokenExpireTime.value = defaultExpireTime
      setTokenExpireTime(defaultExpireTime)
    }
    
    setStorageToken(newToken, newRefreshToken)
    
    console.log('🔑 Token已更新:', {
      token: newToken ? newToken.substring(0, 10) + '...' : 'null',
      expireTime: new Date(tokenExpireTime.value).toLocaleString()
    })
  }

  // 刷新token状态 - 新增：从存储中重新读取token
  const refreshTokenState = () => {
    token.value = getStoredToken()
    refreshToken.value = getStoredRefreshToken()
    tokenExpireTime.value = getTokenExpireTime()
  }
  
  // 验证token有效性
  const validateToken = async () => {
    if (!token.value) {
      console.log('🔒 没有token，用户未登录')
      return false
    }
    
    // 检查token是否过期
    if (isTokenExpired.value) {
      console.log('🔒 Token已过期，尝试刷新或重新登录')
      
      // 尝试刷新token
      if (refreshToken.value) {
        try {
          const success = await refreshTokenIfNeeded()
          if (success) {
            console.log('✅ Token刷新成功')
            return true
          }
        } catch (error) {
          console.error('❌ Token刷新失败:', error)
        }
      }
      
      // 刷新失败，清除登录状态
      console.log('🔒 Token刷新失败，清除登录状态')
      logout()
      return false
    }
    
    // 检查token是否即将过期
    if (isTokenExpiringSoon.value) {
      console.log('⚠️ Token即将过期，尝试刷新')
      try {
        await refreshTokenIfNeeded()
      } catch (error) {
        console.error('❌ 预刷新Token失败:', error)
      }
    }
    
    return true
  }
  
  // 刷新token（如果需要）
  const refreshTokenIfNeeded = async () => {
    if (!refreshToken.value) {
      throw new Error('没有refresh token')
    }
    
    try {
      // 调用refresh token API
      const response = await standardApi.refreshToken(refreshToken.value)
      
      if (response.code === 200 || response.code === 0) {
        // 更新token
        const newExpireTime = response.data.expire_time ? 
          new Date(response.data.expire_time).getTime() : 
          Date.now() + 24 * 60 * 60 * 1000
          
        setToken(response.data.token, response.data.refresh_token, newExpireTime)
        
        // 更新用户信息（如果有）
        if (response.data.user) {
          await setUser(response.data.user)
        }
        
        return true
      } else {
        throw new Error(response.message || 'Token刷新失败')
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      throw error
    }
  }
  
  // 强制验证登录状态（通过API调用）
  const forceValidateAuth = async () => {
    if (!token.value) {
      return false
    }
    
    try {
      // 调用一个轻量级的API来验证token
      const response = await standardApi.getUserStatus()
      
      if (response.code === 200 || response.code === 0) {
        // 更新用户信息
        if (response.data.user) {
          await setUser(response.data.user)
        } else if (response.data) {
          // 处理userStatus接口返回的数据格式
          const userInfo = {
            ...user.value,
            id: response.data.userId || user.value?.id,
            nickname: response.data.nickname || user.value?.nickname,
            noviceCount: response.data.noviceGroupLimit || 0,
            isNewUser: (response.data.noviceGroupLimit || 0) > 0,
          }
          await setUser(userInfo)
          
          // 处理新用户引导
          if (userInfo.noviceCount > 0) {
            localStorage.setItem('show_newuser_guide', JSON.stringify({
              noviceCount: userInfo.noviceCount,
              timestamp: Date.now()
            }))
            console.log('🎯 从getUserStatus检测到新用户，设置引导标记，剩余免费次数:', userInfo.noviceCount)
          }
        }
        return true
      } else {
        // API调用失败，可能token无效
        console.log('🔒 API验证失败，token可能无效')
        logout()
        return false
      }
    } catch (error) {
      console.error('强制验证登录状态失败:', error)
      
      // 检查是否是认证相关错误
      if (error.message.includes('401') || 
          error.message.includes('403') || 
          error.message.includes('登录') ||
          error.message.includes('token')) {
        console.log('🔒 认证失败，清除登录状态')
        logout()
        return false
      }
      
      // 其他错误，可能是网络问题，暂时保持登录状态
      return true
    }
  }

  // 设置用户信息
  const setUser = async (userInfo) => {
    user.value = userInfo
    localStorage.setItem('user_info', JSON.stringify(userInfo))
    
    // 更新 Chatra 客服系统的用户信息
    if (userInfo) {
      try {
        await setChatraUserInfo({
          name: userInfo.nickname || userInfo.username || userInfo.loginName,
          email: userInfo.email,
          phone: userInfo.phone || userInfo.mobile,
          userId: userInfo.id || userInfo.userId
        })
      } catch (error) {
        console.warn('Failed to update Chatra user info:', error)
      }
    }
  }

  // 更新用户信息
  const updateUser = (updates) => {
    if (user.value) {
      user.value = { ...user.value, ...updates }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  // 登录 - 修正：适配后端实际接口格式
  const login = async (loginData) => {
    try {
      // 使用标准API适配器，符合 POST /api/v1/auth 规范
      const response = await standardApi.login(
        loginData.phone, 
        loginData.password,
        loginData.captchaCode,
        loginData.captchaUuid
      )
      
      if (response.code === 200) {
        // 计算token过期时间
        const expireTime = response.data.expire_time ? 
          new Date(response.data.expire_time).getTime() : 
          Date.now() + 24 * 60 * 60 * 1000
        
        // 后端返回的token直接存储
        setToken(response.data.token, response.data.refresh_token, expireTime)
        
        // 构造用户信息对象
        const userInfo = {
          id: response.data.userId || response.data.employeeId,
          employeeId: response.data.employeeId,
          loginName: response.data.loginName,
          actualName: response.data.actualName,
          phone: response.data.phone,
          gender: response.data.gender,
          userType: response.data.userType,
          departmentId: response.data.departmentId,
          departmentName: response.data.departmentName,
          positionId: response.data.positionId,
          riskLevel: response.data.riskLevel,
          lastLoginTime: response.data.lastLoginTime,
          lastLoginIp: response.data.lastLoginIp,
          lastLoginIpRegion: response.data.lastLoginIpRegion,
          // 新增：新用户引导相关字段
          noviceCount: response.data.noviceGroupLimit || 0,
          isNewUser: (response.data.noviceGroupLimit || 0) > 0,
          // 兼容前端期望的字段
          username: response.data.loginName,
          nickname: response.data.actualName,
          avatar: '', // 后端暂无头像字段
          wallet_balance: 0, // 后端暂无钱包字段
          points: 0 // 后端暂无积分字段
        }
        
        await setUser(userInfo)
        
        // 保存最后一次登录成功的账号
        localStorage.setItem('last_login_account', loginData.phone)
        
        // 新增：处理新用户引导
        if (userInfo.noviceCount > 0) {
          // 标记需要显示引导弹窗
          localStorage.setItem('show_newuser_guide', JSON.stringify({
            noviceCount: userInfo.noviceCount,
            timestamp: Date.now()
          }))
          console.log('🎯 新用户检测成功，设置引导标记，剩余免费次数:', userInfo.noviceCount)
          console.log('📦 已设置localStorage标记:', localStorage.getItem('show_newuser_guide'))
        } else {
          console.log('❌ 不是新用户，noviceGroupLimit:', userInfo.noviceCount)
        }
        
        console.log('✅ 登录成功:', {
          userId: userInfo.id,
          noviceCount: userInfo.noviceCount,
          isNewUser: userInfo.isNewUser,
          expireTime: new Date(expireTime).toLocaleString()
        })
        
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 注册 - 修正：适配后端实际接口格式
  const register = async (registerData) => {
    try {
      // 使用标准API适配器，符合 POST /api/v1/auth 规范
      const response = await standardApi.register(registerData)
      
      if (response.code === 200 || response.code === 0) {
        console.log('✅ 注册成功:', response)
        
        // ⚠️  重要提示：由于登录接口需要验证码，注册后无法直接自动登录
        // 注册成功后，用户需要手动登录一次
        
        // 为用户提供新的验证码，方便立即登录
        try {
          console.log('🔄 为用户获取新验证码，便于立即登录...')
          const captchaResponse = await standardApi.getCaptcha()
          
          if (captchaResponse.code === 0 && captchaResponse.ok) {
            // 返回注册成功信息和新验证码
            response.data = {
              ...response.data,
              autoLoginDisabled: true,
              needManualLogin: true,
              loginHelp: {
                message: '注册成功！请使用刚注册的账号和密码登录',
                phone: registerData.phone,
                password: registerData.password, // 包含密码用于自动填充
                newCaptcha: {
                  captchaUuid: captchaResponse.data.captchaUuid,
                  captchaBase64Image: captchaResponse.data.captchaBase64Image,
                  expireSeconds: captchaResponse.data.expireSeconds
                }
              }
            }
          }
        } catch (captchaError) {
          console.warn('获取新验证码失败:', captchaError)
        }
        
        return response
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 第三方登录 - 使用标准API
  const oauthLogin = async (provider, oauthToken, userInfo = null) => {
    try {
      // 使用标准API适配器，符合 POST /api/v1/auth 规范
      const response = await standardApi.oauthLogin(provider, oauthToken, userInfo)
      
      if (response.code === 200) {
        const expireTime = response.data.expire_time ? 
          new Date(response.data.expire_time).getTime() : 
          Date.now() + 24 * 60 * 60 * 1000
          
        setToken(response.data.token, response.data.refresh_token, expireTime)
        await setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 忘记密码 - 使用标准API
  const forgotPassword = async (forgotData) => {
    try {
      // 使用标准API适配器，符合 POST /api/v1/auth 规范
      const response = await standardApi.forgotPassword(
        forgotData.phone,
        forgotData.verificationCode,
        forgotData.newPassword,
        forgotData.confirmNewPassword
      )
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出 - 修正：使用后端实际接口
  const logout = async () => {
    try {
      // 调用登出API（可选）
      if (token.value) {
        await standardApi.logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      refreshToken.value = ''
      tokenExpireTime.value = null
      user.value = null
      clearToken()
      setTokenExpireTime(null)
      
      // 额外清除其他可能的认证信息
      localStorage.removeItem('user_info')
      localStorage.removeItem('last_login_account')
      localStorage.removeItem('show_newuser_guide')
      sessionStorage.removeItem('access_token')
      sessionStorage.removeItem('token')
      sessionStorage.removeItem('user_info')
      
      console.log('🔒 已清除登录状态和所有相关数据')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await standardApi.getUserProfile()
      if (response.code === 200 || response.code === 0) {
        await setUser(response.data)
        return response.data
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已失效
      if (error.message.includes('401') || error.message.includes('登录')) {
        logout()
      }
      throw error
    }
  }

  // 检查登录状态 - 增强版本
  const checkAuthStatus = async (forceValidate = false) => {
    console.log('🔍 检查登录状态...')
    
    if (!token.value) {
      console.log('🔒 没有token，用户未登录')
      return false
    }
    
    // 检查token是否过期
    if (isTokenExpired.value) {
      console.log('🔒 Token已过期')
      return false
    }
    
    // 如果需要强制验证，调用API验证
    if (forceValidate) {
      return await forceValidateAuth()
    }
    
    // 基本检查
    const isValid = !!(token.value && user.value)
    console.log('🔍 基本登录状态检查:', isValid ? '有效' : '无效')
    
    return isValid
  }

  // 更新钱包余额
  const updateWalletBalance = (balance) => {
    if (user.value) {
      user.value.wallet_balance = balance
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  // 更新积分
  const updatePoints = (points) => {
    if (user.value) {
      user.value.points = points
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  // 更新团队信息
  const updateTeamInfo = (teamInfo) => {
    if (user.value) {
      user.value.team_info = { ...user.value.team_info, ...teamInfo }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  // 获取最后一次登录成功的账号
  const getLastLoginAccount = () => {
    return localStorage.getItem('last_login_account') || ''
  }

  // 检查是否为新用户
  const isNewUser = computed(() => {
    return user.value?.isNewUser || user.value?.is_new_user || (user.value?.noviceCount > 0) || false
  })

  // 检查是否有充值记录
  const hasRechargeRecord = computed(() => {
    return user.value?.has_recharge_record || false
  })

  // 检查是否需要绑定手机号
  const needBindPhone = computed(() => {
    return user.value?.need_bind_phone || false
  })

  // 获取用户角色
  const userRole = computed(() => {
    return user.value?.role || 'user'
  })

  // 获取用户等级
  const userLevel = computed(() => {
    return user.value?.level || 1
  })

  // 获取钱包余额
  const walletBalance = computed(() => {
    return user.value?.wallet_balance || 0
  })

  // 获取体验金余额
  const experienceBalance = computed(() => {
    return user.value?.experience_balance || 0
  })

  // 获取体验金数量（兼容字段）
  const experienceAmount = computed(() => {
    return user.value?.experience_amount || user.value?.experience_balance || 0
  })

  // 获取积分
  const userPoints = computed(() => {
    return user.value?.points || 0
  })

  // 新增：获取免费拼团剩余次数
  const noviceCount = computed(() => {
    return user.value?.noviceCount || 0
  })

  // 新增：检查是否可以参与免费拼团
  const canJoinFreeGroup = computed(() => {
    return isNewUser.value && noviceCount.value > 0
  })

  // 获取团队信息
  const teamInfo = computed(() => {
    return user.value?.team_info || {
      direct_invites: 0,
      total_team: 0,
      total_earnings: 0
    }
  })

  // 获取用户统计信息
  const userStats = computed(() => {
    return user.value?.stats || {
      total_orders: 0,
      total_spent: 0,
      total_saved: 0,
      success_groups: 0
    }
  })

  // 初始化
  initUserInfo()

  return {
    // 状态
    token,
    refreshToken,
    tokenExpireTime,
    user,
    isLoggedIn,
    isTokenExpired,
    isTokenExpiringSoon,
    
    // 计算属性
    isNewUser,
    noviceCount,
    canJoinFreeGroup,
    hasRechargeRecord,
    needBindPhone,
    userRole,
    userLevel,
    walletBalance,
    experienceBalance,
    experienceAmount,
    userPoints,
    teamInfo,
    userStats,
    
    // 方法
    setToken,
    refreshTokenState,
    validateToken,
    refreshTokenIfNeeded,
    forceValidateAuth,
    setUser,
    updateUser,
    login,
    register,
    oauthLogin,
    forgotPassword,
    logout,
    getCurrentUser,
    checkAuthStatus,
    updateWalletBalance,
    updatePoints,
    updateTeamInfo,
    getLastLoginAccount
  }
})

// 持久化配置（如果使用 pinia-plugin-persistedstate）
export const authStorePersist = {
  key: 'auth-store',
  storage: localStorage,
  paths: ['token', 'refreshToken', 'tokenExpireTime', 'user']
} 