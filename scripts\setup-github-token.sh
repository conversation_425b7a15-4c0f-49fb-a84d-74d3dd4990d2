#!/bin/bash
# GitHub Token配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🔐 GitHub Token配置脚本" $BLUE

# 检查是否提供了token
if [ -z "$1" ]; then
    print_message "使用方法: $0 <github_token>" $RED
    print_message "请提供GitHub Personal Access Token" $RED
    print_message "创建Token地址: https://github.com/settings/tokens" $YELLOW
    echo
    print_message "必需权限:" $YELLOW
    print_message "  ✅ repo (完整仓库权限)" $YELLOW
    print_message "  ✅ workflow (GitHub Actions权限)" $YELLOW
    print_message "  ✅ write:packages (包写入权限)" $YELLOW
    print_message "  ✅ admin:repo_hook (webhook权限)" $YELLOW
    exit 1
fi

GITHUB_TOKEN="$1"
GITHUB_USER="mengdong88"
REPO_NAME="PingTuan"

print_message "正在配置GitHub Token..." $BLUE

# 验证token有效性
print_message "验证Token权限..." $YELLOW
RESPONSE=$(curl -s -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user)

if echo "$RESPONSE" | grep -q "login"; then
    USER_LOGIN=$(echo "$RESPONSE" | grep -o '"login":"[^"]*' | cut -d'"' -f4)
    print_message "✅ Token验证成功，用户: $USER_LOGIN" $GREEN
else
    print_message "❌ Token验证失败，请检查Token是否正确" $RED
    exit 1
fi

# 检查workflow权限
print_message "检查workflow权限..." $YELLOW
SCOPES_RESPONSE=$(curl -s -I -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user)

if echo "$SCOPES_RESPONSE" | grep -q "workflow"; then
    print_message "✅ 检测到workflow权限" $GREEN
else
    print_message "⚠️  未检测到workflow权限，可能无法推送workflow文件" $YELLOW
    print_message "请确保Token包含 'workflow' 权限" $YELLOW
fi

# 配置Git凭据
print_message "配置Git凭据..." $BLUE

# 方法1: 更新远程URL
git remote set-url origin "https://${GITHUB_USER}:${GITHUB_TOKEN}@github.com/${GITHUB_USER}/${REPO_NAME}.git"

# 方法2: 配置凭据存储
git config --global credential.helper store

# 方法3: 设置环境变量
export GITHUB_TOKEN="$GITHUB_TOKEN"

print_message "✅ Git凭据配置完成" $GREEN

# 测试推送权限
print_message "测试推送权限..." $YELLOW

# 创建测试文件
echo "# Token Test - $(date)" > .git/token-test.md
git add .git/token-test.md
git commit -m "test: 测试新Token权限" > /dev/null 2>&1

if git push origin master > /dev/null 2>&1; then
    print_message "✅ 推送权限测试成功" $GREEN
    # 清理测试文件
    git reset --hard HEAD~1 > /dev/null 2>&1
    rm -f .git/token-test.md
else
    print_message "❌ 推送权限测试失败" $RED
    # 清理测试提交
    git reset --hard HEAD~1 > /dev/null 2>&1
    rm -f .git/token-test.md
    exit 1
fi

# 显示配置摘要
print_message "📋 配置摘要:" $BLUE
echo "  用户: $GITHUB_USER"
echo "  仓库: $REPO_NAME"
echo "  权限: repo, workflow, packages"
echo "  远程URL: https://github.com/${GITHUB_USER}/${REPO_NAME}.git"

print_message "🎉 GitHub Token配置完成!" $GREEN
print_message "现在可以推送workflow文件了" $GREEN

# 提供后续步骤
print_message "📝 后续步骤:" $BLUE
echo "1. 推送GitHub Actions workflow:"
echo "   git add .github/workflows/auto-pr-summary.yml"
echo "   git commit -m 'feat: 添加GitHub Actions自动化'"
echo "   git push origin master"
echo ""
echo "2. 测试自动化脚本:"
echo "   ./scripts/create-pr.sh"
echo ""
echo "3. 查看GitHub Actions:"
echo "   https://github.com/${GITHUB_USER}/${REPO_NAME}/actions"