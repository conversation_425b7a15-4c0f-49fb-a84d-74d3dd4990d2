package net.lab1024.sa.admin.module.business.activities.controller;

import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesAddForm;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesQueryForm;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesUpdateForm;
import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 抽奖活动表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */

@RestController
@Tag(name = "抽奖活动表")
public class ActivitiesController {

    @Resource
    private ActivitiesService activitiesService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/activities/queryPage")
    @SaCheckPermission("activities:query")
    public ResponseDTO<PageResult<ActivitiesVO>> query(@RequestBody @Valid ActivitiesQueryForm queryForm) {
        return ResponseDTO.ok(activitiesService.query(queryForm));
    }

    @Operation(summary = "活动列表")
    @GetMapping("/activities/listAll")
    @SaCheckPermission("activities:query")
    public ResponseDTO<List<ActivitiesVO>> listAll() {
        return ResponseDTO.ok(activitiesService.listAll());
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/activities/add")
    @SaCheckPermission("activities:add")
    public ResponseDTO<String> add(@RequestBody @Valid ActivitiesAddForm addForm) {
        return activitiesService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/activities/update")
    @SaCheckPermission("activities:update")
    public ResponseDTO<String> update(@RequestBody @Valid ActivitiesUpdateForm updateForm) {
        return activitiesService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/activities/batchDelete")
    @SaCheckPermission("activities:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return activitiesService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/activities/delete/{id}")
    @SaCheckPermission("activities:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
        return activitiesService.delete(id);
    }
}
