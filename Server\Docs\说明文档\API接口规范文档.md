# 团购网API接口规范文档

## 1. 接口规范概览

### 1.1 API设计原则
- **RESTful设计**: 使用标准的HTTP方法和状态码
- **统一响应格式**: 所有接口返回统一的JSON格式
- **版本控制**: 通过URL路径进行版本管理
- **安全认证**: 基于Token的身份验证机制
- **错误处理**: 标准化的错误码和错误信息

### 1.2 基础信息
- **API基础URL**: `https://api.example.com`
- **当前版本**: `v1`
- **字符编码**: `UTF-8`
- **内容类型**: `application/json`
- **认证方式**: `Bearer Token`

### 1.3 接口分类
| 分类 | 路径前缀 | 描述 | 状态 |
|------|----------|------|------|
| 认证相关 | `/app/v1/auth` | 用户登录、注册、验证码 | ✅ 已实现 |
| 首页数据 | `/api/v1/home` | 首页展示数据 | ✅ 已实现 |
| 商品管理 | `/api/v1/products` | 商品信息、分类 | ✅ 已实现 |
| 活动管理 | `/api/v1/activities` | 团购活动 | ✅ 已实现 |
| 拼团功能 | `/api/v1/groups` | 拼团操作 | ✅ 已实现 |
| 订单管理 | `/api/v1/orders` | 订单处理 | ✅ 已实现 |
| 支付系统 | `/api/v1/payments` | 支付流程 | ✅ 已实现 |
| 用户中心 | `/api/v1/user` | 用户信息、钱包 | ✅ 已实现 |
| 文件上传 | `/api/v1/upload` | 文件处理 | ⏳ 待实现 |
| 客服支持 | `/api/v1/support` | 客服系统 | ⏳ 待实现 |

## 2. 认证和授权

### 2.1 认证方式
团购网API使用基于Token的认证机制：

```http
Authorization: Bearer <token>
```

### 2.2 Token获取
通过登录接口获取Token：

```http
POST /app/v1/auth
Content-Type: application/json

{
  "type": "login",
  "phone": "***********",
  "password": "123456",
  "captchaCode": "9256",
  "captchaUuid": "4604cf4dc962467a806efc7357c519c0"
}
```

### 2.3 Token管理
- **有效期**: 30天
- **刷新机制**: 自动刷新
- **存储方式**: 客户端安全存储
- **失效处理**: 自动跳转登录页面

### 2.4 权限控制
- **公开接口**: 不需要认证的接口（登录、注册、商品浏览等）
- **用户接口**: 需要用户登录的接口
- **管理接口**: 需要管理员权限的接口

## 3. 请求和响应规范

### 3.1 统一响应格式
所有API接口都返回统一的JSON格式：

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {},
  "dataType": 1,
  "timestamp": 1672531200000
}
```

**字段说明**:
- `code`: 响应状态码，0表示成功，非0表示失败
- `msg`: 响应消息，提供详细的操作结果描述
- `ok`: 布尔值，true表示成功，false表示失败
- `data`: 具体的响应数据
- `dataType`: 数据类型标识
- `timestamp`: 响应时间戳（可选）

### 3.2 HTTP状态码
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 创建成功 | 资源创建成功 |
| 400 | 请求错误 | 参数错误、格式错误 |
| 401 | 未授权 | 未登录或Token无效 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 422 | 数据验证失败 | 数据格式正确但验证失败 |
| 429 | 请求过于频繁 | 触发限流 |
| 500 | 服务器错误 | 系统内部错误 |

### 3.3 错误码规范
业务错误码采用4位数字：

| 错误码范围 | 含义 | 示例 |
|------------|------|------|
| 1000-1999 | 认证相关 | 1001: 用户名或密码错误 |
| 2000-2999 | 用户相关 | 2001: 用户不存在 |
| 3000-3999 | 商品相关 | 3001: 商品库存不足 |
| 4000-4999 | 订单相关 | 4001: 订单不存在 |
| 5000-5999 | 支付相关 | 5001: 余额不足 |
| 6000-6999 | 钱包相关 | 6001: 提现金额超限 |
| 9000-9999 | 系统相关 | 9001: 系统维护中 |

### 3.4 分页响应格式
分页接口统一返回格式：

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [],
    "pageNum": 1,
    "pageSize": 20,
    "total": 100,
    "pages": 5
  }
}
```

**分页参数说明**:
- `pageNum`: 当前页码，从1开始
- `pageSize`: 每页数量
- `total`: 总记录数
- `pages`: 总页数
- `list`: 当前页数据列表

## 4. 核心业务接口规范

### 4.1 认证系统接口

#### 4.1.1 用户登录
```http
POST /app/v1/auth
Content-Type: application/json

{
  "type": "login",
  "phone": "***********",
  "password": "123456",
  "captchaCode": "9256",
  "captchaUuid": "uuid"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "employeeId": 81,
    "userType": "H5",
    "actualName": "***********",
    "phone": "***********",
    "token": "09fa852cca4042f4842dc4a9cff9f9e3",
    "childCount": 0,
    "noviceCount": 3,
    "userId": 81
  }
}
```

#### 4.1.2 用户注册
```http
POST /app/v1/auth
Content-Type: application/json

{
  "type": "register",
  "phone": "13833883389",
  "password": "123456",
  "confirmPassword": "123456",
  "captchaCode": "5831",
  "captchaUuid": "uuid",
  "agreed_to_terms": true,
  "invite_code": ""
}
```

#### 4.1.3 获取验证码
```http
GET /app/v1/captcha
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "captchaUuid": "uuid",
    "captchaBase64Image": "data:image/png;base64,...",
    "expireSeconds": 65
  }
}
```

### 4.2 商品系统接口

#### 4.2.1 商品列表
```http
GET /api/v1/products?pageNum=1&pageSize=20&categoryId=1
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "苹果正品耳机",
        "price": 99.00,
        "groupPrice": 89.00,
        "image": "/uploads/products/headphone.jpg",
        "categoryName": "数码产品",
        "stock": 100,
        "sales": 1520,
        "status": "active",
        "redPacketAmount": 4.50,
        "groupSize": 3
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 50,
    "pages": 3
  }
}
```

#### 4.2.2 商品详情
```http
GET /api/v1/product/{id}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 1,
    "name": "苹果正品耳机",
    "price": 99.00,
    "groupPrice": 89.00,
    "images": [
      "/uploads/products/headphone1.jpg",
      "/uploads/products/headphone2.jpg"
    ],
    "categoryName": "数码产品",
    "stock": 100,
    "sales": 1520,
    "description": "高品质立体声耳机，音质清晰，佩戴舒适",
    "specifications": "颜色：黑色，重量：150g",
    "skus": [
      {
        "id": 26,
        "name": "黑色",
        "price": 99.00,
        "groupPrice": 89.00,
        "stock": 50
      }
    ]
  }
}
```

### 4.3 订单系统接口

#### 4.3.1 创建订单
```http
POST /api/v1/orders
Content-Type: application/json

{
  "productId": 1,
  "skuId": 26,
  "quantity": 1,
  "type": "group",
  "groupId": 123,
  "shippingAddressId": 8,
  "paymentMethod": "balance"
}
```

#### 4.3.2 订单列表
```http
GET /api/v1/orders?pageNum=1&pageSize=20&status=paid
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 456,
        "orderSn": "************",
        "productName": "苹果正品耳机",
        "productImage": "/uploads/products/headphone.jpg",
        "amount": 89.00,
        "status": "paid",
        "type": "group",
        "createTime": "2025-07-27 10:05:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 10,
    "pages": 1
  }
}
```

### 4.4 拼团系统接口

#### 4.4.1 创建拼团
```http
POST /api/v1/groups
Content-Type: application/json

{
  "productId": 1,
  "activityId": 4,
  "skuId": 26,
  "shippingAddressId": 8
}
```

#### 4.4.2 加入拼团
```http
POST /api/v1/groups/{groupId}/join
Content-Type: application/json

{
  "skuId": 26,
  "shippingAddressId": 8
}
```

#### 4.4.3 拼团详情
```http
GET /api/v1/groups/{groupId}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 123,
    "activityId": 4,
    "productName": "苹果正品耳机",
    "groupPrice": 89.00,
    "groupSize": 3,
    "currentCount": 2,
    "status": "active",
    "participants": [
      {
        "userId": 81,
        "userName": "用户1",
        "avatar": "/uploads/avatars/user1.jpg",
        "isLeader": true
      }
    ],
    "timeRemaining": 43200000
  }
}
```

### 4.5 支付系统接口

#### 4.5.1 创建支付
```http
POST /api/v1/payments
Content-Type: application/json

{
  "orderId": 456,
  "paymentMethod": "balance",
  "amount": 89.00,
  "returnUrl": "https://app.example.com/payment/callback"
}
```

#### 4.5.2 支付状态查询
```http
GET /api/v1/payments/{paymentId}/status
```

### 4.6 钱包系统接口

#### 4.6.1 钱包信息
```http
GET /api/v1/wallet?pageNum=1&pageSize=3
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "balance": {
      "totalBalance": 21742.40,
      "balance": 21242.40,
      "experienceBalance": 500.00,
      "points": 0,
      "totalRecharge": 39300.00,
      "totalWithdraw": 18020.00,
      "status": 1
    },
    "transactions": {
      "list": [
        {
          "id": 74,
          "amount": -10.00,
          "balanceAfter": 21242.40,
          "type": "PAYMENT",
          "description": "购物支付",
          "createTime": "2025-07-27 16:01:31"
        }
      ],
      "pageNum": 1,
      "pageSize": 3,
      "total": 42
    }
  }
}
```

#### 4.6.2 提现申请
```http
POST /api/v1/withdrawalsApply
Content-Type: application/json

{
  "amount": "2000",
  "bankName": "越南银行",
  "bankAccount": "**********",
  "accountHolder": "布洛林"
}
```

## 5. 数据模型规范

### 5.1 用户模型
```json
{
  "id": 81,
  "phone": "***********",
  "actualName": "用户姓名",
  "avatar": "/uploads/avatars/user.jpg",
  "status": 1,
  "childCount": 5,
  "noviceCount": 3,
  "hasReal": true,
  "createTime": "2025-07-01 10:00:00"
}
```

### 5.2 商品模型
```json
{
  "id": 1,
  "name": "商品名称",
  "price": 99.00,
  "groupPrice": 89.00,
  "image": "/uploads/products/product.jpg",
  "images": ["/uploads/products/1.jpg"],
  "categoryId": 1,
  "categoryName": "分类名称",
  "stock": 100,
  "sales": 1520,
  "status": "active",
  "description": "商品描述",
  "specifications": "规格信息",
  "redPacketAmount": 4.50,
  "groupSize": 3,
  "skus": [
    {
      "id": 26,
      "name": "SKU名称",
      "price": 99.00,
      "stock": 50
    }
  ]
}
```

### 5.3 订单模型
```json
{
  "id": 456,
  "orderSn": "************",
  "userId": 81,
  "productId": 1,
  "productName": "商品名称",
  "skuId": 26,
  "skuName": "SKU名称",
  "quantity": 1,
  "amount": 89.00,
  "status": "paid",
  "type": "group",
  "groupId": 123,
  "shippingAddress": {
    "name": "收货人",
    "phone": "手机号",
    "address": "详细地址"
  },
  "createTime": "2025-07-27 10:05:00",
  "payTime": "2025-07-27 10:06:00"
}
```

### 5.4 拼团模型
```json
{
  "id": 123,
  "activityId": 4,
  "productId": 1,
  "groupPrice": 89.00,
  "groupSize": 3,
  "currentCount": 2,
  "status": "active",
  "startTime": "2025-07-27 10:00:00",
  "endTime": "2025-07-27 22:00:00",
  "participants": [
    {
      "userId": 81,
      "userName": "用户名",
      "avatar": "/uploads/avatars/user.jpg",
      "isLeader": true,
      "joinTime": "2025-07-27 10:05:00"
    }
  ]
}
```

### 5.5 钱包模型
```json
{
  "userId": 81,
  "totalBalance": 21742.40,
  "balance": 21242.40,
  "experienceBalance": 500.00,
  "points": 0,
  "totalRecharge": 39300.00,
  "totalWithdraw": 18020.00,
  "status": 1
}
```

## 6. 接口安全规范

### 6.1 请求安全
- **HTTPS协议**: 所有API接口必须使用HTTPS
- **请求签名**: 关键接口增加请求签名验证
- **参数验证**: 严格验证所有输入参数
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **XSS防护**: 对输出内容进行HTML转义

### 6.2 认证安全
- **Token加密**: JWT Token使用强加密算法
- **Token过期**: 设置合理的Token过期时间
- **刷新机制**: 支持Token自动刷新
- **单点登录**: 支持多设备登录控制

### 6.3 数据安全
- **敏感数据加密**: 密码、银行卡号等敏感信息加密存储
- **数据脱敏**: 日志和接口响应中的敏感数据脱敏
- **访问控制**: 基于角色的访问控制机制
- **审计日志**: 记录关键操作的审计日志

### 6.4 接口限流
- **频率限制**: 限制单个用户的接口调用频率
- **并发控制**: 控制系统整体并发数
- **黑名单机制**: 支持IP和用户黑名单
- **熔断降级**: 在系统异常时启用熔断机制

## 7. 接口测试规范

### 7.1 测试环境
- **开发环境**: `https://dev-api.example.com`
- **测试环境**: `https://test-api.example.com`
- **预发环境**: `https://pre-api.example.com`
- **生产环境**: `https://api.example.com`

### 7.2 测试工具
- **Postman**: API接口测试
- **JMeter**: 性能和压力测试
- **Swagger**: API文档和在线测试
- **单元测试**: 基于JUnit的单元测试

### 7.3 测试用例
每个接口都应包含以下测试用例：
- **正常场景**: 正确参数的正常调用
- **异常场景**: 错误参数的异常处理
- **边界场景**: 参数边界值的处理
- **安全场景**: 权限验证和安全检查
- **性能场景**: 接口响应时间和并发测试

### 7.4 测试数据
- **测试账号**: 提供标准的测试账号
- **测试商品**: 准备完整的测试商品数据
- **测试订单**: 涵盖各种状态的测试订单
- **测试支付**: 模拟支付的测试数据

## 8. 接口版本管理

### 8.1 版本策略
- **URL版本控制**: `/api/v1/`, `/api/v2/`
- **向后兼容**: 新版本保持向后兼容
- **废弃通知**: 提前通知接口废弃计划
- **平滑迁移**: 支持多版本并存

### 8.2 版本发布
- **版本号规则**: 采用语义化版本号（v1.0.0）
- **发布说明**: 详细的版本更新说明
- **迁移指南**: 版本升级的迁移指南
- **测试验证**: 充分的兼容性测试

### 8.3 版本文档
- **API文档**: 每个版本的完整API文档
- **变更日志**: 详细的接口变更记录
- **兼容性说明**: 版本间的兼容性说明
- **废弃计划**: 旧版本的废弃时间表

## 9. 接口监控和运维

### 9.1 监控指标
- **响应时间**: 接口平均响应时间
- **成功率**: 接口调用成功率
- **错误率**: 接口调用错误率
- **并发数**: 接口并发调用数
- **流量统计**: 接口调用流量统计

### 9.2 告警机制
- **响应时间告警**: 响应时间超过阈值告警
- **错误率告警**: 错误率超过阈值告警
- **服务异常告警**: 服务不可用告警
- **资源告警**: 系统资源使用告警

### 9.3 日志管理
- **访问日志**: 记录所有API访问日志
- **错误日志**: 记录接口调用错误日志
- **业务日志**: 记录关键业务操作日志
- **性能日志**: 记录接口性能数据

### 9.4 运维工具
- **监控面板**: 实时监控接口状态
- **日志分析**: 日志查询和分析工具
- **性能分析**: 接口性能分析工具
- **告警通知**: 多渠道告警通知机制

## 10. 接口文档管理

### 10.1 文档规范
- **接口描述**: 详细的接口功能描述
- **参数说明**: 完整的请求参数说明
- **响应格式**: 标准的响应格式说明
- **错误码**: 详细的错误码说明
- **示例代码**: 多语言的调用示例

### 10.2 文档维护
- **自动生成**: 基于代码注解自动生成文档
- **版本同步**: 文档与接口版本同步更新
- **审核流程**: 文档变更审核流程
- **发布管理**: 文档发布和分发管理

### 10.3 文档格式
- **Markdown**: 使用Markdown格式编写
- **Swagger**: 使用OpenAPI规范
- **在线文档**: 提供在线查看和测试
- **导出功能**: 支持PDF等格式导出

## 11. 最佳实践

### 11.1 接口设计最佳实践
- **职责单一**: 每个接口只负责一个具体功能
- **幂等性**: 确保GET、PUT、DELETE操作的幂等性
- **资源导向**: 以资源为中心设计API
- **状态无关**: 接口应该是无状态的
- **一致性**: 保持接口设计的一致性

### 11.2 性能优化最佳实践
- **分页查询**: 大数据量查询必须分页
- **字段筛选**: 支持字段筛选减少数据传输
- **缓存策略**: 合理使用缓存提升性能
- **异步处理**: 耗时操作采用异步处理
- **连接池**: 使用连接池管理数据库连接

### 11.3 安全最佳实践
- **最小权限**: 遵循最小权限原则
- **输入验证**: 严格验证所有输入数据
- **输出编码**: 对输出数据进行适当编码
- **错误处理**: 统一的错误处理机制
- **安全审计**: 定期进行安全审计

### 11.4 维护最佳实践
- **代码规范**: 遵循统一的代码规范
- **单元测试**: 完善的单元测试覆盖
- **集成测试**: 自动化的集成测试
- **监控告警**: 完善的监控告警机制
- **文档更新**: 及时更新接口文档

## 12. 总结

团购网API接口规范文档定义了完整的API开发、测试、部署和维护标准。通过遵循这些规范，可以确保：

### 12.1 开发效率
- **标准化**: 统一的开发标准和规范
- **复用性**: 可复用的接口设计模式
- **维护性**: 易于维护和扩展的接口架构

### 12.2 系统质量
- **稳定性**: 经过充分测试的稳定接口
- **安全性**: 多层级的安全防护机制
- **性能**: 高性能的接口响应能力

### 12.3 用户体验
- **一致性**: 统一的接口交互体验
- **可靠性**: 可靠的服务可用性
- **易用性**: 简单易用的接口设计

本规范文档将随着业务发展和技术演进持续更新完善，为团购网平台的长期发展提供坚实的技术基础。

---

**文档版本**: V1.0  
**创建日期**: 2025年1月  
**维护团队**: API开发组  
**审核状态**: 已审核