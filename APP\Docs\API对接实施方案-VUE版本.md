# API对接实施方案 - Vue版本

本文档详细说明团购网APP各页面功能与后端API接口的对接方案，基于Vue 3组合式API和现代前端技术栈进行设计。

## 📋 概述

- **项目名称**: 团购网APP
- **前端技术栈**: Vue 3 + Vite + Pinia + Vant UI + TailwindCSS
- **API版本**: v1
- **基础URL**: `/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **状态管理**: Pinia Store
- **路由管理**: Vue Router 4
- **UI组件库**: Vant 4.x

## 🏗️ 项目架构

### 技术栈版本
```json
{
  "vue": "^3.3.4",
  "vite": "^4.4.9",
  "pinia": "^2.1.6",
  "vue-router": "^4.2.4",
  "vant": "^4.9.20",
  "axios": "^1.5.0",
  "tailwindcss": "^3.3.3"
}
```

### 目录结构
```
src/
├── api/                 # API接口层
│   ├── auth.js         # 认证相关API
│   ├── user.js         # 用户相关API
│   ├── product.js      # 商品相关API
│   ├── order.js        # 订单相关API
│   ├── mock-v2.js      # Mock数据
│   └── standardAdapter.js # 标准API适配器
├── components/         # 公共组件
├── config/            # 配置文件
│   └── env.js         # 环境配置
├── router/            # 路由配置
│   └── index.js       # 路由主文件
├── store/             # Pinia状态管理
│   ├── auth.js        # 认证状态
│   ├── user.js        # 用户状态
│   ├── cart.js        # 购物车状态
│   └── product.js     # 商品状态
├── utils/             # 工具函数
│   ├── request.js     # 请求工具
│   ├── message.js     # 消息提示
│   └── format.js      # 格式化工具
├── views/             # 页面组件
│   ├── home/          # 首页相关
│   ├── product/       # 商品相关
│   ├── order/         # 订单相关
│   ├── user/          # 用户相关
│   └── auth/          # 认证相关
├── App.vue            # 根组件
└── main.js            # 入口文件
```

## 🔧 核心配置

### 1. 环境配置 (src/config/env.js)
```javascript
export const ENV_CONFIG = {
  // 是否使用Mock数据
  useMock: import.meta.env.VITE_USE_MOCK !== 'false',
  
  // API基础URL
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  
  // 是否为开发环境
  isDevelopment: import.meta.env.DEV,
  
  // 是否为生产环境
  isProduction: import.meta.env.PROD
}
```

### 2. 请求工具 (src/utils/request.js)
```javascript
import { showError } from '@/utils/message.js'

// 统一的API请求函数
export async function apiRequest(url, options = {}) {
  const {
    method = 'GET',
    data = null,
    headers = {},
    requireAuth = true,
    timeout = 10000
  } = options

  const token = localStorage.getItem('token')
  const requestHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers
  }

  // 添加认证头
  if (requireAuth && token) {
    requestHeaders['Authorization'] = `Bearer ${token}`
  }

  const requestConfig = {
    method,
    headers: requestHeaders,
    signal: AbortSignal.timeout(timeout)
  }

  // 添加请求体
  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestConfig.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(url, requestConfig)
    const responseData = await response.json()

    if (!response.ok) {
      throw new Error(responseData.message || `HTTP ${response.status}`)
    }

    return responseData
  } catch (error) {
    showError(error.message)
    throw error
  }
}
```

### 3. 标准API适配器 (src/api/standardAdapter.js)
```javascript
import { MockApiV2 } from './mock-v2.js'
import { ENV_CONFIG } from '@/config/env.js'

export class StandardApiAdapter {
  constructor(baseURL = '/api/v1') {
    this.isMock = ENV_CONFIG.useMock
    this.baseURL = baseURL
  }

  async request(method, url, data = null, headers = {}) {
    const token = this.getToken()
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    }

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    if (data) {
      if (method.toUpperCase() === 'GET') {
        const params = new URLSearchParams(data)
        url += (url.includes('?') ? '&' : '?') + params.toString()
      } else {
        config.body = JSON.stringify(data)
      }
    }

    try {
      if (this.isMock) {
        return await this.mockRequest(method, url, data)
      } else {
        const response = await fetch(`${this.baseURL}${url}`, config)
        const result = await response.json()
        
        if (!response.ok) {
          throw new Error(result.message || `HTTP ${response.status}`)
        }
        
        return result
      }
    } catch (error) {
      console.error(`API请求失败 [${method.toUpperCase()} ${url}]:`, error)
      throw error
    }
  }

  // 认证相关API
  async login(phone, password) {
    return await this.request('POST', '/auth', {
      type: 'login',
      phone,
      password
    })
  }

  async register(registerData) {
    return await this.request('POST', '/auth', {
      type: 'register',
      ...registerData
    })
  }

  async forgotPassword(phone, verificationCode, newPassword, confirmPassword) {
    return await this.request('POST', '/auth', {
      type: 'forgot_password',
      phone,
      verification_code: verificationCode,
      new_password: newPassword,
      confirm_password: confirmPassword
    })
  }

  async oauthLogin(provider, token, userInfo = null) {
    return await this.request('POST', '/auth', {
      type: 'oauth',
      provider,
      token,
      user_info: userInfo
    })
  }

  // 首页数据API
  async getHomeData(params = {}) {
    return await this.request('GET', '/data/home', params)
  }

  async getBanners(params = {}) {
    return await this.request('GET', '/data/banners', params)
  }

  async getProducts(params = {}) {
    return await this.request('GET', '/data/products', params)
  }

  async getCategories(params = {}) {
    return await this.request('GET', '/data/categories', params)
  }

  async search(params) {
    return await this.request('GET', '/search', params)
  }

  // 商品相关API
  async getProductDetail(productId) {
    return await this.request('GET', `/products/${productId}`)
  }

  async productAction(productId, action, params = {}) {
    return await this.request('POST', `/products/${productId}/action`, {
      action,
      params
    })
  }

  // 拼团相关API
  async getGroupDetail(groupId) {
    return await this.request('GET', `/groups/${groupId}`)
  }

  async groupAction(action, data) {
    return await this.request('POST', '/groups/action', {
      action,
      ...data
    })
  }

  // 订单相关API
  async getOrders(params = {}) {
    return await this.request('GET', '/orders', params)
  }

  async orderAction(orderId, action, params = {}) {
    return await this.request('POST', `/orders/${orderId}/action`, {
      action,
      params
    })
  }

  // 支付相关API
  async getPaymentData(params = {}) {
    return await this.request('GET', '/payment', params)
  }

  async processPayment(paymentData) {
    return await this.request('POST', '/payment', paymentData)
  }

  // 用户相关API
  async getUserDashboard() {
    return await this.request('GET', '/user/dashboard')
  }

  async updateUserProfile(profileData) {
    return await this.request('PUT', '/user/profile', profileData)
  }

  async getUserSettings() {
    return await this.request('GET', '/user/settings')
  }

  async updateUserSettings(settingsData) {
    return await this.request('PUT', '/user/settings', settingsData)
  }

  // 钱包相关API
  async getWallet(params = {}) {
    return await this.request('GET', '/wallet', params)
  }

  async walletAction(action, data) {
    return await this.request('POST', '/wallet/action', {
      action,
      ...data
    })
  }

  // 工具方法
  getToken() {
    return localStorage.getItem('token') || localStorage.getItem('mock_token')
  }

  async mockRequest(method, url, data) {
    // Mock请求处理逻辑
    // 详细实现见现有的standardAdapter.js文件
    return await MockApiV2.handleRequest(method, url, data)
  }
}

// 导出标准API实例
export const standardApi = new StandardApiAdapter()
```

## 🏪 Pinia状态管理

### 1. 认证状态管理 (src/store/auth.js)
```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { standardApi } from '@/api/standardAdapter'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refresh_token') || '')
  const user = ref(null)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isNewUser = computed(() => user.value?.is_new_user || false)

  // 初始化用户信息
  const initUserInfo = () => {
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      try {
        user.value = JSON.parse(userInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  // 设置token
  const setToken = (newToken, newRefreshToken) => {
    token.value = newToken
    if (newRefreshToken) {
      refreshToken.value = newRefreshToken
    }
    localStorage.setItem('token', newToken)
    if (newRefreshToken) {
      localStorage.setItem('refresh_token', newRefreshToken)
    }
  }

  // 设置用户信息
  const setUser = (userInfo) => {
    user.value = userInfo
    localStorage.setItem('user_info', JSON.stringify(userInfo))
  }

  // 登录
  const login = async (loginData) => {
    try {
      const response = await standardApi.login(loginData.phone, loginData.password)
      
      if (response.code === 200) {
        setToken(response.data.token, response.data.refresh_token)
        setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      const response = await standardApi.register(registerData)
      
      if (response.code === 200) {
        setToken(response.data.token, response.data.refresh_token)
        setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 清除本地数据
      token.value = ''
      refreshToken.value = ''
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  // 更新用户信息
  const updateUser = (updates) => {
    if (user.value) {
      user.value = { ...user.value, ...updates }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    
    // 计算属性
    isLoggedIn,
    isNewUser,
    
    // 方法
    initUserInfo,
    setToken,
    setUser,
    login,
    register,
    logout,
    updateUser
  }
})
```

### 2. 商品状态管理 (src/store/product.js)
```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { standardApi } from '@/api/standardAdapter'

export const useProductStore = defineStore('product', () => {
  // 状态
  const products = ref([])
  const currentCategory = ref('recommended')
  const currentGroupType = ref(3)
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)

  // 计算属性
  const currentProducts = computed(() => products.value)
  const categoryTitle = computed(() => {
    const titles = {
      recommended: '推荐商品',
      special: '特价专区',
      hot: '热门商品',
      all: '全部商品'
    }
    return titles[currentCategory.value] || '商品列表'
  })

  // 获取首页数据
  const getHomeData = async (params = {}) => {
    try {
      loading.value = true
      const response = await standardApi.getHomeData(params)
      
      if (response.code === 200) {
        products.value = response.data.initial_products?.items || []
        hasMore.value = response.data.initial_products?.pagination?.has_more || false
        return response.data
      }
    } catch (error) {
      console.error('获取首页数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取商品列表
  const getProducts = async (params = {}) => {
    try {
      loading.value = true
      const response = await standardApi.getProducts({
        category: currentCategory.value,
        group_type: currentGroupType.value,
        page: currentPage.value,
        per_page: 20,
        ...params
      })
      
      if (response.code === 200) {
        if (currentPage.value === 1) {
          products.value = response.data.items || []
        } else {
          products.value.push(...(response.data.items || []))
        }
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索商品
  const searchProducts = async (query, params = {}) => {
    try {
      loading.value = true
      const response = await standardApi.search({
        q: query,
        type: 'product',
        page: 1,
        per_page: 20,
        ...params
      })
      
      if (response.code === 200) {
        products.value = response.data.results?.products || []
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      }
    } catch (error) {
      console.error('搜索商品失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 切换分类
  const filterProducts = async (category) => {
    currentCategory.value = category
    currentPage.value = 1
    await getProducts()
  }

  // 切换拼团类型
  const switchGroupType = async (groupType) => {
    currentGroupType.value = groupType
    currentPage.value = 1
    await getProducts()
  }

  // 加载更多
  const loadMore = async () => {
    if (!hasMore.value || loading.value) return false
    
    currentPage.value += 1
    await getProducts()
    return hasMore.value
  }

  // 刷新商品列表
  const refreshProducts = async () => {
    currentPage.value = 1
    await getProducts()
  }

  return {
    // 状态
    products,
    currentCategory,
    currentGroupType,
    loading,
    hasMore,
    currentPage,
    
    // 计算属性
    currentProducts,
    categoryTitle,
    
    // 方法
    getHomeData,
    getProducts,
    searchProducts,
    filterProducts,
    switchGroupType,
    loadMore,
    refreshProducts
  }
})
```

## 🎯 Vue组件实现

### 1. 首页组件 (src/views/home/<USER>
```vue
<template>
  <div class="bg-gray-50">
    <!-- 顶部搜索区域 -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-4">
      <div class="flex items-center bg-white rounded-full px-4 py-2">
        <iconify-icon icon="material-symbols:search" class="text-gray-400 text-lg mr-3"></iconify-icon>
        <input 
          type="text" 
          v-model="searchKeyword"
          placeholder="搜索商品、品牌或活动" 
          class="flex-1 outline-none text-xs"
          @keyup.enter="handleSearch"
        >
      </div>
    </div>

    <!-- Banner轮播 -->
    <div class="px-4 py-4">
      <div class="relative rounded-2xl overflow-hidden" style="height: 150px;">
        <div 
          v-for="(banner, index) in banners" 
          :key="banner.id"
          class="absolute inset-0 p-4 transition-all duration-500 text-white"
          :class="[
            currentBannerIndex === index ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'
          ]"
          :style="{ background: getBannerBackground(banner) }"
        >
          <div class="text-xs mb-1">{{ banner.activity_text || '🎉 限时活动' }}</div>
          <div class="text-sm font-bold mb-2">{{ banner.title || '平台补贴专区' }}</div>
          <div class="text-xs mb-3">{{ banner.subtitle || '每日签到领奖励' }}</div>
          <div 
            class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs inline-block cursor-pointer" 
            @click="handleBannerClick(banner)"
          >
            {{ banner.button_text || '立即参与' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 商品分类导航 -->
    <div class="bg-white mx-4 rounded-2xl p-4 shadow-sm mb-2">
      <div class="grid grid-cols-4 gap-4">
        <div 
          v-for="category in categories" 
          :key="category.id"
          class="flex flex-col items-center cursor-pointer" 
          @click="filterProducts(category.id)"
        >
          <div :class="[
            'w-12 h-12 rounded-2xl flex items-center justify-center mb-2',
            category.color
          ]">
            <iconify-icon :icon="category.icon" class="text-white text-xl"></iconify-icon>
          </div>
          <span class="text-xs text-gray-700">{{ category.name }}</span>
        </div>
      </div>
    </div>

    <!-- 拼团类型切换 -->
    <div class="px-4 py-2">
      <div class="flex items-center justify-between mb-1">
        <h2 class="text-sm font-bold text-gray-700">{{ categoryTitle }}</h2>
        <div class="flex items-center text-gray-500 text-xs cursor-pointer" @click="refreshProducts">
          <iconify-icon icon="material-symbols:refresh" class="mr-1"></iconify-icon>
          <span>刷新</span>
        </div>
      </div>
      
      <div class="bg-white rounded-2xl p-1 shadow-sm mb-2">
        <div class="grid grid-cols-2 gap-1">
          <div 
            :class="[
              'flex items-center justify-center py-1.5 px-4 rounded-xl cursor-pointer transition-all duration-200 text-xs',
              currentGroupType === 3 ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'
            ]"
            @click="switchGroupType(3)"
          >
            <iconify-icon icon="material-symbols:group" class="mr-2"></iconify-icon>
            <span>3人团</span>
          </div>
          <div 
            :class="[
              'flex items-center justify-center py-1.5 px-4 rounded-xl cursor-pointer transition-all duration-200 text-xs',
              currentGroupType === 10 ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'
            ]"
            @click="switchGroupType(10)"
          >
            <iconify-icon icon="material-symbols:groups" class="mr-2"></iconify-icon>
            <span>10人团</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="px-4">
      <!-- 加载状态 -->
      <div v-if="loading && products.length === 0" class="text-center py-8">
        <van-loading size="24px" />
        <p class="text-gray-500 text-sm mt-2">加载中...</p>
      </div>
      
      <!-- 商品网格 -->
      <div v-else class="grid grid-cols-2 gap-3">
        <div 
          v-for="product in products" 
          :key="product.id"
          class="bg-white rounded-2xl overflow-hidden shadow-sm cursor-pointer product-card"
          @click="goToProductDetail(product.id)"
        >
          <div class="w-full h-40 bg-cover bg-center relative" :style="{ backgroundImage: `url('${product.image}')` }">
            <div v-if="product.tag" class="absolute top-2 left-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
              {{ product.tag }}
            </div>
            <div v-if="product.group_type" class="absolute bottom-2 right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs px-2 py-1 rounded-full">
              {{ product.group_type }}人团
            </div>
          </div>
          <div class="p-3">
            <h3 class="text-xs font-medium text-gray-800 mb-1 line-clamp-2">{{ product.title }}</h3>
            <div class="flex items-center justify-between mb-0.5">
              <span class="text-red-500 font-bold text-xs">{{ formatCurrency(product.price) }}</span>
              <span class="text-gray-400 text-xs line-through">{{ formatCurrency(product.original_price) }}</span>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>{{ product.participants }}人已参团</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 暂无数据 -->
      <div v-if="!loading && products.length === 0" class="text-center py-12">
        <iconify-icon icon="material-symbols:inventory-2-outline" class="text-gray-300 text-5xl mb-4"></iconify-icon>
        <p class="text-gray-500 text-sm">暂无商品数据</p>
        <van-button type="primary" size="small" @click="refreshProducts" class="mt-4">
          重新加载
        </van-button>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && products.length > 0" class="text-center py-4">
      <van-button 
        :loading="loading" 
        @click="loadMore"
        size="small"
        plain
      >
        {{ loading ? '加载中...' : '加载更多' }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '@/store/product'
import { showError, showSuccess } from '@/utils/message'
import { formatCurrency } from '@/utils/format'

// 路由
const router = useRouter()

// 状态管理
const productStore = useProductStore()

// 响应式数据
const searchKeyword = ref('')
const banners = ref([])
const categories = ref([])
const currentBannerIndex = ref(0)

// 计算属性
const products = computed(() => productStore.currentProducts)
const loading = computed(() => productStore.loading)
const hasMore = computed(() => productStore.hasMore)
const currentGroupType = computed(() => productStore.currentGroupType)
const categoryTitle = computed(() => productStore.categoryTitle)

// 方法
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) return
  
  try {
    await productStore.searchProducts(searchKeyword.value)
    showSuccess('搜索完成')
  } catch (error) {
    showError('搜索失败')
  }
}

const filterProducts = async (category) => {
  try {
    await productStore.filterProducts(category)
  } catch (error) {
    showError('筛选失败')
  }
}

const switchGroupType = async (groupType) => {
  try {
    await productStore.switchGroupType(groupType)
  } catch (error) {
    showError('切换失败')
  }
}

const refreshProducts = async () => {
  try {
    await productStore.refreshProducts()
    showSuccess('刷新成功')
  } catch (error) {
    showError('刷新失败')
  }
}

const loadMore = async () => {
  try {
    await productStore.loadMore()
  } catch (error) {
    showError('加载失败')
  }
}

const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`)
}

const getBannerBackground = (banner) => {
  return banner.background_color || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
}

const handleBannerClick = (banner) => {
  if (banner.link_type === 'activity') {
    router.push(`/activity/${banner.link_value}`)
  } else if (banner.link_type === 'product') {
    router.push(`/product/${banner.link_value}`)
  }
}

// 初始化数据
const initData = async () => {
  try {
    const homeData = await productStore.getHomeData()
    banners.value = homeData.banners || []
    categories.value = homeData.categories || []
    
    // 启动Banner轮播
    if (banners.value.length > 1) {
      setInterval(() => {
        currentBannerIndex.value = (currentBannerIndex.value + 1) % banners.value.length
      }, 5000)
    }
  } catch (error) {
    showError('页面加载失败')
  }
}

// 生命周期
onMounted(() => {
  initData()
})
</script>

<style scoped>
.product-card {
  transition: transform 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
```

## 📱 路由配置

### Vue Router 4配置 (src/router/index.js)
```javascript
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '首页', requiresAuth: false }
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/product/ProductDetailPage.vue'),
    meta: { title: '商品详情', requiresAuth: false }
  },
  {
    path: '/order/confirm',
    name: 'OrderConfirm',
    component: () => import('@/views/order/ConfirmPage.vue'),
    meta: { title: '确认订单', requiresAuth: true }
  },
  {
    path: '/payment',
    name: 'Payment',
    component: () => import('@/views/order/PaymentPage.vue'),
    meta: { title: '支付', requiresAuth: true }
  },
  {
    path: '/group/confirm',
    name: 'GroupConfirm',
    component: () => import('@/views/group/GroupConfirmPage.vue'),
    meta: { title: '拼团详情', requiresAuth: true }
  },
  {
    path: '/user',
    name: 'Profile',
    component: () => import('@/views/user/ProfilePage.vue'),
    meta: { title: '个人中心', requiresAuth: true }
  },
  {
    path: '/user/orders',
    name: 'Orders',
    component: () => import('@/views/user/OrdersPage.vue'),
    meta: { title: '我的订单', requiresAuth: true }
  },
  {
    path: '/user/wallet',
    name: 'Wallet',
    component: () => import('@/views/user/WalletPage.vue'),
    meta: { title: '我的钱包', requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
  } else {
    next()
  }
})

export default router
```

## 🎨 组件库集成

### Vant UI组件使用
```javascript
// main.js
import { createApp } from 'vue'
import Vant from 'vant'
import 'vant/lib/index.css'

const app = createApp(App)
app.use(Vant)
```

### 常用组件示例
```vue
<template>
  <!-- 按钮 -->
  <van-button type="primary" @click="handleClick">确认</van-button>
  
  <!-- 加载状态 -->
  <van-loading size="24px" />
  
  <!-- 弹窗 -->
  <van-dialog v-model:show="showDialog" title="提示" @confirm="handleConfirm">
    确认要删除吗？
  </van-dialog>
  
  <!-- 表单 -->
  <van-form @submit="handleSubmit">
    <van-field
      v-model="form.phone"
      name="phone"
      label="手机号"
      placeholder="请输入手机号"
      :rules="[{ required: true, message: '请输入手机号' }]"
    />
    <van-field
      v-model="form.password"
      type="password"
      name="password"
      label="密码"
      placeholder="请输入密码"
      :rules="[{ required: true, message: '请输入密码' }]"
    />
  </van-form>
  
  <!-- 列表 -->
  <van-list
    v-model:loading="loading"
    :finished="finished"
    finished-text="没有更多了"
    @load="onLoad"
  >
    <van-cell
      v-for="item in list"
      :key="item.id"
      :title="item.title"
      :value="item.value"
    />
  </van-list>
</template>
```

## 📊 接口简介索引目录

### 🔐 认证相关接口

| 接口地址         | 方法 | 功能描述                             | 对应Store方法        |
| ---------------- | ---- | ------------------------------------ | -------------------- |
| `/api/v1/auth` | POST | 用户登录、注册、忘记密码、第三方登录 | `authStore.login()` |

### 🏠 首页相关接口

| 接口地址                  | 方法 | 功能描述                                         | 对应Store方法               |
| ------------------------- | ---- | ------------------------------------------------ | ---------------------------- |
| `/api/v1/data/home`     | GET  | 获取首页全部数据（用户状态、Banner、分类、商品） | `productStore.getHomeData()` |
| `/api/v1/data/banners`  | GET  | 获取轮播图数据                                   | `standardApi.getBanners()`   |
| `/api/v1/search`        | GET  | 商品搜索功能                                     | `productStore.searchProducts()` |
| `/api/v1/data/products` | GET  | 获取商品列表（支持分类筛选、拼团类型筛选）       | `productStore.getProducts()` |

### 🗂️ 分类相关接口

| 接口地址                    | 方法 | 功能描述                               | 对应Store方法                |
| --------------------------- | ---- | -------------------------------------- | ---------------------------- |
| `/api/v1/data/categories` | GET  | 获取所有分类数据（热门分类、全部分类） | `standardApi.getCategories()` |

### 🛍️ 商品相关接口

| 接口地址                         | 方法 | 功能描述                                       | 对应Store方法                    |
| -------------------------------- | ---- | ---------------------------------------------- | -------------------------------- |
| `/api/v1/products/{id}`        | GET  | 获取商品详情信息                               | `standardApi.getProductDetail()` |
| `/api/v1/products/{id}/action` | POST | 商品操作（收藏、取消收藏、获取评价、获取拼团） | `standardApi.productAction()`    |

### 👥 拼团相关接口

| 接口地址                  | 方法 | 功能描述                           | 对应Store方法                |
| ------------------------- | ---- | ---------------------------------- | ---------------------------- |
| `/api/v1/groups/{id}`   | GET  | 获取拼团详情和状态                 | `standardApi.getGroupDetail()` |
| `/api/v1/groups/action` | POST | 拼团操作（创建、参与、分享、取消） | `standardApi.groupAction()`  |

### 📦 订单相关接口

| 接口地址                       | 方法 | 功能描述                                           | 对应Store方法               |
| ------------------------------ | ---- | -------------------------------------------------- | --------------------------- |
| `/api/v1/orders`             | GET  | 获取用户订单列表（支持状态筛选）                   | `standardApi.getOrders()`   |
| `/api/v1/orders/{id}/action` | POST | 订单操作（确认收货、再次购买、查看物流、取消订单） | `standardApi.orderAction()` |

### 💳 支付相关接口

| 接口地址            | 方法 | 功能描述                               | 对应Store方法                  |
| ------------------- | ---- | -------------------------------------- | ------------------------------ |
| `/api/v1/payment` | GET  | 获取支付页面数据（支付方式、订单信息） | `standardApi.getPaymentData()` |
| `/api/v1/payment` | POST | 处理支付请求                           | `standardApi.processPayment()` |

### 👤 用户中心相关接口

| 接口地址                   | 方法    | 功能描述                                     | 对应Store方法                      |
| -------------------------- | ------- | -------------------------------------------- | ---------------------------------- |
| `/api/v1/user/dashboard` | GET     | 获取个人中心数据（用户信息、钱包、订单统计） | `standardApi.getUserDashboard()`   |
| `/api/v1/user/profile`   | PUT     | 更新用户信息                                 | `standardApi.updateUserProfile()`  |
| `/api/v1/user/settings`  | GET/PUT | 获取/更新用户设置                            | `standardApi.getUserSettings()`    |

### 💰 钱包相关接口

| 接口地址                  | 方法 | 功能描述                                 | 对应Store方法               |
| ------------------------- | ---- | ---------------------------------------- | --------------------------- |
| `/api/v1/wallet`        | GET  | 获取钱包信息（余额、交易记录、支付方式） | `standardApi.getWallet()`   |
| `/api/v1/wallet/action` | POST | 钱包操作（充值、提现、转账）             | `standardApi.walletAction()` |

## 🎯 页面与API对接映射

### 1. 登录页面 (src/views/Login.vue)

#### Vue组件实现
```vue
<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
    <div class="bg-white rounded-3xl shadow-2xl w-full max-w-md p-8">
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">欢迎回来</h1>
        <p class="text-gray-600">登录您的账户</p>
      </div>

      <van-form @submit="handleSubmit">
        <van-field
          v-model="form.phone"
          name="phone"
          label="手机号"
          placeholder="请输入手机号"
          :rules="phoneRules"
          left-icon="phone-o"
        />
        <van-field
          v-model="form.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="passwordRules"
          left-icon="lock"
        />
        
        <div class="mt-6">
          <van-button
            type="primary"
            native-type="submit"
            block
            :loading="loading"
            class="h-12 text-lg"
          >
            {{ loading ? '登录中...' : '登录' }}
          </van-button>
        </div>
      </van-form>

      <div class="mt-4 text-center">
        <van-button type="default" plain @click="showRegister = true">
          还没有账户？立即注册
        </van-button>
      </div>

      <div class="mt-6">
        <div class="text-center text-gray-500 text-sm mb-4">或使用以下方式登录</div>
        <div class="flex justify-center space-x-4">
          <van-button 
            icon="wechat" 
            type="success" 
            plain 
            @click="handleOAuthLogin('wechat')"
          >
            微信
          </van-button>
          <van-button 
            icon="alipay" 
            type="primary" 
            plain 
            @click="handleOAuthLogin('alipay')"
          >
            支付宝
          </van-button>
        </div>
      </div>
    </div>

    <!-- 注册弹窗 -->
    <van-dialog
      v-model:show="showRegister"
      title="用户注册"
      :show-cancel-button="false"
      :show-confirm-button="false"
      class="register-dialog"
    >
      <van-form @submit="handleRegister" class="p-4">
        <van-field
          v-model="registerForm.phone"
          name="phone"
          label="手机号"
          placeholder="请输入手机号"
          :rules="phoneRules"
        />
        <van-field
          v-model="registerForm.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="passwordRules"
        />
        <van-field
          v-model="registerForm.confirmPassword"
          type="password"
          name="confirmPassword"
          label="确认密码"
          placeholder="请确认密码"
          :rules="confirmPasswordRules"
        />
        <van-field
          v-model="registerForm.verificationCode"
          name="verificationCode"
          label="验证码"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
        >
          <template #button>
            <van-button 
              size="small" 
              type="primary" 
              @click="sendVerificationCode"
              :disabled="!registerForm.phone || sendCodeLoading"
            >
              {{ sendCodeLoading ? '发送中...' : '发送验证码' }}
            </van-button>
          </template>
        </van-field>
        
        <div class="mt-4">
          <van-button
            type="primary"
            native-type="submit"
            block
            :loading="registerLoading"
          >
            {{ registerLoading ? '注册中...' : '注册' }}
          </van-button>
        </div>
      </van-form>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showError, showSuccess } from '@/utils/message'

// 路由
const router = useRouter()
const route = useRoute()

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const registerLoading = ref(false)
const sendCodeLoading = ref(false)
const showRegister = ref(false)

// 表单数据
const form = reactive({
  phone: '',
  password: ''
})

const registerForm = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  agreeTerms: true
})

// 表单验证规则
const phoneRules = [
  { required: true, message: '请输入手机号' },
  { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
]

const passwordRules = [
  { required: true, message: '请输入密码' },
  { min: 6, message: '密码长度至少6位' },
  { max: 20, message: '密码长度不能超过20位' }
]

const confirmPasswordRules = [
  { required: true, message: '请确认密码' },
  {
    validator: (value) => {
      if (value !== registerForm.password) {
        return '两次输入的密码不一致'
      }
      return true
    }
  }
]

// 方法
const handleSubmit = async () => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    // 格式化手机号（添加越南区号）
    const formattedPhone = form.phone.startsWith('+84') 
      ? form.phone 
      : `+84${form.phone.replace(/^0/, '')}`
    
    const response = await authStore.login({
      phone: formattedPhone,
      password: form.password
    })
    
    if (response.code === 200) {
      showSuccess('登录成功')
      
      // 跳转到目标页面或首页
      const redirect = route.query.redirect || '/home'
      await router.push(redirect)
    }
  } catch (error) {
    showError(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

const handleRegister = async () => {
  if (registerLoading.value) return
  
  try {
    registerLoading.value = true
    
    // 格式化手机号
    const formattedPhone = registerForm.phone.startsWith('+84') 
      ? registerForm.phone 
      : `+84${registerForm.phone.replace(/^0/, '')}`
    
    const response = await authStore.register({
      phone: formattedPhone,
      password: registerForm.password,
      confirm_password: registerForm.confirmPassword,
      verification_code: registerForm.verificationCode,
      agreed_to_terms: registerForm.agreeTerms
    })
    
    if (response.code === 200) {
      showSuccess('注册成功')
      showRegister.value = false
      
      // 显示新用户奖励
      if (response.data.user.welcome_bonus) {
        showSuccess(`恭喜获得新用户奖励 ${formatCurrency(response.data.user.welcome_bonus)}`)
      }
      
      // 跳转到首页
      await router.push('/home')
    }
  } catch (error) {
    showError(error.message || '注册失败')
  } finally {
    registerLoading.value = false
  }
}

const handleOAuthLogin = async (provider) => {
  try {
    // 这里应该调用第三方登录SDK
    // 示例：微信登录
    if (provider === 'wechat') {
      // 调用微信登录SDK
      const wechatResult = await callWechatLogin()
      
      const response = await authStore.oauthLogin(
        'wechat',
        wechatResult.accessToken,
        wechatResult.userInfo
      )
      
      if (response.code === 200) {
        showSuccess('登录成功')
        
        // 检查是否需要绑定手机号
        if (response.data.user.need_bind_phone) {
          await router.push('/bind-phone')
        } else {
          await router.push('/home')
        }
      }
    }
  } catch (error) {
    showError(error.message || '第三方登录失败')
  }
}

const sendVerificationCode = async () => {
  if (!registerForm.phone) {
    showError('请先输入手机号')
    return
  }
  
  try {
    sendCodeLoading.value = true
    
    // 发送验证码API调用
    // await authApi.sendVerificationCode(registerForm.phone)
    
    showSuccess('验证码已发送')
    
    // 倒计时逻辑
    let countdown = 60
    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        sendCodeLoading.value = false
      }
    }, 1000)
    
  } catch (error) {
    showError(error.message || '发送验证码失败')
    sendCodeLoading.value = false
  }
}

// 第三方登录SDK调用示例
const callWechatLogin = async () => {
  // 这里应该是实际的微信登录SDK调用
  return new Promise((resolve) => {
    // 模拟微信登录
    setTimeout(() => {
      resolve({
        accessToken: 'mock_wechat_token',
        userInfo: {
          name: 'WeChat User',
          avatar: 'https://example.com/avatar.jpg'
        }
      })
    }, 1000)
  })
}

// 格式化货币
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}
</script>

<style scoped>
.register-dialog {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
```

#### 对应的Store方法调用

```javascript
// 在 src/store/auth.js 中
export const useAuthStore = defineStore('auth', () => {
  // ... 其他代码

  // 登录方法
  const login = async (loginData) => {
    try {
      const response = await standardApi.login(loginData.phone, loginData.password)
      
      if (response.code === 200) {
        setToken(response.data.token, response.data.refresh_token)
        setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 注册方法
  const register = async (registerData) => {
    try {
      const response = await standardApi.register(registerData)
      
      if (response.code === 200) {
        setToken(response.data.token, response.data.refresh_token)
        setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      throw error
    }
  }

  // 第三方登录方法
  const oauthLogin = async (provider, oauthToken, userInfo = null) => {
    try {
      const response = await standardApi.oauthLogin(provider, oauthToken, userInfo)
      
      if (response.code === 200) {
        setToken(response.data.token, response.data.refresh_token)
        setUser(response.data.user)
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  }

  return {
    // ... 其他返回值
    login,
    register,
    oauthLogin
  }
})
```

### 2. 首页组件 (src/views/home/<USER>

#### 组合式API实现
```javascript
// 在 HomePage.vue 的 <script setup> 中
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '@/store/product'
import { useAuthStore } from '@/store/auth'
import { showError, showSuccess } from '@/utils/message'
import { formatCurrency } from '@/utils/format'

// 路由和状态管理
const router = useRouter()
const productStore = useProductStore()
const authStore = useAuthStore()

// 响应式数据
const searchKeyword = ref('')
const banners = ref([])
const categories = ref([])
const currentBannerIndex = ref(0)

// 计算属性
const products = computed(() => productStore.currentProducts)
const loading = computed(() => productStore.loading)
const hasMore = computed(() => productStore.hasMore)
const currentGroupType = computed(() => productStore.currentGroupType)
const categoryTitle = computed(() => productStore.categoryTitle)
const isLoggedIn = computed(() => authStore.isLoggedIn)
const userInfo = computed(() => authStore.user)

// 方法
const initData = async () => {
  try {
    const homeData = await productStore.getHomeData({
      language: 'zh-CN'
    })
    
    banners.value = homeData.banners || []
    categories.value = homeData.categories || []
    
    // 启动Banner轮播
    if (banners.value.length > 1) {
      setInterval(() => {
        currentBannerIndex.value = (currentBannerIndex.value + 1) % banners.value.length
      }, 5000)
    }
  } catch (error) {
    showError('页面加载失败')
  }
}

const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    showError('请输入搜索关键词')
    return
  }
  
  if (searchKeyword.value.length < 2) {
    showError('搜索关键词至少需要2个字符')
    return
  }
  
  try {
    await productStore.searchProducts(searchKeyword.value, {
      type: 'product',
      page: 1,
      per_page: 20
    })
    showSuccess('搜索完成')
  } catch (error) {
    showError('搜索失败')
  }
}

const filterProducts = async (category) => {
  try {
    await productStore.filterProducts(category)
  } catch (error) {
    showError('筛选失败')
  }
}

const switchGroupType = async (groupType) => {
  try {
    await productStore.switchGroupType(groupType)
  } catch (error) {
    showError('切换失败')
  }
}

const refreshProducts = async () => {
  try {
    await productStore.refreshProducts()
    showSuccess('刷新成功')
  } catch (error) {
    showError('刷新失败')
  }
}

const loadMore = async () => {
  try {
    const hasMoreData = await productStore.loadMore()
    if (!hasMoreData) {
      showSuccess('已加载全部商品')
    }
  } catch (error) {
    showError('加载失败')
  }
}

const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`)
}

const handleBannerClick = (banner) => {
  if (banner.link_type === 'activity') {
    router.push(`/activity/${banner.link_value}`)
  } else if (banner.link_type === 'product') {
    router.push(`/product/${banner.link_value}`)
  } else if (banner.link_type === 'external') {
    window.open(banner.link_value, '_blank')
  }
}

const getBannerBackground = (banner) => {
  if (banner.background_color) {
    return banner.background_color
  }
  // 默认渐变背景
  return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
}

// 生命周期
onMounted(() => {
  initData()
})
```

#### 对应的Store方法
```javascript
// 在 src/store/product.js 中
export const useProductStore = defineStore('product', () => {
  // ... 状态定义

  // 获取首页数据
  const getHomeData = async (params = {}) => {
    try {
      loading.value = true
      const response = await standardApi.getHomeData(params)
      
      if (response.code === 200) {
        products.value = response.data.initial_products?.items || []
        hasMore.value = response.data.initial_products?.pagination?.has_more || false
        return response.data
      } else {
        throw new Error(response.message || '获取首页数据失败')
      }
    } catch (error) {
      console.error('获取首页数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索商品
  const searchProducts = async (query, params = {}) => {
    try {
      loading.value = true
      const response = await standardApi.search({
        q: query,
        type: 'product',
        page: 1,
        per_page: 20,
        ...params
      })
      
      if (response.code === 200) {
        products.value = response.data.results?.products || []
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      } else {
        throw new Error(response.message || '搜索失败')
      }
    } catch (error) {
      console.error('搜索商品失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 筛选商品
  const filterProducts = async (category) => {
    try {
      loading.value = true
      currentCategory.value = category
      currentPage.value = 1
      
      const response = await standardApi.getProducts({
        category: category,
        group_type: currentGroupType.value,
        page: 1,
        per_page: 20
      })
      
      if (response.code === 200) {
        products.value = response.data.items || []
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      } else {
        throw new Error(response.message || '筛选失败')
      }
    } catch (error) {
      console.error('筛选商品失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 切换拼团类型
  const switchGroupType = async (groupType) => {
    try {
      loading.value = true
      currentGroupType.value = groupType
      currentPage.value = 1
      
      const response = await standardApi.getProducts({
        category: currentCategory.value,
        group_type: groupType,
        page: 1,
        per_page: 20
      })
      
      if (response.code === 200) {
        products.value = response.data.items || []
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      } else {
        throw new Error(response.message || '切换失败')
      }
    } catch (error) {
      console.error('切换拼团类型失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 加载更多
  const loadMore = async () => {
    if (!hasMore.value || loading.value) return false
    
    try {
      loading.value = true
      currentPage.value += 1
      
      const response = await standardApi.getProducts({
        category: currentCategory.value,
        group_type: currentGroupType.value,
        page: currentPage.value,
        per_page: 20
      })
      
      if (response.code === 200) {
        products.value.push(...(response.data.items || []))
        hasMore.value = response.data.pagination?.has_more || false
        return hasMore.value
      } else {
        throw new Error(response.message || '加载更多失败')
      }
    } catch (error) {
      console.error('加载更多失败:', error)
      currentPage.value -= 1 // 回滚页码
      throw error
    } finally {
      loading.value = false
    }
  }

  // 刷新商品列表
  const refreshProducts = async () => {
    try {
      loading.value = true
      currentPage.value = 1
      
      const response = await standardApi.getProducts({
        category: currentCategory.value,
        group_type: currentGroupType.value,
        page: 1,
        per_page: 20
      })
      
      if (response.code === 200) {
        products.value = response.data.items || []
        hasMore.value = response.data.pagination?.has_more || false
        return response.data
      } else {
        throw new Error(response.message || '刷新失败')
      }
    } catch (error) {
      console.error('刷新商品列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    // ... 其他返回值
    getHomeData,
    searchProducts,
    filterProducts,
    switchGroupType,
    loadMore,
    refreshProducts
  }
})
```

## 🛠️ 工具函数

### 1. 消息提示工具 (src/utils/message.js)
```javascript
import { showToast, showDialog, showConfirmDialog } from 'vant'

// 成功提示
export const showSuccess = (message, duration = 2000) => {
  showToast({
    message,
    type: 'success',
    duration
  })
}

// 错误提示
export const showError = (message, duration = 3000) => {
  showToast({
    message,
    type: 'fail',
    duration
  })
}

// 警告提示
export const showWarning = (message, duration = 2000) => {
  showToast({
    message,
    type: 'text',
    duration
  })
}

// 加载提示
export const showLoading = (message = '加载中...') => {
  return showToast({
    message,
    type: 'loading',
    duration: 0, // 不自动关闭
    forbidClick: true
  })
}

// 确认对话框
export const showConfirm = (options) => {
  return showConfirmDialog({
    title: '提示',
    message: '确认执行此操作吗？',
    ...options
  })
}

// 信息对话框
export const showInfo = (options) => {
  return showDialog({
    title: '提示',
    message: '操作完成',
    ...options
  })
}
```

### 2. 格式化工具 (src/utils/format.js)
```javascript
// 格式化货币
export const formatCurrency = (amount, currency = 'VND') => {
  if (typeof amount !== 'number') {
    return '0 ₫'
  }
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0
  }).format(amount)
}

// 格式化时间
export const formatDateTime = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 相对时间显示
export const formatRelativeTime = (dateString) => {
  if (!dateString) return ''
  
  const now = new Date()
  const date = new Date(dateString)
  const diffInSeconds = Math.floor((now - date) / 1000)
  
  if (diffInSeconds < 60) return '刚刚'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`
  
  return formatDateTime(dateString)
}

// 格式化手机号
export const formatPhone = (phone) => {
  if (!phone) return ''
  
  // 隐藏中间4位
  if (phone.length >= 8) {
    return phone.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2')
  }
  
  return phone
}

// 格式化数字
export const formatNumber = (num) => {
  if (typeof num !== 'number') return '0'
  
  return new Intl.NumberFormat('vi-VN').format(num)
}

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
```

### 3. 验证工具 (src/utils/validate.js)
```javascript
// 手机号验证（越南）
export const validatePhone = (phone) => {
  const phoneRegex = /^\+84[0-9]{9,10}$/
  return phoneRegex.test(phone)
}

// 密码强度验证
export const validatePassword = (password) => {
  if (!password) {
    return { valid: false, message: '密码不能为空' }
  }
  
  if (password.length < 6 || password.length > 20) {
    return { valid: false, message: '密码长度需要6-20位' }
  }
  
  // 检查是否包含至少一个数字和一个字母
  const hasNumber = /\d/.test(password)
  const hasLetter = /[a-zA-Z]/.test(password)
  
  if (!hasNumber || !hasLetter) {
    return { valid: false, message: '密码需要包含数字和字母' }
  }
  
  return { valid: true }
}

// 邮箱验证
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 金额验证
export const validateAmount = (amount, min = 1000, max = 100000000) => {
  if (isNaN(amount) || amount <= 0) {
    return { valid: false, message: '请输入有效金额' }
  }
  
  if (amount < min) {
    return { valid: false, message: `最小金额为 ${formatCurrency(min)}` }
  }
  
  if (amount > max) {
    return { valid: false, message: `最大金额为 ${formatCurrency(max)}` }
  }
  
  return { valid: true }
}

// 身份证验证
export const validateIdCard = (idCard) => {
  // 越南身份证格式验证
  const idCardRegex = /^[0-9]{9,12}$/
  return idCardRegex.test(idCard)
}
```

## 🔄 API错误处理

### 统一错误处理
```javascript
// 在 src/utils/request.js 中
export const handleApiError = (error) => {
  console.error('API错误:', error)
  
  // 根据错误类型进行处理
  if (error.message.includes('401')) {
    // 未授权，跳转到登录页
    const authStore = useAuthStore()
    authStore.logout()
    router.push('/login')
    return '登录已过期，请重新登录'
  }
  
  if (error.message.includes('403')) {
    return '权限不足，无法访问'
  }
  
  if (error.message.includes('404')) {
    return '请求的资源不存在'
  }
  
  if (error.message.includes('500')) {
    return '服务器内部错误，请稍后重试'
  }
  
  if (error.message.includes('网络')) {
    return '网络连接失败，请检查网络设置'
  }
  
  return error.message || '操作失败，请重试'
}

// 在组件中使用
const handleError = (error) => {
  const errorMessage = handleApiError(error)
  showError(errorMessage)
}
```

### 网络状态检测
```javascript
// 在 src/utils/network.js 中
import { ref } from 'vue'

export const networkStatus = ref(navigator.onLine)

// 监听网络状态变化
window.addEventListener('online', () => {
  networkStatus.value = true
})

window.addEventListener('offline', () => {
  networkStatus.value = false
})

// 检查网络状态
export const checkNetwork = () => {
  if (!networkStatus.value) {
    showError('网络连接不可用，请检查网络设置')
    return false
  }
  return true
}
```

## 📊 性能优化

### 1. 懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/product/:id',
    component: () => import('@/views/product/ProductDetailPage.vue')
  }
]

// 图片懒加载
<van-image
  :src="product.image"
  lazy-load
  :show-loading="false"
  :show-error="false"
/>
```

### 2. 缓存策略
```javascript
// 在 src/utils/cache.js 中
export class CacheManager {
  constructor() {
    this.cache = new Map()
    this.ttl = new Map()
  }
  
  set(key, value, ttl = 5 * 60 * 1000) {
    this.cache.set(key, value)
    this.ttl.set(key, Date.now() + ttl)
  }
  
  get(key) {
    const expiry = this.ttl.get(key)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key)
      this.ttl.delete(key)
      return null
    }
    return this.cache.get(key)
  }
  
  has(key) {
    return this.get(key) !== null
  }
  
  clear() {
    this.cache.clear()
    this.ttl.clear()
  }
}

export const apiCache = new CacheManager()
```

### 3. 防抖和节流
```javascript
// 在 src/utils/debounce.js 中
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export const throttle = (func, limit) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 在组件中使用
const debouncedSearch = debounce(handleSearch, 300)

### 3. 商品详情页面 (src/views/product/ProductDetailPage.vue)

#### Vue组件实现
```vue
<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 商品图片轮播 -->
    <div class="bg-white">
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="(image, index) in product.images" :key="index">
          <img :src="image" class="w-full h-80 object-cover" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 商品基本信息 -->
    <div class="bg-white mt-2 p-4">
      <div class="flex items-start justify-between mb-2">
        <h1 class="text-lg font-bold text-gray-800 flex-1 mr-4">{{ product.title }}</h1>
        <van-icon 
          :name="product.is_favorited ? 'heart' : 'heart-o'" 
          :color="product.is_favorited ? '#ff4757' : '#c7c7cc'"
          size="24"
          @click="toggleFavorite"
        />
      </div>
      
      <p class="text-gray-600 text-sm mb-3">{{ product.subtitle }}</p>
      
      <!-- 价格信息 -->
      <div class="mb-4">
        <div class="flex items-baseline mb-2">
          <span class="text-2xl font-bold text-red-500 mr-2">
            {{ formatCurrency(selectedGroupPrice) }}
          </span>
          <span class="text-gray-400 line-through text-sm">
            {{ formatCurrency(product.original_price) }}
          </span>
          <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded ml-2">
            省{{ formatCurrency(product.original_price - selectedGroupPrice) }}
          </span>
        </div>
        
        <!-- 拼团价格选择 -->
        <div class="flex space-x-2">
          <div 
            v-for="(rule, type) in product.group_buy_rules" 
            :key="type"
            :class="[
              'flex-1 border rounded-lg p-3 cursor-pointer transition-all',
              selectedGroupType === type ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            ]"
            @click="selectGroupType(type)"
          >
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800">
                {{ rule.min_participants }}人团
              </div>
              <div class="text-lg font-bold text-red-500">
                {{ formatCurrency(rule.price) }}
              </div>
              <div class="text-xs text-gray-500">
                成功率{{ rule.success_rate }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 商品标签 -->
      <div class="flex flex-wrap gap-2 mb-4">
        <span 
          v-for="tag in product.tags" 
          :key="tag"
          class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded"
        >
          {{ tag }}
        </span>
      </div>
      
      <!-- 销量和评价 -->
      <div class="flex items-center text-sm text-gray-600 space-x-4">
        <span>已售{{ product.sales }}件</span>
        <span class="flex items-center">
          <van-rate :value="product.rating" size="12" readonly />
          <span class="ml-1">{{ product.rating }}分</span>
        </span>
        <span>{{ product.review_count }}条评价</span>
      </div>
    </div>

    <!-- 规格选择 -->
    <div class="bg-white mt-2 p-4" v-if="product.specifications?.length > 0">
      <h3 class="font-medium text-gray-800 mb-3">选择规格</h3>
      <div v-for="spec in product.specifications" :key="spec.name" class="mb-4">
        <div class="text-sm text-gray-600 mb-2">{{ spec.name }}</div>
        <div class="flex flex-wrap gap-2">
          <div
            v-for="option in spec.options"
            :key="option.value"
            :class="[
              'px-3 py-2 border rounded-lg text-sm cursor-pointer transition-all',
              selectedSpecs[spec.name] === option.value 
                ? 'border-blue-500 bg-blue-50 text-blue-600' 
                : 'border-gray-200 text-gray-700',
              option.stock <= 0 ? 'opacity-50 cursor-not-allowed' : ''
            ]"
            @click="selectSpec(spec.name, option)"
          >
            {{ option.value }}
            <span v-if="option.price_diff > 0" class="text-xs text-red-500">
              +{{ formatCurrency(option.price_diff) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 正在拼团 -->
    <div class="bg-white mt-2 p-4" v-if="activeGroups.length > 0">
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-medium text-gray-800">正在拼团</h3>
        <van-button size="mini" plain @click="loadActiveGroups">刷新</van-button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="group in activeGroups" 
          :key="group.id"
          class="border border-gray-200 rounded-lg p-3"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <img :src="group.creator.avatar" class="w-8 h-8 rounded-full mr-2" />
              <div>
                <div class="text-sm font-medium">{{ group.creator.nickname }}</div>
                <div class="text-xs text-gray-500">团长</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-600">
                {{ group.participants }}/{{ group.max_participants }}人
              </div>
              <div class="text-xs text-red-500">
                剩余{{ formatTime(group.remaining_time) }}
              </div>
            </div>
          </div>
          
          <van-button 
            type="primary" 
            size="small" 
            block 
            @click="joinGroup(group.id)"
          >
            立即参团
          </van-button>
        </div>
      </div>
    </div>

    <!-- 商品详情 -->
    <div class="bg-white mt-2 p-4">
      <h3 class="font-medium text-gray-800 mb-3">商品详情</h3>
      <div class="text-sm text-gray-600 leading-relaxed" v-html="product.description"></div>
    </div>

    <!-- 商品评价 -->
    <div class="bg-white mt-2 p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-medium text-gray-800">商品评价</h3>
        <van-button size="mini" plain @click="loadReviews">查看全部</van-button>
      </div>
      
      <div v-if="reviews.length > 0" class="space-y-4">
        <div v-for="review in reviews.slice(0, 3)" :key="review.id" class="border-b border-gray-100 pb-4 last:border-b-0">
          <div class="flex items-start mb-2">
            <img :src="review.user.avatar" class="w-8 h-8 rounded-full mr-3" />
            <div class="flex-1">
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium">{{ review.user.nickname }}</span>
                <span class="text-xs text-gray-500">{{ formatRelativeTime(review.created_at) }}</span>
              </div>
              <van-rate :value="review.rating" size="12" readonly />
            </div>
          </div>
          <p class="text-sm text-gray-700 mb-2">{{ review.content }}</p>
          <div v-if="review.images?.length > 0" class="flex space-x-2">
            <img 
              v-for="img in review.images" 
              :key="img"
              :src="img" 
              class="w-16 h-16 object-cover rounded"
            />
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8 text-gray-500">
        暂无评价
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom">
      <div class="flex space-x-3">
        <van-button 
          icon="chat-o" 
          plain 
          @click="contactService"
          class="flex-shrink-0"
        >
          客服
        </van-button>
        <van-button 
          type="warning" 
          @click="createGroup"
          :disabled="!canCreateGroup"
          class="flex-1"
        >
          发起拼团 {{ formatCurrency(selectedGroupPrice) }}
        </van-button>
        <van-button 
          type="primary" 
          @click="buyNow"
          :disabled="!canBuyNow"
          class="flex-1"
        >
          立即购买 {{ formatCurrency(product.price) }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { standardApi } from '@/api/standardAdapter'
import { showError, showSuccess, showConfirm } from '@/utils/message'
import { formatCurrency, formatRelativeTime } from '@/utils/format'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const product = ref({})
const activeGroups = ref([])
const reviews = ref([])
const loading = ref(false)
const selectedGroupType = ref('group_type_3')
const selectedSpecs = reactive({})
const quantity = ref(1)

// 计算属性
const selectedGroupPrice = computed(() => {
  const rule = product.value.group_buy_rules?.[selectedGroupType.value]
  return rule?.price || product.value.price || 0
})

const canCreateGroup = computed(() => {
  return authStore.isLoggedIn && 
         Object.keys(selectedSpecs).length === (product.value.specifications?.length || 0)
})

const canBuyNow = computed(() => {
  return Object.keys(selectedSpecs).length === (product.value.specifications?.length || 0)
})

// 方法
const loadProductDetail = async () => {
  try {
    loading.value = true
    const productId = route.params.id
    
    const response = await standardApi.getProductDetail(productId)
    
    if (response.code === 200) {
      product.value = response.data
      
      // 初始化默认规格选择
      if (product.value.specifications) {
        product.value.specifications.forEach(spec => {
          const firstAvailableOption = spec.options.find(opt => opt.stock > 0)
          if (firstAvailableOption) {
            selectedSpecs[spec.name] = firstAvailableOption.value
          }
        })
      }
      
      // 加载相关数据
      await Promise.all([
        loadActiveGroups(),
        loadReviews()
      ])
    }
  } catch (error) {
    showError('商品加载失败')
  } finally {
    loading.value = false
  }
}

const loadActiveGroups = async () => {
  try {
    const response = await standardApi.productAction(
      route.params.id, 
      'get_groups', 
      { status: 'active', group_type: parseInt(selectedGroupType.value.split('_')[2]) }
    )
    
    if (response.code === 200) {
      activeGroups.value = response.data.groups || []
    }
  } catch (error) {
    console.error('加载拼团失败:', error)
  }
}

const loadReviews = async () => {
  try {
    const response = await standardApi.productAction(
      route.params.id, 
      'get_reviews', 
      { page: 1, per_page: 3 }
    )
    
    if (response.code === 200) {
      reviews.value = response.data.reviews || []
    }
  } catch (error) {
    console.error('加载评价失败:', error)
  }
}

const toggleFavorite = async () => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  try {
    const action = product.value.is_favorited ? 'unfavorite' : 'favorite'
    const response = await standardApi.productAction(route.params.id, action)
    
    if (response.code === 200) {
      product.value.is_favorited = !product.value.is_favorited
      showSuccess(product.value.is_favorited ? '收藏成功' : '取消收藏')
    }
  } catch (error) {
    showError('操作失败')
  }
}

const selectGroupType = (type) => {
  selectedGroupType.value = type
  loadActiveGroups()
}

const selectSpec = (specName, option) => {
  if (option.stock <= 0) {
    showError('该规格暂无库存')
    return
  }
  
  selectedSpecs[specName] = option.value
}

const createGroup = async () => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  if (!canCreateGroup.value) {
    showError('请选择完整的商品规格')
    return
  }
  
  try {
    const groupType = parseInt(selectedGroupType.value.split('_')[2])
    const response = await standardApi.groupAction('create', {
      product_id: route.params.id,
      params: {
        group_type: groupType,
        specs: selectedSpecs,
        quantity: quantity.value
      }
    })
    
    if (response.code === 200) {
      showSuccess('拼团创建成功')
      router.push(`/group/confirm?group_id=${response.data.group_id}`)
    }
  } catch (error) {
    showError('创建拼团失败')
  }
}

const joinGroup = async (groupId) => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  try {
    const response = await standardApi.groupAction('join', {
      group_id: groupId,
      params: {
        quantity: quantity.value
      }
    })
    
    if (response.code === 200) {
      showSuccess('参团成功')
      router.push(`/group/confirm?group_id=${groupId}`)
    }
  } catch (error) {
    showError('参团失败')
  }
}

const buyNow = async () => {
  if (!canBuyNow.value) {
    showError('请选择完整的商品规格')
    return
  }
  
  // 跳转到订单确认页面
  router.push({
    path: '/order/confirm',
    query: {
      product_id: route.params.id,
      specs: JSON.stringify(selectedSpecs),
      quantity: quantity.value,
      type: 'buy_now'
    }
  })
}

const contactService = () => {
  router.push('/customer-service')
}

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 生命周期
onMounted(() => {
  loadProductDetail()
})
</script>

<style scoped>
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
```

### 4. 订单确认页面 (src/views/order/ConfirmPage.vue)

#### Vue组件实现
```vue
<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 收货地址 -->
    <div class="bg-white p-4 mb-2">
      <div class="flex items-center justify-between" @click="selectAddress">
        <div v-if="selectedAddress" class="flex-1">
          <div class="flex items-center mb-2">
            <span class="font-medium text-gray-800 mr-2">{{ selectedAddress.name }}</span>
            <span class="text-gray-600">{{ selectedAddress.phone }}</span>
          </div>
          <p class="text-sm text-gray-600">{{ selectedAddress.full_address }}</p>
        </div>
        <div v-else class="flex-1">
          <p class="text-gray-500">请选择收货地址</p>
        </div>
        <van-icon name="arrow" class="text-gray-400" />
      </div>
    </div>

    <!-- 商品信息 -->
    <div class="bg-white p-4 mb-2">
      <h3 class="font-medium text-gray-800 mb-3">商品信息</h3>
      <div class="flex">
        <img :src="orderProduct.image" class="w-20 h-20 object-cover rounded mr-3" />
        <div class="flex-1">
          <h4 class="text-sm font-medium text-gray-800 mb-1">{{ orderProduct.title }}</h4>
          <p class="text-xs text-gray-500 mb-2">{{ formatSpecs(orderProduct.specs) }}</p>
          <div class="flex items-center justify-between">
            <span class="text-red-500 font-bold">{{ formatCurrency(orderProduct.price) }}</span>
            <span class="text-gray-600">x{{ orderProduct.quantity }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 配送方式 -->
    <div class="bg-white p-4 mb-2">
      <h3 class="font-medium text-gray-800 mb-3">配送方式</h3>
      <div class="space-y-3">
        <div 
          v-for="method in deliveryMethods" 
          :key="method.id"
          :class="[
            'flex items-center justify-between p-3 border rounded-lg cursor-pointer',
            selectedDeliveryMethod === method.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
          ]"
          @click="selectedDeliveryMethod = method.id"
        >
          <div>
            <div class="font-medium text-gray-800">{{ method.name }}</div>
            <div class="text-sm text-gray-500">{{ method.description }}</div>
          </div>
          <div class="text-right">
            <div class="font-medium text-gray-800">{{ formatCurrency(method.fee) }}</div>
            <div class="text-xs text-gray-500">{{ method.time }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优惠券 -->
    <div class="bg-white p-4 mb-2" @click="selectCoupon">
      <div class="flex items-center justify-between">
        <span class="font-medium text-gray-800">优惠券</span>
        <div class="flex items-center">
          <span v-if="selectedCoupon" class="text-red-500 mr-2">
            -{{ formatCurrency(selectedCoupon.discount) }}
          </span>
          <span class="text-gray-500 mr-2">
            {{ selectedCoupon ? selectedCoupon.title : '选择优惠券' }}
          </span>
          <van-icon name="arrow" class="text-gray-400" />
        </div>
      </div>
    </div>

    <!-- 备注 -->
    <div class="bg-white p-4 mb-2">
      <van-field
        v-model="orderNote"
        label="备注"
        placeholder="选填，请输入备注信息"
        maxlength="100"
        show-word-limit
      />
    </div>

    <!-- 费用明细 -->
    <div class="bg-white p-4 mb-20">
      <h3 class="font-medium text-gray-800 mb-3">费用明细</h3>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-gray-600">商品总价</span>
          <span>{{ formatCurrency(orderSummary.subtotal) }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600">配送费</span>
          <span>{{ formatCurrency(orderSummary.delivery_fee) }}</span>
        </div>
        <div v-if="orderSummary.coupon_discount > 0" class="flex justify-between text-red-500">
          <span>优惠券</span>
          <span>-{{ formatCurrency(orderSummary.coupon_discount) }}</span>
        </div>
        <div class="border-t pt-2 flex justify-between font-bold text-lg">
          <span>实付金额</span>
          <span class="text-red-500">{{ formatCurrency(orderSummary.total) }}</span>
        </div>
      </div>
    </div>

    <!-- 底部提交栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t p-4 safe-area-bottom">
      <div class="flex items-center justify-between mb-3">
        <span class="text-gray-600">实付金额</span>
        <span class="text-xl font-bold text-red-500">{{ formatCurrency(orderSummary.total) }}</span>
      </div>
      <van-button 
        type="primary" 
        block 
        :loading="submitting"
        @click="submitOrder"
        :disabled="!canSubmit"
      >
        {{ submitting ? '提交中...' : '提交订单' }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { standardApi } from '@/api/standardAdapter'
import { showError, showSuccess } from '@/utils/message'
import { formatCurrency } from '@/utils/format'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const selectedAddress = ref(null)
const selectedDeliveryMethod = ref('standard')
const selectedCoupon = ref(null)
const orderNote = ref('')
const submitting = ref(false)
const orderProduct = ref({})
const deliveryMethods = ref([])
const availableCoupons = ref([])

const orderSummary = reactive({
  subtotal: 0,
  delivery_fee: 0,
  coupon_discount: 0,
  total: 0
})

// 计算属性
const canSubmit = computed(() => {
  return selectedAddress.value && !submitting.value
})

// 方法
const loadOrderData = async () => {
  try {
    // 根据路由参数加载订单数据
    const productId = route.query.product_id
    const specs = JSON.parse(route.query.specs || '{}')
    const quantity = parseInt(route.query.quantity || 1)
    
    // 模拟加载订单数据
    orderProduct.value = {
      id: productId,
      title: '智能电子产品',
      image: 'https://example.com/product.jpg',
      price: 299000,
      quantity: quantity,
      specs: specs
    }
    
    deliveryMethods.value = [
      {
        id: 'standard',
        name: '标准配送',
        description: '预计3-5天送达',
        fee: 25000,
        time: '3-5天'
      },
      {
        id: 'express',
        name: '极速配送',
        description: '预计1-2天送达',
        fee: 50000,
        time: '1-2天'
      }
    ]
    
    // 计算费用
    calculateOrderSummary()
    
    // 加载用户地址
    await loadUserAddresses()
    
    // 加载可用优惠券
    await loadAvailableCoupons()
    
  } catch (error) {
    showError('订单数据加载失败')
  }
}

const loadUserAddresses = async () => {
  try {
    const response = await standardApi.getAddresses()
    if (response.code === 200) {
      const addresses = response.data.addresses || []
      selectedAddress.value = addresses.find(addr => addr.is_default) || addresses[0]
    }
  } catch (error) {
    console.error('加载地址失败:', error)
  }
}

const loadAvailableCoupons = async () => {
  try {
    const response = await standardApi.getCoupons({ status: 'available' })
    if (response.code === 200) {
      availableCoupons.value = response.data.coupons || []
    }
  } catch (error) {
    console.error('加载优惠券失败:', error)
  }
}

const calculateOrderSummary = () => {
  const selectedDelivery = deliveryMethods.value.find(m => m.id === selectedDeliveryMethod.value)
  
  orderSummary.subtotal = orderProduct.value.price * orderProduct.value.quantity
  orderSummary.delivery_fee = selectedDelivery?.fee || 0
  orderSummary.coupon_discount = selectedCoupon.value?.discount || 0
  orderSummary.total = orderSummary.subtotal + orderSummary.delivery_fee - orderSummary.coupon_discount
}

const selectAddress = () => {
  router.push('/user/address?from=order')
}

const selectCoupon = () => {
  // 显示优惠券选择弹窗或跳转到优惠券页面
  router.push('/user/coupons?from=order')
}

const formatSpecs = (specs) => {
  if (!specs || Object.keys(specs).length === 0) return ''
  return Object.entries(specs).map(([key, value]) => `${key}: ${value}`).join(', ')
}

const submitOrder = async () => {
  if (!canSubmit.value) return
  
  try {
    submitting.value = true
    
    const orderData = {
      product_id: orderProduct.value.id,
      quantity: orderProduct.value.quantity,
      specs: orderProduct.value.specs,
      address_id: selectedAddress.value.id,
      delivery_method: selectedDeliveryMethod.value,
      coupon_id: selectedCoupon.value?.id,
      note: orderNote.value,
      total_amount: orderSummary.total
    }
    
    // 提交订单
    const response = await standardApi.createOrder(orderData)
    
    if (response.code === 200) {
      showSuccess('订单提交成功')
      
      // 跳转到支付页面
      router.replace({
        path: '/payment',
        query: { order_id: response.data.order_id }
      })
    }
  } catch (error) {
    showError('订单提交失败')
  } finally {
    submitting.value = false
  }
}

// 监听配送方式变化
watch(() => selectedDeliveryMethod.value, calculateOrderSummary)
watch(() => selectedCoupon.value, calculateOrderSummary)

// 生命周期
onMounted(() => {
  loadOrderData()
})
</script>

<style scoped>
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
```

## 📚 接口详细说明

### 1. 认证接口详细说明

#### 1.1 用户登录接口

**接口地址**: `POST /api/v1/auth`

**Vue组件调用示例**:
```javascript
// 在登录组件中
const handleLogin = async () => {
  try {
    const response = await authStore.login({
      phone: form.phone,
      password: form.password
    })
    
    if (response.code === 200) {
      showSuccess('登录成功')
      await router.push('/home')
    }
  } catch (error) {
    showError(error.message)
  }
}
```

**请求参数**:
```javascript
{
  type: "login",           // 必需，操作类型
  phone: "+84123456789",   // 必需，手机号（包含国际区号）
  password: "user_password" // 必需，用户密码
}
```

**返回数据**:
```javascript
{
  code: 200,
  message: "登录成功",
  data: {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    expires_in: 3600,
    user: {
      id: "user_123",
      phone: "+84123456789",
      nickname: "用户昵称",
      avatar: "https://example.com/avatar.jpg",
      is_new_user: false
    }
  },
  timestamp: "2024-12-15T10:00:00Z"
}
```

#### 1.2 用户注册接口

**Vue组件调用示例**:
```javascript
const handleRegister = async () => {
  try {
    const response = await authStore.register({
      phone: registerForm.phone,
      password: registerForm.password,
      confirm_password: registerForm.confirmPassword,
      verification_code: registerForm.verificationCode,
      agreed_to_terms: true
    })
    
    if (response.code === 200) {
      showSuccess('注册成功')
      if (response.data.user.welcome_bonus) {
        showSuccess(`获得新用户奖励 ${formatCurrency(response.data.user.welcome_bonus)}`)
      }
      await router.push('/home')
    }
  } catch (error) {
    showError(error.message)
  }
}
```

### 2. 商品接口详细说明

#### 2.1 获取商品详情接口

**接口地址**: `GET /api/v1/products/{id}`

**Vue组件调用示例**:
```javascript
const loadProductDetail = async () => {
  try {
    const response = await standardApi.getProductDetail(route.params.id)
    
    if (response.code === 200) {
      product.value = response.data
      
      // 处理商品数据
      if (product.value.specifications) {
        initDefaultSpecs()
      }
    }
  } catch (error) {
    showError('商品加载失败')
  }
}
```

**返回数据结构**:
```javascript
{
  code: 200,
  message: "success",
  data: {
    id: "prod_123",
    title: "智能电子产品",
    subtitle: "最新款智能设备，功能强大",
    description: "详细的商品描述内容...",
    price: 299000,
    original_price: 399000,
    discount: 25,
    images: [
      "https://example.com/product1.jpg",
      "https://example.com/product2.jpg"
    ],
    group_buy_rules: {
      group_type_3: {
        min_participants: 3,
        max_participants: 1000,
        price: 249000,
        save_amount: 50000,
        success_rate: 98.5
      },
      group_type_10: {
        min_participants: 10,
        max_participants: 1000,
        price: 199000,
        save_amount: 100000,
        success_rate: 95.2
      }
    },
    specifications: [
      {
        name: "颜色",
        options: [
          { value: "红色", price_diff: 0, stock: 50 },
          { value: "蓝色", price_diff: 0, stock: 30 }
        ]
      }
    ],
    rating: 4.8,
    review_count: 1256,
    sales: 3456,
    is_favorited: false
  }
}
```

#### 2.2 商品操作接口

**接口地址**: `POST /api/v1/products/{id}/action`

**收藏商品示例**:
```javascript
const toggleFavorite = async () => {
  try {
    const action = product.value.is_favorited ? 'unfavorite' : 'favorite'
    const response = await standardApi.productAction(route.params.id, action)
    
    if (response.code === 200) {
      product.value.is_favorited = !product.value.is_favorited
      showSuccess(product.value.is_favorited ? '收藏成功' : '取消收藏')
    }
  } catch (error) {
    showError('操作失败')
  }
}
```

**获取商品评价示例**:
```javascript
const loadReviews = async () => {
  try {
    const response = await standardApi.productAction(
      route.params.id, 
      'get_reviews', 
      { 
        page: 1, 
        per_page: 10,
        rating_filter: 'all',
        sort: 'newest'
      }
    )
    
    if (response.code === 200) {
      reviews.value = response.data.reviews || []
    }
  } catch (error) {
    console.error('加载评价失败:', error)
  }
}
```

### 3. 拼团接口详细说明

#### 3.1 创建拼团接口

**接口地址**: `POST /api/v1/groups/action`

**Vue组件调用示例**:
```javascript
const createGroup = async () => {
  try {
    const response = await standardApi.groupAction('create', {
      product_id: route.params.id,
      params: {
        group_type: 3,
        specs: selectedSpecs,
        quantity: quantity.value
      }
    })
    
    if (response.code === 200) {
      showSuccess('拼团创建成功')
      router.push(`/group/confirm?group_id=${response.data.group_id}`)
    }
  } catch (error) {
    showError('创建拼团失败')
  }
}
```

#### 3.2 参与拼团接口

**Vue组件调用示例**:
```javascript
const joinGroup = async (groupId) => {
  try {
    const response = await standardApi.groupAction('join', {
      group_id: groupId,
      params: {
        quantity: quantity.value
      }
    })
    
    if (response.code === 200) {
      showSuccess('参团成功')
      router.push(`/group/confirm?group_id=${groupId}`)
    }
  } catch (error) {
    showError('参团失败')
  }
}
```

## 🎨 样式和主题

### 1. TailwindCSS配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
        }
      }
    },
  },
  plugins: [],
}
```

### 2. 全局样式
```scss
// src/assets/styles/global.scss
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// 自定义样式
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// Vant组件样式覆盖
.van-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}
```

## 📱 移动端适配

### 1. 视口配置
```html
<!-- index.html -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

### 2. 安全区域适配
```css
/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
```

### 3. 响应式设计
```vue
<template>
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    <!-- 商品卡片 -->
  </div>
</template>
```

## 🧪 测试策略

### 1. 单元测试
```javascript
// tests/unit/store/auth.spec.js
import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/store/auth'

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should login successfully', async () => {
    const authStore = useAuthStore()
    
    // Mock API response
    const mockResponse = {
      code: 200,
      data: {
        token: 'mock_token',
        user: { id: '123', nickname: 'Test User' }
      }
    }
    
    // Test login
    const result = await authStore.login({
      phone: '+84123456789',
      password: 'test123'
    })
    
    expect(authStore.isLoggedIn).toBe(true)
    expect(authStore.user.nickname).toBe('Test User')
  })

  it('should handle login failure', async () => {
    const authStore = useAuthStore()
    
    try {
      await authStore.login({
        phone: '+84123456789',
        password: 'wrong_password'
      })
    } catch (error) {
      expect(error.message).toContain('登录失败')
    }
  })
})
```

### 2. 组件测试
```javascript
// tests/unit/components/ProductCard.spec.js
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import ProductCard from '@/components/ProductCard.vue'

describe('ProductCard', () => {
  const mockProduct = {
    id: '123',
    title: '测试商品',
    price: 100000,
    original_price: 150000,
    image: 'https://example.com/image.jpg',
    group_type: 3
  }

  it('renders product information correctly', () => {
    const wrapper = mount(ProductCard, {
      props: { product: mockProduct }
    })

    expect(wrapper.text()).toContain('测试商品')
    expect(wrapper.text()).toContain('100,000 ₫')
    expect(wrapper.text()).toContain('3人团')
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(ProductCard, {
      props: { product: mockProduct }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 3. API集成测试
```javascript
// tests/integration/api.spec.js
import { describe, it, expect } from 'vitest'
import { standardApi } from '@/api/standardAdapter'

describe('API Integration', () => {
  it('should get home data successfully', async () => {
    const response = await standardApi.getHomeData()
    
    expect(response.code).toBe(200)
    expect(response.data).toHaveProperty('banners')
    expect(response.data).toHaveProperty('categories')
    expect(response.data).toHaveProperty('initial_products')
  })

  it('should handle API errors gracefully', async () => {
    try {
      await standardApi.getProductDetail('invalid_id')
    } catch (error) {
      expect(error.message).toBeDefined()
    }
  })
})
```

### 4. E2E测试
```javascript
// tests/e2e/user-flow.spec.js
import { test, expect } from '@playwright/test'

test('用户登录和购买流程', async ({ page }) => {
  // 访问首页
  await page.goto('/')
  
  // 点击登录
  await page.click('[data-testid="login-button"]')
  
  // 填写登录信息
  await page.fill('[data-testid="phone-input"]', '123456789')
  await page.fill('[data-testid="password-input"]', 'test123')
  await page.click('[data-testid="submit-button"]')
  
  // 验证登录成功
  await expect(page.locator('[data-testid="user-info"]')).toBeVisible()
  
  // 选择商品
  await page.click('[data-testid="product-card"]:first-child')
  
  // 发起拼团
  await page.click('[data-testid="create-group-button"]')
  
  // 验证跳转到拼团确认页
  await expect(page).toHaveURL(/\/group\/confirm/)
})
```

## 🚀 部署配置

### 1. 生产环境构建
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          vant: ['vant']
        }
      }
    }
  },
  server: {
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL || 'http://localhost:3000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  }
})
```

### 2. Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/ {
        proxy_pass http://backend-server/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Vue Router支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 4. CI/CD配置
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test
    
    - name: Build for production
      run: npm run build
      env:
        VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/app
          git pull origin main
          npm install
          npm run build
          sudo systemctl reload nginx
```

## 📊 性能监控

### 1. 性能指标监控
```javascript
// src/utils/performance.js
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
  }

  // 页面加载时间监控
  measurePageLoad(pageName) {
    const loadTime = performance.now()
    this.reportMetric('page_load', {
      page: pageName,
      load_time: loadTime,
      timestamp: new Date().toISOString()
    })
  }

  // API响应时间监控
  measureApiCall(apiName, startTime, endTime, success = true) {
    const duration = endTime - startTime
    this.reportMetric('api_call', {
      api: apiName,
      duration,
      success,
      timestamp: new Date().toISOString()
    })
  }

  // 用户行为监控
  trackUserAction(action, details = {}) {
    this.reportMetric('user_action', {
      action,
      details,
      timestamp: new Date().toISOString()
    })
  }

  // 错误监控
  trackError(error, context = {}) {
    this.reportMetric('error', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    })
  }

  // 上报指标
  reportMetric(type, data) {
    // 发送到监控服务
    if (navigator.sendBeacon) {
      navigator.sendBeacon('/api/v1/metrics', JSON.stringify({
        type,
        data
      }))
    }
  }
}

export const performanceMonitor = new PerformanceMonitor()
```

### 2. Vue应用监控集成
```javascript
// src/main.js
import { performanceMonitor } from '@/utils/performance'

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info)
  performanceMonitor.trackError(err, { info, component: vm?.$options.name })
}

// 路由性能监控
router.beforeEach((to, from, next) => {
  performance.mark('route-start')
  next()
})

router.afterEach((to, from) => {
  performance.mark('route-end')
  performance.measure('route-change', 'route-start', 'route-end')
  
  const measure = performance.getEntriesByName('route-change')[0]
  performanceMonitor.measurePageLoad(to.name, measure.duration)
})
```

## 📋 开发检查清单

### API对接完成度检查

- [x] **认证模块**
  - [x] 用户登录 (login)
  - [x] 用户注册 (register)
  - [x] 忘记密码 (forgot_password)
  - [x] 第三方登录 (oauth)
  - [x] Token刷新机制

- [x] **首页模块**
  - [x] 首页数据加载 (getHomeData)
  - [x] Banner轮播 (getBanners)
  - [x] 商品搜索 (search)
  - [x] 商品列表 (getProducts)
  - [x] 分类筛选 (getCategories)

- [x] **商品模块**
  - [x] 商品详情 (getProductDetail)
  - [x] 商品收藏 (productAction)
  - [x] 商品评价 (productAction)
  - [x] 正在拼团 (productAction)

- [x] **拼团模块**
  - [x] 创建拼团 (groupAction)
  - [x] 参与拼团 (groupAction)
  - [x] 拼团详情 (getGroupDetail)
  - [x] 拼团分享 (groupAction)

- [x] **订单模块**
  - [x] 订单确认 (createOrder)
  - [x] 订单列表 (getOrders)
  - [x] 订单操作 (orderAction)

- [x] **支付模块**
  - [x] 支付数据 (getPaymentData)
  - [x] 支付处理 (processPayment)

- [x] **用户模块**
  - [x] 个人中心 (getUserDashboard)
  - [x] 用户信息 (updateUserProfile)
  - [x] 用户设置 (getUserSettings)

- [x] **钱包模块**
  - [x] 钱包信息 (getWallet)
  - [x] 钱包操作 (walletAction)

### Vue组件完成度检查

- [x] **页面组件**
  - [x] 登录页面 (Login.vue)
  - [x] 首页 (HomePage.vue)
  - [x] 商品详情 (ProductDetailPage.vue)
  - [x] 订单确认 (ConfirmPage.vue)
  - [x] 支付页面 (PaymentPage.vue)
  - [x] 个人中心 (ProfilePage.vue)
  - [x] 钱包页面 (WalletPage.vue)

- [x] **状态管理**
  - [x] 认证状态 (auth.js)
  - [x] 商品状态 (product.js)
  - [x] 用户状态 (user.js)
  - [x] 购物车状态 (cart.js)

- [x] **工具函数**
  - [x] 请求工具 (request.js)
  - [x] 消息提示 (message.js)
  - [x] 格式化工具 (format.js)
  - [x] 验证工具 (validate.js)

### 技术栈集成检查

- [x] **Vue 3**
  - [x] 组合式API (Composition API)
  - [x] 响应式系统 (ref, reactive, computed)
  - [x] 生命周期钩子 (onMounted, onUnmounted)

- [x] **Pinia状态管理**
  - [x] Store定义和使用
  - [x] 状态持久化
  - [x] 跨组件状态共享

- [x] **Vue Router 4**
  - [x] 路由配置
  - [x] 路由守卫
  - [x] 动态路由
  - [x] 编程式导航

- [x] **Vant UI组件库**
  - [x] 组件引入和使用
  - [x] 主题定制
  - [x] 移动端适配

- [x] **TailwindCSS**
  - [x] 工具类样式
  - [x] 响应式设计
  - [x] 自定义配置

## 🔧 故障排除

### 常见问题及解决方案

#### 1. API请求失败
```javascript
// 问题：API请求返回404或500错误
// 解决方案：
// 1. 检查API地址配置
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL)

// 2. 检查代理配置
// vite.config.js中的proxy设置

// 3. 检查网络连接
if (!navigator.onLine) {
  showError('网络连接不可用')
}
```

#### 2. 状态管理问题
```javascript
// 问题：Pinia状态不更新
// 解决方案：
// 1. 确保使用reactive或ref包装状态
const user = ref(null) // 而不是 let user = null

// 2. 检查状态更新方法
const updateUser = (newUser) => {
  user.value = newUser // 正确
  // user = newUser // 错误，会失去响应性
}
```

#### 3. 路由导航问题
```javascript
// 问题：路由跳转失败
// 解决方案：
// 1. 检查路由配置
const routes = [
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/product/ProductDetailPage.vue')
  }
]

// 2. 使用正确的导航方法
router.push({ name: 'ProductDetail', params: { id: '123' } })
```

#### 4. 组件渲染问题
```javascript
// 问题：组件不渲染或渲染错误
// 解决方案：
// 1. 检查计算属性依赖
const filteredProducts = computed(() => {
  return products.value.filter(p => p.category === selectedCategory.value)
})

// 2. 检查条件渲染
<div v-if="products.length > 0">
  <!-- 内容 -->
</div>
<div v-else>
  暂无数据
</div>
```

## 📈 性能优化建议

### 1. 代码分割
```javascript
// 路由级别的代码分割
const routes = [
  {
    path: '/product/:id',
    component: () => import('@/views/product/ProductDetailPage.vue')
  }
]

// 组件级别的代码分割
const HeavyComponent = defineAsyncComponent(() => 
  import('@/components/HeavyComponent.vue')
)
```

### 2. 图片优化
```vue
<template>
  <!-- 使用Vant的图片懒加载 -->
  <van-image
    :src="product.image"
    lazy-load
    :show-loading="false"
    :show-error="false"
  />
  
  <!-- 响应式图片 -->
  <img 
    :src="getResponsiveImage(product.image)" 
    :alt="product.title"
    loading="lazy"
  />
</template>

<script setup>
const getResponsiveImage = (url) => {
  const devicePixelRatio = window.devicePixelRatio || 1
  const width = window.innerWidth * devicePixelRatio
  return `${url}?w=${width}&q=80`
}
</script>
```

### 3. 缓存策略
```javascript
// Service Worker缓存
// public/sw.js
const CACHE_NAME = 'app-cache-v1'
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js'
]

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  )
})
```

## 📚 总结

### 技术架构优势

1. **现代化技术栈**
   - Vue 3组合式API提供更好的逻辑复用和类型推导
   - Pinia提供轻量级、类型安全的状态管理
   - Vite提供快速的开发体验和优化的生产构建

2. **移动端优先**
   - Vant UI组件库专为移动端设计
   - TailwindCSS提供响应式设计能力
   - 完善的移动端适配和性能优化

3. **开发体验**
   - TypeScript支持（可选）
   - 热重载开发服务器
   - 完整的开发工具链

4. **可维护性**
   - 清晰的项目结构
   - 统一的代码规范
   - 完善的错误处理机制

### API设计优势

1. **RESTful设计**
   - 遵循REST API设计原则
   - 统一的响应格式
   - 清晰的错误代码体系

2. **安全性**
   - JWT Token认证
   - 请求参数验证
   - 错误信息安全处理

3. **扩展性**
   - 版本化API设计
   - 模块化接口结构
   - 灵活的参数配置

### 项目特色

1. **Mock与真实API无缝切换**
   - 环境变量控制
   - 统一的API适配器
   - 完整的Mock数据体系

2. **完整的用户体验**
   - 流畅的页面转场
   - 友好的错误提示
   - 响应式的加载状态

3. **生产就绪**
   - 完整的测试覆盖
   - 性能监控体系
   - 部署和运维配置

---

**文档版本**: v2.0 (Vue版本)
**最后更新**: 2024-12-15
**维护人员**: 开发团队

**更新日志**:

- v2.0: 基于Vue 3重写，采用组合式API、Pinia状态管理、Vant UI组件库
- v1.1: 添加详细的接口参数说明、返回数据示例、错误代码对照表
- v1.0: 初始版本，基于传统JavaScript实现

**技术支持**:
- Vue 3官方文档: https://vuejs.org/
- Pinia文档: https://pinia.vuejs.org/
- Vant文档: https://vant-contrib.gitee.io/vant/
- TailwindCSS文档: https://tailwindcss.com/
```

</rewritten_file> 