-- 总收益历史数据初始化脚本
-- 执行时间：2025-07-30T17:03:35.180Z

-- 统计每个用户的历史总收益并更新到钱包表
UPDATE t_wallets w 
SET total_earnings = (
    SELECT IFNULL(SUM(amount), 0)
    FROM t_wallet_transactions wt
    WHERE wt.user_id = w.user_id 
    AND wt.type = 'REFUND_WIN'
    AND wt.deleted_flag = 0
);

-- 验证更新结果
SELECT 
    COUNT(*) as total_users,
    SUM(total_earnings) as total_earnings_sum,
    AVG(total_earnings) as avg_earnings,
    MAX(total_earnings) as max_earnings
FROM t_wallets 
WHERE deleted_flag = 0;

-- 查看前10个有收益的用户
SELECT 
    user_id,
    balance,
    total_earnings,
    created_at,
    updated_at
FROM t_wallets 
WHERE total_earnings > 0 
AND deleted_flag = 0
ORDER BY total_earnings DESC 
LIMIT 10;
