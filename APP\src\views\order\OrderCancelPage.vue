<template>
  <div class="order-cancel-page">
    <!-- 顶部导航 -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between p-4 h-14">
        <!-- 返回按钮 -->
        <button @click="goBack" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:arrow-back-ios" class="text-gray-800 text-xl"></iconify-icon>
        </button>
        
        <!-- 页面标题 -->
        <h1 class="text-sm font-semibold text-gray-800">
          申请退单
        </h1>
        
        <!-- 占位 -->
        <div class="w-6 h-6"></div>
      </div>
    </header>

    <div class="page-content">
      <!-- 订单信息展示 -->
      <div class="order-info-section">
        <div class="bg-white rounded-lg p-4 mb-4">
          <h3 class="text-sm font-semibold text-gray-800 mb-3">订单信息</h3>
          
          <div class="order-item">
            <span class="order-label">订单号:</span>
            <span class="order-value font-mono">{{ orderInfo.orderSn || orderInfo.orderNumber }}</span>
          </div>
          
          <div class="order-item">
            <span class="order-label">商品名称:</span>
            <span class="order-value">{{ productInfo.name }}</span>
          </div>
          
          <div class="order-item">
            <span class="order-label">订单金额:</span>
            <span class="order-value text-red-500 font-semibold">{{ formatPrice(orderAmount) }}</span>
          </div>
          
          <div class="order-item">
            <span class="order-label">下单时间:</span>
            <span class="order-value">{{ formatDate(orderInfo.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 确认信息 -->
      <div class="confirm-section">
        <div class="bg-gray-50 rounded-lg p-4 mb-4">
          <h3 class="text-sm font-semibold text-gray-800 mb-3">退款明细</h3>
          
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">订单金额:</span>
              <span class="text-gray-800">{{ formatPrice(orderAmount) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">违约金 ({{ (breachFeeRate * 100).toFixed(1) }}%):</span>
              <span class="text-red-500">-{{ formatPrice(breachFeeAmount) }}</span>
            </div>
            <hr class="border-gray-300">
            <div class="flex justify-between font-semibold">
              <span class="text-gray-800">实际退款:</span>
              <span class="text-green-600">{{ formatPrice(refundAmount) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <div class="flex flex-col space-y-3 mb-6">
          <van-button 
            size="large" 
            round 
            block
            @click="goBack"
            class="cancel-btn"
          >
            <iconify-icon icon="material-symbols:arrow-back" class="mr-2"></iconify-icon>
            取消退单
          </van-button>
          
          <van-button 
            type="danger" 
            size="large" 
            round 
            block
            :loading="cancelLoading"
            :disabled="!canSubmit"
            @click="confirmCancel"
            class="confirm-btn"
          >
            <iconify-icon icon="material-symbols:check" class="mr-2"></iconify-icon>
            确认退单
          </van-button>
        </div>

        <!-- 风险提示 -->
        <div class="risk-warning">
          <div class="text-center">
            <iconify-icon icon="material-symbols:warning" class="text-red-500 text-lg mb-1"></iconify-icon>
            <p class="text-xs text-gray-500">
              确认退单后将无法撤回，请仔细核对退款金额
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 退单成功弹窗 -->
    <div 
      v-if="showSuccessDialog" 
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
      
      <!-- 弹窗内容 -->
      <div class="relative w-full max-w-md mx-auto">
        <div class="bg-white rounded-2xl shadow-xl p-6 text-center">
          <!-- 成功图标 -->
          <div class="mb-4">
            <div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <iconify-icon icon="material-symbols:check-circle" class="text-green-500 text-3xl"></iconify-icon>
            </div>
          </div>
          
          <!-- 标题 -->
          <h3 class="text-lg font-semibold text-gray-800 mb-2">退单申请成功</h3>
          
          <!-- 详情信息 -->
          <div class="text-sm text-gray-600 mb-6 space-y-2">
            <p>您的退单申请已成功提交</p>
            <div class="bg-gray-50 rounded-lg p-3 text-left">
              <div class="flex justify-between mb-1">
                <span>订单号：</span>
                <span class="font-mono">{{ orderInfo.orderSn || orderInfo.orderNumber }}</span>
              </div>
              <div class="flex justify-between mb-1">
                <span>退单原因：</span>
                <span>{{ finalReason }}</span>
              </div>
              <div class="flex justify-between mb-1">
                <span>违约金：</span>
                <span class="text-red-500">{{ formatPrice(breachFeeAmount) }}</span>
              </div>
              <div class="flex justify-between">
                <span>实际退款：</span>
                <span class="text-green-600 font-semibold">{{ formatPrice(refundAmount) }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-500">退款将在1-3个工作日内到账，请注意查收</p>
          </div>
          
          <!-- 按钮 -->
          <div class="flex justify-center">
            <button
              @click="goToHome"
              class="py-3 px-6 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition-colors"
            >
              <iconify-icon icon="material-symbols:home" class="mr-1"></iconify-icon>
              返回首页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 退单失败弹窗 -->
    <div 
      v-if="showFailDialog" 
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
      
      <!-- 弹窗内容 -->
      <div class="relative w-full max-w-md mx-auto">
        <div class="bg-white rounded-2xl shadow-xl p-6 text-center">
          <!-- 失败图标 -->
          <div class="mb-4">
            <div class="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <iconify-icon icon="material-symbols:error" class="text-red-500 text-3xl"></iconify-icon>
            </div>
          </div>
          
          <!-- 标题 -->
          <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ failErrorTitle || '退单申请失败' }}</h3>
          
          <!-- 错误信息 -->
          <div class="text-sm text-gray-600 mb-6">
            <div class="bg-red-50 rounded-lg p-3 text-left mb-3">
              <p class="text-red-700 mb-2">{{ failMessage }}</p>
              <!-- 解决建议 -->
              <div v-if="failSuggestions.length > 0" class="text-xs text-gray-600 mt-2 pt-2 border-t border-red-200">
                <p class="font-medium mb-1">💡 解决建议：</p>
                <ul class="list-disc list-inside space-y-1">
                  <li v-for="suggestion in failSuggestions" :key="suggestion">{{ suggestion }}</li>
                </ul>
              </div>
            </div>
            <p class="text-xs text-gray-500">如问题持续出现，请联系客服获取帮助</p>
          </div>
          
          <!-- 按钮 -->
          <div class="flex space-x-3">
            <button
              @click="closeFailDialog"
              class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              重新尝试
            </button>
            <button
              @click="goToHome"
              class="flex-1 py-3 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors"
            >
              <iconify-icon icon="material-symbols:home" class="mr-1"></iconify-icon>
              返回首页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'
import { formatPrice as utilFormatPrice } from '@/utils/format'
import { cancelGroupOrder, getBreachFeeRate } from '@/api/order'
import { useAuthStore } from '@/store/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const cancelLoading = ref(false)
const breachFeeRate = ref(0.1) // 违约金比例
const selectedReason = ref('') // 选择的退单原因
const customReason = ref('') // 自定义原因

// 弹窗相关状态
const showSuccessDialog = ref(false) // 成功弹窗
const showFailDialog = ref(false) // 失败弹窗
const failMessage = ref('') // 失败消息
const failErrorTitle = ref('') // 失败标题
const failSuggestions = ref([]) // 解决建议
const finalReason = ref('') // 最终的退单原因

// 订单信息
const orderInfo = ref({
  orderSn: '',
  orderNumber: '',
  createTime: '',
  userId: null
})

// 商品信息
const productInfo = ref({
  name: '快速获得30K现金奖励 - 拼团成功即可获得'
})

// 退单原因选项
const cancelReasons = ref([
  { value: 'change_mind', label: '不想要了' },
  { value: 'price_issue', label: '价格问题' },
  { value: 'quality_concern', label: '担心商品质量' },
  { value: 'delivery_issue', label: '配送问题' },
  { value: 'address_error', label: '地址填写错误' },
  { value: 'other', label: '其他原因' }
])

// 计算属性
const orderAmount = computed(() => {
  return parseFloat(route.query.amount || route.query.originalAmount || 0)
})

const breachFeeAmount = computed(() => {
  return orderAmount.value * breachFeeRate.value
})

const refundAmount = computed(() => {
  return orderAmount.value - breachFeeAmount.value
})

const canSubmit = computed(() => {
  return true // 始终允许提交退单申请
})

// 初始化数据
const initOrderData = () => {
  // 从路由参数获取订单信息
  orderInfo.value = {
    orderSn: route.query.orderSn || route.query.orderId || '',
    orderNumber: route.query.orderSn || route.query.orderId || '',
    createTime: route.query.createTime || new Date().toISOString(),
    userId: route.query.userId || authStore.user?.id || authStore.user?.userId
  }

  // 从localStorage获取更详细的信息
  const savedOrder = localStorage.getItem('waitingOrder')
  if (savedOrder) {
    try {
      const orderData = JSON.parse(savedOrder)
      orderInfo.value = {
        ...orderInfo.value,
        ...orderData,
        orderSn: orderData.orderSn || orderData.orderNumber || orderInfo.value.orderSn
      }
      
      if (orderData.productName) {
        productInfo.value.name = orderData.productName
      }
    } catch (e) {
      console.error('解析localStorage订单信息失败:', e)
    }
  }

  console.log('📦 初始化订单数据:', {
    orderInfo: orderInfo.value,
    routeQuery: route.query,
    orderAmount: orderAmount.value
  })
}

// 加载违约金配置
const loadBreachFeeRate = async () => {
  try {
    const response = await getBreachFeeRate()
    if (response && response.code === 0 && response.data) {
      breachFeeRate.value = response.data.breachFeeRate || 0.1
      console.log('✅ 违约金配置加载成功:', breachFeeRate.value)
    }
  } catch (error) {
    console.warn('⚠️ 违约金配置加载失败，使用默认值:', error)
    breachFeeRate.value = 0.1 // 默认10%
  }
}

// 确认退单
const confirmCancel = async () => {
  try {
    // 二次确认
    const result = await showConfirmDialog({
      title: '确认退单',
      message: `确定要申请退单吗？\n\n退单原因：${getReason()}\n违约金：${formatPrice(breachFeeAmount.value)}\n实际退款：${formatPrice(refundAmount.value)}\n\n此操作无法撤回！`,
      confirmButtonText: '确认退单',
      cancelButtonText: '再考虑一下'
    })
    
    if (result !== 'confirm') return

    cancelLoading.value = true
    
    // 获取订单号和用户ID
    const orderSn = orderInfo.value.orderSn || orderInfo.value.orderNumber
    let userId = orderInfo.value.userId
    
    // 确保userId是数字类型
    if (typeof userId === 'string') {
      userId = parseInt(userId)
    }
    
    // 多重来源确保获取到userId
    if (!userId) {
      userId = authStore.user?.id || authStore.user?.userId
      if (typeof userId === 'string') {
        userId = parseInt(userId)
      }
    }

    if (!orderSn) {
      showError('订单号不能为空')
      return
    }

    if (!userId) {
      showError('用户信息获取失败，请重新登录')
      return
    }

    console.log('🚀 发起退单请求:', {
      orderSn,
      userId,
      reason: getReason(),
      breachFeeAmount: breachFeeAmount.value,
      refundAmount: refundAmount.value
    })

    // 调用退单API
    const response = await cancelGroupOrder(orderSn, getReason(), userId)
    console.log('📥 [DEBUG] OrderCancelPage response:', JSON.stringify(response, null, 2))
    
    // 退单成功的判断条件：
    // 1. HTTP状态成功 (code: 200) 且 2. 业务逻辑成功 (data.success: true)
    if (response && response.code === 200 && response.data?.success === true) {
      // 退单成功
      finalReason.value = getReason()
      showSuccessDialog.value = true
      console.log('✅ [DEBUG] OrderCancelPage: 退单成功')
      
    } else {
      // 退单失败处理
      console.error('❌ [DEBUG] OrderCancelPage: 退单失败', {
        code: response?.code,
        dataSuccess: response?.data?.success,
        message: response?.data?.message || response?.message
      })
      
      let errorMessage = '退单申请失败，请重试'
      let errorTitle = '退单失败'
      let suggestions = []
      
      if (response?.code === 200 && response?.data?.success === false) {
        // 业务逻辑失败
        const businessError = response.data?.message || response.data?.errorMessage
        if (businessError?.includes('订单状态不支持退单')) {
          errorTitle = '订单状态错误'
          errorMessage = '当前订单状态不支持退单操作'
          suggestions = [
            '只有已拼中的订单才能申请退单',
            '请确认订单是否已经发货或已完成',
            '已退单或已取消的订单无法再次退单'
          ]
        } else if (businessError?.includes('无权操作')) {
          errorTitle = '权限不足'
          errorMessage = '您无权操作该订单'
          suggestions = [
            '请确认这是您本人的订单',
            '检查是否登录了正确的账号'
          ]
        } else if (businessError?.includes('并发')) {
          errorTitle = '系统繁忙'
          errorMessage = '订单正在处理中，请稍后重试'
          suggestions = [
            '等待1-2分钟后重新尝试',
            '避免重复点击退单按钮'
          ]
        } else if (businessError?.includes('不是中奖订单')) {
          errorTitle = '非中奖订单'
          errorMessage = '只有中奖订单才能申请退单'
          suggestions = [
            '未中奖订单会自动退款，无需手动申请',
            '请查看订单详情确认中奖状态'
          ]
        } else {
          errorMessage = businessError || '业务规则验证失败'
          suggestions = ['请检查订单信息是否正确', '稍后重试或联系客服']
        }
      } else if (response?.code === 400) {
        // 参数错误
        errorTitle = '参数错误'
        errorMessage = response.message || '请求参数错误，请检查输入信息'
        suggestions = [
          '请检查退单原因是否填写',
          '确认退单理由长度不超过200字'
        ]
      } else if (response?.code === 500) {
        // 服务器错误
        errorTitle = '系统错误'
        errorMessage = '系统繁忙，请稍后重试'
        suggestions = [
          '稍等几分钟后重新尝试',
          '如问题持续出现请联系客服'
        ]
      } else if (response?.code && response.code !== 200) {
        // 其他HTTP错误
        errorTitle = `请求失败(${response.code})`
        errorMessage = response.message || '网络请求异常，请检查网络连接'
        suggestions = [
          '检查网络连接是否正常',
          '尝试刷新页面后重新操作'
        ]
      }
      
      failMessage.value = errorMessage
      failErrorTitle.value = errorTitle
      failSuggestions.value = suggestions
      showFailDialog.value = true
    }
  } catch (error) {
    console.error('退单失败:', error)
    failMessage.value = '网络连接异常，请检查网络后重试'
    failErrorTitle.value = '网络错误'
    failSuggestions.value = [
      '检查网络连接是否正常',
      '尝试刷新页面后重新操作',
      '如问题持续请联系客服'
    ]
    showFailDialog.value = true
  } finally {
    cancelLoading.value = false
  }
}

// 获取退单原因文本
const getReason = () => {
  return '用户申请退单'
}

// 工具函数
const formatPrice = (price) => {
  return utilFormatPrice(price)
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    return dateString
  }
}

// 弹窗控制方法
const closeSuccessDialog = () => {
  showSuccessDialog.value = false
}

const closeFailDialog = () => {
  showFailDialog.value = false
  failMessage.value = ''
  failErrorTitle.value = ''
  failSuggestions.value = []
}

const goBack = () => {
  if (history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 返回首页
const goToHome = () => {
  router.push('/')
}

// 页面挂载时初始化
onMounted(() => {
  console.log('📱 OrderCancelPage mounted')
  initOrderData()
  loadBreachFeeRate()
})
</script>

<style lang="scss" scoped>
.order-cancel-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-content {
  padding: 16px;
}

.order-info-section {
  .order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .order-label {
      font-size: 14px;
      color: #6b7280;
      min-width: 80px;
    }

    .order-value {
      font-size: 14px;
      color: #111827;
      text-align: right;
    }
  }
}

.cancel-reason-section {
  label {
    padding: 12px 16px;
    border-radius: 8px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9fafb;
    }

    input[type="radio"]:checked + span {
      color: #2563eb;
      font-weight: 500;
    }
  }

  textarea {
    &:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
  }
}

.action-section {
  // 按钮容器样式
  .flex.flex-col {
    gap: 12px !important; // 确保按钮间隔为12px
  }
  
  // 统一按钮样式，确保对齐
  .cancel-btn,
  .confirm-btn {
    width: 100%;
    height: 48px;
    border: 2px solid transparent !important;
    font-weight: 600;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 0 16px !important;
    box-sizing: border-box !important;
    
    &:hover {
      transform: translateY(-1px);
    }
  }

  .cancel-btn {
    background: white !important;
    border: 2px solid #d1d5db !important;
    color: #6b7280;
    
    &:hover {
      border-color: #9ca3af !important;
      color: #374151;
    }
  }

  .confirm-btn {
    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
    color: white;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    
    &:hover:not(:disabled) {
      box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }

  .risk-warning {
    padding: 16px;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .page-content {
    padding: 12px;
  }

  .action-section .flex {
    flex-direction: column;
    space-x: 0;
    
    button {
      margin-bottom: 12px;
    }
  }
}
</style>