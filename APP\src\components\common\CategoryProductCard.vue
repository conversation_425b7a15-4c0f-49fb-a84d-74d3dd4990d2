<template>
  <div class="category-product-card" @click="handleCardClick">
    <!-- 商品主要信息区域 -->
    <div class="product-main">
      <!-- 左侧商品图片 -->
      <div class="product-image-wrapper">
        <img
          :src="getImageUrl(product.image || product.imageUrl || product.mainImage || product.thumbnail)"
          :alt="product.goodsName || product.name || product.title"
          class="product-image"
          @load="onImageLoad"
          @error="onImageError"
        />
      </div>

      <!-- 右侧商品信息区域 -->
      <div class="product-info">
        <!-- 商品标题 -->
        <h3 class="product-title">
          {{ product.goodsName || product.name || product.title || '商品名称' }}
        </h3>
        
        <!-- 价格和促销信息区域 -->
        <div class="price-promotion-section">
          <div class="price-area">
            <!-- 现价（拼团价） -->
            <div class="current-price">
              {{ formatPrice(getDisplayPrice(product)) }}
            </div>
            
            <!-- 原价划线 -->
            <div class="original-price">
              原价 {{ formatPrice(getOriginalPrice(product)) }}
            </div>
          </div>
          
          <!-- 促销信息标签 -->
          <div class="promotion-tags">
            <div class="guarantee-tag">
              支持货到付款
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 按钮操作区域 - 移到外层，跨越整个卡片宽度 -->
    <div class="action-buttons">
      <button 
        v-if="canDirectBuy(product)"
        class="direct-buy-button" 
        @click.stop="handleDirectBuy"
      >
        直接购买
        <span class="button-price">{{ formatPrice(getDirectBuyPrice(product)) }}</span>
      </button>
      <button 
        class="group-buy-button" 
        :class="{ 'full-width': !canDirectBuy(product) }"
        @click.stop="handleGroupBuy"
      >
        {{ getGroupSize(product) }}人拼团价
        <span class="button-price">{{ formatPrice(getDisplayPrice(product)) }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { getImageUrl } from '@/config/image'
import { useRouter } from 'vue-router'
import { formatPrice, getGroupSize } from '@/utils/format'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

// 移除了emit定义，现在组件自己处理点击事件
const router = useRouter()

// 格式化价格 - 使用统一的越南盾格式化函数

// 获取显示价格（拼团价格）
const getDisplayPrice = (product) => {
  return product.price || product.salePrice || product.currentPrice || product.participationPrice || 0
}

// 获取原价（用于显示划线价）
const getOriginalPrice = (product) => {
  return product.originalPrice || product.marketPrice || product.listPrice || getDisplayPrice(product)
}

// 获取直接购买价格（使用alonePrice字段）
const getDirectBuyPrice = (product) => {
  return product.alonePrice || 0
}

// 判断是否可以直接购买（根据aloneFlag字段）
const canDirectBuy = (product) => {
  return product.aloneFlag === 1 || product.aloneFlag === true
}

// 拼团人数获取现在使用统一的工具函数

// 获取返现金额
const getCashbackAmount = (product) => {
  const price = getDisplayPrice(product)
  // 红包金额为拼团购买价格的5%
  return (price * 0.05).toFixed(2) || '0.00'
}

// 图片加载成功
const onImageLoad = () => {
  console.log('商品图片加载成功')
}

// 图片加载失败
const onImageError = (event) => {
  console.log('商品图片加载失败，使用默认图片')
  event.target.src = getImageUrl('')
}

// 直接购买
const handleDirectBuy = () => {
  console.log('🛒 直接购买商品:', props.product)
  
  // 跳转到直接购买商品详情页面（与首页逻辑一致）
  const productId = props.product.goodsId || props.product.id
  router.push({
    path: `/product/direct-buy/${productId}`,
    query: {
      productId: productId,
      type: 'direct'
    }
  })
}

// 参与拼团
const handleGroupBuy = () => {
  console.log('👥 拼团购买商品:', props.product)
  
  // 跳转到商品详情页面（与首页逻辑一致）
  const productId = props.product.goodsId || props.product.id
  
  console.log('🔄 跳转到商品详情页（拼团）:', {
    productId,
    source: 'category_page'
  })
  
  router.push(`/product/${productId}?type=category&source=category_page`)
}

// 处理商品卡片点击（点击非按钮区域时跳转到商品详情）
const handleCardClick = () => {
  console.log('🔄 点击商品卡片事件触发')
  console.log('🔄 商品数据:', props.product)
  console.log('🔄 商品ID字段:', {
    goodsId: props.product.goodsId,
    id: props.product.id,
    goodsName: props.product.goodsName
  })
  
  // 跳转到商品详情页面（默认为拼团模式）
  const productId = props.product.goodsId || props.product.id
  
  if (productId) {
    console.log('✅ 准备跳转到商品详情页:', {
      productId,
      source: 'category_card_click',
      routePath: `/product/${productId}`
    })
    
    try {
      router.push(`/product/${productId}?type=category&source=category_card`)
      console.log('✅ 路由跳转命令已执行')
    } catch (error) {
      console.error('❌ 路由跳转失败:', error)
    }
  } else {
    console.error('❌ 商品ID缺失，无法跳转')
    console.error('❌ 完整商品数据:', props.product)
  }
}
</script>

<style lang="scss" scoped>
.category-product-card {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
  min-height: 120px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.product-main {
  display: flex;
  flex: 1;
}

.product-image-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 10px;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 6px;

  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #f5f5f5;
  }

}

.product-info {
  flex: 1;
  padding: 12px 12px 0 0;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.price-promotion-section {
  margin-bottom: 10px;
}

.price-area {
  margin-bottom: 4px;

  .current-price {
    font-size: 18px;
    font-weight: 600;
    color: #e74c3c;
    line-height: 1;
  }

  .original-price {
    font-size: 12px;
    color: #000;
    text-decoration: line-through;
    margin-top: 2px;
  }
}

.promotion-tags {
  .guarantee-tag {
    font-size: 10px;
    color: #666;
    background: #f8f9fa;
    padding: 3px 6px;
    border-radius: 4px;
    display: inline-block;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  padding: 0 10px 10px;
  margin-top: auto;

  button {
    flex: 1;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 12px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 16px;
    line-height: 1.1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    &:active {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transform: translateY(0);
    }

    .button-price {
      font-size: 10px;
      margin-top: 0px;
      opacity: 0.9;
      line-height: 1;
    }
  }

  .direct-buy-button {
    background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
    color: white;
    border: none;

    &:hover {
      box-shadow: 0 3px 6px rgba(255, 107, 53, 0.25);
    }
  }

  .group-buy-button {
    background: linear-gradient(135deg, #ff4444 0%, #e73c3c 100%);
    color: white;

    &:hover {
      box-shadow: 0 3px 6px rgba(255, 68, 68, 0.25);
    }

    &.full-width {
      flex: none;
      width: 100%;
    }
  }
}
</style>