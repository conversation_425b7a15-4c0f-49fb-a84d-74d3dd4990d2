-- ========================================
-- 初始化所有用户的总收益数据
-- 日期: 2025-07-31
-- 功能: 基于现有的REFUND_WIN交易记录计算总收益
-- 使用说明: mysql -u root -p tgw_pp < init_total_earnings.sql
-- 注意: 请确保已执行migration_total_earnings.sql
-- ========================================

-- 显示开始信息
SELECT '开始初始化用户总收益数据...' as message;

-- 检查total_earnings字段是否存在
SET @field_exists := (SELECT COUNT(*) 
                     FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 't_wallets' 
                     AND COLUMN_NAME = 'total_earnings');

-- 如果字段不存在，停止执行
SELECT CASE 
    WHEN @field_exists = 0 THEN 'ERROR: total_earnings字段不存在！请先执行migration_total_earnings.sql'
    ELSE '字段检查通过，开始数据初始化...'
END as field_validation;

-- 显示执行前的统计信息
SELECT '执行前统计信息:' as step;
SELECT 
    COUNT(*) as '总用户数',
    COUNT(CASE WHEN total_earnings > 0 THEN 1 END) as '当前有收益用户数',
    IFNULL(SUM(total_earnings), 0) as '当前总收益金额'
FROM t_wallets;

-- 显示REFUND_WIN交易统计
SELECT 'REFUND_WIN交易统计:' as step;
SELECT 
    COUNT(DISTINCT user_id) as '有REFUND_WIN记录的用户数',
    COUNT(*) as 'REFUND_WIN交易总数',
    SUM(amount) as 'REFUND_WIN金额总计'
FROM t_wallet_transactions 
WHERE type = 'REFUND_WIN';

-- 更新所有用户的total_earnings字段
SELECT '开始更新用户总收益数据...' as step;

UPDATE t_wallets w 
SET total_earnings = (
    SELECT IFNULL(SUM(amount), 0) 
    FROM t_wallet_transactions wt 
    WHERE wt.user_id = w.user_id 
    AND wt.type = 'REFUND_WIN'
),
update_time = CURRENT_TIMESTAMP;

-- 显示更新影响的行数
SELECT ROW_COUNT() as '更新的用户数';

-- 显示更新后的统计信息
SELECT '更新后统计信息:' as step;
SELECT 
    COUNT(*) as '总用户数',
    COUNT(CASE WHEN total_earnings > 0 THEN 1 END) as '有收益用户数',
    IFNULL(SUM(total_earnings), 0) as '总收益金额',
    IFNULL(AVG(total_earnings), 0) as '平均收益',
    IFNULL(MAX(total_earnings), 0) as '最高收益'
FROM t_wallets;

-- 显示收益排行榜（前10名）
SELECT '收益排行榜(前10名):' as step;
SELECT 
    w.user_id as '用户ID',
    w.total_earnings as '总收益',
    (SELECT COUNT(*) FROM t_wallet_transactions wt 
     WHERE wt.user_id = w.user_id AND wt.type = 'REFUND_WIN') as 'REFUND_WIN次数',
    w.update_time as '更新时间'
FROM t_wallets w 
WHERE w.total_earnings > 0
ORDER BY w.total_earnings DESC
LIMIT 10;

-- 数据一致性验证
SELECT '数据一致性验证:' as step;
SELECT 
    w.user_id as '用户ID',
    w.total_earnings as '存储的收益',
    IFNULL(SUM(wt.amount), 0) as '计算的收益',
    (w.total_earnings - IFNULL(SUM(wt.amount), 0)) as '差异'
FROM t_wallets w
LEFT JOIN t_wallet_transactions wt ON w.user_id = wt.user_id 
    AND wt.type = 'REFUND_WIN'
GROUP BY w.user_id, w.total_earnings
HAVING ABS(w.total_earnings - IFNULL(SUM(wt.amount), 0)) > 0.01
ORDER BY ABS(w.total_earnings - IFNULL(SUM(wt.amount), 0)) DESC
LIMIT 5;

-- 如果没有差异记录，显示验证通过信息
SELECT CASE 
    WHEN (SELECT COUNT(*) FROM (
        SELECT w.user_id
        FROM t_wallets w
        LEFT JOIN t_wallet_transactions wt ON w.user_id = wt.user_id 
            AND wt.type = 'REFUND_WIN'
        GROUP BY w.user_id, w.total_earnings
        HAVING ABS(w.total_earnings - IFNULL(SUM(wt.amount), 0)) > 0.01
    ) as inconsistent) = 0 
    THEN '✅ 数据一致性验证通过！所有用户的总收益数据与交易记录一致。'
    ELSE '⚠️ 发现数据不一致，请检查上述差异记录。'
END as validation_result;

-- 显示完成信息
SELECT '用户总收益数据初始化完成！' as message;
SELECT '接下来请部署后端代码，然后测试新功能。' as next_step;