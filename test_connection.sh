#!/bin/bash

# 测试连接脚本
SERVER_IP="*************"

echo "=== 连接测试脚本 ==="
echo ""

echo "测试1: SSH连接"
echo ">>> 请输入SSH密码 (服务器登录密码):"
ssh root@$SERVER_IP "echo 'SSH连接成功'; whoami; pwd"

if [ $? -ne 0 ]; then
    echo "SSH连接失败，请检查密码"
    exit 1
fi

echo ""
echo "测试2: MySQL服务状态"
echo ">>> 请输入SSH密码:"
ssh root@$SERVER_IP "systemctl status mysql --no-pager -l"

echo ""
echo "测试3: MySQL连接和数据库列表"
echo ">>> 请输入SSH密码，然后输入MySQL密码:"
ssh root@$SERVER_IP "mysql -u root -p -e 'SHOW DATABASES;'"

echo ""
echo "测试4: 检查tgw_pp数据库"
echo ">>> 请输入SSH密码，然后输入MySQL密码:"
ssh root@$SERVER_IP "mysql -u root -p -e 'USE tgw_pp; SHOW TABLES;'"

echo ""
echo "测试完成！"
