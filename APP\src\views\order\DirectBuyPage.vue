<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- Header导航模块 -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between p-4 h-14">
        <!-- 返回按钮 -->
        <button @click="goBack" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:arrow-back-ios" class="text-gray-800 text-xl"></iconify-icon>
        </button>
        
        <!-- 页面标题 -->
        <h1 class="text-sm font-semibold text-gray-800">
          直接购买
        </h1>
        
        <!-- 返回主页按钮 -->
        <button @click="goToHome" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:home" class="text-gray-800 text-xl"></iconify-icon>
        </button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="pb-24" v-if="!loading">
      <!-- 订单金额展示模块 -->
      <section class="bg-white py-4 px-4 text-center">
        <div class="mb-2">
          <span class="font-bold text-gray-800" style="font-size: 2.7rem;">{{ paymentAmount.toLocaleString() }}</span>
          <span class="text-gray-800" style="font-size: 1.62rem;">₫</span>
        </div>
        <p class="text-xs text-gray-500 leading-relaxed">
          直接购买商品：{{ getOrderDescription() }}
        </p>
      </section>

      <!-- 购买提示模块 -->
      <section class="bg-white border-b border-gray-100 p-4">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-semibold text-gray-800">支付方式</h3>
          <!-- 刷新钱包信息按钮 -->
          <button 
            @click="refreshWalletInfo"
            :disabled="loading"
            class="flex items-center text-xs text-blue-600 hover:text-blue-800 transition-colors"
          >
            <iconify-icon 
              :class="['mr-1', loading ? 'animate-spin' : '']"
              icon="material-symbols:refresh"
            ></iconify-icon>
            {{ loading ? '刷新中...' : '刷新余额' }}
          </button>
        </div>
      </section>

      <!-- 支付方式选择模块 -->
      <section class="bg-white">
        <!-- Leshop Pay选项 -->
        <div 
          :class="['payment-option', { 'selected': selectedPayment === 'leshop' }]" 
          class="border-b border-gray-50 p-3 flex items-center cursor-pointer"
          @click="selectPayment('leshop')"
        >
          <div class="flex items-center flex-1">
            <!-- Leshop Pay图标 -->
            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
              <iconify-icon icon="material-symbols:wallet" class="text-white text-2xl"></iconify-icon>
            </div>
            <div class="flex-1">
              <h4 class="text-xs font-semibold text-gray-800">Ví Leshop Pay (LH钱包)</h4>
              <p :class="[
                'text-xs',
                isBalanceSufficient ? 'text-green-600' : 'text-red-500'
              ]" style="font-size: 10px;">
                当前余额：{{ getBalanceText }}
                <span v-if="!isBalanceSufficient" class="text-red-500 ml-1">(余额不足)</span>
              </p>
              <!-- 钱包状态指示 -->
              <div v-if="walletData" class="mt-1">
                <span v-if="loading" class="text-xs text-gray-400">加载中...</span>
                <span v-else-if="walletData.currency" class="text-xs text-gray-400">
                  货币：{{ walletData.currency }}
                </span>
              </div>
            </div>
          </div>
          <!-- 选择按钮 -->
          <div class="flex items-center">
            <span class="text-sm text-blue-600 mr-2">选择</span>
            <div :class="['custom-radio', { 'selected': selectedPayment === 'leshop' }]"></div>
          </div>
        </div>

        <!-- 积分支付选项 -->
        <div 
          :class="['payment-option', { 'selected': selectedPayment === 'points' }]"
          class="border-b border-gray-50 p-3 flex items-center cursor-pointer"
          @click="selectPayment('points')"
        >
          <div class="flex items-center flex-1">
            <!-- 积分图标 -->
            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
              <iconify-icon icon="material-symbols:stars" class="text-white text-2xl"></iconify-icon>
            </div>
            <div class="flex-1">
              <h4 class="text-xs font-semibold text-gray-800">积分支付 （与VND按1:1等值使用）</h4>
              <p class="text-xs text-gray-500" style="font-size: 10px;">
                当前积分：{{ getUserPoints.toLocaleString() }}
                <span v-if="walletData" :class="isPointsSufficient ? 'text-green-600' : 'text-red-500'">
                  ({{ isPointsSufficient ? '充足' : '不足' }})
                </span>
              </p>
            </div>
          </div>
          <!-- 选择按钮 -->
          <div class="flex items-center">
            <span class="text-xs text-blue-600 mr-2" style="font-size: 10px;">选择</span>
            <div :class="['custom-radio', { 'selected': selectedPayment === 'points' }]"></div>
          </div>
        </div>

        <!-- VietQR Pay选项 -->
        <div 
          :class="['payment-option', { 'selected': selectedPayment === 'vietqr' }]"
          class="p-3 flex items-center cursor-pointer"
          @click="selectPayment('vietqr')"
        >
          <div class="flex items-center flex-1">
            <!-- VietQR图标 -->
            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <iconify-icon icon="material-symbols:qr-code" class="text-white text-2xl"></iconify-icon>
            </div>
            <div class="flex-1">
              <h4 class="text-xs font-semibold text-gray-800">VietQR Pay (越南二维码支付)</h4>
              <p class="text-xs text-gray-500" style="font-size: 10px;">
                越南外贸银行、越南投资发展银行、越南工商银行等
              </p>
            </div>
          </div>
          <!-- 选择按钮 -->
          <div class="flex items-center">
            <span class="text-xs text-blue-600 mr-2" style="font-size: 10px;">选择</span>
            <div :class="['custom-radio', { 'selected': selectedPayment === 'vietqr' }]"></div>
          </div>
        </div>
      </section>

      <!-- 收货地址选择模块 -->
      <section class="bg-white mt-4 p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-sm font-semibold text-gray-800">收货地址</h3>
          <button 
            @click="openAddressModal"
            class="flex items-center text-xs text-blue-600 hover:text-blue-800 transition-colors"
          >
            <iconify-icon icon="material-symbols:edit" class="mr-1"></iconify-icon>
            更改地址
          </button>
        </div>
        
        <!-- 默认地址显示 -->
        <div v-if="selectedAddress" class="bg-gray-50 rounded-lg p-3">
          <div class="flex items-start">
            <iconify-icon icon="material-symbols:location-on" class="text-blue-500 text-lg mr-2 mt-0.5"></iconify-icon>
            <div class="flex-1">
              <div class="flex items-center mb-1">
                <span class="text-sm font-semibold text-gray-800">{{ selectedAddress.recipientName }}</span>
                <span class="text-xs text-gray-500 ml-2">{{ formatPhoneNumber(selectedAddress.phoneNumber) }}</span>
                <span v-if="selectedAddress.isDefault" class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-600 text-xs rounded-full">默认</span>
              </div>
              <p class="text-xs text-gray-600 leading-relaxed">
                {{ formatFullAddress(selectedAddress) }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- 无地址提示 -->
        <div v-else class="bg-gray-50 rounded-lg p-3 text-center">
          <iconify-icon icon="material-symbols:add-location" class="text-gray-400 text-2xl mb-2"></iconify-icon>
          <p class="text-xs text-gray-500 mb-2">请选择收货地址</p>
          <button 
            @click="openAddressModal"
            class="text-xs text-blue-600 hover:text-blue-800"
          >
            点击选择地址
          </button>
        </div>
      </section>


    </main>

    <!-- 加载状态 -->
    <main v-else class="pb-24 flex justify-center items-center h-96">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-sm text-gray-500">正在加载商品信息...</p>
      </div>
    </main>

    <!-- 地址选择弹窗 -->
    <div 
      v-if="showAddressModal" 
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" @click="showAddressModal = false"></div>
      
      <!-- 弹窗内容 -->
      <div class="relative w-full max-w-md mx-auto">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden max-h-[80vh] flex flex-col">
          <!-- 弹窗头部 -->
          <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 text-white flex items-center justify-between">
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:location-on" class="text-xl mr-2"></iconify-icon>
              <h3 class="text-sm font-semibold">选择收货地址</h3>
            </div>
            <button @click="showAddressModal = false" class="text-white hover:text-gray-200">
              <iconify-icon icon="material-symbols:close" class="text-xl"></iconify-icon>
            </button>
          </div>
          
          <!-- 地址列表 -->
          <div class="flex-1 overflow-y-auto">
            <!-- 加载状态 -->
            <div v-if="addressLoading" class="p-6 text-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-3"></div>
              <p class="text-sm text-gray-500">正在加载地址...</p>
            </div>
            
            <!-- 地址列表 -->
            <div v-else-if="addressList.length > 0" class="p-4 space-y-3">
              <div 
                v-for="address in addressList" 
                :key="address.id"
                :class="[
                  'border rounded-lg p-3 cursor-pointer transition-all',
                  selectedAddress && selectedAddress.id === address.id 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                ]"
                @click="selectAddress(address)"
              >
                <div class="flex items-start">
                  <div class="flex-1">
                    <div class="flex items-center mb-1">
                      <span class="text-sm font-semibold text-gray-800">{{ address.recipientName }}</span>
                      <span class="text-xs text-gray-500 ml-2">{{ formatPhoneNumber(address.phoneNumber) }}</span>
                      <span v-if="address.isDefault" class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-600 text-xs rounded-full">默认</span>
                    </div>
                    <p class="text-xs text-gray-600 leading-relaxed">
                      {{ formatFullAddress(address) }}
                    </p>
                  </div>
                  <div class="ml-2 flex-shrink-0">
                    <div :class="[
                      'w-4 h-4 rounded-full border-2 flex items-center justify-center',
                      selectedAddress && selectedAddress.id === address.id
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    ]">
                      <div v-if="selectedAddress && selectedAddress.id === address.id" class="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 无地址提示 -->
            <div v-else class="p-6 text-center">
              <iconify-icon icon="material-symbols:add-location" class="text-gray-400 text-3xl mb-3"></iconify-icon>
              <p class="text-sm text-gray-500 mb-3">暂无收货地址</p>
              <button 
                @click="goToAddAddress"
                class="px-4 py-2 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
              >
                添加地址
              </button>
            </div>
          </div>
          
          <!-- 底部按钮 -->
          <div class="p-4 border-t border-gray-100 flex space-x-3">
            <button 
              @click="goToAddAddress"
              class="flex-1 py-2 px-4 border border-gray-300 text-gray-700 text-xs rounded-lg hover:bg-gray-50 transition-colors"
            >
              添加新地址
            </button>
            <button 
              @click="confirmAddressSelection"
              :disabled="!selectedAddress"
              :class="[
                'flex-1 py-2 px-4 text-xs rounded-lg transition-colors',
                selectedAddress
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
            >
              确认选择
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认支付模块 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 payment-shadow">
      <!-- 余额不足提示 -->
      <div v-if="selectedPayment === 'leshop' && !isBalanceSufficient" class="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <iconify-icon icon="material-symbols:warning" class="text-red-500 text-lg mr-2"></iconify-icon>
          <div class="text-xs text-red-700">
            <p class="font-semibold">余额不足</p>
            <p>当前余额：{{ getBalanceText }}，需要：{{ formatCurrency(paymentAmount) }}</p>
          </div>
        </div>
      </div>
      
      <!-- 积分不足提示 -->
      <div v-if="selectedPayment === 'points' && !isPointsSufficient" class="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <iconify-icon icon="material-symbols:warning" class="text-red-500 text-lg mr-2"></iconify-icon>
          <div class="text-xs text-red-700">
            <p class="font-semibold">积分不足</p>
            <p>当前积分：{{ getUserPoints.toLocaleString() }}，需要：{{ getPointsRequired.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      
      <button 
        :disabled="isProcessing || !selectedPayment || (selectedPayment === 'leshop' && !isBalanceSufficient) || (selectedPayment === 'points' && !isPointsSufficient)"
        :class="[
          'w-full py-4 px-6 rounded-lg font-semibold transition-colors',
          isProcessing || !selectedPayment || (selectedPayment === 'leshop' && !isBalanceSufficient) || (selectedPayment === 'points' && !isPointsSufficient)
            ? 'bg-gray-400 text-gray-600 cursor-not-allowed' 
            : 'bg-orange-500 text-white hover:bg-orange-600'
        ]"
        style="font-size: 0.825rem;"
        @click="confirmDirectBuy"
      >
        {{ isProcessing ? '处理中...' : getPaymentButtonText() }}
      </button>
    </div>

    <!-- 支付进度弹窗 -->
    <div 
      v-if="showPaymentModal" 
      class="fixed inset-0 z-50 flex items-center justify-center p-4"
    >
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
      
      <!-- 弹窗内容 -->
      <div class="relative w-full max-w-xs mx-auto">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform scale-90">
          <!-- 弹窗头部 -->
          <div class="bg-gradient-to-r from-orange-500 to-red-500 p-5 text-white text-center">
            <div class="w-12 h-12 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <iconify-icon icon="material-symbols:local-shipping" class="text-2xl"></iconify-icon>
            </div>
            <h3 class="text-sm font-semibold mb-2">直接购买处理中</h3>
          </div>
          
          <!-- 弹窗主体 -->
          <div class="p-5 text-center">
            <div class="mb-3">
              <div class="inline-flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full mb-2">
                <iconify-icon 
                  :icon="getPaymentIcon()" 
                  class="text-orange-500 text-xl"
                ></iconify-icon>
              </div>
            </div>
            
            <p class="text-gray-700 mb-3 text-xs">
              {{ getPaymentMessage() }}
            </p>
            
            <!-- 加载动画 -->
            <div class="flex justify-center mb-3">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
            </div>
            
            <p class="text-xs text-gray-500" style="font-size: 10px;">
              请稍候，正在处理您的购买订单...
            </p>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError } from '@/utils/message.js'

export default {
  name: 'DirectBuyPage',
  components: {
    
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 响应式数据
    const loading = ref(true)
    const isProcessing = ref(false)
    const showPaymentModal = ref(false)
    const selectedPayment = ref('leshop')
    const walletData = ref(null)
    const paymentAmount = ref(10000)
    const orderInfo = ref({
      productName: '精选商品',
      description: '直接购买商品'
    })
    
    // 地址相关数据
    const showAddressModal = ref(false)
    const addressLoading = ref(false)
    const addressList = ref([])
    const selectedAddress = ref(null)
    
    // API 服务
    let apiService = null
    
    // 初始化API服务
    const initApiService = async () => {
      try {
        apiService = new StandardApiAdapter('/api/v1')
      } catch (error) {
        console.error('❌ Failed to initialize API service:', error)
        throw error
      }
    }
    
    // 计算属性
    const getUserBalance = computed(() => {
      return walletData.value?.balance || 0
    })
    
    const getExperienceBalance = computed(() => {
      return walletData.value?.experienceBalance || 0
    })
    
    const getUserPoints = computed(() => {
      return walletData.value?.points || 0
    })
    
    const getBalanceText = computed(() => {
      return formatCurrency(getUserBalance.value)
    })
    
    const isBalanceSufficient = computed(() => {
      return getUserBalance.value >= paymentAmount.value
    })
    
    const getPointsRequired = computed(() => {
      return paymentAmount.value
    })
    
    const isPointsSufficient = computed(() => {
      return getUserPoints.value >= getPointsRequired.value
    })
    
    // 格式化货币
    const formatCurrency = (amount) => {
      if (typeof amount !== 'number') return '0₫'
      return amount.toLocaleString('vi-VN') + '₫'
    }
    
    // 获取订单描述
    const getOrderDescription = () => {
      return orderInfo.value.productName || '直接购买商品'
    }
    
    // 选择支付方式
    const selectPayment = (method) => {
      selectedPayment.value = method
      console.log('选择支付方式:', method)
    }
    
    // 获取支付图标
    const getPaymentIcon = () => {
      switch (selectedPayment.value) {
        case 'leshop': return 'material-symbols:wallet'
        case 'points': return 'material-symbols:stars'
        case 'vietqr': return 'material-symbols:qr-code'
        default: return 'material-symbols:payment'
      }
    }
    
    // 获取支付消息
    const getPaymentMessage = () => {
      switch (selectedPayment.value) {
        case 'leshop': return '正在使用Leshop钱包支付...'
        case 'points': return '正在使用积分支付...'
        case 'vietqr': return '正在处理VietQR支付...'
        default: return '正在处理支付...'
      }
    }
    
    // 获取支付按钮文本
    const getPaymentButtonText = () => {
      return `立即购买 ${formatCurrency(paymentAmount.value)}`
    }
    
    // 获取支付方式对应的useCurrency值
    const getUseCurrency = (paymentMethod) => {
      switch (paymentMethod) {
        case 'leshop': return 0  // 余额支付
        case 'points': return 2  // 积分支付
        case 'vietqr': return 0  // 第三方支付（暂时使用余额支付的值）
        default: return 0
      }
    }
    
    
    // 确认直接购买
    const confirmDirectBuy = async () => {
      try {
        isProcessing.value = true
        showPaymentModal.value = true
        
        console.log('🔄 开始直接购买处理...', {
          paymentMethod: selectedPayment.value,
          amount: paymentAmount.value,
          address: selectedAddress.value,
          productId: orderInfo.value.productId
        })
        
        // 1. 检查登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          throw new Error('请先登录')
        }
        
        // 2. 检查必要参数
        if (!selectedAddress.value || !selectedAddress.value.id) {
          throw new Error('请选择收货地址')
        }
        
        // 3. 获取商品ID
        const productId = orderInfo.value.productId
        if (!productId) {
          throw new Error('商品信息不完整，请重新选择商品')
        }
        
        // 4. 构建直接购买订单数据（与拼团参数结构保持一致）
        const orderData = {
          skuId: orderInfo.value.skuId || parseInt(productId), // 使用skuId，如果没有则使用productId
          quantity: 1, // 数量(现在无论填多少，后台强制1)
          shippingAddressId: selectedAddress.value.id, // 收货地址ID
          aloneFlag: 1,  // 购买方式：1=直接购买，0=默认
          useCurrency: getUseCurrency(selectedPayment.value)  // 支付方式：1=余额支付，2=积分支付
        }
        
        console.log('📦 直接购买订单数据:', orderData)
        
        // 5. 调用新的下单API
        const response = await fetch('/api/v1/placeOrder', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          },
          body: JSON.stringify(orderData)
        })
        
        const result = await response.json()
        console.log('📋 下单API响应:', result)
        
        if (response.ok && result.code === 0) {
          // 检查data中的ok字段
          if (result.data && result.data.ok === false) {
            // 订单失败，跳转到失败页面
            const errorMsg = result.data.msg || result.data.message || '订单处理失败'
            console.error('❌ 订单失败:', errorMsg)
            
            showPaymentModal.value = false
            
            // 跳转到失败页面，传递失败信息和商品信息
            setTimeout(() => {
              router.push({
                path: '/order/direct-buy-failure',
                query: {
                  reason: errorMsg,
                  productId: orderInfo.value.productId,
                  productName: orderInfo.value.productName,
                  price: paymentAmount.value,
                  productImage: orderInfo.value.productImage
                }
              })
            }, 500)
            return
          }
          
          // 下单成功
          const orderId = result.data?.orderId || result.data?.id || result.data
          console.log('✅ 直接购买成功，订单ID:', orderId)
          
          showPaymentModal.value = false
          showSuccess('购买成功！订单已提交，商品将尽快安排发货')
          
          // 跳转到订单详情页面
          setTimeout(() => {
            router.push(`/user/orders?orderId=${orderId}`)
          }, 1500)
        } else {
          // 接口调用失败
          throw new Error(result.msg || result.message || '直接购买失败')
        }
        
      } catch (error) {
        console.error('❌ 直接购买失败:', error)
        showError(error.message || '直接购买失败，请重试')
        showPaymentModal.value = false
      } finally {
        isProcessing.value = false
      }
    }
    
    // 返回上一页
    const goBack = () => {
      router.back()
    }
    
    const goToHome = () => {
      router.push('/')
    }
    
    // 获取地址列表
    const getAddressList = async () => {
      try {
        addressLoading.value = true
        
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录，无法获取地址列表')
          return
        }
        
        const response = await fetch('/api/v1/address', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        const result = await response.json()
        
        if (response.ok && result.code === 0) {
          addressList.value = result.data?.list || []
          
          if (!selectedAddress.value && addressList.value.length > 0) {
            const defaultAddress = addressList.value.find(addr => addr.isDefault === 1)
            selectedAddress.value = defaultAddress || addressList.value[0]
          }
        } else {
          console.warn('⚠️ 地址列表获取失败:', result.msg)
        }
      } catch (error) {
        console.error('❌ 地址列表获取异常:', error)
      } finally {
        addressLoading.value = false
      }
    }
    
    // 选择地址
    const selectAddress = (address) => {
      selectedAddress.value = address
    }
    
    // 确认地址选择
    const confirmAddressSelection = () => {
      if (selectedAddress.value) {
        showAddressModal.value = false
        showSuccess('地址选择成功')
      }
    }
    
    // 跳转到添加地址页面
    const goToAddAddress = () => {
      showAddressModal.value = false
      router.push('/user/address/add')
    }
    
    // 格式化手机号
    const formatPhoneNumber = (phone) => {
      if (!phone) return ''
      if (phone.length <= 7) return phone
      return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4)
    }
    
    // 格式化完整地址
    const formatFullAddress = (address) => {
      if (!address) return ''
      const parts = []
      if (address.province) parts.push(address.province)
      if (address.city) parts.push(address.city)
      if (address.district) parts.push(address.district)
      if (address.addressLine) parts.push(address.addressLine)
      return parts.join(' ')
    }
    
    // 打开地址选择弹窗
    const openAddressModal = async () => {
      showAddressModal.value = true
      await getAddressList()
    }
    
    // 获取钱包信息
    const getWalletInfo = async (forceRefresh = false) => {
      try {
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录或无token，跳过钱包信息获取')
          walletData.value = {
            balance: 0,
            points: 0,
            currency: 'VND',
            _lastUpdated: new Date().toISOString(),
            _error: '未登录'
          }
          return
        }
        
        console.log('🔄 获取钱包信息...', forceRefresh ? '(强制刷新)' : '')
        
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        }
        
        if (forceRefresh) {
          headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
          headers['Pragma'] = 'no-cache'
          headers['Expires'] = '0'
        }
        
        const timestamp = new Date().getTime()
        const url = `/api/v1/wallet?_t=${timestamp}`
        
        const response = await fetch(url, {
          method: 'GET',
          headers: headers,
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-store'
        })
        
        const result = await response.json()
        console.log('💰 直接购买页面钱包API响应:', result)
        
        if (response.ok && result.code === 0) {
          const walletInfo = result.data || {}
          walletData.value = {
            balance: walletInfo.balance?.balance || 0,
            experienceBalance: walletInfo.balance?.experience_balance || 0,
            points: walletInfo.balance?.points || 0,
            currency: walletInfo.currency || 'VND',
            transactions: walletInfo.transactions || [],
            _lastUpdated: new Date().toISOString(),
            _error: null
          }
          
          console.log('✅ 钱包信息获取成功:', walletData.value)
        } else {
          console.error('❌ 钱包API返回错误:', result.msg || '未知错误')
          
          if (result.code === 30007 || response.status === 401 || response.status === 403) {
            console.warn('🚨 认证失败，需要重新登录')
            authStore.logout()
            throw new Error('认证失败，请重新登录')
          }
          
          throw new Error(result.msg || '获取钱包信息失败')
        }
        
      } catch (error) {
        console.error('❌ 获取钱包信息失败:', error)
        
        walletData.value = {
          balance: 0,
          points: 0,
          currency: 'VND',
          _lastUpdated: new Date().toISOString(),
          _error: error.message || '获取钱包信息失败'
        }
        
        throw error
      }
    }
    
    // 刷新钱包信息
    const refreshWalletInfo = async () => {
      try {
        loading.value = true
        console.log('🔄 开始刷新钱包信息...')
        
        await getWalletInfo(true)
        
        showSuccess('余额刷新成功')
        console.log('✅ 钱包信息刷新完成')
        
      } catch (error) {
        console.error('❌ 刷新钱包信息失败:', error)
        
        if (error.message.includes('认证失败')) {
          showError('认证失败，请重新登录')
          setTimeout(() => {
            router.push('/login')
          }, 2000)
        } else {
          showError('刷新失败，请重试')
        }
      } finally {
        loading.value = false
      }
    }
    
    // 获取商品信息并设置价格
    const getProductInfo = async (productId) => {
      try {
        console.log('🔄 获取商品信息，productId:', productId)
        
        // 先尝试从本地缓存获取
        const homeData = localStorage.getItem('homeData')
        if (homeData) {
          const data = JSON.parse(homeData)
          if (data.products && Array.isArray(data.products)) {
            const product = data.products.find(p => 
              String(p.goodsId || p.id) === String(productId)
            )
            if (product) {
              console.log('✅ 从缓存获取商品信息:', product)
              return {
                alonePrice: product.alonePrice || 0,
                goodsName: product.goodsName || product.name || '精选商品',
                aloneFlag: product.aloneFlag,
                skuId: product.skuId || product.id || productId
              }
            }
          }
        }
        
        // 如果缓存中没有，调用API获取
        const response = await fetch(`/api/v1/product/${productId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        const result = await response.json()
        console.log('📦 商品API响应:', result)
        
        if (response.ok && result.code === 0) {
          const product = result.data || {}
          return {
            alonePrice: product.alonePrice || 0,
            goodsName: product.goodsName || product.name || '精选商品',
            aloneFlag: product.aloneFlag,
            skuId: product.skuId || product.id || productId
          }
        } else {
          console.warn('⚠️ 商品信息获取失败:', result.msg)
          return {
            alonePrice: 0,
            goodsName: '精选商品',
            aloneFlag: false,
            skuId: productId
          }
        }
        
      } catch (error) {
        console.error('❌ 获取商品信息失败:', error)
        return {
          alonePrice: 0,
          goodsName: '精选商品',
          aloneFlag: false,
          skuId: productId
        }
      }
    }
    
    // 初始化
    const initializeData = async () => {
      try {
        loading.value = true
        
        // 检查用户登录状态
        if (!authStore.isLoggedIn) {
          showError('请先登录')
          router.push('/login')
          return
        }
        
        await initApiService()
        
        // 获取路由参数
        const productId = route.query.productId
        const productName = route.query.productName
        
        console.log('📋 直接购买页面初始化参数:', {
          productId,
          productName
        })
        
        // 验证必要参数
        if (!productId) {
          console.warn('⚠️ 缺少商品ID参数，直接购买可能失败')
          showError('商品信息不完整，请重新选择商品')
          return
        }
        
        // 获取商品信息并设置价格
        const productInfo = await getProductInfo(productId)
        
        // 使用alonePrice作为支付金额
        paymentAmount.value = productInfo.alonePrice || 0
        
        // 设置订单信息
        orderInfo.value.productName = productInfo.goodsName
        orderInfo.value.productId = parseInt(productId)
        orderInfo.value.aloneFlag = productInfo.aloneFlag
        orderInfo.value.skuId = productInfo.skuId
        orderInfo.value.productImage = productInfo.image || productInfo.imageUrl || productInfo.mainImage
        
        console.log('💰 设置直接购买价格:', paymentAmount.value)
        
        // 检查是否可以直接购买
        if (!productInfo.aloneFlag || productInfo.aloneFlag === false) {
          console.warn('⚠️ 该商品不支持直接购买')
          showError('该商品不支持直接购买')
          router.back()
          return
        }
        
        // 获取钱包信息和地址列表
        await getWalletInfo()
        await getAddressList()
        
      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        showError('页面初始化失败，请刷新重试')
      } finally {
        loading.value = false
      }
    }
    
    // 生命周期
    onMounted(() => {
      initializeData()
    })
    
    return {
      // 响应式数据
      loading,
      isProcessing,
      showPaymentModal,
      selectedPayment,
      walletData,
      paymentAmount,
      orderInfo,
      showAddressModal,
      addressLoading,
      addressList,
      selectedAddress,
      
      // 计算属性
      getUserBalance,
      getExperienceBalance,
      getUserPoints,
      getBalanceText,
      isBalanceSufficient,
      getPointsRequired,
      isPointsSufficient,
      
      // 方法
      formatCurrency,
      getOrderDescription,
      selectPayment,
      getPaymentIcon,
      getPaymentMessage,
      getPaymentButtonText,
      confirmDirectBuy,
      goBack,
      goToHome,
      getAddressList,
      selectAddress,
      confirmAddressSelection,
      goToAddAddress,
      formatPhoneNumber,
      formatFullAddress,
      openAddressModal,
      getWalletInfo,
      refreshWalletInfo
    }
  }
}
</script>

<style scoped>
.payment-shadow {
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.payment-option {
  transition: all 0.3s ease;
}

.payment-option.selected {
  background-color: #f0f9ff;
  border-color: #3b82f6;
}

.custom-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.custom-radio.selected {
  border-color: #3b82f6;
  background-color: #3b82f6;
}

.custom-radio.selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

::-webkit-scrollbar {
  display: none;
}
</style>