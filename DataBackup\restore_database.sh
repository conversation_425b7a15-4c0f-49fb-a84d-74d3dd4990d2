#!/bin/bash

# MySQL 数据库恢复脚本
# 使用方法: ./restore_database.sh [备份文件] [目标数据库名] [MySQL用户名]

# 配置参数
BACKUP_DIR="DataBackup"
DEFAULT_MYSQL_USER="root"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "MySQL 数据库恢复脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [备份文件] [目标数据库名] [MySQL用户名]"
    echo ""
    echo "参数:"
    echo "  备份文件        要恢复的 .sql 备份文件"
    echo "  目标数据库名    要恢复到的数据库名称"
    echo "  MySQL用户名     MySQL 用户名 (默认: root)"
    echo ""
    echo "示例:"
    echo "  $0                                    # 交互式选择备份文件"
    echo "  $0 backup.sql                        # 恢复到同名数据库"
    echo "  $0 backup.sql mydb                   # 恢复到指定数据库"
    echo "  $0 backup.sql mydb admin             # 使用指定用户恢复"
    echo ""
    echo "注意:"
    echo "  - 如果目标数据库不存在，脚本会自动创建"
    echo "  - 如果目标数据库已存在，会提示是否覆盖"
    echo ""
}

# 列出可用的备份文件
list_backup_files() {
    echo "可用的备份文件:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -lt "$BACKUP_DIR"/*.sql 2>/dev/null | nl -w2 -s'. '
    else
        echo "备份目录 $BACKUP_DIR 不存在"
        return 1
    fi
}

# 交互式选择备份文件
select_backup_file() {
    list_backup_files
    echo ""
    read -p "请输入备份文件编号或完整文件名: " selection
    
    if [[ "$selection" =~ ^[0-9]+$ ]]; then
        # 如果输入的是数字，按编号选择
        BACKUP_FILE=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | sed -n "${selection}p")
    else
        # 如果输入的是文件名
        if [[ "$selection" == *.sql ]]; then
            BACKUP_FILE="$selection"
        else
            BACKUP_FILE="$BACKUP_DIR/$selection"
        fi
    fi
    
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "备份文件不存在: $BACKUP_FILE"
        return 1
    fi
    
    echo "$BACKUP_FILE"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 设置参数
if [ -z "$1" ]; then
    print_info "未指定备份文件，进入交互模式..."
    BACKUP_FILE=$(select_backup_file)
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    BACKUP_FILE="$1"
    # 如果只提供了文件名，尝试在 DataBackup 目录中查找
    if [ ! -f "$BACKUP_FILE" ] && [ -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_FILE"
    fi
fi

# 从备份文件名推断数据库名
if [ -z "$2" ]; then
    BASENAME=$(basename "$BACKUP_FILE" .sql)
    # 移除时间戳部分，提取数据库名
    TARGET_DB=$(echo "$BASENAME" | sed 's/_[0-9]\{8\}_[0-9]\{6\}$//' | sed 's/_backup$//')
else
    TARGET_DB="$2"
fi

MYSQL_USER=${3:-"$DEFAULT_MYSQL_USER"}

# 验证备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    print_error "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

print_info "恢复配置:"
print_info "备份文件: $BACKUP_FILE"
print_info "目标数据库: $TARGET_DB"
print_info "MySQL用户: $MYSQL_USER"
print_info "文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"

# 确认操作
echo ""
read -p "确认要恢复数据库吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "操作已取消"
    exit 0
fi

# 检查 MySQL 连接
print_step "步骤1: 检查 MySQL 连接..."
mysql -u "$MYSQL_USER" -p -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    print_error "无法连接到 MySQL，请检查用户名和密码"
    exit 1
fi
print_info "MySQL 连接成功"

# 检查目标数据库是否存在
print_step "步骤2: 检查目标数据库..."
DB_EXISTS=$(mysql -u "$MYSQL_USER" -p -e "SHOW DATABASES LIKE '$TARGET_DB';" 2>/dev/null | grep "$TARGET_DB")

if [ -n "$DB_EXISTS" ]; then
    print_warning "数据库 '$TARGET_DB' 已存在"
    read -p "是否要覆盖现有数据库？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
    print_info "将覆盖现有数据库"
else
    print_info "将创建新数据库: $TARGET_DB"
fi

# 创建或重建数据库
print_step "步骤3: 准备目标数据库..."
mysql -u "$MYSQL_USER" -p -e "DROP DATABASE IF EXISTS \`$TARGET_DB\`; CREATE DATABASE \`$TARGET_DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
    print_error "创建数据库失败"
    exit 1
fi
print_info "数据库准备完成"

# 恢复数据
print_step "步骤4: 恢复数据..."
print_info "正在恢复数据，请稍候..."

mysql -u "$MYSQL_USER" -p "$TARGET_DB" < "$BACKUP_FILE"

if [ $? -ne 0 ]; then
    print_error "数据恢复失败"
    exit 1
fi

# 验证恢复结果
print_step "步骤5: 验证恢复结果..."
TABLE_COUNT=$(mysql -u "$MYSQL_USER" -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null | wc -l)
TABLE_COUNT=$((TABLE_COUNT - 1))  # 减去表头

if [ $TABLE_COUNT -gt 0 ]; then
    print_info "恢复成功！"
    print_info "数据库: $TARGET_DB"
    print_info "表数量: $TABLE_COUNT"
    
    echo ""
    echo "=== 数据库表列表 ==="
    mysql -u "$MYSQL_USER" -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null
else
    print_warning "恢复完成，但未检测到表，请手动验证"
fi

print_info "数据库恢复完成！"
