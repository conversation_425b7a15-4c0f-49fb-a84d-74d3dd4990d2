package net.lab1024.sa.admin.module.business.activities.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesEntity;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesQueryForm;
import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitiesDao extends BaseMapper<ActivitiesEntity> {

    List<ActivitiesVO> query(Page page, @Param("query") ActivitiesQueryForm query);

    /**
     * 更新删除状态
     */
    void updateDeleted(@Param("id")Long id, @Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
