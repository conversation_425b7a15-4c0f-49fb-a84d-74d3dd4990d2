#!/usr/bin/env node
/**
 * PR摘要自动生成脚本
 * 分析两次PR之间的修改，生成详细的摘要信息
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PRSummaryGenerator {
  constructor() {
    this.currentBranch = this.getCurrentBranch();
    this.baseBranch = 'master';
    this.changeLogsDir = '.git/change-logs';
    this.commitLogsDir = '.git/commit-logs';
  }

  getCurrentBranch() {
    try {
      return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    } catch (error) {
      console.error('Error getting current branch:', error);
      return 'unknown';
    }
  }

  getCommitsSinceLastPR() {
    try {
      // 获取当前分支相对于master的所有提交
      const commits = execSync(`git log ${this.baseBranch}..HEAD --oneline`, { encoding: 'utf8' });
      return commits.trim().split('\n').filter(line => line.trim());
    } catch (error) {
      console.error('Error getting commits:', error);
      return [];
    }
  }

  getFileChanges() {
    try {
      // 获取文件变更统计
      const diffStat = execSync(`git diff ${this.baseBranch}...HEAD --stat`, { encoding: 'utf8' });
      const diffNumStat = execSync(`git diff ${this.baseBranch}...HEAD --numstat`, { encoding: 'utf8' });
      
      const changes = [];
      const lines = diffNumStat.trim().split('\n').filter(line => line.trim());
      
      lines.forEach(line => {
        const [added, removed, file] = line.split('\t');
        if (file && file !== '') {
          changes.push({
            file,
            added: parseInt(added) || 0,
            removed: parseInt(removed) || 0,
            type: this.getFileType(file)
          });
        }
      });

      return { changes, stat: diffStat };
    } catch (error) {
      console.error('Error getting file changes:', error);
      return { changes: [], stat: '' };
    }
  }

  getFileType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const typeMap = {
      '.vue': 'Vue组件',
      '.js': 'JavaScript',
      '.ts': 'TypeScript',
      '.css': 'CSS样式',
      '.scss': 'SCSS样式',
      '.html': 'HTML',
      '.md': '文档',
      '.json': '配置文件',
      '.png': '图片',
      '.jpg': '图片',
      '.jpeg': '图片',
      '.svg': '图标'
    };
    
    return typeMap[ext] || '其他';
  }

  categorizeChanges(changes) {
    const categories = {
      '新增功能': [],
      '页面优化': [],
      'API接口': [],
      '样式更新': [],
      '配置修改': [],
      '文档更新': [],
      '其他修改': []
    };

    changes.forEach(change => {
      const { file, type } = change;
      const filePath = file.toLowerCase();

      if (filePath.includes('views/') || filePath.includes('pages/')) {
        categories['页面优化'].push(change);
      } else if (filePath.includes('api/') || filePath.includes('service')) {
        categories['API接口'].push(change);
      } else if (filePath.includes('component') && type === 'Vue组件') {
        categories['新增功能'].push(change);
      } else if (type.includes('样式') || type === 'CSS样式' || type === 'SCSS样式') {
        categories['样式更新'].push(change);
      } else if (type === '配置文件' || filePath.includes('config')) {
        categories['配置修改'].push(change);
      } else if (type === '文档' || filePath.includes('readme') || filePath.includes('doc')) {
        categories['文档更新'].push(change);
      } else {
        categories['其他修改'].push(change);
      }
    });

    // 过滤空分类
    Object.keys(categories).forEach(key => {
      if (categories[key].length === 0) {
        delete categories[key];
      }
    });

    return categories;
  }

  generateSummary() {
    const commits = this.getCommitsSinceLastPR();
    const { changes, stat } = this.getFileChanges();
    const categories = this.categorizeChanges(changes);

    const totalAdded = changes.reduce((sum, change) => sum + change.added, 0);
    const totalRemoved = changes.reduce((sum, change) => sum + change.removed, 0);

    let summary = '';

    // 生成摘要标题
    summary += `## 📋 PR摘要 - ${this.currentBranch}\n\n`;
    summary += `**分支**: \`${this.currentBranch}\` → \`${this.baseBranch}\`\n`;
    summary += `**提交数量**: ${commits.length} 个提交\n`;
    summary += `**文件变更**: ${changes.length} 个文件\n`;
    summary += `**代码变更**: +${totalAdded} -${totalRemoved}\n\n`;

    // 生成功能分类
    summary += `## 🚀 主要变更\n\n`;
    Object.keys(categories).forEach(category => {
      const items = categories[category];
      if (items.length > 0) {
        summary += `### ${category}\n`;
        items.forEach(item => {
          summary += `- \`${item.file}\` (+${item.added} -${item.removed})\n`;
        });
        summary += '\n';
      }
    });

    // 生成提交历史
    summary += `## 📝 提交历史\n\n`;
    commits.forEach(commit => {
      summary += `- ${commit}\n`;
    });
    summary += '\n';

    // 生成文件统计
    summary += `## 📊 文件变更统计\n\n`;
    summary += '```\n';
    summary += stat;
    summary += '```\n\n';

    // 生成检查清单
    summary += `## ✅ 检查清单\n\n`;
    summary += `- [ ] 代码已经过review\n`;
    summary += `- [ ] 功能测试已通过\n`;
    summary += `- [ ] 无破坏性变更\n`;
    summary += `- [ ] 文档已更新\n`;
    summary += `- [ ] 已考虑向后兼容性\n\n`;

    // 生成注意事项
    summary += `## 📝 注意事项\n\n`;
    summary += `本次PR包含 ${changes.length} 个文件的修改，主要集中在以下方面：\n`;
    Object.keys(categories).forEach(category => {
      summary += `- ${category}: ${categories[category].length} 个文件\n`;
    });

    return summary;
  }

  saveSummary(summary) {
    const summaryFile = `.git/pr-summary-${this.currentBranch}.md`;
    fs.writeFileSync(summaryFile, summary);
    console.log(`✅ PR摘要已生成: ${summaryFile}`);
    return summaryFile;
  }

  run() {
    console.log(`🚀 正在生成PR摘要...`);
    console.log(`当前分支: ${this.currentBranch}`);
    console.log(`基础分支: ${this.baseBranch}`);

    const summary = this.generateSummary();
    const summaryFile = this.saveSummary(summary);

    console.log(`\n📋 PR摘要预览:`);
    console.log('='.repeat(50));
    console.log(summary);
    console.log('='.repeat(50));

    return summaryFile;
  }
}

// 运行脚本
if (require.main === module) {
  const generator = new PRSummaryGenerator();
  generator.run();
}

module.exports = PRSummaryGenerator;