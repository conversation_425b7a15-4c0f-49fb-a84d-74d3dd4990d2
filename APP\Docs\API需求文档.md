# API 需求文档 (优化精简版)

本文档基于DRY、KISS、SOLID、YAGNI原则，对团购网APP后端API进行精简优化设计。

## 设计原则

- **DRY (Don't Repeat Yourself)**: 合并相似功能接口，统一数据模型
- **KISS (Keep It Simple, Stupid)**: 简化接口设计，减少不必要的复杂性
- **SOLID**: 单一职责原则，每个接口功能明确独立
- **YAGNI (You Aren't Gonna Need It)**: 移除过度设计，仅保留实际需要的功能

## 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": { ... },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

## 统一分页格式

```json
{
  "items": [...],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 100,
    "has_more": true
  }
}
```

## 1. 通用基础接口

### 1.1 统一数据获取
**合并原有的多个首页、分类、商品列表接口**

- **Endpoint:** `GET /api/v1/data/{type}`
- **描述:** 统一获取各类数据（首页、商品、分类等）
- **认证:** 可选
- **路径参数:**
  - `type`: `string` - 数据类型 ('home', 'products', 'categories', 'banners')
- **请求参数:**
  - `category`: `string` (可选) - 商品分类
  - `group_type`: `number` (可选) - 拼团类型
  - `page`: `number` (可选, 默认1)
  - `per_page`: `number` (可选, 默认20)
  - `language`: `string` (可选, 默认'zh-CN')

### 1.2 统一搜索接口
- **Endpoint:** `GET /api/v1/search`
- **描述:** 全站搜索（商品、品牌、活动）
- **请求参数:**
  - `q`: `string` (必需) - 搜索关键词
  - `type`: `string` (可选) - 搜索类型 ('all', 'product', 'brand', 'activity')
  - `page`: `number` (可选, 默认1)

## 2. 商品相关接口

### 2.1 商品详情
- **Endpoint:** `GET /api/v1/products/{id}`
- **描述:** 获取商品完整信息（包含评价预览、正在拼团等）
- **认证:** 可选

### 2.2 商品相关操作
- **Endpoint:** `POST /api/v1/products/{id}/action`
- **描述:** 商品相关操作（收藏/取消收藏、获取评价、获取拼团等）
- **认证:** 必需（收藏操作）
- **请求体:**
```json
{
  "action": "favorite|unfavorite|get_reviews|get_groups",
  "params": { "page": 1, "filter": "all" }
}
```

## 3. 订单相关接口

### 3.1 订单管理
- **Endpoint:** `GET /api/v1/orders`
- **描述:** 获取订单列表
- **认证:** 必需
- **请求参数:**
  - `status`: `string` (可选) - 订单状态筛选
  - `page`: `number` (可选)

### 3.2 订单操作
- **Endpoint:** `POST /api/v1/orders/{id}/action`
- **描述:** 订单操作（确认收货、再次购买、取消订单等）
- **认证:** 必需
- **请求体:**
```json
{
  "action": "confirm|repurchase|cancel|track",
  "params": {}
}
```

## 4. 用户相关接口

### 4.1 用户中心数据
- **Endpoint:** `GET /api/v1/user/dashboard`
- **描述:** 获取个人中心所有数据（用户信息、钱包、订单统计、收藏等）
- **认证:** 必需

### 4.2 用户数据操作
- **Endpoint:** `GET|POST|PUT|DELETE /api/v1/user/{type}`
- **描述:** 用户数据CRUD操作
- **认证:** 必需
- **类型:**
  - `favorites` - 收藏管理
  - `history` - 浏览历史
  - `addresses` - 地址管理
  - `coupons` - 优惠券
  - `settings` - 设置

## 5. 钱包与支付

### 5.1 钱包信息
- **Endpoint:** `GET /api/v1/wallet`
- **描述:** 获取钱包余额和交易记录
- **认证:** 必需
- **请求参数:**
  - `type`: `string` (可选) - 记录类型筛选 ('all', 'income', 'expense')
  - `page`: `number` (可选)

### 5.2 钱包操作
- **Endpoint:** `POST /api/v1/wallet/action`
- **描述:** 钱包操作（充值、提现、转账）
- **认证:** 必需
- **请求体:**
```json
{
  "action": "recharge|withdraw|transfer",
  "amount": 100000,
  "method_id": "momo|zalo|bank",
  "target_account": "account_info"
}
```

### 5.3 支付处理
- **Endpoint:** `POST /api/v1/payment`
- **描述:** 统一支付处理
- **认证:** 必需
- **请求体:**
```json
{
  "order_id": "order-123",
  "payment_method": "leshop_wallet|vietqr|momo",
  "amount": 10000
}
```

## 6. 拼团相关接口

### 6.1 拼团信息
- **Endpoint:** `GET /api/v1/groups/{id}`
- **描述:** 获取拼团详情、参与者、剩余时间等
- **认证:** 必需

### 6.2 拼团操作
- **Endpoint:** `POST /api/v1/groups/action`
- **描述:** 拼团操作（发起、参与、分享、取消）
- **认证:** 必需
- **请求体:**
```json
{
  "action": "create|join|share|cancel",
  "product_id": "prod-123",
  "group_id": "group-456",
  "params": {}
}
```

## 7. 认证相关接口

### 7.1 统一认证
- **Endpoint:** `POST /api/v1/auth`
- **描述:** 统一处理登录、注册、第三方登录
- **请求体:**
```json
{
  "type": "login|register|oauth|forgot_password",
  "phone": "+***********",
  "password": "password",
  "provider": "google|facebook|zalo",
  "token": "oauth_token"
}
```

### 7.2 Token刷新
- **Endpoint:** `POST /api/v1/auth/refresh`
- **描述:** 刷新访问令牌
- **认证:** 必需 (Refresh Token)

## 8. 内容管理接口

### 8.1 活动信息
- **Endpoint:** `GET /api/v1/activities/{id}`
- **描述:** 获取活动详情和相关商品
- **认证:** 可选

### 8.2 客服支持
- **Endpoint:** `GET|POST /api/v1/support`
- **描述:** 获取帮助信息或提交反馈
- **认证:** 可选（提交反馈需要）
- **请求体 (POST):**
```json
{
  "type": "feedback|complaint|suggestion",
  "content": "反馈内容",
  "contact": "联系方式"
}
```

## 9. 文件上传接口

### 9.1 统一文件上传
- **Endpoint:** `POST /api/v1/upload`
- **描述:** 统一处理图片、文件上传
- **认证:** 必需
- **请求参数:**
  - `type`: `string` - 上传类型 ('avatar', 'review_image', 'feedback_image')
  - `file`: `File` - 文件对象

## 接口优化说明

### 合并的接口
1. **数据获取类接口**: 将首页数据、商品列表、分类数据合并为统一的数据获取接口
2. **操作类接口**: 将各种CRUD操作合并为action模式，减少接口数量
3. **认证接口**: 统一登录、注册、第三方登录为一个接口
4. **钱包操作**: 合并充值、提现、转账操作

### 移除的功能
1. **过度细分的接口**: 如单独的Banner接口、分类图标接口等
2. **冗余的状态查询**: 合并到主要数据获取接口中
3. **不必要的复杂化**: 简化分页、筛选逻辑

### 统一的设计模式
1. **RESTful设计**: 使用标准HTTP方法和状态码
2. **统一错误处理**: 标准化错误响应格式
3. **版本管理**: 通过URL路径进行API版本控制
4. **参数标准化**: 统一命名规范和数据格式

这样的设计将原来的20+个接口精简为不到15个核心接口，提高了可维护性和开发效率。