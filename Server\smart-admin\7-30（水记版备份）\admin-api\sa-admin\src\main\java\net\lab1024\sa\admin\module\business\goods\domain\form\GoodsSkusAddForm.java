package net.lab1024.sa.admin.module.business.goods.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;

@Data
public class GoodsSkusAddForm {

    @Schema(description = "商品ID")
    @NotNull(message = "商品ID 不能为空")
    private Long goodsId;

    @Schema(description = "SKU编码")
    @NotBlank(message = "SKU编码 不能为空")
    private String skuCode;

    @Schema(description = "规格属性")
    @NotBlank(message = "规格属性 不能为空")
    private Map<String, Object> attributes;

    @Schema(description = "商品标价")
    @NotNull(message = "商品标价 不能为空")
    private BigDecimal price;

    @Schema(description = "库存数量")
    @NotNull(message = "库存数量 不能为空")
    private Integer stock;

    @Schema(description = "销售数量")
    @NotNull(message = "销售数量 不能为空")
    private Integer salesCount;

    @Schema(description = "SKU状态")
    @NotBlank(message = "SKU状态 不能为空")
    private String status;

}