import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showError } from '@/utils/message'

/**
 * 页面级登录状态检查组合式函数
 * 专门用于用户相关的二级页面，确保用户已登录
 */
export function usePageAuth(options = {}) {
  const {
    redirectPath = '/login',
    showErrorMessage = true,
    errorMessage = '请先登录后再访问此页面'
  } = options
  
  const router = useRouter()
  const authStore = useAuthStore()
  
  /**
   * 检查登录状态并处理跳转
   */
  const checkAuthAndRedirect = () => {
    console.log('🔍 [PageAuth] 检查登录状态:', {
      isLoggedIn: authStore.isLoggedIn,
      hasToken: !!authStore.token,
      hasUser: !!authStore.user,
      currentPath: router.currentRoute.value.path
    })
    
    // 检查登录状态
    if (!authStore.isLoggedIn || !authStore.token) {
      console.warn('❌ [PageAuth] 用户未登录，准备跳转到登录页')
      
      // 显示错误提示
      if (showErrorMessage) {
        showError(errorMessage)
      }
      
      // 跳转到登录页，并记录当前页面用于登录后回跳
      const currentPath = router.currentRoute.value.fullPath
      console.log('🚀 [PageAuth] 强制跳转到登录页:', {
        from: currentPath,
        to: redirectPath
      })
      
      // 使用多种方式确保跳转成功
      try {
        router.replace({
          path: redirectPath,
          query: { redirect: currentPath }
        })
      } catch (error) {
        console.error('❌ [PageAuth] router.replace失败:', error)
        // 备用方案：使用 window.location
        const redirectUrl = `${redirectPath}?redirect=${encodeURIComponent(currentPath)}`
        console.log('🔄 [PageAuth] 使用window.location跳转:', redirectUrl)
        window.location.href = redirectUrl
      }
      
      return false
    }
    
    console.log('✅ [PageAuth] 登录状态检查通过')
    return true
  }
  
  /**
   * 在页面挂载时自动检查
   */
  const enableAutoCheck = () => {
    onMounted(() => {
      console.log('🚀 [PageAuth] 页面挂载，开始登录状态检查')
      checkAuthAndRedirect()
    })
  }
  
  return {
    checkAuthAndRedirect,
    enableAutoCheck,
    isLoggedIn: () => authStore.isLoggedIn && !!authStore.token
  }
}

/**
 * 便捷方法：为页面启用自动登录检查
 * 直接在setup中调用即可
 */
export function requireAuth(options = {}) {
  const { enableAutoCheck } = usePageAuth(options)
  enableAutoCheck()
}