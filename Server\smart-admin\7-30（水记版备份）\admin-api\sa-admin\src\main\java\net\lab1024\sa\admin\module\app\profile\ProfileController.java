package net.lab1024.sa.admin.module.app.profile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.withdrawals.domain.form.WithdrawalsAddForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "APP:个人资料")
public class ProfileController {
    @Resource
    private ProfileService profileService;

    @Operation(summary = "个人钱包")
    @GetMapping("/app/v1/wallet")
    public ResponseDTO<Object> appWallet(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize
    ) {
        return profileService.wallet(type, sort, pageNum, pageSize);
    }

    @Operation(summary = "提现申请记录")
    @GetMapping("/app/v1/withdrawals")
    public ResponseDTO<Object> appWithdrawals(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize
    ) {
        return profileService.withdrawals(status, sort, pageNum, pageSize);
    }

    @Operation(summary = "提交提现申请")
    @PostMapping("/app/v1/withdrawalsApply")
    public ResponseDTO<Object> withdrawalsApply(@Valid @RequestBody WithdrawalsAddForm withdrawalsAddForm) {
        return profileService.withdrawalsApply(withdrawalsAddForm);
    }
}
