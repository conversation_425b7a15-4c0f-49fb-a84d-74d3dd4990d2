# 【前端】APP前端编译及启动操作说明（WSL版）

## 项目信息
- **项目路径**: `/mnt/d/Dev/团购网/APP`
- **技术栈**: Vue 3 + Vite + Pinia + Vant UI
- **开发端口**: 3000
- **生产构建目录**: `dist/`

## 环境检查

### 检查Node.js版本
```bash
node --version
npm --version
```

### 安装依赖（首次运行或package.json更新后）
```bash
cd /mnt/d/Dev/团购网/APP
npm install
```

## 手动编译命令

### 进入项目目录
```bash
cd /mnt/d/Dev/团购网/APP
```

### 开发环境编译
```bash
# 启动开发服务器（热重载）
npm run dev

# 使用真实API的开发模式（推荐）
npm run dev:real
```

### 生产环境编译
```bash
# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 手动启动命令

### 开发模式启动
```bash
# 进入项目目录
cd /mnt/d/Dev/团购网/APP

# 启动开发服务器
npm run dev:real
```

### 生产模式启动
```bash
# 先构建项目
npm run build

# 预览生产版本
npm run preview
```

## 检查启动状态

### 检查端口3000是否被占用
```bash
ss -tlnp | grep :3000
```

### 检查Node进程
```bash
ps aux | grep node | grep vite
```

### 访问测试
如果启动成功，您可以访问：
- 开发环境：http://localhost:3000
- 预览环境：http://localhost:4173

## 手动停止命令

### 方式1：使用Ctrl+C停止
如果在前台运行，直接按 `Ctrl+C` 停止服务

### 方式2：查找并终止进程
```bash
# 查找Node.js/Vite进程
ps aux | grep node | grep vite

# 终止指定进程（替换PID为实际进程ID）
kill -9 <PID>

# 或者直接终止所有相关进程
pkill -f "vite"
pkill -f "npm run dev"
```

### 方式3：按端口查找并终止
```bash
# 查找占用3000端口的进程
lsof -ti:3000

# 终止占用3000端口的进程
lsof -ti:3000 | xargs kill -9
```

## 常见问题

### 依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 端口被占用
```bash
# 修改开发端口（在vite.config.js中）
# 或者终止占用端口的进程
lsof -ti:3000 | xargs kill -9
```

### 热重载不工作
```bash
# 检查文件监听限制
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```