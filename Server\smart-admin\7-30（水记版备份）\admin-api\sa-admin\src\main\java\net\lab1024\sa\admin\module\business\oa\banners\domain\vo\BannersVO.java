package net.lab1024.sa.admin.module.business.oa.banners.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 横幅管理 列表VO
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Data
public class BannersVO {


    @Schema(description = "Banner ID")
    private Integer id;

    @Schema(description = "Banner标题")
    private String title;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "跳转链接")
    private String linkUrl;

    @Schema(description = "链接类型")
    private String linkType;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "状态1 开, 0 关")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
