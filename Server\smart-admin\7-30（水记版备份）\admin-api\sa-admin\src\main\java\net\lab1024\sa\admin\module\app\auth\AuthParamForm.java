package net.lab1024.sa.admin.module.app.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import net.lab1024.sa.base.module.support.captcha.domain.CaptchaForm;
import org.hibernate.validator.constraints.Length;

@Data
public class AuthParamForm extends CaptchaForm {

    @Schema(description = "登录手机号")
    @NotBlank(message = "登录手机号不能为空")
    @Length(max = 16, message = "登录账号最多16字符")
    private String phone;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "确认密码")
    private String confirmPassword;

    @Schema(description = "是否同意用户协议")
    private Boolean agreedToTerms;

    @Schema(description = "邀请码")
    private String inviteCode;

    @Schema(description = "操作类型")
    @NotBlank(message = "操作类型不能为空")
    private String type;
}
