<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.orders.dao.OrdersDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t1.id,
        t1.order_sn,
        t1.user_id,
        t2.actual_name user_name,
        t1.activity_id,
        t1.status,
        t1.draw_result,
        t1.reward_amount,
        t1.goods_id,
        t1.sku_id,
        t1.alone_flag,
        t1.win_option,
        t1.amount_paid,
        t1.experience_paid,
        t1.subsidy_paid,
        t1.points_paid,
        t1.shipping_address_id,
        t1.payment_method,
        t1.payment_time,
        t1.draw_time,
        t1.complete_time,
        t1.create_time,
        t1.settle_flag,
        t1.deleted_flag,
        t1.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.orders.domain.vo.OrdersVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_orders t1 left join t_employee t2 on t1.user_id=t2.employee_id
        <where>
            <!--订单号-->
            <if test="queryForm.orderSn != null">
                AND t1.order_sn = #{queryForm.orderSn}
            </if>
            <!--创建时间-->
            <if test="queryForm.createTimeBegin != null">
                AND t1.create_time &gt;= #{queryForm.createTimeBegin}
            </if>
            <if test="queryForm.createTimeEnd != null">
                AND t1.create_time &lt;= #{queryForm.createTimeEnd}
            </if>
            <!--结算-->
            <if test="queryForm.settleFlag != null">
                AND t1.settle_flag = #{queryForm.settleFlag}
            </if>
            <!--用户ID-->
            <if test="queryForm.userId != null">
                AND t1.user_id = #{queryForm.userId}
            </if>
            <!--用户名-->
            <if test="queryForm.userName != null">
                AND INSTR(t2.actual_name,#{queryForm.userName})
            </if>
            <if test="queryForm.deletedFlag != null">
                AND t1.deleted_flag = #{queryForm.deletedFlag}
            </if>

        </where>
        <if test="queryForm.sortItemList == null or 1 > queryForm.sortItemList.size()">
        ORDER BY id DESC
        </if>
    </select>

    <update id="batchUpdateDeleted">
        update t_orders set deleted_flag = #{deletedFlag}
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="updateDeleted">
        update t_orders set deleted_flag = #{deletedFlag}
        where id = #{id}
    </update>

</mapper>
