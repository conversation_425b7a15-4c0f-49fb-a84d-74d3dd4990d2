# 启动脚本说明

本目录包含了社交拼团APP系统的完整启动和管理脚本，按开发环境和生产环境明确分类。

## 📜 脚本分类

### 🟢 开发环境脚本 (DEV)

#### 启动脚本
- **`start-backend-dev.sh`** - 启动开发环境后端API服务 (Maven方式)
- **`start-frontend-dev.sh`** - 启动前端移动应用开发服务器
- **`start-admin-web-dev.sh`** - 启动管理端后台开发服务器
- **`start-all-dev.sh`** - 一键启动所有开发环境服务

#### 停止脚本
- **`stop-all-dev.sh`** - 停止所有开发环境服务

#### 管理脚本
- **`restart-all-dev.sh`** - 重启所有开发环境服务

### 🔴 生产环境脚本 (PROD)

#### 启动脚本
- **`start-backend-prod.sh`** - 启动生产环境后端服务 (JAR包方式)
- **`start-all-prod.sh`** - 构建并启动生产环境服务

#### 停止脚本
- **`stop-backend-prod.sh`** - 停止生产环境后端服务
- **`stop-all-prod.sh`** - 停止所有生产环境服务

#### 管理脚本
- **`restart-all-prod.sh`** - 重启生产环境服务

### 🔧 通用工具脚本

- **`check-status.sh`** - 检查所有服务状态
- **`logs.sh`** - 交互式日志查看工具

### 🌐 Nginx代理工具脚本

- **`setup-nginx-proxy.sh`** - 自动安装和配置Nginx代理 (需要sudo权限)
- **`nginx-manager.sh`** - Nginx服务管理工具 (交互式菜单)
- **`diagnose-api-issue.sh`** - API问题诊断工具 (快速排查网络代理问题)

### 📋 兼容脚本 (原有)

- **`start.sh`** - 原有的JAR包启动脚本
- **`stop.sh`** - 原有的JAR包停止脚本
- **`log.sh`** - 原有的日志脚本

## 🎯 快速使用指南

### 🟢 开发环境操作

```bash
# 启动完整开发环境
./start-all-dev.sh

# 单独启动服务
./start-backend-dev.sh    # 只启动后端
./start-frontend-dev.sh   # 只启动前端
./start-admin-web-dev.sh  # 只启动管理端

# 停止开发环境
./stop-all-dev.sh

# 重启开发环境
./restart-all-dev.sh
```

### 🔴 生产环境操作

```bash
# 启动生产环境（包含构建）
./start-all-prod.sh

# 只启动后端生产服务
./start-backend-prod.sh

# 停止生产环境
./stop-all-prod.sh

# 重启生产环境
./restart-all-prod.sh
```

### 🔧 通用操作

```bash
# 检查服务状态
./check-status.sh

# 查看日志
./logs.sh
```

### 🌐 Nginx代理管理

```bash
# 安装配置Nginx代理 (首次使用)
sudo ./setup-nginx-proxy.sh

# Nginx服务管理 (交互式菜单)
./nginx-manager.sh

# API问题诊断
./diagnose-api-issue.sh
```

## 📊 环境对比

| 项目 | 开发环境 (DEV) | 生产环境 (PROD) |
|------|---------------|----------------|
| **后端启动方式** | Maven (spring-boot:run) | JAR包 (java -jar) |
| **配置文件** | dev 配置 | prod 配置 |
| **前端服务** | Vite 开发服务器 | 构建静态文件 |
| **管理端服务** | Vue 开发服务器 | 构建静态文件 |
| **日志目录** | `/logs/` | `/logs/prod/` |
| **端口** | 后端:8686, 前端:3000, 管理端:3001 | 后端:8686 |

## 📊 服务端口

### 开发环境端口
| 服务     | 端口 | 访问地址                       |
| -------- | ---- | ------------------------------ |
| 前端应用 | 3000 | http://localhost:3000          |
| 后端API  | 8686 | http://localhost:8686          |
| 管理后台 | 3001 | http://localhost:3001          |
| API文档  | 8686 | http://localhost:8686/doc.html |

### 生产环境端口
| 服务     | 端口 | 访问地址                       |
| -------- | ---- | ------------------------------ |
| 后端API  | 8686 | http://localhost:8686          |
| API文档  | 8686 | http://localhost:8686/doc.html |

*注：生产环境的前端和管理端通过构建生成静态文件，需部署到Web服务器*

## 📝 日志文件位置

### 开发环境日志
```
/mnt/d/Dev/团购网/logs/
├── backend.log      # 后端开发服务日志
├── frontend.log     # 前端应用日志
└── admin-web.log    # 管理端日志
```

### 生产环境日志
```
/mnt/d/Dev/团购网/logs/prod/
└── backend-prod.log # 后端生产服务日志
```

## 🔧 依赖服务

启动前请确保以下服务正常运行：
- **MySQL 8.0** (端口: 3306)
- **Redis** (端口: 6379)

## ⚙️ 环境配置说明

### 开发环境特点
- 使用热重载，代码变更自动生效
- 详细的调试日志输出
- 支持开发工具和调试器
- Maven 直接运行，便于调试

### 生产环境特点
- 优化构建，性能最佳
- 精简日志输出
- JAR包独立运行
- 静态资源需要Web服务器

## ❗ 重要注意事项

### 文件命名规则
- **`*-dev.sh`** - 开发环境专用脚本
- **`*-prod.sh`** - 生产环境专用脚本
- **无后缀** - 通用工具脚本

### 使用建议
1. **开发阶段**: 使用 `-dev` 系列脚本
2. **测试阶段**: 使用 `-prod` 系列脚本测试生产配置
3. **部署阶段**: 使用 `-prod` 系列脚本
4. **权限设置**: 所有脚本需要执行权限 (`chmod +x *.sh`)

### 端口冲突处理
- 确保目标端口未被其他程序占用
- 开发和生产环境可以同时运行(使用不同端口)
- 使用 `./check-status.sh` 检查端口状态

## 🚨 故障排除

### 常见问题
1. **权限不足**: `chmod +x *.sh`
2. **端口占用**: `netstat -tlnp | grep :端口号`
3. **服务未启动**: 检查 MySQL 和 Redis
4. **依赖缺失**: 前端首次启动会自动安装依赖  
5. **环境混乱**: 停止所有服务后重新启动
6. **API返回HTML**: 使用 `./diagnose-api-issue.sh` 诊断代理问题
7. **Nginx配置错误**: 使用 `./nginx-manager.sh` 检查和管理服务

### 环境切换
```bash
# 从开发环境切换到生产环境
./stop-all-dev.sh      # 停止开发环境
./start-all-prod.sh    # 启动生产环境

# 从生产环境切换到开发环境  
./stop-all-prod.sh     # 停止生产环境
./start-all-dev.sh     # 启动开发环境
```

### 获取帮助
- 查看脚本执行日志
- 使用 `./check-status.sh` 检查状态
- 使用 `./logs.sh` 查看详细日志
- 使用 `./diagnose-api-issue.sh` 诊断API问题
- 使用 `./nginx-manager.sh` 管理Nginx服务
- 检查对应环境的日志目录

---

## 📊 脚本统计

- **开发环境脚本**: 6个 (完整覆盖3端开发)
- **生产环境脚本**: 5个 (生产部署优化)
- **通用工具脚本**: 2个 (状态监控+日志管理)
- **Nginx代理工具脚本**: 3个 (网络代理管理)
- **兼容脚本**: 3个 (向后兼容)
- **总计**: 19个完整脚本

## 🔍 脚本验证

所有脚本已通过完整性验证，详细清单请查看 `SCRIPT_LIST.md`

## 💡 最佳实践建议

### 开发阶段 🟢
1. **首次启动**: `./start-all-dev.sh` 
2. **日常开发**: 单独启动需要的服务
3. **调试问题**: `./check-status.sh` + `./logs.sh`
4. **结束开发**: `./stop-all-dev.sh`

### 测试阶段 🟡  
1. **模拟生产**: `./start-all-prod.sh`
2. **性能测试**: 只启动后端 `./start-backend-prod.sh`
3. **问题排查**: `./check-status.sh` + `./logs.sh`

### 生产部署 🔴
1. **正式部署**: `./start-all-prod.sh`
2. **服务重启**: `./restart-all-prod.sh` 
3. **运维监控**: 定期执行 `./check-status.sh`

## 🚀 快速上手

```bash
# 1. 进入脚本目录
cd /mnt/d/Dev/团购网/Server/sh/

# 2. 确保脚本权限
chmod +x *.sh

# 3. 开发环境一键启动
./start-all-dev.sh

# 4. 检查服务状态
./check-status.sh

# 5. 查看实时日志
./logs.sh
```

---

**创建日期**: 2025年8月1日  
**最后更新**: 2025年8月1日  
**维护者**: 开发团队  
**版本**: v2.0 - 开发/生产环境分离版本  
**状态**: ✅ 生产就绪