### 本地/远程服务端切换

1. 查看当前环境状态

```bash
node switch-env.js --status
```

2. 切换到本地开发环

```bash
node switch-env.js --local
```

3. 切换到远程生产环境

```bash
node switch-env.js --remote
```

4. 交互式选择环境

```bash
node switch-env.js
```

### 本地启动

1、启动前端：

cd D:\Dev\团购网\APP

npm run dev

2、启动Admin-API

d:\start-tgw-server.bat

3、启动管理后台Admin-Web

cd D:\Dev\团购网\Server\smart-admin\admin-web

npm run dev

### 数据库备份恢复

1. D:\db-config.bat - 配置文件 (ASCII)

- 所有注释和输出都使用英文
- 配置参数保持不变

2. D:\sync-database.bat - 主同步脚本 (ASCII)

- 完整的菜单系统
- 英文界面，避免乱码
- 所有功能保持不变

3. D:\simple-db-sync.bat - 简化版 (ASCII)

- 一键快速同步
- 纯英文输出

  🔧 使用方法

  现在可以直接在Windows CMD中运行：

  D:\sync-database.bat

  或者快速同步：

  D:\simple-db-sync.bat

### 拉取代码脚本

安全地运行脚本：

  D:\pull-smart-admin-code.bat

 优势：

1. 更安全 - 不会丢失现有文件，只覆盖相同名称的文件
2. 保留配置 - 如果你有本地配置文件，可能会被保留
3. 渐进式更新 - 新文件会添加，旧文件会更新
4. 有备份 - 仍然会创建备份目录以防需要回滚

  工作方式：

1. 先备份整个目录到带时间戳的backup目录
2. 使用xcopy /E /I /H /Y覆盖现有目录
3. 保留目录结构和权限
4. 只在主目录清理临时的checkout文件

  现在可以

### 上传代码脚本

### 编译

D:/build-complete.bat

运行编译后：

  BUILD SUCCESS - 所有模块构建成功：

- ✅ sa-parent - 父模块 (SUCCESS 0.004s)
- ✅ sa-base - 基础模块 (SUCCESS 8.251s)
- ✅ sa-admin - 主应用模块 (SUCCESS 17.092s)

  生成的JAR文件：
- 🎯 主应用JAR：tgw-pp.jar (这是可执行的Spring Boot应用)
- 📦 基础模块JAR：sa-base-dev-3.0.0.jar (依赖库)

---

### 启动

  请运行启动脚本：

  D:/start-tgw-server.bat

生产环境启动：

cd /mnt/d/Dev/团购网/Server/smart-admin && nohup java -jar admin-api/sa-admin/target/tgw-pp.jar

  🎯 预期启动结果

  启动成功后，您将看到：

1. Spring Boot启动日志
2. 数据库连接成功信息
3. Redis连接成功信息
4. 应用启动完成标志

  🌐 服务访问地址

  启动成功后可以访问：

- 后端API: http://localhost:8686
- Swagger文档: http://localhost:8686/doc.html
- 健康检查: http://localhost:8686/actuator/health

=================================

 🎉 前端配置完成！

  我已经为您完成了前端连接本地后端的配置：

  ✅ 完成的配置

1. 修改了前端代理配置：
   - 将API代理从 https://pp.oripicks.com/ 改为 http://localhost:8686
   - 优化了本地连接的请求头和错误处理
2. 创建了启动脚本：
   - D:/start-frontend.bat - 单独启动前端
   - D:/start-all-services.bat - 一键启动全套服务

### 🚀 启动完整的本地开发环境

  运行完整启动脚本：
  D:/start-all-services.bat

  这个脚本会：

1. ✅ 检查MySQL和Redis服务
2. 🚀 在新窗口启动后端服务 (8686端口)
3. 🌐 在新窗口启动前端服务 (3000端口)
4. 🔗 自动打开浏览器访问前端应用

  🌐 访问地址

- 前端应用: http://localhost:3000
- 后端API: http://localhost:8686
- Swagger文档: http://localhost:8686/doc.html

  📋 工作流程

  前端请求 → Vite代理 → 本地后端 → 本地数据库

### 🎯 切换系统

  我为您创建了一套完整的开发环境切换系统：

  📁 创建的脚本文件

1. D:/switch-backend.bat - 后端配置切换工具
2. D:/start-local-dev.bat - 本地开发环境启动
3. D:/start-remote-dev.bat - 远程开发环境启动
4. D:/start-tgw-dev.bat - 主启动菜单 ⭐

  🚀 使用方法

### 主启动菜单（推荐）：

  D:/start-tgw-dev.bat

  手动切换配置：
  D:/switch-backend.bat

  📋 功能说明

| 模式        | 后端地址                | 数据源          | 适用场景           |
| ----------- | ----------------------- | --------------- | ------------------ |
| 🏠 本地模式 | http://localhost:8686   | 本地MySQL+Redis | 开发调试、功能开发 |
| 🌐 远程模式 | https://pp.oripicks.com | 远程生产数据    | 功能测试、数据验证 |

  ⚡ 快速切换流程

1. 运行主菜单：D:/start-tgw-dev.bat
2. 选择模式：输入 1(本地) 或 2(远程)
3. 自动配置：脚本自动切换配置并启动服务
4. 访问应用：浏览器自动打开 http://localhost:3000

  💡 使用提示

- 切换配置后会自动重启前端服务
- 本地模式需要确保后端服务运行
- 远程模式直接连接生产环境，请谨慎操作
- 可随时通过主菜单切换模式

  现在您可以运行 D:/start-tgw-dev.bat 体验完整的开发环境切换功能了！
