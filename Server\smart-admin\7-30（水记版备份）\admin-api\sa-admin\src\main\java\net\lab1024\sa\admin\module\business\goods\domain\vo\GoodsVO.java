package net.lab1024.sa.admin.module.business.goods.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import net.lab1024.sa.admin.module.business.goods.constant.GoodsStatusEnum;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class GoodsVO {

    @Schema(description = "商品分类")
    private Long categoryId;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "活动详情")
    private ActivitiesVO activity;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品类型")
    private String goodsType;

    @Schema(description = "商品货币")
    private String goodsCurrency;

    @SchemaEnum(GoodsStatusEnum.class)
    private Integer goodsStatus;

    @Schema(description = "付款模式")
    private Integer payMode;

    @Schema(description = "产地")
    private String place;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品图片数组")
    private List<Map<String, Object>> images;

    @Schema(description = "详情图片数组")
    private List<Map<String, Object>> detailImages;

    @Schema(description = "商品价格")
    private String price;

    @Schema(description = "上架状态")
    private Boolean shelvesFlag;

    @Schema(description = "备注|可选")
    private String remark;

    @Schema(description = "商品id")
    private Long goodsId;

    @Schema(description = "商品分类")
    private String categoryName;

    @Schema(description = "单独购买标记")
    private Boolean aloneFlag;

    @Schema(description = "单独购买价格")
    private String alonePrice;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    private List<GoodsSkusEntity> skus;
}
