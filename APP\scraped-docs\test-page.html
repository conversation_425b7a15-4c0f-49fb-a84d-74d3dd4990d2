<!DOCTYPE html>
<html>
<head><title>ShowDoc</title></head>
<body>
<h1>测试抓取结果</h1>
<p>抓取时间: 2025-07-21T12:54:50.612Z</p>
<p>原始链接: <a href="https://www.showdoc.com.cn/item/password/qepay?page_id=7090664555712182&redirect=%2Fqepay%2F7090664555712182">https://www.showdoc.com.cn/item/password/qepay?page_id=7090664555712182&redirect=%2Fqepay%2F7090664555712182</a></p>
<hr>
<div id="app"><div data-v-4216e1c2="" class="hello"><div data-v-168051ae="" data-v-4216e1c2=""></div> <section data-v-4216e1c2="" class="el-container"><div data-v-4216e1c2="" class="el-card center-card is-always-shadow" onkeydown="if(event.keyCode==13)return false;"><!----><div class="el-card__body"><form data-v-4216e1c2="" class="el-form demo-ruleForm"><h2 data-v-4216e1c2="">请输入访问密码</h2> <div data-v-4216e1c2="" class="el-form-item el-form-item--feedback"><!----><div class="el-form-item__content" style="margin-left: 0px;"><div data-v-4216e1c2="" class="el-input"><!----><input type="password" autocomplete="off" placeholder="密码" class="el-input__inner"><!----><!----><!----><!----></div><!----></div></div> <div data-v-4216e1c2="" class="el-form-item el-form-item--feedback"><!----><div class="el-form-item__content" style="margin-left: 0px;"><div data-v-4216e1c2="" class="el-input"><!----><input type="text" autocomplete="off" placeholder="验证码" class="el-input__inner"><!----><!----><!----><!----></div> <img data-v-4216e1c2="" src="https://showdoc-server.cdn.dfyun.com.cn/server/index.php?s=/api/common/showCaptcha&amp;captcha_id=8717581&amp;1753102489000" class="v_code_img"><!----></div></div> <div data-v-4216e1c2="" class="el-form-item el-form-item--feedback"><!----><div class="el-form-item__content" style="margin-left: 0px;"><button data-v-4216e1c2="" type="button" class="el-button el-button--primary" style="width: 100%;"><!----><!----><span>提交</span></button><!----></div></div> <div data-v-4216e1c2="" class="el-form-item el-form-item--feedback"><!----><div class="el-form-item__content" style="margin-left: 0px;"><a data-v-4216e1c2="" href="/user/login?redirect=%2Fqepay%2F7090664555712182" class="">登录</a>&nbsp;&nbsp;&nbsp;
        <!----></div></div></form></div></div></section> <div data-v-065ddbfc="" data-v-4216e1c2=""></div></div></div><div class="index-item-block"><div>INDEX_HTML</div><div>ITEM_HTML</div></div><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/manifest.763aada0159bf6d465bd.js"></script><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/vendor.e06a04bea902a8c6f95c.js"></script><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/app.8efb3b750dc5c05a073d.js"></script><script>// CDN容灾
  if (typeof window.SHOWDOC_CDN_STASTUS == 'undefined') {
    // 把全局变量中的静态资源路径给改一下
    window.DocConfig.staticPath = '/static/'
    changeCDNToRoot()
    setTimeout(() => {
      changeCDNToRoot()
    }, 500)
}
  // 修改js路径到Root路径，不使用cdn
  function changeCDNToRoot() {
    const linkTags = document.getElementsByTagName('link') // 获取所有 link 标签
    const scriptTags = document.getElementsByTagName('script') // 获取所有 script 标签
    const imgTags = document.getElementsByTagName('img') // 获取所有 img 标签

    const loadScript = src => {
      return new Promise((resolve, reject) => {
        const scriptTag = document.createElement('script')
        scriptTag.src = src
        scriptTag.onload = resolve
        scriptTag.onerror = reject
        document.head.appendChild(scriptTag)
      })
    }

    const loadStylesheet = href => {
      return new Promise((resolve, reject) => {
        const linkTag = document.createElement('link')
        linkTag.href = href
        linkTag.rel = 'stylesheet'
        linkTag.onload = resolve
        linkTag.onerror = reject
        document.head.appendChild(linkTag)
      })
    }

    const updateLinkTags = async () => {
      for (let i = 0; i < linkTags.length; i++) {
        const linkTag = linkTags[i]
        const href = linkTag.getAttribute('href')

        if (href && href.includes('dfyun')) {
          const newHref = href.replace(
            'https://showdoc.cdn.dfyun.com.cn/',
            '/'
          )
          await loadStylesheet(newHref)
          linkTag.parentNode.removeChild(linkTag)
        }
      }
    }

    const updateScriptTags = async () => {
      for (let i = 0; i < scriptTags.length; i++) {
        const scriptTag = scriptTags[i]
        const src = scriptTag.getAttribute('src')

        if (src && src.includes('dfyun') && !src.includes('showdoc-server')) {
          const newSrc = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')
          await loadScript(newSrc)
          scriptTag.parentNode.removeChild(scriptTag)
        }
      }
    }

    const updateImgTags = () => {
      for (let i = 0; i < imgTags.length; i++) {
        const imgTag = imgTags[i]
        let src = imgTag.getAttribute('src')

        if (src && src.includes('dfyun')) {
          src = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')
          imgTag.setAttribute('src', src)
        }
      }
    }

    updateLinkTags()
      .then(() => {
        updateScriptTags()
          .then(() => {
            console.log('All scripts loaded successfully')
            updateImgTags()
          })
          .catch(error => {
            console.error('Error loading scripts:', error)
          })
      })
      .catch(error => {
        console.error('Error loading stylesheets:', error)
      })
  }</script><script language="JavaScript">// 防止被镜像站
  var host = window.location.host
  if (
    host.indexOf('localhost') === -1 &&
    host.indexOf('wu') === -1 &&
    host.indexOf('showdoc') === -1 &&
    host.indexOf('star7th.com') === -1 &&
    host.indexOf('192.168.') === -1 &&
    host.indexOf('gaoyixia.com') === -1 &&
    host.indexOf('dongjingyu.cn') === -1 &&
    host.indexOf('127.0.0.1') === -1
  ) {
    var href = window.location.href
    var j = href.replace(new RegExp(host, 'g'), 'www.showdoc.com.cn')
    window.location.href = j
  }</script><script>// 百度统计
  if (window.location.host == 'www.showdoc.com.cn') {
    var _hmt = _hmt || []
    ;(function() {
      var hm = document.createElement('script')
      hm.src = 'https://hm.baidu.com/hm.js?84e82fa31c8ca8671f6b6f972b7e54fb'
      var s = document.getElementsByTagName('script')[0]
      s.parentNode.insertBefore(hm, s)
    })()
  }</script>
</body>
</html>