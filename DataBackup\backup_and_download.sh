#!/bin/bash

# MySQL 数据库备份和下载脚本
# 服务器IP: *************
# 使用方法: ./backup_and_download.sh [数据库名] [本地保存目录]

# 配置参数
SERVER_IP="*************"
SERVER_USER="root"
REMOTE_BACKUP_DIR="/root"
DEFAULT_LOCAL_DIR="DataBackup"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "MySQL 数据库备份和下载脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [数据库名] [本地保存目录]"
    echo ""
    echo "参数:"
    echo "  数据库名        要备份的数据库名称 (默认: tgw_pp)"
    echo "  本地保存目录    备份文件保存的本地目录 (默认: $DEFAULT_LOCAL_DIR)"
    echo ""
    echo "示例:"
    echo "  $0                           # 备份 tgw_pp 到默认目录"
    echo "  $0 mydb                      # 备份 mydb 到默认目录"
    echo "  $0 mydb /mnt/d/backups       # 备份 mydb 到指定目录"
    echo ""
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 设置参数
DATABASE_NAME=${1:-"tgw_pp"}
LOCAL_DIR=${2:-"$DEFAULT_LOCAL_DIR"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_backup_${TIMESTAMP}.sql"
REMOTE_BACKUP_PATH="${REMOTE_BACKUP_DIR}/${BACKUP_FILENAME}"

print_info "开始备份数据库: $DATABASE_NAME"
print_info "服务器: $SERVER_IP"
print_info "备份文件名: $BACKUP_FILENAME"
print_info "本地保存目录: $LOCAL_DIR"

# 创建本地目录
if [ ! -d "$LOCAL_DIR" ]; then
    print_info "创建本地目录: $LOCAL_DIR"
    mkdir -p "$LOCAL_DIR"
fi

# 步骤1: 在远程服务器上备份数据库
print_info "步骤1: 在服务器上备份数据库..."
ssh $SERVER_USER@$SERVER_IP "mysqldump -u root -p $DATABASE_NAME > $REMOTE_BACKUP_PATH"

if [ $? -ne 0 ]; then
    print_error "数据库备份失败！"
    exit 1
fi

# 步骤2: 检查远程备份文件
print_info "步骤2: 检查远程备份文件..."
REMOTE_FILE_SIZE=$(ssh $SERVER_USER@$SERVER_IP "ls -lh $REMOTE_BACKUP_PATH | awk '{print \$5}'")

if [ -z "$REMOTE_FILE_SIZE" ]; then
    print_error "远程备份文件不存在！"
    exit 1
fi

print_info "远程备份文件大小: $REMOTE_FILE_SIZE"

# 步骤3: 下载备份文件到本地
print_info "步骤3: 下载备份文件到本地..."
scp $SERVER_USER@$SERVER_IP:$REMOTE_BACKUP_PATH "$LOCAL_DIR/"

if [ $? -ne 0 ]; then
    print_error "文件下载失败！"
    exit 1
fi

# 步骤4: 验证本地文件
LOCAL_FILE_PATH="$LOCAL_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    LOCAL_FILE_SIZE=$(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')
    print_info "本地文件大小: $LOCAL_FILE_SIZE"
    print_info "文件下载成功: $LOCAL_FILE_PATH"
else
    print_error "本地文件不存在！"
    exit 1
fi

# 步骤5: 清理远程备份文件（可选）
read -p "是否删除服务器上的备份文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "步骤5: 清理远程备份文件..."
    ssh $SERVER_USER@$SERVER_IP "rm -f $REMOTE_BACKUP_PATH"
    print_info "远程备份文件已删除"
else
    print_warning "远程备份文件保留在: $REMOTE_BACKUP_PATH"
fi

print_info "备份和下载完成！"
print_info "本地备份文件: $LOCAL_FILE_PATH"

# 显示文件信息
echo ""
echo "=== 备份文件信息 ==="
ls -lh "$LOCAL_FILE_PATH"
echo ""
echo "=== 最近的备份文件 ==="
ls -lt "$LOCAL_DIR"/*.sql 2>/dev/null | head -5
