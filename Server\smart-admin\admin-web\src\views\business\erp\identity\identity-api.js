/*
 * 实名认证管理 API 接口
 * Created: 2025-08-11
 */
import { postRequest, getRequest } from '/@/lib/axios'

export const identityApi = {
  
  /**
   * 分页查询实名认证列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPage: (params) => {
    return postRequest('/admin/identity-verification/page', params)
  },

  /**
   * 获取实名认证详情
   * @param {number} id - 认证ID
   * @returns {Promise}
   */
  getDetail: (id) => {
    return getRequest(`/admin/identity-verification/detail/${id}`)
  },

  /**
   * 审核通过
   * @param {number} id - 认证ID
   * @param {Object} params - 审核参数
   * @returns {Promise}
   */
  approve: (id, params = {}) => {
    return postRequest('/admin/identity-verification/audit', {
      verificationId: id,
      auditResult: 'approved',
      auditRemark: params.remark || ''
    })
  },

  /**
   * 审核拒绝
   * @param {number} id - 认证ID
   * @param {Object} params - 审核参数（包含拒绝原因）
   * @returns {Promise}
   */
  reject: (id, params) => {
    return postRequest('/admin/identity-verification/audit', {
      verificationId: id,
      auditResult: 'rejected',
      rejectReason: params.reason || '',
      auditRemark: params.remark || ''
    })
  },

  /**
   * 批量审核通过
   * @param {Array} ids - 认证ID数组
   * @param {Object} params - 审核参数
   * @returns {Promise}
   */
  batchApprove: (ids, params = {}) => {
    const auditForms = ids.map(id => ({
      verificationId: id,
      auditResult: 'approved',
      auditRemark: params.remark || ''
    }))
    return postRequest('/admin/identity-verification/batch-audit', auditForms)
  },

  /**
   * 批量审核拒绝
   * @param {Array} ids - 认证ID数组
   * @param {Object} params - 审核参数（包含拒绝原因）
   * @returns {Promise}
   */
  batchReject: (ids, params) => {
    const auditForms = ids.map(id => ({
      verificationId: id,
      auditResult: 'rejected',
      rejectReason: params.reason || '',
      auditRemark: params.remark || ''
    }))
    return postRequest('/admin/identity-verification/batch-audit', auditForms)
  },

  /**
   * 导出实名认证数据
   * @param {Object} params - 查询条件
   * @returns {Promise}
   */
  exportData: (params) => {
    return postRequest('/admin/identity-verification/export', params, {
      responseType: 'blob',
      timeout: 60000 // 60秒超时
    }).then((response) => {
      // 处理文件下载
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `实名认证数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    })
  },

  /**
   * 获取统计信息
   * @returns {Promise}
   */
  getStatistics: () => {
    return getRequest('/admin/identity-verification/statistics')
  },

  /**
   * 获取审核历史记录
   * @param {number} id - 认证ID
   * @returns {Promise}
   */
  getReviewHistory: (id) => {
    return getRequest(`/admin/identity-verification/audit-log/${id}`)
  }
}