package net.lab1024.sa.admin.module.business.activities.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;

@Data
public class ActivitiesVO {

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "活动类型")
    private String type;

    @Schema(description = "活动状态")
    private Integer status;

    @Schema(description = "返还比例%")
    private Integer returnRatio;

    @Schema(description = "活动配置参数")
    private Map<String, Object> configInfo;

    @Schema(description = "必不中开关")
    private Integer forceLossFlag;

    @Schema(description = "参与人数限制")
    private Integer participantLimit;

    @Schema(description = "当前参与人数")
    private Integer currentParticipants;

    @Schema(description = "活动开始时间")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    @Schema(description = "开奖计时")
    private Integer remainingTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
