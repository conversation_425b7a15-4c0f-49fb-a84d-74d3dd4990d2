package net.lab1024.sa.admin.module.business.goods.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class GoodsSkusVO {
    @Schema(description = "SkuId")
    private Long id;

    @Schema(description = "商品分类ID")
    private Long categoryId;

    @Schema(description = "商品分类名")
    private String categoryName;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品类型")
    private String goodsType;

    @Schema(description = "商品货币")
    private String goodsCurrency;

    @Schema(description = "付款模式")
    private Integer payMode;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "商品价格")
    private String price;

    @Schema(description = "单独购买价格")
    private BigDecimal alonePrice;

    @Schema(description = "单独购买标记")
    private Boolean aloneFlag;

    @Schema(description = "商品原价")
    private String originalPrice;

    @Schema(description = "上架状态")
    private Boolean shelvesFlag;

    @Schema(description = "备注|可选")
    private String remark;

    @Schema(description = "商品id")
    private Long goodsId;

    @Schema(description = "状态")
    private Integer goodsStatus;

    @Schema(description = "销售数量")
    private Integer salesCount;

    private LocalDateTime createTime;
}
