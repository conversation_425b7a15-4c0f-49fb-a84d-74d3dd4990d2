# 🚀 快速启动指南

## 一键启动（推荐）

### Windows系统
双击运行 `start-dev-mock.bat` 文件

### Mac/Linux系统
在终端运行：
```bash
chmod +x start-dev-mock.sh
./start-dev-mock.sh
```

## 手动启动

### 1. 安装依赖
```bash
cd APP
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 打开浏览器
访问：http://localhost:3000/login

### 4. 启用Mock模式
- 点击右下角紫色齿轮图标
- 勾选"启用Mock模式"

## 测试账号

| 账号 | 密码 | 说明 |
|------|------|------|
| +84123456789 | 123456 | 老用户，余额1,500,000₫ |
| +84987654321 | password123 | 新用户，余额50,000₫ |

## 可用页面

- 登录页面：http://localhost:3000/login
- Mock测试页面：http://localhost:3000/mock-test

## 调试工具

- **F12**: 浏览器开发者工具
- **右下角齿轮**: Mock调试面板
- **控制台**: 查看验证码和API日志

## 常见问题

### 端口被占用
```bash
npm run dev -- --port 3001
```

### 依赖安装失败
```bash
npm cache clean --force
npm install --registry=https://registry.npmmirror.com
```

---

**启动成功后，就可以开始调试Mock数据了！** 🎉 