#!/bin/bash

echo "🔍 系统服务状态检查"
echo "======================"

# 检查MySQL
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL: 运行中"
else
    echo "❌ MySQL: 未运行"
fi

# 检查Redis
if systemctl is-active --quiet redis-server; then
    echo "✅ Redis: 运行中"
else
    echo "❌ Redis: 未运行"
fi

# 检查后端端口
if netstat -tlnp 2>/dev/null | grep -q :8686; then
    echo "✅ 后端API (8686): 运行中"
    # 尝试检查健康状态
    if command -v curl >/dev/null 2>&1; then
        HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8686/actuator/health 2>/dev/null || echo "000")
        if [ "$HEALTH_CHECK" = "200" ]; then
            echo "   🩺 健康检查: 正常"
        else
            echo "   ⚠️  健康检查: 异常 (HTTP $HEALTH_CHECK)"
        fi
    fi
else
    echo "❌ 后端API (8686): 未运行"
fi

# 检查前端端口
if netstat -tlnp 2>/dev/null | grep -q :3000; then
    echo "✅ 前端应用 (3000): 运行中"
else
    echo "❌ 前端应用 (3000): 未运行"
fi

# 检查管理端端口
if netstat -tlnp 2>/dev/null | grep -q :3001; then
    echo "✅ 管理后台 (3001): 运行中"
else
    echo "❌ 管理后台 (3001): 未运行"
fi

echo ""
echo "📊 进程状态："

# 检查后端进程
BACKEND_PROCESSES=$(ps aux | grep -E "(spring-boot:run|tgw-pp.jar)" | grep -v grep | wc -l)
if [ $BACKEND_PROCESSES -gt 0 ]; then
    echo "   🚀 后端进程: $BACKEND_PROCESSES 个"
    ps aux | grep -E "(spring-boot:run|tgw-pp.jar)" | grep -v grep | awk '{print "      PID: " $2 " - " $11 " " $12}'
else
    echo "   ❌ 后端进程: 无"
fi

# 检查前端进程
FRONTEND_PROCESSES=$(ps aux | grep -E "npm.*dev|vite" | grep -v grep | wc -l)
if [ $FRONTEND_PROCESSES -gt 0 ]; then
    echo "   🌐 前端进程: $FRONTEND_PROCESSES 个"
else
    echo "   ❌ 前端进程: 无"
fi

echo ""
echo "💾 内存使用："
echo "   系统内存: $(free -h | awk 'NR==2{printf "已用: %s / 总共: %s (%.1f%%)", $3, $2, $3/$2*100}')"

echo ""
echo "🌐 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8686"
echo "   管理后台: http://localhost:3001"
echo "   API文档:  http://localhost:8686/doc.html"

echo ""
echo "📝 日志文件："
PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs"
if [ -d "$LOG_DIR" ]; then
    echo "   日志目录: $LOG_DIR"
    if [ -f "$LOG_DIR/backend.log" ]; then
        BACKEND_LOG_SIZE=$(du -h "$LOG_DIR/backend.log" | cut -f1)
        echo "   后端日志: backend.log ($BACKEND_LOG_SIZE)"
    fi
    if [ -f "$LOG_DIR/frontend.log" ]; then
        FRONTEND_LOG_SIZE=$(du -h "$LOG_DIR/frontend.log" | cut -f1)
        echo "   前端日志: frontend.log ($FRONTEND_LOG_SIZE)"
    fi
    if [ -f "$LOG_DIR/admin-web.log" ]; then
        ADMIN_LOG_SIZE=$(du -h "$LOG_DIR/admin-web.log" | cut -f1)
        echo "   管理端日志: admin-web.log ($ADMIN_LOG_SIZE)"
    fi
else
    echo "   日志目录: 未创建"
fi