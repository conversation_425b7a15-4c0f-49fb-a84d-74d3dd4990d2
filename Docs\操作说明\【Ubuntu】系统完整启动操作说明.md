# Ubuntu 系统完整启动操作说明

本文档介绍如何在 Ubuntu 系统下启动社交拼团APP的前端、后端和管理端服务。

## 📋 项目架构概览

### 系统组成
- **前端 (APP)**: Vue 3 + Vite 移动端应用 (端口: 3000)
- **后端 (Admin-API)**: Spring Boot 3 + MySQL + Redis API服务 (端口: 8686)
- **管理端 (Admin-Web)**: Vue 3 + Ant Design 管理后台 (端口: 3001)

### 目录结构
```
/mnt/d/Dev/团购网/
├── APP/                    # 前端移动应用
├── Server/smart-admin/     # 后端服务
│   ├── admin-api/          # Spring Boot API
│   └── admin-web/          # Vue管理后台
└── Docs/                   # 文档目录
```

## 🔧 环境准备

### 必要软件安装

#### 1. Java 17 (后端必需)
```bash
# 安装OpenJDK 17
sudo apt update
sudo apt install openjdk-17-jdk -y

# 验证安装
java -version
javac -version

# 设置JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### 2. Maven 3.8+ (后端构建)
```bash
# 安装Maven
sudo apt install maven -y

# 验证安装
mvn -version
```

#### 3. Node.js 18+ 和 npm (前端必需)
```bash
# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 4. MySQL 8.0 (数据库)
```bash
# 安装MySQL服务器
sudo apt install mysql-server -y

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 验证服务状态
sudo systemctl status mysql
```

#### 5. Redis (缓存服务)
```bash
# 安装Redis
sudo apt install redis-server -y

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 验证服务状态
sudo systemctl status redis-server

# 测试连接
redis-cli ping
```

### 数据库配置

#### MySQL配置
```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库
CREATE DATABASE IF NOT EXISTS tgw_pp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户并授权
CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY 'kzka20220726';
GRANT ALL PRIVILEGES ON tgw_pp.* TO 'root'@'localhost';
FLUSH PRIVILEGES;

# 退出MySQL
exit;
```

#### Redis配置
```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 设置密码（可选）
# requirepass kzka20220726

# 重启Redis服务
sudo systemctl restart redis-server
```

## 🚀 服务启动步骤

### 第一步：后端服务启动

#### 1. 进入后端目录
```bash
cd /mnt/d/Dev/团购网/Server/smart-admin
```

#### 2. 编译项目
```bash
# 编译整个项目
mvn clean compile -DskipTests

# 或者完整构建
mvn clean package -DskipTests
```

#### 3. 启动后端API服务
```bash
# 方法1: 使用Maven直接运行 (推荐开发环境)
cd admin-api/sa-admin
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 方法2: 使用JAR包运行 (推荐生产环境)
java -jar admin-api/sa-admin/target/tgw-pp.jar --spring.profiles.active=dev

# 方法3: 后台运行
nohup java -jar admin-api/sa-admin/target/tgw-pp.jar --spring.profiles.active=dev > backend.log 2>&1 &
```

#### 4. 验证后端启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8686

# 测试API接口
curl http://localhost:8686/actuator/health

# 访问Swagger文档
# 浏览器打开: http://localhost:8686/doc.html
```

### 第二步：前端应用启动

#### 1. 进入前端目录
```bash
cd /mnt/d/Dev/团购网/APP
```

#### 2. 安装依赖 (首次运行)
```bash
# 安装项目依赖
npm install

# 如果网络较慢，可使用淘宝镜像
npm install --registry=https://registry.npmmirror.com
```

#### 3. 启动前端开发服务器
```bash
# 开发模式启动 (连接本地后端)
npm run dev

# 连接真实API环境
npm run dev:real

# 指定端口启动
npm run dev -- --port 3000
```

#### 4. 验证前端启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3000

# 访问前端应用
# 浏览器打开: http://localhost:3000
```

### 第三步：管理端启动

#### 1. 进入管理端目录
```bash
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web
```

#### 2. 安装依赖 (首次运行)
```bash
# 安装项目依赖
npm install

# 如果网络较慢，可使用淘宝镜像
npm install --registry=https://registry.npmmirror.com
```

#### 3. 启动管理端开发服务器
```bash
# 开发模式启动
npm run dev

# 指定端口启动 (避免与前端冲突)
npm run dev -- --port 3001
```

#### 4. 验证管理端启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3001

# 访问管理后台
# 浏览器打开: http://localhost:3001
```

## 📝 启动脚本

为了简化启动过程，可以创建以下启动脚本：

### 创建后端启动脚本
```bash
# 创建启动脚本
cat > /mnt/d/Dev/团购网/start-backend.sh << 'EOF'
#!/bin/bash

echo "🚀 启动后端服务..."

# 检查MySQL和Redis服务
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "❌ MySQL服务未运行，正在启动..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "❌ Redis服务未运行，正在启动..."
    sudo systemctl start redis-server
fi

# 进入后端目录
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin

# 检查是否已编译
if [ ! -f "target/tgw-pp.jar" ]; then
    echo "📦 编译后端项目..."
    cd ../..
    mvn clean package -DskipTests
    cd admin-api/sa-admin
fi

# 启动后端服务
echo "🌟 启动后端API服务..."
mvn spring-boot:run -Dspring-boot.run.profiles=dev

EOF

# 添加执行权限
chmod +x /mnt/d/Dev/团购网/start-backend.sh
```

### 创建前端启动脚本
```bash
# 创建启动脚本
cat > /mnt/d/Dev/团购网/start-frontend.sh << 'EOF'
#!/bin/bash

echo "🌐 启动前端应用..."

# 进入前端目录
cd /mnt/d/Dev/团购网/APP

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端服务
echo "🌟 启动前端开发服务器..."
npm run dev

EOF

# 添加执行权限
chmod +x /mnt/d/Dev/团购网/start-frontend.sh
```

### 创建管理端启动脚本
```bash
# 创建启动脚本
cat > /mnt/d/Dev/团购网/start-admin-web.sh << 'EOF'
#!/bin/bash

echo "🔧 启动管理端..."

# 进入管理端目录
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装管理端依赖..."
    npm install
fi

# 启动管理端服务
echo "🌟 启动管理端开发服务器..."
npm run dev -- --port 3001

EOF

# 添加执行权限
chmod +x /mnt/d/Dev/团购网/start-admin-web.sh
```

### 创建一键启动脚本
```bash
# 创建一键启动脚本
cat > /mnt/d/Dev/团购网/start-all.sh << 'EOF'
#!/bin/bash

echo "🎯 启动完整开发环境..."

# 创建日志目录
mkdir -p logs

# 检查并启动MySQL和Redis
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "🔄 启动MySQL服务..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "🔄 启动Redis服务..."
    sudo systemctl start redis-server
fi

# 启动后端服务（后台运行）
echo "🚀 启动后端服务..."
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > ../../../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动完成
echo "⏳ 等待后端服务启动完成..."
sleep 30

# 启动前端应用（后台运行）
echo "🌐 启动前端应用..."
cd /mnt/d/Dev/团购网/APP
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "✅ 前端应用已启动 (PID: $FRONTEND_PID)"

# 启动管理端（后台运行）
echo "🔧 启动管理端..."
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web
nohup npm run dev -- --port 3001 > ../../logs/admin-web.log 2>&1 &
ADMIN_PID=$!
echo "✅ 管理端已启动 (PID: $ADMIN_PID)"

# 保存PID到文件
cd /mnt/d/Dev/团购网
echo $BACKEND_PID > logs/backend.pid
echo $FRONTEND_PID > logs/frontend.pid
echo $ADMIN_PID > logs/admin-web.pid

echo ""
echo "🎉 所有服务启动完成！"
echo ""
echo "📍 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8686"
echo "   管理后台: http://localhost:3001"
echo "   API文档:  http://localhost:8686/doc.html"
echo ""
echo "📝 日志文件："
echo "   后端日志: logs/backend.log"
echo "   前端日志: logs/frontend.log"
echo "   管理端日志: logs/admin-web.log"
echo ""
echo "🛑 停止所有服务请运行: ./stop-all.sh"

EOF

# 添加执行权限
chmod +x /mnt/d/Dev/团购网/start-all.sh
```

### 创建停止脚本
```bash
# 创建停止脚本
cat > /mnt/d/Dev/团购网/stop-all.sh << 'EOF'
#!/bin/bash

echo "🛑 停止所有服务..."

cd /mnt/d/Dev/团购网

# 停止后端服务
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止 (PID: $BACKEND_PID)"
    fi
    rm -f logs/backend.pid
fi

# 停止前端服务
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止 (PID: $FRONTEND_PID)"
    fi
    rm -f logs/frontend.pid
fi

# 停止管理端服务
if [ -f "logs/admin-web.pid" ]; then
    ADMIN_PID=$(cat logs/admin-web.pid)
    if kill -0 $ADMIN_PID 2>/dev/null; then
        kill $ADMIN_PID
        echo "✅ 管理端已停止 (PID: $ADMIN_PID)"
    fi
    rm -f logs/admin-web.pid
fi

# 强制停止相关进程（备用方案）
echo "🔍 检查残留进程..."
pkill -f "spring-boot:run"
pkill -f "vite"
pkill -f "node.*vite"

echo "🎉 所有服务已停止！"

EOF

# 添加执行权限
chmod +x /mnt/d/Dev/团购网/stop-all.sh
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 端口被占用
```bash
# 查看端口占用情况
sudo netstat -tlnp | grep :8686  # 后端
sudo netstat -tlnp | grep :3000  # 前端
sudo netstat -tlnp | grep :3001  # 管理端

# 杀死占用端口的进程
sudo kill -9 <PID>
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 重启MySQL服务
sudo systemctl restart mysql

# 测试数据库连接
mysql -u root -pkzka20220726 -h localhost -P 3306 -e "SELECT 1"
```

#### 3. Redis连接失败
```bash
# 检查Redis服务状态
sudo systemctl status redis-server

# 重启Redis服务
sudo systemctl restart redis-server

# 测试Redis连接
redis-cli ping
```

#### 4. Java版本问题
```bash
# 检查Java版本
java -version

# 切换Java版本 (如果安装了多个版本)
sudo update-alternatives --config java
```

#### 5. Maven构建失败
```bash
# 清理Maven缓存
mvn clean

# 强制重新下载依赖
mvn clean compile -U

# 跳过测试重新构建
mvn clean package -DskipTests
```

#### 6. NPM安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 使用淘宝镜像
npm install --registry=https://registry.npmmirror.com
```

### 日志查看

#### 实时查看日志
```bash
# 后端日志
tail -f /mnt/d/Dev/团购网/logs/backend.log

# 前端日志
tail -f /mnt/d/Dev/团购网/logs/frontend.log

# 管理端日志
tail -f /mnt/d/Dev/团购网/logs/admin-web.log
```

#### 系统服务日志
```bash
# 查看MySQL日志
sudo journalctl -u mysql -f

# 查看Redis日志
sudo journalctl -u redis-server -f
```

## ⚡ 快速启动命令

### 一键启动所有服务
```bash
cd /mnt/d/Dev/团购网
./start-all.sh
```

### 一键停止所有服务
```bash
cd /mnt/d/Dev/团购网
./stop-all.sh
```

### 单独启动服务
```bash
# 启动后端
./start-backend.sh

# 启动前端
./start-frontend.sh

# 启动管理端
./start-admin-web.sh
```

## 📊 系统监控

### 服务状态检查
```bash
# 创建状态检查脚本
cat > /mnt/d/Dev/团购网/check-status.sh << 'EOF'
#!/bin/bash

echo "🔍 系统服务状态检查"
echo "======================"

# 检查MySQL
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL: 运行中"
else
    echo "❌ MySQL: 未运行"
fi

# 检查Redis
if systemctl is-active --quiet redis-server; then
    echo "✅ Redis: 运行中"
else
    echo "❌ Redis: 未运行"
fi

# 检查后端端口
if netstat -tlnp | grep -q :8686; then
    echo "✅ 后端API (8686): 运行中"
else
    echo "❌ 后端API (8686): 未运行"
fi

# 检查前端端口
if netstat -tlnp | grep -q :3000; then
    echo "✅ 前端应用 (3000): 运行中"
else
    echo "❌ 前端应用 (3000): 未运行"
fi

# 检查管理端端口
if netstat -tlnp | grep -q :3001; then
    echo "✅ 管理后台 (3001): 运行中"
else
    echo "❌ 管理后台 (3001): 未运行"
fi

echo ""
echo "🌐 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8686"
echo "   管理后台: http://localhost:3001"
echo "   API文档:  http://localhost:8686/doc.html"

EOF

chmod +x /mnt/d/Dev/团购网/check-status.sh
```

### 使用状态检查
```bash
cd /mnt/d/Dev/团购网
./check-status.sh
```

## 📅 定期维护

### 日志清理
```bash
# 创建日志清理脚本
cat > /mnt/d/Dev/团购网/clean-logs.sh << 'EOF'
#!/bin/bash

echo "🧹 清理日志文件..."

cd /mnt/d/Dev/团购网

# 备份重要日志
if [ -f "logs/backend.log" ]; then
    cp logs/backend.log logs/backend.log.bak
fi

# 清理日志文件
> logs/backend.log
> logs/frontend.log  
> logs/admin-web.log

echo "✅ 日志文件已清理完成"

EOF

chmod +x /mnt/d/Dev/团购网/clean-logs.sh
```

### 数据库备份
```bash
# 创建数据库备份脚本
cat > /mnt/d/Dev/团购网/backup-database.sh << 'EOF'
#!/bin/bash

echo "💾 备份数据库..."

BACKUP_DIR="/mnt/d/Dev/团购网/backups"
mkdir -p $BACKUP_DIR

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/tgw_pp_backup_$TIMESTAMP.sql"

mysqldump -u root -pkzka20220726 --single-transaction --routines --triggers tgw_pp > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份成功: $BACKUP_FILE"
    
    # 保留最近10个备份文件
    cd $BACKUP_DIR
    ls -t tgw_pp_backup_*.sql | tail -n +11 | xargs -r rm
    
    echo "📝 当前备份文件："
    ls -lh tgw_pp_backup_*.sql
else
    echo "❌ 数据库备份失败"
fi

EOF

chmod +x /mnt/d/Dev/团购网/backup-database.sh
```

## 🚀 生产环境部署

### 编译生产版本
```bash
# 后端生产构建
cd /mnt/d/Dev/团购网/Server/smart-admin
mvn clean package -Pprod -DskipTests

# 前端生产构建
cd /mnt/d/Dev/团购网/APP
npm run build

# 管理端生产构建
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web
npm run build:prod
```

### 生产环境启动
```bash
# 后端生产启动
nohup java -jar /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin/target/tgw-pp.jar --spring.profiles.active=prod > /var/log/tgw-backend.log 2>&1 &

# 前端使用Nginx部署
sudo cp -r /mnt/d/Dev/团购网/APP/dist/* /var/www/html/

# 管理端使用Nginx部署  
sudo cp -r /mnt/d/Dev/团购网/Server/smart-admin/admin-web/dist/* /var/www/html/admin/
```

---

## 📝 总结

本文档提供了在Ubuntu系统下完整启动社交拼团APP系统的详细操作说明，包括：

1. **环境准备**: Java、Node.js、MySQL、Redis等必要软件安装
2. **数据库配置**: MySQL和Redis的基础配置
3. **服务启动**: 后端、前端、管理端的详细启动步骤
4. **启动脚本**: 一键启动和停止的便捷脚本
5. **故障排除**: 常见问题的解决方案
6. **系统监控**: 服务状态检查和日志查看
7. **定期维护**: 日志清理和数据库备份
8. **生产部署**: 生产环境的部署指导

使用本文档，开发人员可以快速在Ubuntu环境下搭建完整的开发环境。

**联系方式**: 如有问题请联系开发团队

**最后更新**: 2025年8月1日