package net.lab1024.sa.admin.module.business.identity.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实名认证实体类
 *
 * <AUTHOR>
 * @Date 2025-01-27
 */
@Data
@TableName("t_identity_verification")
public class IdentityVerificationEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证正面照片路径
     */
    private String idCardFrontUrl;

    /**
     * 身份证背面照片路径
     */
    private String idCardBackUrl;

    /**
     * 手持身份证照片路径
     */
    private String idCardHandheldUrl;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 银行开户姓名
     */
    private String bankAccountName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 开户行地址
     */
    private String bankAddress;

    /**
     * 银行预留手机号
     */
    private String bankPhone;

    /**
     * 认证状态：not_submitted-未提交, pending-待审核, approved-已通过, rejected-已拒绝
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 审核结果：approved-通过, rejected-拒绝
     */
    private String auditResult;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最后重试时间
     */
    private LocalDateTime lastRetryTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}