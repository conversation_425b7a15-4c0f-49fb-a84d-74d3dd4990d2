# 社交拼团APP数据库设计文档

| 版本   | 日期       | 作者   | 审核人 | 变更描述                                                     |
| :----- | :--------- | :----- | :----- | :----------------------------------------------------------- |
| V1.0 | 2025-06-25 | Gemini |        | 基于V2.2.2版APP PRD、V1.0版后台PRD及V1.2版页面设计文档创建数据库设计 |

---

## 1. 引言

### 1.1. 文档目的
本文档为社交拼团APP系统提供完整的数据库设计方案，包括实体关系图(ERD)、表结构详述、索引设计等。遵循第三范式设计原则，确保数据一致性、可扩展性和可追溯性。

### 1.2. 设计原则
- **数据完整性**：通过外键约束确保数据关联的正确性
- **可追溯性**：所有资金变动和关键操作均有详细日志记录
- **可扩展性**：为未来功能预留扩展空间
- **性能优化**：合理设计索引，支持高并发查询

---

## 2. 数据库架构总览

### 2.1. 核心业务模块
- **用户管理模块**：用户信息、钱包、地址管理
- **商品管理模块**：商品信息、SKU、分类管理
- **活动管理模块**：抽奖活动、营销配置
- **订单管理模块**：订单流程、支付、物流
- **裂变系统模块**：邀请关系、返佣记录
- **财务管理模块**：资金流水、提现管理
- **风控系统模块**：用户策略、风控日志
- **后台管理模块**：管理员、权限、操作日志
- **内容管理模块**：Banner、弹窗、公告

### 2.2. 实体关系图 (ERD)

```mermaid
erDiagram
    %% 用户相关
    users {
        bigint id PK
        varchar phone_number UK
        varchar password_hash
        varchar nickname
        varchar avatar_url
        bigint inviter_id FK
        enum status
        enum risk_level
        timestamp created_at
        timestamp updated_at
    }

    wallets {
        bigint user_id PK,FK
        decimal balance
        decimal experience_balance
        bigint points
        timestamp created_at
        timestamp updated_at
    }

    wallet_transactions {
        bigint id PK
        bigint user_id FK
        decimal amount
        decimal balance_after
        enum type
        bigint related_id
        varchar description
        timestamp created_at
    }

    user_addresses {
        bigint id PK
        bigint user_id FK
        varchar recipient_name
        varchar phone_number
        varchar address_line
        boolean is_default
        timestamp created_at
    }

    %% 商品相关
    product_categories {
        int id PK
        varchar name
        varchar icon_url
        int sort_order
        enum status
        timestamp created_at
    }

    products {
        bigint id PK
        varchar name
        text description
        int category_id FK
        json images
        enum status
        timestamp created_at
        timestamp updated_at
    }

    product_skus {
        bigint id PK
        bigint product_id FK
        varchar sku_code UK
        json attributes
        decimal price
        int stock
        enum status
        timestamp created_at
    }

    %% 活动相关
    activities {
        bigint id PK
        bigint product_sku_id FK
        varchar name
        text description
        enum type
        enum status
        json config
        boolean force_loss_flag
        timestamp start_time
        timestamp end_time
        timestamp created_at
    }

    %% 订单相关
    orders {
        bigint id PK
        varchar order_sn UK
        bigint user_id FK
        bigint activity_id FK
        enum status
        enum draw_result
        enum win_option
        decimal amount_paid
        decimal experience_used
        decimal subsidy_amount
        bigint shipping_address_id FK
        timestamp payment_time
        timestamp draw_time
        timestamp created_at
        timestamp updated_at
    }

    order_logistics {
        bigint id PK
        bigint order_id FK
        varchar tracking_number
        varchar courier_company
        enum status
        json tracking_info
        timestamp shipped_at
        timestamp delivered_at
        timestamp created_at
    }

    %% 财务相关
    withdrawals {
        bigint id PK
        bigint user_id FK
        decimal amount
        decimal fee
        decimal actual_amount
        enum status
        varchar bank_info
        varchar rejection_reason
        int processed_by FK
        timestamp created_at
        timestamp processed_at
    }

    %% 裂变系统
    invitation_records {
        bigint id PK
        bigint inviter_id FK
        bigint invitee_id FK
        decimal commission_earned
        enum status
        timestamp created_at
    }

    team_rewards {
        bigint id PK
        bigint user_id FK
        date reward_date
        int qualified_count
        decimal reward_amount
        enum status
        timestamp created_at
    }

    %% 风控系统
    user_risk_strategies {
        bigint id PK
        bigint user_id FK
        enum strategy_type
        int effect_times
        int used_times
        enum status
        int created_by FK
        timestamp expires_at
        timestamp created_at
    }

    risk_control_logs {
        bigint id PK
        bigint user_id FK
        bigint order_id FK
        enum action_type
        text reason
        json original_data
        json modified_data
        int operated_by FK
        timestamp created_at
    }

    %% 内容管理
    banners {
        int id PK
        varchar title
        varchar image_url
        varchar link_url
        enum link_type
        int sort_order
        enum status
        timestamp start_time
        timestamp end_time
        timestamp created_at
    }

    popups {
        int id PK
        varchar title
        varchar image_url
        varchar link_url
        json trigger_rules
        enum status
        timestamp start_time
        timestamp end_time
        timestamp created_at
    }

    %% 后台管理
    admin_users {
        int id PK
        varchar username UK
        varchar password_hash
        varchar real_name
        varchar email
        enum status
        timestamp last_login_at
        timestamp created_at
    }

    roles {
        int id PK
        varchar name UK
        varchar description
        enum status
        timestamp created_at
    }

    permissions {
        int id PK
        varchar name UK
        varchar code UK
        varchar description
        varchar module
        timestamp created_at
    }

    admin_role_relations {
        int admin_id PK,FK
        int role_id PK,FK
        timestamp created_at
    }

    role_permission_relations {
        int role_id PK,FK
        int permission_id PK,FK
        timestamp created_at
    }

    admin_action_logs {
        bigint id PK
        int admin_id FK
        varchar action
        varchar module
        text description
        json request_data
        varchar ip_address
        timestamp created_at
    }

    %% 系统配置
    system_configs {
        varchar config_key PK
        text config_value
        varchar description
        enum value_type
        timestamp updated_at
    }

    %% 关系定义
    users ||--|| wallets : "has"
    users ||--o{ wallet_transactions : "has"
    users ||--o{ user_addresses : "has"
    users ||--o{ orders : "places"
    users ||--o{ withdrawals : "applies"
    users ||--o{ invitation_records : "invites"
    users ||--o{ invitation_records : "invited_by"
    users ||--o{ team_rewards : "earns"
    users ||--o{ user_risk_strategies : "has"

    product_categories ||--o{ products : "contains"
    products ||--o{ product_skus : "has"
    product_skus ||--o{ activities : "used_in"

    activities ||--o{ orders : "generates"
    orders ||--o| order_logistics : "has"
    orders }o--|| user_addresses : "ships_to"

    admin_users ||--o{ admin_action_logs : "performs"
    admin_users }o--o{ roles : "has"
    roles }o--o{ permissions : "has"
```

---

## 3. 数据表结构详述

### 3.1. 用户管理模块

#### 3.1.1. 用户表 `users`
存储用户基本信息和社交关系。

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 用户唯一ID |
| `phone_number` | VARCHAR(20) | UNIQUE NOT NULL | | 手机号，登录凭证 |
| `password_hash` | VARCHAR(255) | NOT NULL | | 加密后的密码 |
| `nickname` | VARCHAR(50) | | | 用户昵称 |
| `avatar_url` | VARCHAR(500) | | | 头像URL |
| `inviter_id` | BIGINT | FOREIGN KEY(users.id) | NULL | 邀请人ID |
| `status` | ENUM('active','frozen','deleted') | NOT NULL | 'active' | 账户状态 |
| `risk_level` | ENUM('normal','high','watch') | NOT NULL | 'normal' | 风控等级 |
| `last_login_at` | TIMESTAMP | | NULL | 最后登录时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `phone_number`
- KEY: `inviter_id`
- KEY: `status`
- KEY: `created_at`

#### 3.1.2. 用户钱包表 `wallets`
存储用户资金信息，与用户表一对一关系。

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `user_id` | BIGINT | PRIMARY KEY FOREIGN KEY(users.id) | | 用户ID |
| `balance` | DECIMAL(15,2) | NOT NULL | 0.00 | 账户余额(可提现) |
| `experience_balance` | DECIMAL(15,2) | NOT NULL | 0.00 | 体验金(不可提现) |
| `points` | BIGINT | NOT NULL | 0 | 用户积分 |
| `total_recharge` | DECIMAL(15,2) | NOT NULL | 0.00 | 累计充值金额 |
| `total_withdraw` | DECIMAL(15,2) | NOT NULL | 0.00 | 累计提现金额 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计：**
- PRIMARY KEY: `user_id`

#### 3.1.3. 钱包流水表 `wallet_transactions`
记录所有资金变动，确保资金可追溯。

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 流水ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `amount` | DECIMAL(15,2) | NOT NULL | | 变动金额(正数收入，负数支出) |
| `balance_after` | DECIMAL(15,2) | NOT NULL | | 变动后余额 |
| `type` | ENUM('RECHARGE','WITHDRAWAL','PAYMENT','REFUND_WIN','REFUND_LOSS','SUBSIDY','COMMISSION','TEAM_REWARD','MANUAL_ADJUST','EXPERIENCE_GIFT') | NOT NULL | | 交易类型 |
| `related_id` | BIGINT | | NULL | 关联业务ID |
| `description` | VARCHAR(255) | | | 交易描述 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id, created_at`
- KEY: `type`
- KEY: `related_id`

#### 3.1.4. 用户收货地址表 `user_addresses`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 地址ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `recipient_name` | VARCHAR(50) | NOT NULL | | 收件人姓名 |
| `phone_number` | VARCHAR(20) | NOT NULL | | 收件人电话 |
| `province` | VARCHAR(50) | NOT NULL | | 省份 |
| `city` | VARCHAR(50) | NOT NULL | | 城市 |
| `district` | VARCHAR(50) | NOT NULL | | 区县 |
| `address_line` | VARCHAR(255) | NOT NULL | | 详细地址 |
| `is_default` | BOOLEAN | NOT NULL | FALSE | 是否默认地址 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id`

### 3.2. 商品管理模块

#### 3.2.1. 商品分类表 `product_categories`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | 分类ID |
| `name` | VARCHAR(100) | NOT NULL | | 分类名称 |
| `icon_url` | VARCHAR(500) | | | 分类图标 |
| `sort_order` | INT | NOT NULL | 0 | 排序权重 |
| `status` | ENUM('active','inactive') | NOT NULL | 'active' | 分类状态 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `status, sort_order`

#### 3.2.2. 商品表(SPU) `products`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 商品ID |
| `name` | VARCHAR(255) | NOT NULL | | 商品名称 |
| `description` | TEXT | | | 商品描述 |
| `category_id` | INT | FOREIGN KEY(product_categories.id) | | 分类ID |
| `images` | JSON | | | 商品图片数组 |
| `detail_images` | JSON | | | 详情图片数组 |
| `status` | ENUM('active','inactive','deleted') | NOT NULL | 'active' | 商品状态 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `category_id`
- KEY: `status`

#### 3.2.3. 商品规格表(SKU) `product_skus`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | SKU ID |
| `product_id` | BIGINT | FOREIGN KEY(products.id) NOT NULL | | 商品ID |
| `sku_code` | VARCHAR(100) | UNIQUE NOT NULL | | SKU编码 |
| `attributes` | JSON | | | 规格属性 |
| `price` | DECIMAL(10,2) | NOT NULL | | 商品标价 |
| `stock` | INT | NOT NULL | 0 | 库存数量 |
| `sales_count` | INT | NOT NULL | 0 | 销售数量 |
| `status` | ENUM('active','inactive','deleted') | NOT NULL | 'active' | SKU状态 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `sku_code`
- KEY: `product_id`
- KEY: `status`

### 3.3. 活动管理模块

#### 3.3.1. 抽奖活动表 `activities`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 活动ID |
| `product_sku_id` | BIGINT | FOREIGN KEY(product_skus.id) NOT NULL | | 关联SKU |
| `name` | VARCHAR(255) | NOT NULL | | 活动名称 |
| `description` | TEXT | | | 活动描述 |
| `type` | ENUM('NOVICE','LOW_PRICE','HIGH_PRICE') | NOT NULL | | 活动类型 |
| `status` | ENUM('pending','active','finished','cancelled') | NOT NULL | 'pending' | 活动状态 |
| `config` | JSON | NOT NULL | | 活动配置参数 |
| `force_loss_flag` | BOOLEAN | NOT NULL | FALSE | 必不中开关 |
| `participant_limit` | INT | | NULL | 参与人数限制 |
| `current_participants` | INT | NOT NULL | 0 | 当前参与人数 |
| `start_time` | TIMESTAMP | NOT NULL | | 活动开始时间 |
| `end_time` | TIMESTAMP | NOT NULL | | 活动结束时间 |
| `draw_time` | TIMESTAMP | | | 开奖时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `product_sku_id`
- KEY: `type, status`
- KEY: `start_time, end_time`

### 3.4. 订单管理模块

#### 3.4.1. 订单表 `orders`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 订单ID |
| `order_sn` | VARCHAR(64) | UNIQUE NOT NULL | | 订单号 |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `activity_id` | BIGINT | FOREIGN KEY(activities.id) NOT NULL | | 活动ID |
| `status` | ENUM('PENDING_PAYMENT','WON_PENDING_ACTION','LOST','COMPLETED','CANCELLED','EXPIRED') | NOT NULL | 'PENDING_PAYMENT' | 订单状态 |
| `draw_result` | ENUM('WON','LOST') | | NULL | 开奖结果 |
| `win_option` | ENUM('TAKE_GOODS','CASH_OUT','PAY_REMAINDER','FORFEIT_DEPOSIT') | | NULL | 中签后选择 |
| `amount_paid` | DECIMAL(10,2) | NOT NULL | | 用户实付金额 |
| `experience_used` | DECIMAL(10,2) | NOT NULL | 0.00 | 使用体验金 |
| `subsidy_amount` | DECIMAL(10,2) | NOT NULL | 0.00 | 补贴金额 |
| `shipping_address_id` | BIGINT | FOREIGN KEY(user_addresses.id) | | 收货地址ID |
| `payment_method` | VARCHAR(50) | | | 支付方式 |
| `payment_time` | TIMESTAMP | | NULL | 支付时间 |
| `draw_time` | TIMESTAMP | | NULL | 开奖时间 |
| `completed_at` | TIMESTAMP | | NULL | 完成时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `order_sn`
- KEY: `user_id, created_at`
- KEY: `activity_id`
- KEY: `status`
- KEY: `draw_result`

#### 3.4.2. 订单物流表 `order_logistics`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 物流ID |
| `order_id` | BIGINT | FOREIGN KEY(orders.id) NOT NULL | | 订单ID |
| `tracking_number` | VARCHAR(100) | | | 快递单号 |
| `courier_company` | VARCHAR(50) | | | 快递公司 |
| `status` | ENUM('pending','shipped','in_transit','delivered','returned') | NOT NULL | 'pending' | 物流状态 |
| `tracking_info` | JSON | | | 物流跟踪信息 |
| `shipped_at` | TIMESTAMP | | | 发货时间 |
| `delivered_at` | TIMESTAMP | | | 签收时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `order_id`
- KEY: `tracking_number`

### 3.5. 裂变系统模块

#### 3.5.1. 邀请记录表 `invitation_records`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 记录ID |
| `inviter_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 邀请人ID |
| `invitee_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 被邀请人ID |
| `commission_earned` | DECIMAL(10,2) | NOT NULL | 0.00 | 已获得佣金 |
| `status` | ENUM('pending','active','invalid') | NOT NULL | 'pending' | 邀请状态 |
| `first_order_at` | TIMESTAMP | | NULL | 首次下单时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `invitee_id`
- KEY: `inviter_id`
- KEY: `status`

#### 3.5.2. 团队奖励表 `team_rewards`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 奖励ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `reward_date` | DATE | NOT NULL | | 奖励日期 |
| `qualified_count` | INT | NOT NULL | | 达标人数 |
| `reward_amount` | DECIMAL(10,2) | NOT NULL | | 奖励金额 |
| `status` | ENUM('pending','paid') | NOT NULL | 'pending' | 发放状态 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id, reward_date`
- KEY: `status`

### 3.6. 财务管理模块

#### 3.6.1. 提现申请表 `withdrawals`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 申请ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `amount` | DECIMAL(15,2) | NOT NULL | | 申请金额 |
| `fee` | DECIMAL(15,2) | NOT NULL | | 手续费 |
| `actual_amount` | DECIMAL(15,2) | NOT NULL | | 实际到账 |
| `status` | ENUM('pending','approved','rejected','completed','failed') | NOT NULL | 'pending' | 申请状态 |
| `bank_name` | VARCHAR(100) | | | 银行名称 |
| `bank_account` | VARCHAR(50) | | | 银行账号 |
| `account_holder` | VARCHAR(50) | | | 账户姓名 |
| `rejection_reason` | VARCHAR(255) | | | 驳回原因 |
| `processed_by` | INT | FOREIGN KEY(admin_users.id) | | 处理人 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 申请时间 |
| `processed_at` | TIMESTAMP | | | 处理时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id, created_at`
- KEY: `status`
- KEY: `processed_by`

### 3.7. 风控系统模块

#### 3.7.1. 用户风控策略表 `user_risk_strategies`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 策略ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) NOT NULL | | 用户ID |
| `strategy_type` | ENUM('FORCE_WIN','FORCE_LOSS','PROBABILITY_ADJUST') | NOT NULL | | 策略类型 |
| `effect_times` | INT | NOT NULL | | 生效次数 |
| `used_times` | INT | NOT NULL | 0 | 已使用次数 |
| `config_data` | JSON | | | 策略配置 |
| `status` | ENUM('active','expired','disabled') | NOT NULL | 'active' | 策略状态 |
| `created_by` | INT | FOREIGN KEY(admin_users.id) NOT NULL | | 创建人 |
| `expires_at` | TIMESTAMP | | | 过期时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id, status`
- KEY: `strategy_type`
- KEY: `expires_at`

#### 3.7.2. 风控日志表 `risk_control_logs`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 日志ID |
| `user_id` | BIGINT | FOREIGN KEY(users.id) | | 用户ID |
| `order_id` | BIGINT | FOREIGN KEY(orders.id) | | 订单ID |
| `action_type` | ENUM('DRAW_RESULT_OVERRIDE','PROBABILITY_ADJUST','USER_RISK_LEVEL_CHANGE','ACTIVITY_FORCE_LOSS') | NOT NULL | | 操作类型 |
| `reason` | TEXT | | | 操作原因 |
| `original_data` | JSON | | | 原始数据 |
| `modified_data` | JSON | | | 修改后数据 |
| `operated_by` | INT | FOREIGN KEY(admin_users.id) | | 操作人 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `user_id`
- KEY: `order_id`
- KEY: `action_type`
- KEY: `operated_by`

### 3.8. 内容管理模块

#### 3.8.1. Banner管理表 `banners`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | Banner ID |
| `title` | VARCHAR(255) | NOT NULL | | Banner标题 |
| `image_url` | VARCHAR(500) | NOT NULL | | 图片URL |
| `link_url` | VARCHAR(500) | | | 跳转链接 |
| `link_type` | ENUM('product','activity','h5','none') | NOT NULL | 'none' | 链接类型 |
| `sort_order` | INT | NOT NULL | 0 | 排序权重 |
| `status` | ENUM('active','inactive') | NOT NULL | 'active' | 状态 |
| `start_time` | TIMESTAMP | | | 开始时间 |
| `end_time` | TIMESTAMP | | | 结束时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `status, sort_order`
- KEY: `start_time, end_time`

#### 3.8.2. 弹窗管理表 `popups`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | 弹窗ID |
| `title` | VARCHAR(255) | NOT NULL | | 弹窗标题 |
| `image_url` | VARCHAR(500) | NOT NULL | | 图片URL |
| `link_url` | VARCHAR(500) | | | 跳转链接 |
| `trigger_rules` | JSON | NOT NULL | | 触发规则 |
| `status` | ENUM('active','inactive') | NOT NULL | 'active' | 状态 |
| `start_time` | TIMESTAMP | | | 开始时间 |
| `end_time` | TIMESTAMP | | | 结束时间 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `status`
- KEY: `start_time, end_time`

### 3.9. 后台管理模块

#### 3.9.1. 管理员表 `admin_users`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | 管理员ID |
| `username` | VARCHAR(50) | UNIQUE NOT NULL | | 用户名 |
| `password_hash` | VARCHAR(255) | NOT NULL | | 密码哈希 |
| `real_name` | VARCHAR(50) | | | 真实姓名 |
| `email` | VARCHAR(100) | | | 邮箱 |
| `phone` | VARCHAR(20) | | | 手机号 |
| `status` | ENUM('active','inactive','deleted') | NOT NULL | 'active' | 状态 |
| `last_login_at` | TIMESTAMP | | | 最后登录 |
| `last_login_ip` | VARCHAR(45) | | | 最后登录IP |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `username`
- KEY: `status`

#### 3.9.2. 角色表 `roles`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | 角色ID |
| `name` | VARCHAR(50) | UNIQUE NOT NULL | | 角色名称 |
| `description` | VARCHAR(255) | | | 角色描述 |
| `status` | ENUM('active','inactive') | NOT NULL | 'active' | 状态 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `name`

#### 3.9.3. 权限表 `permissions`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | INT | PRIMARY KEY AUTO_INCREMENT | | 权限ID |
| `name` | VARCHAR(100) | UNIQUE NOT NULL | | 权限名称 |
| `code` | VARCHAR(100) | UNIQUE NOT NULL | | 权限代码 |
| `description` | VARCHAR(255) | | | 权限描述 |
| `module` | VARCHAR(50) | NOT NULL | | 所属模块 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `id`
- UNIQUE KEY: `name`
- UNIQUE KEY: `code`
- KEY: `module`

#### 3.9.4. 管理员角色关联表 `admin_role_relations`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `admin_id` | INT | FOREIGN KEY(admin_users.id) NOT NULL | | 管理员ID |
| `role_id` | INT | FOREIGN KEY(roles.id) NOT NULL | | 角色ID |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `admin_id, role_id`

#### 3.9.5. 角色权限关联表 `role_permission_relations`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `role_id` | INT | FOREIGN KEY(roles.id) NOT NULL | | 角色ID |
| `permission_id` | INT | FOREIGN KEY(permissions.id) NOT NULL | | 权限ID |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY: `role_id, permission_id`

#### 3.9.6. 管理员操作日志表 `admin_action_logs`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `id` | BIGINT | PRIMARY KEY AUTO_INCREMENT | | 日志ID |
| `admin_id` | INT | FOREIGN KEY(admin_users.id) NOT NULL | | 操作人ID |
| `action` | VARCHAR(100) | NOT NULL | | 操作动作 |
| `module` | VARCHAR(50) | NOT NULL | | 操作模块 |
| `description` | TEXT | | | 操作描述 |
| `request_data` | JSON | | | 请求数据 |
| `ip_address` | VARCHAR(45) | | | 操作IP |
| `user_agent` | VARCHAR(500) | | | 用户代理 |
| `created_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

**索引设计：**
- PRIMARY KEY: `id`
- KEY: `admin_id, created_at`
- KEY: `module`
- KEY: `action`

### 3.10. 系统配置模块

#### 3.10.1. 系统配置表 `system_configs`

| 字段名 | 数据类型 | 约束 | 默认值 | 说明 |
|--------|----------|------|---------|------|
| `config_key` | VARCHAR(100) | PRIMARY KEY | | 配置键 |
| `config_value` | TEXT | NOT NULL | | 配置值 |
| `description` | VARCHAR(255) | | | 配置说明 |
| `value_type` | ENUM('string','number','boolean','json') | NOT NULL | 'string' | 值类型 |
| `is_public` | BOOLEAN | NOT NULL | FALSE | 是否公开 |
| `updated_at` | TIMESTAMP | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计：**
- PRIMARY KEY: `config_key`
- KEY: `is_public`

---

## 4. 数据库优化建议

### 4.1. 性能优化
1. **分区策略**：对大表如`orders`、`wallet_transactions`按时间分区
2. **读写分离**：配置主从复制，读操作分流到从库
3. **缓存策略**：热点数据使用Redis缓存
4. **索引优化**：定期分析慢查询，优化索引

### 4.2. 数据安全
1. **备份策略**：每日全量备份，每小时增量备份
2. **权限控制**：严格控制数据库访问权限
3. **审计日志**：开启数据库审计日志
4. **加密存储**：敏感字段加密存储

### 4.3. 扩展性考虑
1. **垂直分库**：按业务模块分库部署
2. **水平分表**：大表按用户ID或时间分表
3. **微服务支持**：为未来微服务架构预留接口

---

## 5. MySQL建表脚本

### 5.1. 脚本文件说明

已根据本设计文档生成完整的MySQL建表脚本：`社交拼团APP数据库建表脚本.sql`

### 5.2. 脚本特性

#### 5.2.1. 数据库配置
- **字符集**：utf8mb4，支持完整的Unicode字符集
- **排序规则**：utf8mb4_unicode_ci，确保正确的中文排序
- **引擎**：InnoDB，支持事务和外键约束
- **外键检查**：脚本执行期间暂时关闭，执行完毕后恢复

#### 5.2.2. 表结构特点
- **完整性约束**：所有外键关系均已配置
- **索引优化**：为高频查询字段创建索引
- **数据类型**：严格按照设计文档定义
- **字段注释**：每个字段都有详细的中文注释

#### 5.2.3. 初始数据
脚本包含以下初始数据：

**系统配置数据：**
- 低价区中签率：15%
- 高价区中签率：8%
- 新用户体验金：20元
- 邀请返佣比例：0.5%
- 团队奖励阈值：5人
- 最低提现金额：10元
- 提现手续费率：1%

**权限角色数据：**
- 5个默认角色：超级管理员、普通管理员、运营人员、财务人员、客服人员
- 24个权限点：覆盖用户、订单、商品、活动、财务、风控、内容、系统管理
- 默认超级管理员账户：用户名admin，密码admin123

### 5.3. 执行说明

#### 5.3.1. 执行环境要求
- MySQL 5.7+ 或 MariaDB 10.2+
- 支持JSON数据类型
- 支持外键约束

#### 5.3.2. 执行步骤
1. 连接MySQL服务器
2. 执行整个SQL脚本文件
3. 验证表结构和初始数据
4. 根据需要调整系统配置参数

#### 5.3.3. 注意事项
- 脚本会删除同名数据库和表，请谨慎执行
- 初始管理员密码需要在生产环境中修改
- 建议在测试环境先验证脚本的正确性

### 5.4. 表统计信息

| 模块 | 表数量 | 核心表 |
|------|--------|--------|
| 用户管理 | 4 | users, wallets, wallet_transactions |
| 商品管理 | 3 | products, product_skus, product_categories |
| 活动管理 | 1 | activities |
| 订单管理 | 2 | orders, order_logistics |
| 裂变系统 | 2 | invitation_records, team_rewards |
| 财务管理 | 1 | withdrawals |
| 风控系统 | 2 | user_risk_strategies, risk_control_logs |
| 内容管理 | 2 | banners, popups |
| 后台管理 | 6 | admin_users, roles, permissions及关联表 |
| 系统配置 | 1 | system_configs |
| **总计** | **24** | **核心业务表12个** |

---

**文档结束** 