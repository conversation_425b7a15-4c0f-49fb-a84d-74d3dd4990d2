# 优化计划（7-14）- 后端开发任务清单

根据 `优化计划（7-14）.md` 文档，归纳总结出后端程序员需要实施的开发任务。

## 任务概览

本次优化涉及6个主要模块的后端开发任务：
- 新用户激励机制
- 首页界面与布局优化
- 商品详情与购买流程优化
- 用户中心功能增强
- 积分与风控系统
- API接口优化

---

## 一、数据库结构调整

### 1.1 用户表 (users) 字段增加
```sql
-- 新用户激励机制
ALTER TABLE users ADD COLUMN freeGroupBuyChances INT DEFAULT 1 COMMENT '免费拼团机会次数';

-- 实名认证
ALTER TABLE users ADD COLUMN isRealNameVerified BOOLEAN DEFAULT FALSE COMMENT '是否实名认证';
```

### 1.2 商品表 (products) 字段增加
```sql
-- 积分支付支持
ALTER TABLE products ADD COLUMN allowPointsPurchase BOOLEAN DEFAULT FALSE COMMENT '是否允许积分购买';

-- 积分商品标识
ALTER TABLE products ADD COLUMN isPointsOnly BOOLEAN DEFAULT FALSE COMMENT '是否仅限积分购买';
```

### 1.3 订单表 (orders) 字段增加
```sql
-- 订单类型区分
ALTER TABLE orders ADD COLUMN orderType ENUM('GROUP_BUY', 'DIRECT_BUY') DEFAULT 'GROUP_BUY' COMMENT '订单类型';

-- 支付方式记录
ALTER TABLE orders ADD COLUMN paymentMethod ENUM('balance', 'points') DEFAULT 'balance' COMMENT '支付方式';
```

### 1.4 新建实名认证表 (user_real_name_info)
```sql
CREATE TABLE user_real_name_info (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  userId BIGINT NOT NULL COMMENT '用户ID',
  realName VARCHAR(50) NOT NULL COMMENT '真实姓名',
  idCardNumber VARCHAR(20) NOT NULL COMMENT '身份证号',
  idCardFrontImage VARCHAR(255) NOT NULL COMMENT '身份证正面照片',
  idCardBackImage VARCHAR(255) NOT NULL COMMENT '身份证反面照片',
  bankCardNumber VARCHAR(30) NOT NULL COMMENT '银行卡号',
  bankName VARCHAR(100) NOT NULL COMMENT '开户银行',
  bankCardHolderName VARCHAR(50) NOT NULL COMMENT '银行卡持卡人姓名',
  status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING' COMMENT '审核状态',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);
```

### 1.5 Banner表字段增加
```sql
-- Banner跳转链接
ALTER TABLE banners ADD COLUMN linkUrl VARCHAR(255) COMMENT 'Banner跳转链接';
```

---

## 二、业务逻辑开发

### 2.1 新用户激励机制

#### 任务清单：
- [ ] **用户注册时初始化免费机会**
  - 新用户注册时，`freeGroupBuyChances` 字段设为1
  - 创建后台配置项，设置"未中奖"现金奖励金额

- [ ] **修改下单逻辑**
  - 在 `POST /api/v1/placeOrder` 接口中增加免费下单检测
  - 当 `freeGroupBuyChances > 0` 时，允许零元下单
  - 下单成功后，扣减用户的免费机会次数

- [ ] **修改拼团开奖逻辑**
  - 识别使用免费机会的订单
  - 强制设置为"未中奖"状态
  - 发放配置的现金奖励到用户余额

- [ ] **API接口调整**
  - `GET /api/user/profile`: 返回 `freeGroupBuyChances` 字段
  - `POST /api/orders`: 处理免费下单逻辑

### 2.2 首页界面与布局优化

#### 任务清单：
- [ ] **Banner管理功能**
  - 后台管理系统增加Banner链接配置功能
  - 支持为每个Banner设置跳转链接

- [ ] **商品分类系统调整**
  - 后台商品管理适配新的分类体系：全部、手机数码、奢饰品、积分商城
  - 确保商品可以正确分类和筛选

- [ ] **API接口调整**
  - `GET /api/banners`: 返回数据包含 `linkUrl` 字段
  - `GET /api/products`: 支持新的分类体系筛选

### 2.3 商品详情与购买流程优化

#### 任务清单：
- [ ] **直接购买订单逻辑**
  - 在现有 `POST /api/v1/placeOrder` 接口中增加 `orderType` 参数处理
  - 实现直接购买订单流程：
    - 使用商品原价计算订单金额
    - 立即生成有效订单，无需拼团等待
    - 支付成功后直接进入"待发货"状态
    - 支付成功后立即扣减商品库存

- [ ] **积分支付逻辑**
  - 在下单接口中增加 `paymentMethod` 参数处理
  - 实现积分支付流程：
    - 验证用户积分余额是否充足
    - 支付成功后扣减用户积分
    - 记录积分支付记录

- [ ] **订单类型区分处理**
  ```javascript
  // 后端接口处理逻辑示例
  if (orderType === 'DIRECT_BUY') {
    // 直接购买逻辑
    - 使用商品原价计算订单金额
    - 立即生成有效订单，无需拼团等待
    - 根据 paymentMethod 处理支付方式
    - 支付成功后直接进入"待发货"状态
  } else {
    // 拼团购买逻辑 (保持现有逻辑不变)
    - 使用拼团价格计算订单金额  
    - 创建拼团订单，等待拼团完成
    - 默认使用余额支付
    - 拼团成功后进入开奖流程
  }
  ```

- [ ] **API接口调整**
  - `GET /api/products/{id}`: 返回 `allowPointsPurchase` 字段
  - `POST /api/v1/placeOrder`: 支持 `orderType` 和 `paymentMethod` 参数

### 2.4 用户中心功能增强

#### 任务清单：
- [ ] **实名认证功能**
  - 创建实名认证数据表
  - 实现实名资料提交和审核流程
  - 提现前检查实名认证状态

- [ ] **团队关系链开发**
  - 设计并实现用户邀请关系存储
  - 计算每个用户的下级贡献值
  - 计算团队总收益统计

- [ ] **团队数据统计**
  - 实现下级用户列表查询
  - 计算下级消费记录和贡献值
  - 提供团队收益汇总数据

- [ ] **API接口开发**
  - `POST /api/user/verifyRealName`: 实名资料提交接口
  - `GET /api/team/info`: 获取团队信息接口（总览）
  - `GET /api/team/members`: 获取下级成员列表接口
  - `GET /api/user/profile`: 返回 `isRealNameVerified` 字段

### 2.5 积分与风控系统

#### 任务清单：
- [ ] **积分上限控制**
  - 实现每日用户获得积分的上限逻辑
  - 创建积分获取记录表，按日期统计
  - 在积分发放时检查日限额

- [ ] **积分退款规则**
  - 在退单逻辑中增加支付方式判断
  - 如果是积分支付，不退还积分部分
  - 只退还现金支付部分

- [ ] **提现手续费**
  - 在提现逻辑中扣除10%手续费
  - 记录手续费扣除明细

- [ ] **积分商品支持**
  - 实现积分商品标识和筛选
  - 积分商品只支持积分支付
  - 积分商品不支持拼团功能

- [ ] **API接口调整**
  - `GET /api/products`: 支持 `isPointsOnly` 参数筛选积分商品
  - 下单接口已支持积分支付，无需新增

---

## 三、API接口开发/调整

### 3.1 新增接口
```
POST /api/user/verifyRealName     # 实名认证资料提交
GET /api/team/info                # 获取团队信息总览
GET /api/team/members             # 获取下级成员列表
```

### 3.2 现有接口调整
```
GET /api/user/profile             # 新增返回字段：freeGroupBuyChances, isRealNameVerified
GET /api/banners                  # 新增返回字段：linkUrl
GET /api/products                 # 新增筛选参数：isPointsOnly
GET /api/products/{id}            # 新增返回字段：allowPointsPurchase
POST /api/v1/placeOrder           # 新增参数：orderType, paymentMethod
```

---

## 四、后台管理系统调整

### 4.1 配置管理
- [ ] **新用户激励配置**
  - 免费拼团机会次数设置
  - 未中奖现金奖励金额设置

- [ ] **Banner管理**
  - 增加Banner链接配置功能
  - 支持链接有效性验证

- [ ] **商品管理**
  - 增加积分购买属性配置
  - 增加积分商品标识配置
  - 商品分类管理适配新体系

- [ ] **积分系统配置**
  - 每日积分获取上限设置
  - 提现手续费率设置

### 4.2 审核管理
- [ ] **实名认证审核**
  - 实名认证资料审核界面
  - 审核通过/拒绝操作
  - 审核记录查询

---

## 五、开发优先级建议

### 高优先级（P0）
1. 数据库结构调整
2. 直接购买订单逻辑
3. 积分支付逻辑
4. API接口调整

### 中优先级（P1）
1. 新用户激励机制
2. 实名认证功能
3. 积分与风控系统

### 低优先级（P2）
1. 团队关系链开发
2. Banner管理功能
3. 后台管理系统调整

---

## 六、测试要点

### 6.1 功能测试
- [ ] 新用户注册后免费机会正确初始化
- [ ] 免费拼团必不中逻辑正确
- [ ] 直接购买订单流程完整
- [ ] 积分支付扣减正确
- [ ] 实名认证流程完整
- [ ] 团队数据统计准确

### 6.2 边界测试
- [ ] 积分余额不足时的处理
- [ ] 免费机会用完后的处理
- [ ] 提现手续费计算正确
- [ ] 积分日限额控制有效

### 6.3 性能测试
- [ ] 团队数据查询性能
- [ ] 积分商品筛选性能
- [ ] 订单创建性能

---

## 七、部署注意事项

### 7.1 数据库迁移
- 按顺序执行所有 ALTER TABLE 语句
- 创建新表前检查表是否已存在
- 备份现有数据

### 7.2 API兼容性
- 新增的接口参数都有默认值
- 现有接口保持向后兼容
- 逐步迁移前端调用

### 7.3 配置项初始化
- 设置合理的默认配置值
- 提供配置项说明文档
- 测试环境先行验证

---

## 八、完成标准

每个任务完成后需要：
- [ ] 代码审查通过
- [ ] 单元测试覆盖
- [ ] 功能测试验证
- [ ] 文档更新完成
- [ ] 部署脚本就绪 