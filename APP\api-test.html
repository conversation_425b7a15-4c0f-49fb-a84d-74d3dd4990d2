<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-item { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; max-height: 200px; overflow-y: auto; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        input { width: 100%; padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API 连接测试工具</h1>
        
        <div class="test-item">
            <h3>1. 测试本地前端开发服务器</h3>
            <button onclick="testLocalFrontend()">测试 localhost:3000</button>
            <div id="frontend-result" class="result"></div>
        </div>

        <div class="test-item">
            <h3>2. 测试本地后端API</h3>
            <button onclick="testLocalBackend()">测试 localhost:8686</button>
            <div id="backend-result" class="result"></div>
        </div>

        <div class="test-item">
            <h3>3. 测试代理API路径</h3>
            <input type="text" id="api-path" value="/api/v1/myTeam?pageNum=0&pageSize=20" placeholder="API路径">
            <button onclick="testProxiedAPI()">通过代理测试</button>
            <div id="proxy-result" class="result"></div>
        </div>

        <div class="test-item">
            <h3>4. 测试直接API路径</h3>
            <input type="text" id="direct-path" value="/app/v1/myTeam?pageNum=0&pageSize=20" placeholder="直接API路径">
            <button onclick="testDirectAPI()">直接测试</button>
            <div id="direct-result" class="result"></div>
        </div>

        <div class="test-item">
            <h3>5. 当前环境信息</h3>
            <button onclick="showEnvironment()">显示环境信息</button>
            <div id="env-result" class="result"></div>
        </div>
    </div>

    <script>
        function updateResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = 'result ' + (isError ? 'error' : 'success');
        }

        async function testLocalFrontend() {
            try {
                const response = await fetch('http://localhost:3000/');
                const text = await response.text();
                updateResult('frontend-result', `
                    状态: ${response.status}<br>
                    内容类型: ${response.headers.get('content-type')}<br>
                    响应长度: ${text.length} 字符<br>
                    ${text.includes('Vue') ? '✅ 前端正常运行' : '⚠️ 可能不是Vue应用'}
                `);
            } catch (error) {
                updateResult('frontend-result', `❌ 前端连接失败: ${error.message}`, true);
            }
        }

        async function testLocalBackend() {
            try {
                const response = await fetch('http://localhost:8686/actuator/health');
                const data = await response.json();
                updateResult('backend-result', `
                    状态: ${response.status}<br>
                    健康状态: ${JSON.stringify(data, null, 2)}<br>
                    ✅ 后端正常运行
                `);
            } catch (error) {
                updateResult('backend-result', `❌ 后端连接失败: ${error.message}`, true);
            }
        }

        async function testProxiedAPI() {
            const path = document.getElementById('api-path').value;
            try {
                const response = await fetch(`http://localhost:3000${path}`, {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch {
                    data = text;
                }

                updateResult('proxy-result', `
                    状态: ${response.status}<br>
                    内容类型: ${response.headers.get('content-type')}<br>
                    响应: <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                `, response.status >= 400);
            } catch (error) {
                updateResult('proxy-result', `❌ 代理API测试失败: ${error.message}`, true);
            }
        }

        async function testDirectAPI() {
            const path = document.getElementById('direct-path').value;
            try {
                const response = await fetch(`http://localhost:8686${path}`, {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch {
                    data = text;
                }

                updateResult('direct-result', `
                    状态: ${response.status}<br>
                    内容类型: ${response.headers.get('content-type')}<br>
                    响应: <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                `, response.status >= 400);
            } catch (error) {
                updateResult('direct-result', `❌ 直接API测试失败: ${error.message}`, true);
            }
        }

        function showEnvironment() {
            const info = `
                当前URL: ${window.location.href}<br>
                用户代理: ${navigator.userAgent}<br>
                时间: ${new Date().toLocaleString()}<br>
                协议: ${window.location.protocol}<br>
                主机: ${window.location.host}<br>
                端口: ${window.location.port || '默认端口'}
            `;
            updateResult('env-result', info);
        }

        // 页面加载时自动显示环境信息
        window.onload = function() {
            showEnvironment();
        }
    </script>
</body>
</html>