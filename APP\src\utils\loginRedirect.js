/**
 * 登录成功后的跳转处理工具
 * 解决登录后跳转被路由守卫拦截的问题
 */

/**
 * 处理登录成功后的页面跳转
 * @param {Object} router - Vue Router 实例
 * @param {Object} route - 当前路由信息
 * @param {Object} authStore - 认证状态管理
 * @param {string} fallbackPath - 默认跳转路径
 */
export async function handleLoginRedirect(router, route, authStore, fallbackPath = '/user') {
  console.log('🔄 开始处理登录跳转...')
  
  // 1. 等待状态完全更新
  let attempts = 0
  const maxAttempts = 20
  
  while (attempts < maxAttempts) {
    if (authStore.isLoggedIn && authStore.token && authStore.user) {
      console.log('✅ 登录状态已确认')
      break
    }
    
    await new Promise(resolve => setTimeout(resolve, 50))
    attempts++
    console.log(`⏳ 等待状态更新 ${attempts}/${maxAttempts}`)
  }
  
  if (!authStore.isLoggedIn) {
    console.error('❌ 登录状态验证失败')
    throw new Error('登录状态验证失败')
  }
  
  // 2. 确定跳转目标
  const redirectPath = route.query.redirect || fallbackPath
  console.log('🎯 目标路径:', redirectPath)
  
  // 3. 特殊处理：如果目标路径是用户相关页面，确保不会循环跳转
  if (redirectPath === '/login') {
    console.log('🔄 避免跳转回登录页，使用默认路径')
    return handleJump(router, fallbackPath)
  }
  
  return handleJump(router, redirectPath)
}

/**
 * 执行实际的页面跳转
 */
async function handleJump(router, path) {
  try {
    console.log('🚀 执行页面跳转:', path)
    
    // 使用 replace 避免历史记录中留下登录页面
    await router.replace({
      path: path,
      query: { 
        from: 'login',
        timestamp: Date.now() // 添加时间戳强制刷新
      }
    })
    
    console.log('✅ 页面跳转成功')
    return true
    
  } catch (error) {
    console.error('❌ 页面跳转失败:', error)
    
    // 备用方案：强制导航到用户中心
    try {
      window.location.href = '/user'
      return true
    } catch (fallbackError) {
      console.error('❌ 备用跳转也失败:', fallbackError)
      throw new Error('页面跳转完全失败')
    }
  }
}

/**
 * 检查当前路由是否需要登录
 */
export function requiresAuth(route) {
  return route.meta?.requiresAuth === true
}

/**
 * 生成带重定向参数的登录URL
 */
export function createLoginUrl(currentPath) {
  const loginPath = '/login'
  if (currentPath && currentPath !== '/login') {
    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`
  }
  return loginPath
}