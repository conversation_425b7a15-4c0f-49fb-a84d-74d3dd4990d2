<!--
  实名认证管理 - 主列表页面
  Created: 2025-08-11
-->
<template>
  <div class="identity-verification-container">
    <!-- 搜索表单 -->
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="用户姓名" class="smart-query-form-item">
          <a-input
            v-model:value="queryForm.realName"
            placeholder="请输入用户姓名"
            style="width: 200px"
            allowClear
          />
        </a-form-item>
        <a-form-item label="手机号" class="smart-query-form-item">
          <a-input
            v-model:value="queryForm.phoneNumber"
            placeholder="请输入手机号"
            style="width: 200px"
            allowClear
          />
        </a-form-item>
        <a-form-item label="身份证号" class="smart-query-form-item">
          <a-input
            v-model:value="queryForm.idCardNumber"
            placeholder="请输入身份证号"
            style="width: 200px"
            allowClear
          />
        </a-form-item>
        <a-form-item label="审核状态" class="smart-query-form-item">
          <a-select
            v-model:value="queryForm.status"
            placeholder="请选择状态"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="0">待审核</a-select-option>
            <a-select-option value="1">审核通过</a-select-option>
            <a-select-option value="2">审核拒绝</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="提交时间" class="smart-query-form-item">
          <a-range-picker
            v-model:value="queryForm.createTimeRange"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button-group>
            <a-button type="primary" @click="onSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="onReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-button-group>
        </a-form-item>
      </a-row>
    </a-form>

    <!-- 操作按钮区域 -->
    <div class="smart-table-operate">
      <a-space>
        <a-button
          type="primary"
          :disabled="selectedRowKeys.length === 0"
          @click="batchApprove"
        >
          <template #icon><CheckOutlined /></template>
          批量通过
        </a-button>
        <a-button
          @click="exportData"
          :loading="exportLoading"
        >
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
        <a-button @click="refreshTable">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      :scroll="{ x: 1200 }"
      @change="onTableChange"
      row-key="id"
    >
      <!-- 头像列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'userAvatar'">
          <a-avatar :src="record.userAvatar" :size="32">
            {{ record.realName ? record.realName.charAt(0) : 'U' }}
          </a-avatar>
        </template>
        
        <!-- 身份证号列（脱敏显示） -->
        <template v-else-if="column.dataIndex === 'idCardNumber'">
          <span>{{ maskIdCard(record.idCardNumber) }}</span>
        </template>

        <!-- 审核状态列 -->
        <template v-else-if="column.dataIndex === 'status'">
          <identity-status-tag :status="record.status" />
        </template>

        <!-- 提交时间列 -->
        <template v-else-if="column.dataIndex === 'createTime'">
          <span>{{ formatDateTime(record.createTime) }}</span>
        </template>

        <!-- 审核时间列 -->
        <template v-else-if="column.dataIndex === 'reviewTime'">
          <span v-if="record.reviewTime">{{ formatDateTime(record.reviewTime) }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="showDetail(record)"
            >
              查看详情
            </a-button>
            <a-button
              v-if="record.status === 0"
              type="link"
              size="small"
              @click="approveRecord(record)"
            >
              审核通过
            </a-button>
            <a-button
              v-if="record.status === 0"
              type="link"
              size="small"
              danger
              @click="rejectRecord(record)"
            >
              审核拒绝
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情弹窗 -->
    <identity-detail-modal
      v-model:visible="detailModalVisible"
      :record="selectedRecord"
      @review="onReviewFromDetail"
    />

    <!-- 审核弹窗 -->
    <identity-review-modal
      v-model:visible="reviewModalVisible"
      :record="selectedRecord"
      :review-type="reviewType"
      @success="onReviewSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined, ExportOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { identityApi } from '/@/api/business/identity/identity-verification-api'
import IdentityDetailModal from './components/identity-detail-modal.vue'
import IdentityReviewModal from './components/identity-review-modal.vue'
import IdentityStatusTag from './components/identity-status-tag.vue'
import { smartSentry } from '/@/lib/smart-sentry'
import { formatDateTime, maskIdCard } from '/@/utils/format'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const selectedRowKeys = ref([])
const detailModalVisible = ref(false)
const reviewModalVisible = ref(false)
const selectedRecord = ref(null)
const reviewType = ref('') // 'approve' 或 'reject'

// 查询表单
const queryForm = reactive({
  realName: '',
  phoneNumber: '',
  idCardNumber: '',
  status: '',
  createTimeRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: (total) => `共 ${total} 条记录`,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100']
})

// 行选择配置
const rowSelection = reactive({
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record) => ({
    disabled: record.status !== 0 // 只有待审核状态才能选择
  })
})

// 表格列配置
const columns = [
  {
    title: '用户信息',
    dataIndex: 'userAvatar',
    width: 100
  },
  {
    title: '用户姓名',
    dataIndex: 'realName',
    width: 120,
    sorter: true
  },
  {
    title: '手机号',
    dataIndex: 'phoneNumber',
    width: 130
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNumber',
    width: 180
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 100,
    filters: [
      { text: '待审核', value: 0 },
      { text: '审核通过', value: 1 },
      { text: '审核拒绝', value: 2 }
    ]
  },
  {
    title: '提交时间',
    dataIndex: 'createTime',
    width: 180,
    sorter: true
  },
  {
    title: '审核时间',
    dataIndex: 'reviewTime',
    width: 180
  },
  {
    title: '审核人',
    dataIndex: 'reviewerName',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 查询数据
const queryData = async () => {
  try {
    loading.value = true
    
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...queryForm
    }

    // 处理时间范围
    if (queryForm.createTimeRange && queryForm.createTimeRange.length === 2) {
      params.startTime = queryForm.createTimeRange[0].format('YYYY-MM-DD HH:mm:ss')
      params.endTime = queryForm.createTimeRange[1].format('YYYY-MM-DD HH:mm:ss')
    }
    delete params.createTimeRange

    console.log('🚀 [前端] 准备发送API请求')
    
    const res = await identityApi.getPage(params)
    
    console.log('📥 [前端] API响应成功')
    
    if (res && res.data) {
      console.log('✅ [前端] API响应成功，解析数据:')
      console.log('  - 数据列表:', res.data.records || [])
      console.log('  - 总记录数:', res.data.total || 0)
      console.log('  - 列表长度:', (res.data.records || []).length)
      
      tableData.value = res.data.records || []
      pagination.total = res.data.total || 0
      
      console.log('📊 [前端] 表格数据已更新:', tableData.value)
      console.log('📄 [前端] 分页信息已更新:', pagination)
    } else {
      console.warn('⚠️ [前端] API响应数据格式异常:', res)
    }
  } catch (error) {
    console.error('❌ [前端] 查询失败，错误详情:', error)
    console.error('❌ [前端] 错误堆栈:', error.stack)
    message.error('查询失败：' + error.message)
    smartSentry.captureError(error)
  } finally {
    loading.value = false
    console.log('🔄 [前端] 查询请求完成，loading状态已重置')
  }
}

// 搜索
const onSearch = () => {
  pagination.current = 1
  queryData()
}

// 重置
const onReset = () => {
  Object.assign(queryForm, {
    realName: '',
    phoneNumber: '',
    idCardNumber: '',
    status: '',
    createTimeRange: []
  })
  pagination.current = 1
  queryData()
}

// 刷新表格
const refreshTable = () => {
  queryData()
}

// 表格变化事件
const onTableChange = (paginationInfo, filters, sorter) => {
  // 处理分页
  if (paginationInfo) {
    pagination.current = paginationInfo.current
    pagination.pageSize = paginationInfo.pageSize
  }

  // 处理筛选
  if (filters.status && filters.status.length > 0) {
    queryForm.status = filters.status[0]
  }

  queryData()
}

// 批量审核通过
const batchApprove = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审核的记录')
    return
  }

  try {
    await identityApi.batchApprove(selectedRowKeys.value)
    message.success('批量审核通过成功')
    selectedRowKeys.value = []
    queryData()
  } catch (error) {
    message.error('批量审核失败：' + error.message)
    smartSentry.captureError(error)
  }
}

// 导出数据
const exportData = async () => {
  try {
    exportLoading.value = true
    const params = { ...queryForm }
    
    // 处理时间范围
    if (queryForm.createTimeRange && queryForm.createTimeRange.length === 2) {
      params.startTime = queryForm.createTimeRange[0].format('YYYY-MM-DD HH:mm:ss')
      params.endTime = queryForm.createTimeRange[1].format('YYYY-MM-DD HH:mm:ss')
    }
    delete params.createTimeRange

    await identityApi.exportData(params)
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败：' + error.message)
    smartSentry.captureError(error)
  } finally {
    exportLoading.value = false
  }
}

// 显示详情
const showDetail = (record) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

// 审核通过
const approveRecord = (record) => {
  selectedRecord.value = record
  reviewType.value = 'approve'
  reviewModalVisible.value = true
}

// 审核拒绝
const rejectRecord = (record) => {
  selectedRecord.value = record
  reviewType.value = 'reject'
  reviewModalVisible.value = true
}

// 从详情页面进行审核
const onReviewFromDetail = (type) => {
  detailModalVisible.value = false
  reviewType.value = type
  reviewModalVisible.value = true
}

// 审核成功回调
const onReviewSuccess = () => {
  reviewModalVisible.value = false
  selectedRecord.value = null
  queryData()
}

// 组件挂载
onMounted(() => {
  console.log('🎉 [前端] 实名认证管理组件已挂载，开始初始化数据查询')
  console.log('🔧 [前端] 初始分页配置:', pagination)
  console.log('🔧 [前端] 初始查询条件:', queryForm)
  queryData()
})
</script>

<style scoped>
.identity-verification-container {
  padding: 0;
}

.text-gray-400 {
  color: #9ca3af;
}

:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

:deep(.ant-avatar) {
  flex-shrink: 0;
}
</style>