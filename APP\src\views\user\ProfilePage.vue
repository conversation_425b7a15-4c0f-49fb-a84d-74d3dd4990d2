<template>
  <div class="bg-gray-100 min-h-screen pb-20">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex flex-col items-center justify-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
      <p class="text-gray-600 text-sm">正在加载个人信息...</p>
    </div>

    <!-- 认证错误状态 -->
    <div v-else-if="authError" class="flex flex-col items-center justify-center min-h-screen px-4">
      <div class="bg-white rounded-2xl p-8 shadow-sm text-center max-w-sm mx-auto">
        <iconify-icon icon="material-symbols:error" class="text-red-500 text-6xl mb-4"></iconify-icon>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">认证失败</h3>
        <p class="text-gray-600 text-sm mb-6">
          登录状态已过期，请重新登录
        </p>
        <div class="space-y-3">
          <button 
            @click="retryAuthentication"
            :disabled="retryCount >= maxRetries"
            class="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium text-sm hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ retryCount >= maxRetries ? '重试次数已用完' : `重试 (${retryCount}/${maxRetries})` }}
          </button>
          <button 
            @click="$router.push('/login')"
            class="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium text-sm hover:bg-gray-300 transition-colors"
          >
            重新登录
          </button>
        </div>
      </div>
    </div>

    <!-- 正常内容 -->
    <div v-else>
      <!-- 用户信息头部 -->
      <div class="bg-gradient-to-br from-blue-500 to-purple-600 px-4 py-8 text-white">
        <div class="flex items-center mb-6">
          <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mr-4 flex items-center justify-center">
            <iconify-icon icon="material-symbols:person" class="text-3xl"></iconify-icon>
          </div>
          <div class="flex-1">
            <h2 class="text-lg font-bold mb-1">{{ getUserDisplayName }}</h2>
            <p class="text-xs opacity-90" style="font-size: 10px;">手机号：{{ getMaskedPhone }}</p>
          </div>
          <iconify-icon icon="material-symbols:edit" class="text-xl opacity-80 cursor-pointer" @click="goToSettings"></iconify-icon>
        </div>
        
        <!-- 我的钱包 -->
        <div class="bg-white bg-opacity-10 rounded-2xl p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:account-balance-wallet" class="text-xl mr-2"></iconify-icon>
              <span class="font-medium">我的钱包</span>
              <!-- 刷新按钮 -->
              <button 
                @click="refreshWalletInfo"
                class="p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors ml-2"
                title="刷新钱包信息"
                style="margin-top: 6px;"
              >
                <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
              </button>
            </div>
            <div class="flex items-center">
              <!-- 查看流水入口 -->
              <div class="flex items-center cursor-pointer" @click="goToWallet">
                <span class="text-sm mr-1">查看流水</span>
                <iconify-icon icon="material-symbols:chevron-right" class="text-lg"></iconify-icon>
              </div>
            </div>
          </div>
          
          <!-- 钱包信息显示 -->
          <div v-if="walletData && !walletData._error" class="space-y-3 mb-4">
            <!-- 账户余额显示 - 居中，带图标和黄色渐变色 -->
            <div class="text-center py-2">
              <div class="flex items-center justify-center mb-1">
                <iconify-icon icon="material-symbols:account-balance-wallet" class="text-yellow-400 text-xl mr-2"></iconify-icon>
                <div class="text-sm font-bold text-white">账户余额</div>
              </div>
              <div class="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">{{ getBalanceText }}</div>
            </div>
            
            <!-- 总收益和积分并排显示 -->
            <div class="flex items-center justify-between">
              <div class="text-left">
                <div class="text-sm font-bold text-white">总收益</div>
                <div class="text-sm font-medium text-white">{{ getTotalEarningsText }}</div>
              </div>
              <div class="text-right">
                <div class="text-sm font-bold text-white">积分</div>
                <div class="text-sm font-medium text-white">{{ getUserPoints.toLocaleString() }}</div>
              </div>
            </div>
            
            <!-- 累计违约金显示 -->
            <div v-if="getTotalBreachFee > 0" class="border-t border-white border-opacity-20 pt-3 mt-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <iconify-icon icon="material-symbols:warning" class="text-orange-400 text-sm mr-1"></iconify-icon>
                  <span class="text-xs opacity-80">累计违约金</span>
                </div>
                <div class="text-xs font-medium text-orange-400">
                  {{ formatPrice(getTotalBreachFee) }}₫
                </div>
              </div>
            </div>
          </div>
          
          <!-- 钱包错误状态 -->
          <div v-else-if="walletData && walletData._error" class="flex items-center justify-center py-4">
            <div class="text-center">
              <iconify-icon icon="material-symbols:error" class="text-red-300 text-2xl mb-2"></iconify-icon>
              <p class="text-xs opacity-80">{{ walletData._error }}</p>
            </div>
          </div>
          
          <!-- 钱包加载状态 -->
          <div v-else class="flex items-center justify-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
          </div>
          
          <!-- 提现和充值按钮 -->
          <div class="flex space-x-3">
            <button @click="goToWithdraw" class="flex-1 bg-white bg-opacity-20 text-white py-2 px-4 rounded-lg font-medium text-xs hover:bg-opacity-30 transition-colors">
              <iconify-icon icon="material-symbols:money" class="mr-1"></iconify-icon>
              提现
            </button>
            <button @click="goToRecharge" class="flex-1 bg-white bg-opacity-20 text-white py-2 px-4 rounded-lg font-medium text-xs hover:bg-opacity-30 transition-colors">
              <iconify-icon icon="material-symbols:add-circle" class="mr-1"></iconify-icon>
              充值
            </button>
          </div>
        </div>
      </div>

      <!-- 我的团队 -->
      <div class="bg-white mx-4 rounded-2xl p-4 -mt-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-bold text-gray-800 text-sm">我的团队</h3>
        </div>
        
        <!-- 团队统计 -->
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="flex flex-col items-center">
            <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:groups" class="text-indigo-500"></iconify-icon>
            </div>
            <span class="text-xs text-gray-600">团队成员</span>
            <span class="text-sm font-bold text-indigo-600">{{ teamStats.totalMembers }}</span>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:stars" class="text-green-500"></iconify-icon>
            </div>
            <span class="text-xs text-gray-600">累计奖励积分</span>
            <span class="text-sm font-bold text-green-600">{{ teamStats.totalRewardPoints }}</span>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:trending-up" class="text-purple-500"></iconify-icon>
            </div>
            <span class="text-xs text-gray-600">本周新增成员</span>
            <span class="text-sm font-bold text-purple-600">{{ teamStats.thisWeekNewMembers }}</span>
          </div>
        </div>
        
        <!-- 成员消费排行榜 -->
        <div class="border-t border-gray-100 pt-4">
          <!-- div class="flex items-center justify-between mb-3">
            <div class="text-xs font-medium text-gray-700">成员消费排行榜</div>
            <div 
              class="text-xs text-blue-500 cursor-pointer hover:text-blue-600 transition-colors"
              @click="goToMemberList"
            >
              成员列表 →
            </div>
          </div -->
          <!-- div class="space-y-2">
            <div 
              v-for="(member, index) in teamMembersRanking.slice(0, 3)" 
              :key="member.id"
              class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-3">
                  <span class="text-white text-xs font-bold">{{ index + 1 }}</span>
                </div>
                <div>
                  <div class="text-xs font-medium text-gray-800">ID: {{ member.id }}</div>
                  <div class="text-xs text-gray-500">总消费：{{ formatPrice(member.totalConsumption) }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-xs font-bold text-orange-600">{{ member.rewardPoints }}</div>
                <div class="text-xs text-gray-500">积分贡献</div>
              </div>
            </div>
          </div -->
          
          <!-- 分享按钮 -->
          <div class="mt-4 pt-3 border-t border-gray-100">
            <button 
              @click="shareToFriends"
              class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium text-sm hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-sm"
            >
              <iconify-icon icon="material-symbols:share" class="mr-2"></iconify-icon>
              分享给好友，一起赚钱
            </button>
            
            <!-- 邀请奖励说明 -->
            <div class="mt-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
              <div class="text-sm text-gray-700 leading-relaxed space-y-2">
                <div class="font-medium text-orange-700 mb-3">
                  💰 邀请奖励规则
                </div>
                
                <!-- 基础奖励 -->
                <div class="text-gray-600">
                  每成功邀请 1位好友注册并参与拼团一次，奖励 <span class="font-bold text-orange-600">10,000₫</span>
                </div>
                
                <!-- 阶梯奖励标题 -->
                <div class="font-medium text-gray-700 mt-3 mb-2">
                  邀请达到以下人数，额外再送大额奖励（可叠加领取）：
                </div>
                
                <!-- 阶梯奖励列表 -->
                <div class="space-y-1.5 pl-2">
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600">满 10人：</span>
                    <span class="font-bold text-orange-600">额外奖励 100,000₫</span>
                  </div>
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600">满 20人：</span>
                    <span class="font-bold text-orange-600">额外奖励 300,000₫</span>
                  </div>
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600">满 50人：</span>
                    <span class="font-bold text-red-600">额外奖励 1,000,000₫</span>
                  </div>
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600">满 100人：</span>
                    <span class="font-bold text-red-600">额外奖励 3,000,000₫</span>
                  </div>
                </div>
                <div class="font-medium text-gray-700 mt-3 mb-2">
                  💰300人以上，可以申请成为皇家商城合伙人，最低奖励<span class="font-bold text-orange-600">30M！</span>
                </div>                
                <!-- 底部提示 -->
                <div class="flex items-center justify-center mt-3 pt-2 border-t border-orange-200">
                  <span class="text-sm font-medium text-orange-700">
                    👉 奖励可叠加，邀请越多，赚得越多！
                  </span>
                </div>
                
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单管理 -->
      <div class="bg-white mx-4 rounded-2xl p-4 mt-4 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-bold text-gray-800 text-sm">我的订单</h3>
          <span class="text-blue-500 text-xs cursor-pointer" @click="goToOrders">查看全部</span>
        </div>
        <div class="grid grid-cols-3 gap-4">
          <div class="flex flex-col items-center cursor-pointer" @click="goToOrdersByStatus('shipping')">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:local-shipping" class="text-green-500"></iconify-icon>
            </div>
            <span class="text-xs text-gray-600">待发货</span>
          </div>
          <div class="flex flex-col items-center cursor-pointer" @click="goToOrdersByStatus('receiving')">
            <div class="relative w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:inventory" class="text-purple-500"></iconify-icon>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">1</div>
            </div>
            <span class="text-xs text-gray-600">待收货</span>
          </div>
          <div class="flex flex-col items-center cursor-pointer" @click="goToOrdersByStatus('review')">
            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:rate-review" class="text-yellow-500"></iconify-icon>
            </div>
            <span class="text-xs text-gray-600">待评价</span>
          </div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="px-4 py-4">
        <div class="bg-white rounded-2xl shadow-sm">
          <!-- 钱包相关 -->
          <!-- 地址管理 -->
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between cursor-pointer" style="min-height: 60px;" @click="goToAddress">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                  <iconify-icon icon="material-symbols:location-on" class="text-orange-500"></iconify-icon>
                </div>
                <div>
                  <div class="font-medium text-gray-800 text-xs">收货地址</div>
                  <div class="text-xs text-gray-500" style="font-size: 10px;">管理收货地址</div>
                </div>
              </div>
              <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
            </div>
          </div>

          <!-- 实名认证 -->
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between cursor-pointer" style="min-height: 60px;" @click="goToIdentityVerification">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <iconify-icon icon="material-symbols:verified-user" class="text-blue-500"></iconify-icon>
                </div>
                <div>
                  <div class="font-medium text-gray-800 text-xs flex items-center">
                    实名认证
                    <div 
                      v-if="identityVerification.status === 'verified'"
                      class="ml-2 px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full flex items-center"
                    >
                      <iconify-icon icon="material-symbols:check-circle" class="mr-1 text-xs"></iconify-icon>
                      已认证
                    </div>
                    <div 
                      v-else-if="identityVerification.status === 'pending'"
                      class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full flex items-center"
                    >
                      <iconify-icon icon="material-symbols:schedule" class="mr-1 text-xs"></iconify-icon>
                      审核中
                    </div>
                    <div 
                      v-else-if="identityVerification.status === 'rejected'"
                      class="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full flex items-center"
                    >
                      <iconify-icon icon="material-symbols:cancel" class="mr-1 text-xs"></iconify-icon>
                      已拒绝
                    </div>
                    <div 
                      v-else
                      class="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full flex items-center"
                    >
                      <iconify-icon icon="material-symbols:info" class="mr-1 text-xs"></iconify-icon>
                      未认证
                    </div>
                  </div>
                  <div class="text-xs text-gray-500" style="font-size: 10px;">
                    {{ getIdentityVerificationDescription }}
                  </div>
                </div>
              </div>
              <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
            </div>
          </div>

          <!-- 客服帮助 -->
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between cursor-pointer" style="min-height: 60px;" @click="goToCustomerService">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                  <iconify-icon icon="material-symbols:support-agent" class="text-purple-500"></iconify-icon>
                </div>
                <div>
                  <div class="font-medium text-gray-800 text-xs">客服中心</div>
                  <div class="text-xs text-gray-500" style="font-size: 10px;">在线客服、常见问题</div>
                </div>
              </div>
              <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
            </div>
          </div>

          <!-- 设置 -->
          <div class="p-4">
            <div class="flex items-center justify-between cursor-pointer" style="min-height: 60px;" @click="goToSettings">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  <iconify-icon icon="material-symbols:settings" class="text-gray-500"></iconify-icon>
                </div>
                <div>
                  <div class="font-medium text-gray-800 text-xs">设置</div>
                  <div class="text-xs text-gray-500" style="font-size: 10px;">账号安全、通知设置</div>
                </div>
              </div>
              <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 退出登录 -->
      <div class="px-4 py-4">
        <div class="bg-white rounded-2xl shadow-sm">
          <div class="p-4">
            <button 
              @click="showLogoutConfirm" 
              class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
            >
              <iconify-icon icon="material-symbols:logout" class="text-xl mr-2"></iconify-icon>
              <span>退出登录</span>
            </button>
          </div>
        </div>
      </div>


      <!-- 底部导航栏 -->
      <BottomNav current="user" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError, showLoading, hideLoading } from '@/utils/message.js'
import BottomNav from '@/components/common/BottomNav.vue'
import ShareButton from '@/components/common/ShareButton.vue'
import { formatPrice } from '@/utils/format'
import { useAuthGuard } from '@/composables/useAuthGuard'

export default {
  name: 'ProfilePage',
  components: {
    BottomNav,
    ShareButton
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 登录状态检查
    const {
      isAuthenticated,
      isLoading: authLoading,
      authError: guardAuthError,
      requireAuth,
      recheckAuth
    } = useAuthGuard({
      redirectPath: '/login',
      showMessages: true,
      autoRedirect: true,
      checkInterval: 30000 // 30秒检查一次
    })
    
    // 响应式数据
    const loading = ref(true)
    const walletData = ref(null) // 钱包数据
    const authError = ref(false) // 认证错误状态（保留原有逻辑兼容性）
    const retryCount = ref(0) // 重试次数
    const maxRetries = 3 // 最大重试次数
    
    // 实名认证相关数据
    const identityVerification = ref({
      status: 'not_verified', // not_verified, pending, verified, rejected
      hasReal: false, // 从API获取的实名认证状态
      realName: '',
      idCard: '',
      bankAccountName: '',
      bankName: '',
      bankCard: '',
      submitTime: null,
      auditTime: null,
      rejectReason: ''
    })
    
    // 团队相关数据（硬编码模拟）
    const showTeamPreview = ref(true) // 是否显示团队预览
    const teamMembers = ref([
      { id: 'U001588', rewardPoints: 1250, joinDate: '2024-01-15', status: 'active' },
      { id: 'U002341', rewardPoints: 890, joinDate: '2024-01-20', status: 'active' },
      { id: 'U003129', rewardPoints: 2150, joinDate: '2024-02-03', status: 'active' },
      { id: 'U004567', rewardPoints: 750, joinDate: '2024-02-10', status: 'active' },
      { id: 'U005234', rewardPoints: 1650, joinDate: '2024-02-18', status: 'active' },
      { id: 'U006789', rewardPoints: 580, joinDate: '2024-03-01', status: 'active' },
      { id: 'U007412', rewardPoints: 920, joinDate: '2024-03-08', status: 'active' },
      { id: 'U008156', rewardPoints: 1380, joinDate: '2024-03-15', status: 'active' }
    ])
    
    const teamStats = ref({
      totalMembers: 0,
      totalRewardPoints: 0,
      activeMembers: 0,
      thisWeekNewMembers: 2, // 暂时保持固定值，后续可从API获取
      thisMonthRewards: 0
    })
    
    // 团队成员消费排行榜数据（硬编码模拟）
    const teamMembersRanking = ref([
      { id: 'U003129', rewardPoints: 2150, totalConsumption: 45800, joinDate: '2024-02-03', status: 'active' },
      { id: 'U005234', rewardPoints: 1650, totalConsumption: 38200, joinDate: '2024-02-18', status: 'active' },
      { id: 'U007412', rewardPoints: 1380, totalConsumption: 32600, joinDate: '2024-03-08', status: 'active' },
      { id: 'U001588', rewardPoints: 1250, totalConsumption: 28900, joinDate: '2024-01-15', status: 'active' },
      { id: 'U008156', rewardPoints: 920, totalConsumption: 21500, joinDate: '2024-03-15', status: 'active' },
      { id: 'U002341', rewardPoints: 890, totalConsumption: 19800, joinDate: '2024-01-20', status: 'active' },
      { id: 'U004567', rewardPoints: 750, totalConsumption: 16400, joinDate: '2024-02-10', status: 'active' },
      { id: 'U006789', rewardPoints: 580, totalConsumption: 12700, joinDate: '2024-03-01', status: 'active' }
    ])
    
    // API服务
    let apiAdapter = null
    
    // 初始化API服务
    const initApiService = async () => {
      try {
        apiAdapter = new StandardApiAdapter()
        console.log('✅ 个人中心API服务初始化成功')
      } catch (err) {
        console.error('❌ 个人中心API服务初始化失败:', err)
        showError('API服务初始化失败')
      }
    }
    
    // 检查认证状态和token有效性
    const checkAuthenticationStatus = async () => {
      try {
        console.log('🔍 检查认证状态...')
        
        // 1. 检查基本登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录或无token')
          authError.value = true
          return false
        }
        
        // 2. 检查token格式
        const token = authStore.token
        if (!token || typeof token !== 'string' || token.length < 10) {
          console.warn('❌ Token格式无效')
          authError.value = true
          await handleAuthFailure('Token格式无效')
          return false
        }
        
        // 3. 验证token有效性（通过API调用）
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
        
        // 使用轻量级API验证token
        const response = await fetch('/api/v1/wallet', {
          method: 'GET',
          headers: headers,
          mode: 'cors',
          credentials: 'omit'
        })
        
        if (!response.ok) {
          const result = await response.json()
          if (result.code === 30007 || result.code === 401 || result.code === 403) {
            console.warn('❌ Token已失效:', result.code)
            authError.value = true
            await handleAuthFailure('登录已过期')
            return false
          }
        }
        
        console.log('✅ 认证状态检查通过')
        authError.value = false
        retryCount.value = 0
        return true
        
      } catch (error) {
        console.error('❌ 认证状态检查异常:', error)
        authError.value = true
        
        // 网络错误不立即跳转，允许重试
        if (retryCount.value < maxRetries) {
          retryCount.value++
          console.log(`🔄 认证检查重试 ${retryCount.value}/${maxRetries}`)
          return false
        } else {
          await handleAuthFailure('网络异常，请检查网络连接')
          return false
        }
      }
    }
    
    // 处理认证失败
    const handleAuthFailure = async (reason = '认证失败') => {
      try {
        console.error('🔐 认证失败处理:', reason)
        
        // 1. 清除认证状态
        await authStore.logout()
        
        // 2. 设置错误状态
        authError.value = true
        
        // 3. 显示错误提示
        showError(`${reason}，请重新登录`)
        
        // 4. 延迟跳转到登录页面
        setTimeout(() => {
          router.push({
            name: 'Login',
            query: { redirect: '/user' }
          })
        }, 2000)
        
      } catch (error) {
        console.error('❌ 认证失败处理异常:', error)
        // 强制跳转
        router.push('/login')
      }
    }
    
    // 获取钱包信息
    const getWalletInfo = async (forceRefresh = false) => {
      try {
        // 检查登录状态 - 直接检查authStore，不使用requireAuth避免弹窗
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 用户未登录，跳过钱包信息获取')
          walletData.value = {
            balance: 0,
            totalBalance: 0,
            experienceBalance: 0,
            points: 0,
            currency: 'VND',
            _lastUpdated: new Date().toISOString(),
            _error: '用户未登录'
          }
          return
        }
        
        if (!apiAdapter) {
          await initApiService()
        }
        
        console.log('🔄 获取钱包信息...', forceRefresh ? '(强制刷新)' : '')
        
        // 2. 使用StandardApiAdapter获取钱包信息
        const params = {}
        if (forceRefresh) {
          params._t = new Date().getTime()
        }
        
        const result = await apiAdapter.getWallet(params)
        console.log('💰 个人中心钱包API响应:', result)
        
        if (result.code === 200 && result.data) {
          // 3. 解析钱包数据，适配实际API返回的数据结构
          const rawData = result.data || {}
          
          // 构建标准化的钱包数据结构
          walletData.value = {
            // 主要余额信息
            balance: rawData.balance?.balance || rawData.balance?.totalBalance || 0,
            totalBalance: rawData.balance?.totalBalance || 0,
            experienceBalance: rawData.balance?.experienceBalance || 0,
            points: rawData.balance?.points || 0,
            totalRecharge: rawData.balance?.totalRecharge || 0,
            totalWithdraw: rawData.balance?.totalWithdraw || 0,
            totalEarnings: rawData.balance?.totalEarnings || 0,
            
            // 账户状态
            status: rawData.balance?.status || 1,
            
            // 交易记录
            transactions: rawData.transactions || [],
            
            // 货币类型
            currency: 'VND',
            
            // 记录更新时间
            _lastUpdated: new Date().toISOString(),
            
            // 保存原始数据用于调试
            _raw: rawData
          }
          
          console.log('✅ 个人中心钱包信息解析成功:', {
            balance: walletData.value.balance,
            totalBalance: walletData.value.totalBalance,
            experienceBalance: walletData.value.experienceBalance,
            points: walletData.value.points,
            status: walletData.value.status
          })
          
          // 重置错误状态
          authError.value = false
          retryCount.value = 0
          
        } else {
          console.warn('⚠️ 钱包信息获取失败:', result.message)
          walletData.value = {
            balance: 0,
            totalBalance: 0,
            experienceBalance: 0,
            points: 0,
            currency: 'VND',
            _lastUpdated: new Date().toISOString(),
            _error: result.message || '获取失败'
          }
        }
        
      } catch (error) {
        console.error('❌ 钱包信息获取异常:', error)
        
        // 处理认证相关错误
        if (error.message?.includes('登录已过期') || 
            error.message?.includes('认证失败') ||
            error.message?.includes('token')) {
          console.log('🔐 检测到认证错误，触发重新检查')
          await recheckAuth()
          return
        }
        
        // 处理其他错误
        if (error.message?.includes('网络')) {
          showError('网络连接失败，请检查网络')
        } else {
          showError('获取钱包信息失败，请重试')
        }
        
        walletData.value = {
          balance: 0,
          totalBalance: 0,
          experienceBalance: 0,
          points: 0,
          currency: 'VND',
          _lastUpdated: new Date().toISOString(),
          _error: error.message || '网络异常'
        }
      }
    }
    
    // 刷新钱包信息（手动触发）
    const refreshWalletInfo = async () => {
      try {
        console.log('🔄 手动刷新钱包信息')
        await getWalletInfo(true)
        showSuccess('钱包信息已刷新')
      } catch (error) {
        console.error('❌ 手动刷新失败:', error)
        showError('刷新失败，请重试')
      }
    }
    
    // 重试认证
    const retryAuthentication = async () => {
      try {
        console.log('🔄 重试认证...')
        retryCount.value = 0
        authError.value = false
        await initializeData()
      } catch (error) {
        console.error('❌ 重试认证失败:', error)
        showError('重试失败，请重新登录')
      }
    }
    
    // 计算属性
    const getUserDisplayName = computed(() => {
      if (authStore.user) {
        return authStore.user.actualName || authStore.user.nickname || authStore.user.username || authStore.user.loginName || 'Nguyễn Minh'
      }
      return 'Nguyễn Minh'
    })
    
    const getMaskedPhone = computed(() => {
      if (authStore.user && authStore.user.phone) {
        const phone = authStore.user.phone
        if (phone.length >= 7) {
          return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4)
        }
        return phone
      }
      return '090****8888'
    })
    
    const getUserBalance = computed(() => {
      if (!walletData.value) return 0
      return parseFloat(
        walletData.value.balance || 
        walletData.value.totalBalance || 
        0
      )
    })
    
    const getBalanceText = computed(() => {
      const balance = getUserBalance.value
      const currency = walletData.value?.currency || 'VND'
      
      if (currency === 'VND') {
        return `${balance.toLocaleString()}₫`
      } else {
        return `¥${balance.toLocaleString()}`
      }
    })
    
    const getUserPoints = computed(() => {
      if (!walletData.value) return 0
      return parseFloat(walletData.value.points || 0)
    })
    
    const getExperienceBalance = computed(() => {
      if (!walletData.value) return 0
      return parseFloat(walletData.value.experienceBalance || 0)
    })
    
    const getTotalEarnings = computed(() => {
      if (!walletData.value) return 0
      return parseFloat(walletData.value.totalEarnings || 0)
    })
    
    const getTotalBreachFee = computed(() => {
      if (!walletData.value) return 0
      return parseFloat(walletData.value.totalBreachFee || 0)
    })
    
    const getTotalEarningsText = computed(() => {
      const earnings = getTotalEarnings.value
      const currency = walletData.value?.currency || 'VND'
      
      if (currency === 'VND') {
        return `${earnings.toLocaleString()}₫`
      } else {
        return `¥${earnings.toLocaleString()}`
      }
    })
    
    // 实名认证相关计算属性
    const getIdentityVerificationDescription = computed(() => {
      const status = identityVerification.value.status
      switch (status) {
        case 'verified':
          return '已完成实名认证'
        case 'pending':
          return '正在审核中，请耐心等待'
        case 'rejected':
          return '认证被拒绝，请重新提交'
        default:
          return '未完成实名认证，提现需要先认证'
      }
    })
    
    // 实名认证相关方法
    const loadIdentityVerificationStatus = async () => {
      try {
        // 检查登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录，跳过实名认证状态获取')
          return
        }
        
        if (!apiAdapter) {
          await initApiService()
        }
        
        console.log('🔍 获取实名认证状态...')
        
        // 使用StandardApiAdapter获取钱包信息（其中包含实名认证状态）
        const result = await apiAdapter.getWallet()
        console.log('📝 用户wallet API响应:', result)
        
        if (result.code === 200 && result.data) {
          const data = result.data || {}
          const balance = data.balance || {}
          
          // 从wallet API的balance对象中获取hasReal字段
          const hasReal = balance.hasReal === true || balance.hasReal === 1
          
          identityVerification.value = {
            status: hasReal ? 'verified' : 'not_verified',
            hasReal: hasReal,
            realName: balance.realName || '',
            idCard: balance.idCard || '',
            bankAccountName: balance.bankAccountName || '',
            bankName: balance.bankName || '',
            bankCard: balance.bankCard || '',
            submitTime: balance.submitTime || null,
            auditTime: balance.auditTime || null,
            rejectReason: balance.rejectReason || ''
          }
          
          console.log('✅ 实名认证状态加载成功:', {
            hasReal: hasReal,
            status: identityVerification.value.status
          })
          
        } else {
          console.warn('⚠️ 实名认证状态获取失败:', result.message)
          // 保持默认的未认证状态
        }
        
      } catch (error) {
        console.error('❌ 实名认证状态获取失败:', error)
        
        // 处理认证相关错误
        if (error.message?.includes('登录已过期') || 
            error.message?.includes('认证失败') ||
            error.message?.includes('token')) {
          console.log('🔐 实名认证状态获取检测到认证错误，触发重新检查')
          await recheckAuth()
          return
        }
        
        // 不显示错误消息，保持默认的未认证状态
      }
    }
    
    // 获取本周新增成员数量（使用API返回的weekCount字段）
    const getThisWeekNewMembers = (data) => {
      return data?.weekCount || 0
    }
    
    // 获取团队数据
    const getTeamData = async () => {
      try {
        // 检查登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录，跳过团队数据获取')
          return
        }
        
        if (!apiAdapter) {
          await initApiService()
        }
        
        console.log('🔄 获取团队数据...')
        
        // 使用StandardApiAdapter获取团队数据
        const result = await apiAdapter.getMyTeam()
        console.log('👥 团队API响应:', result)
        
        if (result.code === 200 && result.data) {
          const data = result.data || {}
          const membersList = data.list || []
          
          // 获取本周新增成员数量（使用API的weekCount字段）
          const thisWeekNewMembers = getThisWeekNewMembers(data)
          
          // 更新团队统计信息
          teamStats.value = {
            totalMembers: data.total || 0,
            totalRewardPoints: data.totalTeamReward || 0,
            activeMembers: data.total || 0,
            thisWeekNewMembers: thisWeekNewMembers,
            thisMonthRewards: data.totalTeamReward || 0
          }
          
          // 更新团队成员排行榜数据，按积分贡献排序
          teamMembersRanking.value = membersList
            .map(member => ({
              id: member.id,
              rewardPoints: member.contribution || 0,
              totalConsumption: member.consume || 0,
              joinDate: member.createTime,
              status: 'active'
            }))
            .sort((a, b) => b.rewardPoints - a.rewardPoints) // 按积分贡献降序排列
          
          console.log('✅ 团队统计数据更新成功:', teamStats.value)
          console.log('✅ 团队排行榜数据更新成功:', teamMembersRanking.value.slice(0, 3))
          
        } else {
          console.warn('⚠️ 团队数据获取失败:', result.message)
          // 使用默认值，不显示错误
        }
        
      } catch (error) {
        console.error('❌ 获取团队数据失败:', error)
        
        // 处理认证相关错误
        if (error.message?.includes('登录已过期') || 
            error.message?.includes('认证失败') ||
            error.message?.includes('token')) {
          console.log('🔐 团队数据获取检测到认证错误，触发重新检查')
          await recheckAuth()
          return
        }
        
        // 其他错误不显示提示，因为这不是关键功能
      }
    }
    
    // 导航方法
    const goToHome = () => {
      router.push('/')
    }
    
    const goToWithdraw = () => {
      console.log('🔄 跳转到提现页面')
      router.push('/user/withdraw')
    }
    
    const goToRecharge = () => {
      showError('充值功能正在开发中')
    }
    
    const goToWallet = () => {
      console.log('🔄 跳转到钱包页面')
      router.push('/user/wallet')
    }
    
    const goToCoupons = () => {
      console.log('🔄 跳转到优惠券页面')
      router.push('/user/coupons')
    }
    
    const goToFavorites = () => {
      console.log('🔄 跳转到收藏页面')
      router.push('/user/favorites')
    }
    
    const goToHistory = () => {
      console.log('🔄 跳转到浏览足迹页面')
      router.push('/user/history')
    }
    
    const goToAddress = () => {
      router.push('/user/address')
    }
    
    const goToIdentityVerification = () => {
      console.log('🔄 跳转到实名认证页面')
      const status = identityVerification.value.status
      
      if (status === 'not_verified' || status === 'rejected') {
        // 未认证或被拒绝，跳转到认证页面
        router.push('/user/identity-verification')
      } else {
        // 已认证或审核中，跳转到管理页面
        router.push('/user/identity-manage')
      }
    }
    
    const goToTeam = () => {
      console.log('🔄 跳转到我的团队页面')
      router.push('/user/team')
    }
    
    const goToMemberList = () => {
      console.log('🔄 跳转到成员列表页面')
      router.push('/user/team?tab=ranking')
    }
    
    const shareToFriends = async () => {
      console.log('🔄 分享给好友')
      try {
        const { generateAndCopyShareLink } = await import('@/utils/share')
        await generateAndCopyShareLink(
          '/home',
          (shareLink) => {
            showSuccess('邀请链接已复制，快去分享给好友一起赚钱吧！')
            console.log('分享链接已生成:', shareLink)
          },
          (error) => {
            showError('复制失败，请重试')
            console.error('分享失败:', error)
          }
        )
      } catch (error) {
        console.error('分享功能加载失败:', error)
        showError('分享功能暂时不可用')
      }
    }
    
    const goToCustomerService = () => {
      console.log('🔄 跳转到客服中心页面')
      router.push('/user/customer-service')
    }
    
    const goToSettings = () => {
      console.log('🔄 跳转到设置页面')
      router.push('/user/settings')
    }
    
    const goToOrders = () => {
      console.log('🔄 跳转到订单页面')
      router.push('/user/orders')
    }
    
    const goToOrdersByStatus = (status) => {
      console.log('🔄 跳转到订单页面，状态:', status)
      // 将状态映射到正确的tab名称
      const statusMap = {
        'shipping': 'SHIPPING',
        'receiving': 'SHIPPING', 
        'review': 'COMPLETED'
      }
      const tabName = statusMap[status] || status
      router.push(`/user/orders?tab=${tabName}`)
    }

    // 显示退出登录确认对话框
    const showLogoutConfirm = () => {
      // 使用浏览器原生确认对话框
      if (confirm('确定要退出登录吗？')) {
        logout()
      }
    }

    // 退出登录
    const logout = async () => {
      try {
        console.log('🔐 开始退出登录...')
        
        // 显示加载提示
        showLoading('正在退出...')
        
        // 1. 调用后端登出API（如果有的话）
        try {
          if (apiAdapter && authStore.token) {
            await apiAdapter.logout()
            console.log('✅ 后端登出成功')
          }
        } catch (error) {
          console.warn('⚠️ 后端登出失败，继续本地登出:', error)
          // 即使后端登出失败，也继续本地登出
        }
        
        // 2. 清除本地认证状态
        await authStore.logout()
        
        // 3. 清除相关的本地存储
        localStorage.removeItem('userSettings')
        localStorage.removeItem('walletData')
        localStorage.removeItem('hasRecharge')
        
        // 4. 重置页面状态
        walletData.value = null
        authError.value = false
        retryCount.value = 0
        
        console.log('✅ 退出登录成功')
        
        // 5. 显示成功提示
        hideLoading()
        showSuccess('已退出登录')
        
        // 6. 直接跳转到首页
        setTimeout(() => {
          router.push('/')
        }, 800)
        
      } catch (error) {
        console.error('❌ 退出登录失败:', error)
        hideLoading()
        showError('退出登录失败，请重试')
      }
    }
    
    // 初始化数据
    const initializeData = async () => {
      try {
        loading.value = true
        authError.value = false
        
        console.log('🚀 个人中心页面初始化开始')
        console.log('🔍 检查认证状态:', {
          isAuthenticated: isAuthenticated.value,
          authStoreLoggedIn: authStore.isLoggedIn,
          hasToken: !!authStore.token
        })
        
        // 1. 检查基本登录状态 - 直接检查authStore状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 用户未登录，跳过数据初始化')
          // 设置默认数据
          walletData.value = {
            balance: 0,
            totalBalance: 0,
            experienceBalance: 0,
            points: 0,
            currency: 'VND',
            _lastUpdated: new Date().toISOString(),
            _error: '用户未登录'
          }
          return
        }
        
        console.log('✅ 用户已登录，开始获取数据')
        
        // 2. 获取钱包信息（包含认证检查）
        console.log('📥 开始获取钱包信息...')
        await getWalletInfo(true)
        
        // 3. 加载实名认证状态
        console.log('📥 开始获取实名认证状态...')
        await loadIdentityVerificationStatus()
        
        // 4. 获取团队数据
        console.log('📥 开始获取团队数据...')
        await getTeamData()
        
        console.log('✅ 个人中心页面初始化完成')
        
      } catch (error) {
        console.error('❌ 个人中心页面初始化失败:', error)
        authError.value = true
        
        // 设置默认钱包数据
        walletData.value = {
          balance: 0,
          totalBalance: 0,
          experienceBalance: 0,
          points: 0,
          currency: 'VND',
          _lastUpdated: new Date().toISOString(),
          _error: '初始化失败'
        }
        
        showError('页面初始化失败，请重试')
        
      } finally {
        loading.value = false
      }
    }
    
    // 页面可见性变化时刷新钱包信息
    const handleVisibilityChange = () => {
      if (!document.hidden && authStore.isLoggedIn && walletData.value && !authError.value) {
        console.log('📱 个人中心页面重新可见，刷新钱包信息')
        getWalletInfo(true)
      }
    }
    
    // 窗口重新获得焦点时刷新
    const handleWindowFocus = () => {
      if (authStore.isLoggedIn && walletData.value && !authError.value) {
        console.log('🔍 窗口重新获得焦点，刷新钱包信息')
        getWalletInfo(true)
      }
    }
    
    // 生命周期
    onMounted(() => {
      console.log('🚀 个人中心页面挂载')
      
      // 初始化数据
      initializeData()
      
      // 监听页面可见性变化
      document.addEventListener('visibilitychange', handleVisibilityChange)
      
      // 监听窗口焦点变化
      window.addEventListener('focus', handleWindowFocus)
      
      console.log('✅ 个人中心页面事件监听器已注册')
    })
    
    onUnmounted(() => {
      console.log('🔄 个人中心页面卸载')
      
      // 移除事件监听器
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleWindowFocus)
      
      console.log('✅ 个人中心页面事件监听器已移除')
    })
    
    return {
      // 响应式数据
      loading,
      walletData,
      authError,
      retryCount,
      maxRetries,
      
      // 团队相关数据
      showTeamPreview,
      teamMembers,
      teamStats,
      teamMembersRanking,
      
      // 实名认证相关数据
      identityVerification,
      
      // 计算属性
      getUserDisplayName,
      getMaskedPhone,
      getUserBalance,
      getBalanceText,
      getUserPoints,
      getExperienceBalance,
      getTotalEarnings,
      getTotalEarningsText,
      getTotalBreachFee,
      getIdentityVerificationDescription,
      
      // 方法
      refreshWalletInfo,
      retryAuthentication,
      goToHome,
      goToWithdraw,
      goToRecharge,
      goToWallet,
      goToFavorites,
      goToAddress,
      goToIdentityVerification,
      goToMemberList,
      shareToFriends,
      goToCustomerService,
      goToSettings,
      goToOrders,
      goToOrdersByStatus,
      showLogoutConfirm,
      logout,
      formatPrice
    }
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
body {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 渐变背景 */
.gradient-bg { 
  background: linear-gradient(135deg, #4F46E5, #8B5CF6); 
}

/* 统计项分隔线 */
.stat-item:not(:last-child) { 
  border-right: 1px solid rgba(255, 255, 255, 0.2); 
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 点击效果 */
.cursor-pointer:active {
  transform: scale(0.98);
}
</style> 