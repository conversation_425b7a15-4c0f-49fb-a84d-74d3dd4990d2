<!--
  实名认证审核弹窗组件
  Created: 2025-08-11
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="500"
    :confirm-loading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="record" class="review-modal">
      <!-- 用户基本信息 -->
      <div class="user-info">
        <div class="user-avatar">
          <a-avatar :src="record.userAvatar" :size="48">
            {{ record.realName ? record.realName.charAt(0) : 'U' }}
          </a-avatar>
        </div>
        <div class="user-details">
          <div class="user-name">{{ record.realName || '未填写' }}</div>
          <div class="user-meta">身份证号：{{ maskIdCard(record.idCardNumber) }}</div>
        </div>
      </div>

      <a-divider />

      <!-- 审核表单 -->
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <!-- 审核操作确认 -->
        <div class="review-confirmation">
          <a-alert
            :message="confirmationMessage"
            :type="reviewType === 'approve' ? 'success' : 'warning'"
            show-icon
            banner
          />
        </div>

        <!-- 拒绝原因（仅拒绝时显示） -->
        <a-form-item
          v-if="reviewType === 'reject'"
          name="rejectReason"
          label="拒绝原因"
          required
        >
          <a-select
            v-model:value="formData.rejectReason"
            placeholder="请选择拒绝原因"
            allowClear
          >
            <a-select-option value="身份证图片不清晰">身份证图片不清晰</a-select-option>
            <a-select-option value="身份证信息与填写不符">身份证信息与填写不符</a-select-option>
            <a-select-option value="身份证已过期">身份证已过期</a-select-option>
            <a-select-option value="图片疑似伪造">图片疑似伪造</a-select-option>
            <a-select-option value="信息填写不完整">信息填写不完整</a-select-option>
            <a-select-option value="重复提交认证">重复提交认证</a-select-option>
            <a-select-option value="其他原因">其他原因</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 自定义拒绝原因 -->
        <a-form-item
          v-if="reviewType === 'reject' && formData.rejectReason === '其他原因'"
          name="customRejectReason"
          label="请详细说明拒绝原因"
          required
        >
          <a-textarea
            v-model:value="formData.customRejectReason"
            placeholder="请详细说明拒绝原因，以便用户了解并重新提交"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <!-- 审核备注 -->
        <a-form-item name="reviewNote" label="审核备注">
          <a-textarea
            v-model:value="formData.reviewNote"
            placeholder="可填写审核备注信息（可选）"
            :rows="2"
            :maxlength="100"
            show-count
          />
        </a-form-item>

        <!-- 快速填充模板（仅拒绝时显示） -->
        <div v-if="reviewType === 'reject'" class="quick-templates">
          <div class="template-label">快速模板：</div>
          <a-space wrap>
            <a-button
              v-for="template in quickTemplates"
              :key="template.value"
              size="small"
              type="dashed"
              @click="applyTemplate(template)"
            >
              {{ template.label }}
            </a-button>
          </a-space>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { identityApi } from '../identity-api'
import { maskIdCard } from '/@/utils/format'
import { smartSentry } from '/@/lib/smart-sentry'

// 属性定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  },
  reviewType: {
    type: String, // 'approve' 或 'reject'
    required: true
  }
})

// 事件定义
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const confirmLoading = ref(false)
const formRef = ref()

const modalVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const modalTitle = computed(() => {
  return props.reviewType === 'approve' ? '审核通过' : '审核拒绝'
})

const confirmationMessage = computed(() => {
  if (!props.record) return ''
  
  const userName = props.record.realName || '该用户'
  return props.reviewType === 'approve' 
    ? `确认通过 ${userName} 的实名认证申请？`
    : `确认拒绝 ${userName} 的实名认证申请？`
})

// 表单数据
const formData = reactive({
  rejectReason: '',
  customRejectReason: '',
  reviewNote: ''
})

// 表单验证规则
const rules = {
  rejectReason: [
    { required: true, message: '请选择拒绝原因', trigger: 'change' }
  ],
  customRejectReason: [
    { required: true, message: '请填写详细的拒绝原因', trigger: 'blur' }
  ]
}

// 快速模板
const quickTemplates = [
  {
    label: '图片不清晰',
    value: {
      rejectReason: '身份证图片不清晰',
      reviewNote: '请重新拍摄清晰的身份证正反面照片'
    }
  },
  {
    label: '信息不符',
    value: {
      rejectReason: '身份证信息与填写不符',
      reviewNote: '请确保填写信息与身份证完全一致'
    }
  },
  {
    label: '证件过期',
    value: {
      rejectReason: '身份证已过期',
      reviewNote: '请使用有效期内的身份证进行认证'
    }
  }
]

// 监听显示状态重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    rejectReason: '',
    customRejectReason: '',
    reviewNote: ''
  })
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 应用快速模板
const applyTemplate = (template) => {
  formData.rejectReason = template.value.rejectReason
  formData.reviewNote = template.value.reviewNote
  formData.customRejectReason = ''
}

// 提交审核
const handleSubmit = async () => {
  if (!props.record) return
  
  try {
    // 表单验证（仅拒绝时需要验证）
    if (props.reviewType === 'reject') {
      await formRef.value.validate()
    }
    
    confirmLoading.value = true
    
    // 构建审核参数
    const params = {
      remark: formData.reviewNote
    }
    
    if (props.reviewType === 'reject') {
      // 拒绝原因处理
      if (formData.rejectReason === '其他原因') {
        params.reason = formData.customRejectReason
      } else {
        params.reason = formData.rejectReason
      }
    }
    
    // 调用审核API
    console.log('🔍 [审核弹窗] 准备调用API:', {
      reviewType: props.reviewType,
      recordId: props.record.id,
      params: params
    })
    
    if (props.reviewType === 'approve') {
      await identityApi.approve(props.record.id, params)
      message.success('审核通过成功')
    } else {
      await identityApi.reject(props.record.id, params)
      message.success('审核拒绝成功')
    }
    
    // 通知父组件审核成功
    emit('success')
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    
    const action = props.reviewType === 'approve' ? '审核通过' : '审核拒绝'
    message.error(`${action}失败：${error.message}`)
    smartSentry.captureError(error)
  } finally {
    confirmLoading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}
</script>

<style scoped>
.review-modal {
  padding: 0;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px 0;
}

.user-avatar {
  margin-right: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.user-meta {
  font-size: 14px;
  color: #8c8c8c;
}

.review-confirmation {
  margin-bottom: 16px;
}

.quick-templates {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.template-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.ant-alert) {
  border-radius: 6px;
}

:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-textarea) {
  border-radius: 4px;
}

:deep(.ant-btn-dashed) {
  border-style: dashed;
  transition: all 0.2s;
}

:deep(.ant-btn-dashed:hover) {
  border-color: #1890ff;
  color: #1890ff;
}
</style>