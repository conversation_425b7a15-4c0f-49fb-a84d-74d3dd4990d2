package net.lab1024.sa.admin.module.app.orders;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.app.auth.AuthParamForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "APP:下单，订单列表，订单详情")
public class AppOrdersController {
    @Resource
    AppOrdersService appOrdersService;

    @Operation(summary = "订单列表")
    @GetMapping("/app/v1/orders")
    public ResponseDTO<Object> orders(@RequestParam(required = false) String sort,
                                      @RequestParam(required = false) Long pageNum,
                                      @RequestParam(required = false) Long pageSize) {
        return ResponseDTO.ok(appOrdersService.orders(sort, pageNum ,pageSize));
    }

    @Operation(summary = "订单详情")
    @GetMapping("/app/v1/order/{orderId}")
    public ResponseDTO<Object> orderDetail(@PathVariable Long orderId) {
        return appOrdersService.orderDetail(orderId);
    }

    @Operation(summary = "订单物流")
    @GetMapping("/app/v1/order/logistics/{orderId}")
    public ResponseDTO<Object> orderLogistics(@PathVariable Long orderId) {
        return appOrdersService.Logistics(orderId);
    }

    @Operation(summary = "下单")
    @PostMapping("/app/v1/placeOrder")
    public ResponseDTO<Object> placeOrder(@Valid @RequestBody AppOrderParamForm appOrderParamForm) {
        return ResponseDTO.ok(appOrdersService.placeOrder(appOrderParamForm));
    }
}
