package net.lab1024.sa.admin.module.app.auth;

import cn.hutool.extra.servlet.JakartaServletUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.system.login.service.LoginService;
import net.lab1024.sa.base.common.annoation.NoNeedLogin;
import net.lab1024.sa.base.common.constant.RequestHeaderConst;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.captcha.CaptchaService;
import net.lab1024.sa.base.module.support.captcha.domain.CaptchaVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Slf4j
@RestController
@Tag(name = "APP:登录、注册")
public class AuthController {
    @Resource
    private AuthService authService;

    @Resource
    private CaptchaService captchaService;

    @Resource
    private LoginService loginService;

    @Operation(summary = "APP登录")
    @NoNeedLogin
    @PostMapping("/app/v1/auth")
    public ResponseDTO<Object> auth(@Valid @RequestBody AuthParamForm authFormLogin, HttpServletRequest request){
        String ip = JakartaServletUtil.getClientIP(request);
        String userAgent = JakartaServletUtil.getHeaderIgnoreCase(request, RequestHeaderConst.USER_AGENT);
        if (Objects.equals(authFormLogin.getType(), "login")) {
            return authService.login(authFormLogin, ip, userAgent);
        } else if (Objects.equals(authFormLogin.getType(), "register")) {
            return authService.register(authFormLogin);
        }
        return ResponseDTO.userErrorParam("error param");
    }

    @Operation(summary = "APP获取图形验证码")
    @NoNeedLogin
    @GetMapping("/app/v1/captcha")
    public ResponseDTO<CaptchaVO> generateCaptcha() {
        return ResponseDTO.ok(captchaService.generateCaptcha());
    }

    @Operation(summary = "APP登出")
    @GetMapping("/app/v1/logout")
    public ResponseDTO<String> logout() {
        return loginService.logout(SmartRequestUtil.getRequestUser());
    }

}
