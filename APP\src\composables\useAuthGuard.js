import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showError, showLoading, hideLoading } from '@/utils/message'

/**
 * 页面级别的登录检查组合式函数
 * 为需要登录的页面提供完整的认证检查和错误处理
 */
export function useAuthGuard(options = {}) {
  const router = useRouter()
  const route = useRoute()
  const authStore = useAuthStore()
  
  // 配置选项
  const {
    redirectPath = '/login',
    showMessages = true,
    autoRedirect = true,
    checkInterval = 30000, // 30秒检查一次
    maxRetries = 3
  } = options
  
  // 响应式状态
  const isAuthenticated = ref(false)
  const isLoading = ref(true)
  const authError = ref(null)
  const retryCount = ref(0)
  const lastCheckTime = ref(null)
  
  // 定时器引用
  let checkTimer = null
  
  // 计算属性
  const needsAuth = computed(() => {
    return route.meta?.requiresAuth === true
  })
  
  const canAccess = computed(() => {
    return !needsAuth.value || isAuthenticated.value
  })
  
  const shouldShowError = computed(() => {
    return needsAuth.value && !isAuthenticated.value && authError.value
  })
  
  /**
   * 执行登录状态检查
   */
  const checkAuthStatus = async (silent = false) => {
    try {
      if (!silent && showMessages) {
        showLoading('检查登录状态...')
      }
      
      // 1. 基础检查：token 和 store 状态
      if (!authStore.token || !authStore.isLoggedIn) {
        throw new Error('未登录')
      }
      
      // 2. 验证 token 有效性（只在必要时进行网络检查）
      if (silent || Date.now() - (lastCheckTime.value?.getTime() || 0) > 60000) {
        try {
          const isValid = await authStore.checkAuthStatus()
          if (!isValid) {
            throw new Error('登录已过期')
          }
        } catch (error) {
          // 如果是网络错误，不立即判定为登录失效
          if (error.message?.includes('网络')) {
            console.warn('⚠️ 网络检查失败，跳过本次验证')
          } else {
            throw error
          }
        }
      }
      
      // 3. 更新状态
      isAuthenticated.value = true
      authError.value = null
      retryCount.value = 0
      lastCheckTime.value = new Date()
      
      console.log('✅ 登录状态检查通过')
      return true
      
    } catch (error) {
      console.error('❌ 登录状态检查失败:', error.message)
      
      isAuthenticated.value = false
      authError.value = error.message
      
      // 处理不同类型的错误
      if (error.message.includes('网络')) {
        // 网络错误，允许重试
        if (retryCount.value < maxRetries) {
          retryCount.value++
          if (showMessages) {
            showError(`网络异常，正在重试 (${retryCount.value}/${maxRetries})`)
          }
          
          // 延迟重试
          setTimeout(() => {
            checkAuthStatus(true)
          }, 2000)
          
          return false
        }
      }
      
      // 认证失败，清理状态
      await authStore.logout()
      
      if (showMessages) {
        showError(error.message)
      }
      
      return false
      
    } finally {
      if (!silent && showMessages) {
        hideLoading()
      }
      isLoading.value = false
    }
  }
  
  /**
   * 跳转到登录页面
   */
  const redirectToLogin = () => {
    if (!autoRedirect) return
    
    const currentPath = route.fullPath
    const loginPath = {
      path: redirectPath,
      query: { redirect: currentPath }
    }
    
    console.log('🔐 跳转到登录页面:', loginPath)
    router.push(loginPath)
  }
  
  /**
   * 手动重新检查认证状态
   */
  const recheckAuth = async () => {
    isLoading.value = true
    authError.value = null
    retryCount.value = 0
    
    const success = await checkAuthStatus()
    if (!success && needsAuth.value) {
      redirectToLogin()
    }
    
    return success
  }
  
  /**
   * 强制要求登录
   */
  const requireAuth = (message = '此页面需要登录访问') => {
    if (!isAuthenticated.value) {
      if (showMessages) {
        showError(message)
      }
      redirectToLogin()
      return false
    }
    return true
  }
  
  /**
   * 启动定期检查
   */
  const startPeriodicCheck = () => {
    if (checkTimer) return
    
    checkTimer = setInterval(async () => {
      if (needsAuth.value && isAuthenticated.value) {
        const success = await checkAuthStatus(true)
        if (!success) {
          redirectToLogin()
        }
      }
    }, checkInterval)
    
    console.log('🔄 启动定期登录状态检查，间隔:', checkInterval + 'ms')
  }
  
  /**
   * 停止定期检查
   */
  const stopPeriodicCheck = () => {
    if (checkTimer) {
      clearInterval(checkTimer)
      checkTimer = null
      console.log('⏹️ 停止定期登录状态检查')
    }
  }
  
  /**
   * 页面可见性变化处理
   */
  const handleVisibilityChange = async () => {
    if (!document.hidden && needsAuth.value) {
      console.log('📱 页面重新可见，检查登录状态')
      const success = await checkAuthStatus(true)
      if (!success) {
        redirectToLogin()
      }
    }
  }
  
  /**
   * 窗口焦点变化处理
   */
  const handleFocusChange = async () => {
    if (needsAuth.value) {
      console.log('🔍 窗口重新获得焦点，检查登录状态')
      const success = await checkAuthStatus(true)
      if (!success) {
        redirectToLogin()
      }
    }
  }
  
  // 生命周期处理
  onMounted(async () => {
    console.log('🚀 AuthGuard 初始化，页面:', route.path, '需要认证:', needsAuth.value)
    
    if (needsAuth.value) {
      // 执行初始检查
      const success = await checkAuthStatus()
      
      if (success) {
        // 启动定期检查
        startPeriodicCheck()
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', handleVisibilityChange)
        window.addEventListener('focus', handleFocusChange)
      } else {
        // 检查失败，跳转登录
        redirectToLogin()
      }
    } else {
      // 不需要认证的页面
      isLoading.value = false
      isAuthenticated.value = true
    }
  })
  
  onBeforeUnmount(() => {
    console.log('🔄 AuthGuard 清理')
    
    // 清理定时器
    stopPeriodicCheck()
    
    // 移除事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleFocusChange)
  })
  
  return {
    // 状态
    isAuthenticated,
    isLoading,
    authError,
    needsAuth,
    canAccess,
    shouldShowError,
    retryCount,
    maxRetries,
    lastCheckTime,
    
    // 方法
    checkAuthStatus,
    recheckAuth,
    requireAuth,
    redirectToLogin,
    
    // 用户信息
    user: computed(() => authStore.user),
    token: computed(() => authStore.token),
    isLoggedIn: computed(() => authStore.isLoggedIn)
  }
}

/**
 * 简化版本的登录检查，适用于组件内快速检查
 */
export function useQuickAuthCheck() {
  const authStore = useAuthStore()
  const router = useRouter()
  const route = useRoute()
  
  const requireAuth = (message = '请先登录', redirectPath = '/login') => {
    if (!authStore.isLoggedIn || !authStore.token) {
      if (message) {
        showError(message)
      }
      
      router.push({
        path: redirectPath,
        query: { redirect: route.fullPath }
      })
      
      return false
    }
    
    return true
  }
  
  const isAuthenticated = computed(() => authStore.isLoggedIn && !!authStore.token)
  
  return {
    isAuthenticated,
    requireAuth,
    user: computed(() => authStore.user),
    token: computed(() => authStore.token)
  }
}