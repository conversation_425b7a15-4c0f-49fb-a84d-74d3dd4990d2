# 钱包页面认证失败处理优化说明

## 修改概述

根据用户需求，当钱包页面遇到登录过期或认证失败时，不再显示认证失败界面，而是直接跳转到登录页面，提供更好的用户体验。

## 修改文件

- `APP/src/views/user/WalletPage.vue`

## 具体修改内容

### 1. 优化 `loadWalletData()` 函数

#### 原有逻辑
- 检测到认证失败时，显示错误消息
- 用户需要手动处理错误信息

#### 修改后逻辑
- 检测到未登录状态时，直接跳转到登录页面
- 钱包API返回认证失败错误码（30007、401、403）时，清除本地认证状态并跳转登录页面
- 交易记录API认证失败时，同样直接跳转登录页面
- 网络请求异常中包含认证相关错误时，直接跳转登录页面

### 2. 优化 `onMounted()` 初始化逻辑

#### 原有逻辑
```javascript
if (!authStore.isLoggedIn) {
  showError('请先登录')
  router.push('/login')
  return
}
```

#### 修改后逻辑
```javascript
if (!authStore.isLoggedIn) {
  console.log('❌ 用户未登录，直接跳转到登录页面')
  router.push('/login')
  return
}
```

**改进点**：
- 移除了错误提示，直接跳转
- 添加了调试日志

### 3. 优化 `handleRecharge()` 充值函数

#### 新增认证失败处理
- 检查充值API响应的错误码（30007、401、403）
- 认证失败时清除本地状态并跳转登录页面
- 处理网络请求异常中的认证错误

### 4. 优化 `handleWithdraw()` 提现函数

#### 新增认证失败处理
- 检查提现API响应的错误码（30007、401、403）
- 认证失败时清除本地状态并跳转登录页面
- 处理网络请求异常中的认证错误

## 认证错误处理逻辑

### 1. 错误码检测
检测以下认证失败相关的错误码：
- `30007` - 后端自定义的登录失效错误码
- `401` - HTTP未授权状态码
- `403` - HTTP禁止访问状态码

### 2. 错误信息检测
检测错误信息中包含的关键词：
- "认证"
- "登录"
- "30007"
- "401"
- "403"

### 3. 处理流程
1. **清除本地认证状态**：调用 `authStore.logout()`
2. **跳转登录页面**：`router.push('/login')`
3. **记录调试日志**：便于问题排查
4. **停止后续处理**：使用 `return` 终止函数执行

## 用户体验提升

### 修改前
1. 用户进入钱包页面
2. 遇到登录过期
3. 显示"认证失败"界面
4. 用户需要点击按钮跳转到登录页面

### 修改后
1. 用户进入钱包页面
2. 遇到登录过期
3. 自动跳转到登录页面
4. 无需用户额外操作

## 技术实现特点

### 1. 多层次检测
- 页面初始化时检测
- API响应时检测
- 网络异常时检测

### 2. 统一处理逻辑
所有认证失败都采用相同的处理流程，确保一致性

### 3. 状态清理
认证失败时会清除本地的认证状态，避免状态不一致

### 4. 调试支持
添加了详细的控制台日志，便于开发和调试

## 适用场景

### 1. 主动访问钱包页面
- 用户直接访问钱包页面时，如果登录已过期，直接跳转登录

### 2. 使用钱包功能时
- 充值操作时遇到认证失败
- 提现操作时遇到认证失败
- 查看交易记录时遇到认证失败

### 3. 网络异常情况
- 网络请求返回认证相关错误
- 服务器返回认证失败响应

## 注意事项

### 1. 保持现有功能不变
- 只修改认证失败的处理逻辑
- 其他业务逻辑保持不变

### 2. 错误处理完整性
- 确保所有可能的认证失败场景都被覆盖
- 避免遗漏任何API调用

### 3. 用户体验优化
- 减少用户操作步骤
- 提供无缝的登录跳转体验

现在钱包页面在遇到登录过期时会直接跳转到登录页面，不再显示认证失败的界面，提供了更流畅的用户体验。 