# API对接实施方案

本文档详细说明团购网APP各页面功能与后端API接口的对接方案，基于优化后的API需求文档进行设计。

## 📋 概述

- **项目名称**: 团购网APP
- **API版本**: v1
- **基础URL**: `/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

## 📚 接口简介索引目录

### 🔐 认证相关接口

| 接口地址         | 方法 | 功能描述                             | 页面       |
| ---------------- | ---- | ------------------------------------ | ---------- |
| `/api/v1/auth` | POST | 用户登录、注册、忘记密码、第三方登录 | login.html |

### 🏠 首页相关接口

| 接口地址             | 方法 | 功能描述                                         | 页面      |
| -------------------- | ---- | ------------------------------------------------ | --------- |
| `/api/v1/home`     | GET  | 获取首页全部数据（Banner、分类、商品）           | home.html |
| `/api/v1/banners`  | GET  | 获取轮播图数据                                   | home.html |
| `/api/v1/search`   | GET  | 商品搜索功能                                     | home.html |
| `/api/v1/products` | GET  | 获取商品列表（支持分类筛选、拼团类型筛选）       | home.html |

### 🗂️ 分类相关接口

| 接口地址               | 方法 | 功能描述                               | 页面          |
| ---------------------- | ---- | -------------------------------------- | ------------- |
| `/api/v1/categories` | GET  | 获取所有分类数据（热门分类、全部分类） | category.html |

### 🛍️ 商品相关接口

| 接口地址                         | 方法 | 功能描述                                       | 页面                |
| -------------------------------- | ---- | ---------------------------------------------- | ------------------- |
| `/api/v1/products/{id}`        | GET  | 获取商品详情信息                               | product_detail.html |
| `/api/v1/products/{id}/action` | POST | 商品操作（收藏、取消收藏、获取评价、获取拼团） | product_detail.html |

### 👥 拼团相关接口

| 接口地址                  | 方法 | 功能描述                           | 页面                                     |
| ------------------------- | ---- | ---------------------------------- | ---------------------------------------- |
| `/api/v1/groups/{id}`   | GET  | 获取拼团详情和状态                 | group_buying_*.html                      |
| `/api/v1/groups/action` | POST | 拼团操作（创建、参与、分享、取消） | product_detail.html, group_buying_*.html |

### 📦 订单相关接口

| 接口地址                       | 方法 | 功能描述                                           | 页面        |
| ------------------------------ | ---- | -------------------------------------------------- | ----------- |
| `/api/v1/orders`             | GET  | 获取用户订单列表（支持状态筛选）                   | orders.html |
| `/api/v1/orders/{id}/action` | POST | 订单操作（确认收货、再次购买、查看物流、取消订单） | orders.html |

### 💳 支付相关接口

| 接口地址            | 方法 | 功能描述                               | 页面         |
| ------------------- | ---- | -------------------------------------- | ------------ |
| `/api/v1/payment` | GET  | 获取支付页面数据（支付方式、订单信息） | payment.html |
| `/api/v1/payment` | POST | 处理支付请求                           | payment.html |

### 👤 用户中心相关接口

| 接口地址                   | 方法    | 功能描述                                     | 页面          |
| -------------------------- | ------- | -------------------------------------------- | ------------- |
| `/api/v1/user/dashboard` | GET     | 获取个人中心数据（用户信息、钱包、订单统计） | profile.html  |
| `/api/v1/user/profile`   | PUT     | 更新用户信息                                 | profile.html  |
| `/api/v1/user/settings`  | GET/PUT | 获取/更新用户设置                            | settings.html |

### 💰 钱包相关接口

| 接口地址                  | 方法 | 功能描述                                 | 页面                                      |
| ------------------------- | ---- | ---------------------------------------- | ----------------------------------------- |
| `/api/v1/wallet`        | GET  | 获取钱包信息（余额、交易记录、支付方式） | wallet.html                               |
| `/api/v1/wallet/action` | POST | 钱包操作（充值、提现、转账）             | wallet.html, recharge.html, withdraw.html |

### 📋 用户数据管理接口

| 接口地址                   | 方法                | 功能描述                           | 页面           |
| -------------------------- | ------------------- | ---------------------------------- | -------------- |
| `/api/v1/user/favorites` | GET/DELETE          | 获取收藏列表、删除收藏             | favorites.html |
| `/api/v1/user/history`   | GET/DELETE          | 获取浏览历史、清空历史             | history.html   |
| `/api/v1/user/addresses` | GET/POST/PUT/DELETE | 地址管理（获取、添加、编辑、删除） | address.html   |
| `/api/v1/user/coupons`   | GET                 | 获取优惠券列表                     | coupons.html   |

### 🎯 活动相关接口

| 接口地址                      | 方法 | 功能描述                           | 页面                   |
| ----------------------------- | ---- | ---------------------------------- | ---------------------- |
| `/api/v1/activities`        | GET  | 获取全部活动列表（无需登录）       | home.html              |
| `/api/v1/activities?name=*` | GET  | 获取指定拼团类型的活动（无需登录） | home.html              |

### 🛠️ 支持服务接口

| 接口地址            | 方法     | 功能描述                     | 页面                  |
| ------------------- | -------- | ---------------------------- | --------------------- |
| `/api/v1/support` | GET/POST | 获取帮助内容、提交用户反馈   | customer_service.html |
| `/api/v1/upload`  | POST     | 文件上传（头像、评价图片等） | 通用                  |

### 📊 系统监控接口

| 接口地址            | 方法 | 功能描述         | 页面 |
| ------------------- | ---- | ---------------- | ---- |
| `/api/v1/metrics` | POST | 性能监控数据上报 | 通用 |

### 🔄 接口操作类型说明

#### 认证接口操作类型

- `login` - 用户登录
- `register` - 用户注册
- `forgot_password` - 忘记密码
- `oauth` - 第三方登录

#### 商品操作类型

- `favorite` - 收藏商品
- `unfavorite` - 取消收藏
- `get_reviews` - 获取商品评价
- `get_groups` - 获取正在拼团

#### 拼团操作类型

- `create` - 发起拼团
- `join` - 参与拼团
- `share` - 分享拼团
- `cancel` - 取消拼团

#### 订单操作类型

- `confirm` - 确认收货
- `repurchase` - 再次购买
- `track` - 查看物流
- `cancel` - 取消订单

#### 钱包操作类型

- `recharge` - 充值
- `withdraw` - 提现
- `transfer` - 转账

### 📱 支持的页面列表

| 页面文件                   | 页面名称     | 主要功能                 | 关联接口数量 |
| -------------------------- | ------------ | ------------------------ | ------------ |
| `login.html`             | 登录页面     | 用户认证、注册、密码重置 | 1个核心接口  |
| `home.html`              | 首页         | 商品展示、搜索、分类筛选 | 4个核心接口  |
| `category.html`          | 分类页面     | 分类浏览、分类搜索       | 2个核心接口  |
| `product_detail.html`    | 商品详情页   | 商品信息、评价、拼团     | 3个核心接口  |
| `group_buying_*.html`    | 拼团相关页面 | 拼团状态、分享、支付     | 2个核心接口  |
| `orders.html`            | 订单页面     | 订单管理、物流查询       | 2个核心接口  |
| `payment.html`           | 支付页面     | 支付方式选择、支付处理   | 1个核心接口  |
| `profile.html`           | 个人中心     | 用户信息、数据统计       | 2个核心接口  |
| `wallet.html`            | 钱包页面     | 余额管理、交易记录       | 2个核心接口  |
| `favorites.html`         | 收藏页面     | 收藏商品管理             | 1个核心接口  |
| `history.html`           | 浏览历史     | 浏览记录管理             | 1个核心接口  |
| `address.html`           | 地址管理     | 收货地址管理             | 1个核心接口  |
| `settings.html`          | 设置页面     | 用户设置管理             | 1个核心接口  |
| `customer_service.html`  | 客服中心     | 帮助支持、反馈提交       | 1个核心接口  |
| `activity_popup_ad.html` | 活动页面     | 活动详情、参与活动       | 1个核心接口  |

### 🌐 多语言支持

所有接口均支持多语言，通过以下方式指定语言：

- **查询参数**: `?language=zh-CN` 或 `?language=vi`
- **请求头**: `Accept-Language: zh-CN,vi;q=0.9`

支持的语言代码：

- `zh-CN` - 简体中文
- `vi` - 越南语

### 💱 支持的货币和支付方式

#### 货币单位

- **主要货币**: 越南盾 (VND)
- **显示格式**: 1,000,000 ₫

#### 支付方式

- **MoMo钱包**: 即时到账，无手续费
- **银行转账**: 1-3工作日，低手续费
- **银行卡**: 即时到账，标准手续费
- **钱包余额**: 即时到账，无手续费

### 📈 接口性能指标

| 接口类型 | 响应时间目标 | 并发支持 | 缓存策略   |
| -------- | ------------ | -------- | ---------- |
| 认证接口 | < 500ms      | 1000/min | 无缓存     |
| 商品查询 | < 300ms      | 5000/min | 5分钟缓存  |
| 搜索接口 | < 200ms      | 3000/min | 1分钟缓存  |
| 用户数据 | < 400ms      | 2000/min | 10分钟缓存 |
| 支付接口 | < 1000ms     | 500/min  | 无缓存     |
| 文件上传 | < 3000ms     | 100/min  | 无缓存     |

## 🎯 页面与API对接映射

### 1. 登录页面 (login.html)

#### 功能模块及对接接口：

| 功能模块             | 对接接口         | 方法 | 说明                     |
| -------------------- | ---------------- | ---- | ------------------------ |
| **手机号登录** | `/api/v1/auth` | POST | 用户名密码登录           |
| **用户注册**   | `/api/v1/auth` | POST | 新用户注册               |
| **忘记密码**   | `/api/v1/auth` | POST | 密码重置                 |
| **第三方登录** | `/api/v1/auth` | POST | Google/Facebook/Zalo登录 |

#### 详细接口说明：

##### 1.1 用户登录接口

**接口地址**: `POST /api/v1/auth`

**请求参数**:

```json
{
  "type": "login",           // 必需，string，操作类型，固定值"login"
  "phone": "+84123456789",   // 必需，string，手机号（包含国际区号）
  "password": "user_password" // 必需，string，用户密码，6-20位
}
```

**返回数据**:

```json
{
  "code": 200,                    // number，状态码，200表示成功
  "message": "登录成功",           // string，操作结果消息
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // string，访问令牌
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // string，刷新令牌
    "expires_in": 3600,           // number，token有效期（秒）
    "user": {
      "id": "user_123",           // string，用户ID
      "phone": "+84123456789",    // string，手机号
      "nickname": "用户昵称",      // string，用户昵称
      "avatar": "https://example.com/avatar.jpg", // string，头像URL
      "is_new_user": false        // boolean，是否新用户
    }
  },
  "timestamp": "2024-12-15T10:00:00Z" // string，响应时间戳
}
```

**错误响应示例**:

```json
{
  "code": 401,
  "message": "手机号或密码错误",
  "data": null,
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 1.2 用户注册接口

**接口地址**: `POST /api/v1/auth`

**请求参数**:

```json
{
  "type": "register",        // 必需，string，操作类型，固定值"register"
  "phone": "+84123456789",   // 必需，string，手机号（包含国际区号）
  "password": "new_password", // 必需，string，用户密码，6-20位
  "confirm_password": "new_password", // 必需，string，确认密码，需与password一致
  "verification_code": "123456", // 必需，string，短信验证码，6位数字
  "agreed_to_terms": true,   // 必需，boolean，是否同意用户协议
  "invite_code": "INVITE123" // 可选，string，邀请码
}
```

**返回数据**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user": {
      "id": "user_124",
      "phone": "+84123456789",
      "nickname": "新用户123456789",
      "avatar": "https://example.com/default_avatar.jpg",
      "is_new_user": true,
      "welcome_bonus": 50000     // number，新用户奖励金额（越南盾）
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 1.3 第三方登录接口

**接口地址**: `POST /api/v1/auth`

**请求参数**:

```json
{
  "type": "oauth",           // 必需，string，操作类型，固定值"oauth"
  "provider": "google",      // 必需，string，第三方平台（google/facebook/zalo）
  "token": "oauth_access_token", // 必需，string，第三方平台返回的访问令牌
  "user_info": {             // 可选，object，第三方用户信息
    "name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

**返回数据**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user": {
      "id": "user_125",
      "phone": null,             // 第三方登录可能没有手机号
      "nickname": "John Doe",
      "avatar": "https://example.com/avatar.jpg",
      "email": "<EMAIL>",
      "oauth_provider": "google",
      "is_new_user": false,
      "need_bind_phone": true    // boolean，是否需要绑定手机号
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 1.4 忘记密码接口

**接口地址**: `POST /api/v1/auth`

**请求参数**:

```json
{
  "type": "forgot_password", // 必需，string，操作类型，固定值"forgot_password"
  "phone": "+84123456789",   // 必需，string，手机号
  "verification_code": "123456", // 必需，string，短信验证码
  "new_password": "new_password", // 必需，string，新密码，6-20位
  "confirm_password": "new_password" // 必需，string，确认新密码
}
```

**返回数据**:

```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "success": true
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

#### 详细对接方案：

```javascript
// 登录功能
async function login(phone, password) {
    try {
        const response = await fetch('/api/v1/auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: 'login',
                phone: phone,
                password: password
            })
        });
    
        const data = await response.json();
    
        if (data.code === 200) {
            // 存储令牌
            localStorage.setItem('token', data.data.token);
            localStorage.setItem('refresh_token', data.data.refresh_token);
            localStorage.setItem('user_info', JSON.stringify(data.data.user));
        
            // 跳转到首页
            window.location.href = 'home.html';
        } else {
            showError(data.message);
        }
    } catch (error) {
        showError('网络错误，请重试');
    }
}

// 注册功能
async function register(phone, password, confirmPassword, verificationCode, agreeTerms, inviteCode = '') {
    try {
        const response = await fetch('/api/v1/auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: 'register',
                phone: phone,
                password: password,
                confirm_password: confirmPassword,
                verification_code: verificationCode,
                agreed_to_terms: agreeTerms,
                invite_code: inviteCode
            })
        });
    
        const data = await response.json();
    
        if (data.code === 200) {
            // 存储令牌
            localStorage.setItem('token', data.data.token);
            localStorage.setItem('refresh_token', data.data.refresh_token);
            localStorage.setItem('user_info', JSON.stringify(data.data.user));
        
            // 显示新用户奖励
            if (data.data.user.welcome_bonus) {
                showWelcomeBonus(data.data.user.welcome_bonus);
            }
        
            // 跳转到首页
            window.location.href = 'home.html';
        } else {
            showError(data.message);
        }
    } catch (error) {
        showError('注册失败，请重试');
    }
}

// 第三方登录
async function oauthLogin(provider, oauthToken, userInfo = null) {
    try {
        const response = await fetch('/api/v1/auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: 'oauth',
                provider: provider,
                token: oauthToken,
                user_info: userInfo
            })
        });
    
        const data = await response.json();
    
        if (data.code === 200) {
            // 存储令牌
            localStorage.setItem('token', data.data.token);
            localStorage.setItem('refresh_token', data.data.refresh_token);
            localStorage.setItem('user_info', JSON.stringify(data.data.user));
        
            // 检查是否需要绑定手机号
            if (data.data.user.need_bind_phone) {
                window.location.href = 'bind_phone.html';
            } else {
                window.location.href = 'home.html';
            }
        } else {
            showError(data.message);
        }
    } catch (error) {
        showError('登录失败，请重试');
    }
}

// 忘记密码
async function resetPassword(phone, verificationCode, newPassword, confirmPassword) {
    try {
        const response = await fetch('/api/v1/auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: 'forgot_password',
                phone: phone,
                verification_code: verificationCode,
                new_password: newPassword,
                confirm_password: confirmPassword
            })
        });
    
        const data = await response.json();
    
        if (data.code === 200) {
            showSuccess('密码重置成功，请重新登录');
            // 切换到登录页面
            switchToLoginTab();
        } else {
            showError(data.message);
        }
    } catch (error) {
        showError('密码重置失败，请重试');
    }
}

// 工具函数
function showError(message) {
    // 显示错误消息
    alert(message);
}

function showSuccess(message) {
    // 显示成功消息
    alert(message);
}

function showWelcomeBonus(amount) {
    // 显示新用户奖励弹窗
    alert(`恭喜您注册成功！获得新用户奖励 ${formatCurrency(amount)}`);
}

function formatCurrency(amount) {
    // 格式化货币显示
    return new Intl.NumberFormat('vi-VN', { 
        style: 'currency', 
        currency: 'VND' 
    }).format(amount);
}
```

---

### 2. 首页 (home.html / index.html)

#### 功能模块及对接接口：

| 功能模块                 | 对接接口                    | 方法 | 说明                    |
| ------------------------ | --------------------------- | ---- | ----------------------- |
| **页面初始化数据** | `/api/v1/data/home`       | GET  | 获取首页全部数据        |
| **Banner轮播**     | `/api/v1/data/banners`    | GET  | 获取轮播图数据          |
| **商品搜索**       | `/api/v1/search`          | GET  | 搜索功能                |
| **商品列表加载**   | `/api/v1/data/products`   | GET  | 按分类/拼团类型获取商品 |
| **商品筛选**       | `/api/v1/data/products`   | GET  | 分类筛选和拼团类型筛选  |
| **平台补贴弹窗**   | `/api/v1/activities/{id}` | GET  | 获取活动信息            |

#### 详细接口说明：

##### 2.1 用户状态接口

**接口地址**: `GET /api/v1/userStatus`

**请求参数**:

```
无需参数，需要认证
```

**返回数据**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "isLoggedIn": true,        // boolean，用户是否已登录
    "nickname": "13833883388", // string，用户昵称（登录用户）
    "unreadMessages": 0        // number，未读消息数量
  },
  "dataType": 1,
  "timestamp": "2024-12-15T10:00:00Z"
}
```

**错误响应示例**:

```json
{
  "code": 30007,
  "msg": "您还未登录或登录失效，请重新登录！",
  "ok": false,
  "dataType": 1,
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 2.2 首页数据初始化接口

**接口地址**: `GET /api/v1/home`

**请求参数**:

```
language: string (可选，默认'zh-CN') - 语言设置，支持'zh-CN', 'vi'
```

**返回数据**:

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "banners": [
      {
        "id": "banner_1",          // string，Banner ID
        "title": "平台补贴专区",    // string，Banner标题
        "subtitle": "每日签到领奖励", // string，副标题
        "image": "https://example.com/banner1.jpg", // string，Banner图片URL
        "link_type": "activity",   // string，链接类型（activity/product/external）
        "link_value": "subsidy_activity", // string，链接目标
        "background_color": "#FF6B9D", // string，背景色
        "text_color": "#FFFFFF",   // string，文字颜色
        "button_text": "立即参与"   // string，按钮文字
      }
    ],
    "categories": [
      {
        "id": "recommended",       // string，分类ID
        "name": "推荐商品",        // string，分类名称
        "icon": "material-symbols:star", // string，图标
        "color": "from-orange-400 to-red-500", // string，渐变色类名
        "is_hot": true            // boolean，是否热门分类
      }
    ],
    "initial_products": {
      "items": [
        {
          "id": "prod_123",        // string，商品ID
          "title": "智能电子产品", // string，商品标题
          "price": 299000,         // number，现价（越南盾）
          "original_price": 399000, // number，原价
          "discount": 25,          // number，折扣百分比
          "image": "https://example.com/product1.jpg", // string，商品主图
          "group_type": 3,         // number，拼团人数
          "group_price": 249000,   // number，拼团价
          "participants": 1256,    // number，已参团人数
          "rating": 4.8,           // number，评分
          "sales": 2340,           // number，销量
          "tags": ["热销", "包邮"], // array，商品标签
          "countdown": 3600        // number，限时秒杀倒计时（秒，0表示非限时）
        }
      ],
      "pagination": {
        "page": 1,               // number，当前页码
        "per_page": 20,          // number，每页数量
        "total": 156,            // number，总数量
        "has_more": true         // boolean，是否有更多数据
      }
    }
  },
  "dataType": 1,
  "timestamp": "2024-12-15T10:00:00Z"
}
```

**错误响应示例**:

```json
{
  "code": 30007,
  "msg": "您还未登录或登录失效，请重新登录！",
  "ok": false,
  "dataType": 1,
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 2.3 商品搜索接口

**接口地址**: `GET /api/v1/search`

**请求参数**:

```
q: string (必需) - 搜索关键词，最少2个字符
type: string (可选，默认'all') - 搜索类型（all/product/brand/activity）
page: number (可选，默认1) - 页码
per_page: number (可选，默认20) - 每页数量，最大50
category: string (可选) - 分类筛选
price_min: number (可选) - 最低价格筛选
price_max: number (可选) - 最高价格筛选
sort: string (可选，默认'relevance') - 排序方式（relevance/price_asc/price_desc/sales/rating）
```

**返回数据**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": {
      "products": [
        {
          "id": "prod_124",
          "title": "智能手机",
          "price": 1200000,
          "original_price": 1500000,
          "discount": 20,
          "image": "https://example.com/phone.jpg",
          "group_type": 5,
          "group_price": 999000,
          "rating": 4.5,
          "sales": 890,
          "highlight": "<em>智能</em>手机", // string，高亮搜索关键词
          "match_score": 0.95      // number，匹配度得分
        }
      ],
      "brands": [
        {
          "id": "brand_1",
          "name": "Apple",
          "logo": "https://example.com/apple_logo.jpg",
          "product_count": 45
        }
      ],
      "activities": [
        {
          "id": "activity_1",
          "title": "智能设备专场",
          "image": "https://example.com/activity.jpg",
          "start_time": "2024-12-15T00:00:00Z",
          "end_time": "2024-12-25T23:59:59Z"
        }
      ]
    },
    "suggestions": ["智能手机", "智能手表", "智能音箱"], // array，搜索建议
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 67,
      "has_more": true
    },
    "search_stats": {
      "total_products": 45,      // number，商品搜索结果数
      "total_brands": 3,         // number，品牌搜索结果数
      "total_activities": 2,     // number，活动搜索结果数
      "search_time": 0.02       // number，搜索耗时（秒）
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 2.4 商品列表获取接口

**接口地址**: `GET /api/v1/products`

**请求参数**:

```
category: string (可选) - 分类筛选（recommended/special/hot/all）
group_type: number (可选) - 拼团类型筛选（3/10）
page: number (可选，默认1) - 页码
per_page: number (可选，默认20) - 每页数量
sort: string (可选，默认'default') - 排序（default/price_asc/price_desc/sales/new）
price_range: string (可选) - 价格区间（0-100000/100000-500000/500000+）
```

**返回数据**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "prod_125",
        "title": "时尚服装新款",
        "subtitle": "舒适面料，多色可选",
        "price": 89000,
        "original_price": 129000,
        "discount": 31,
        "image": "https://example.com/clothes.jpg",
        "images": [               // array，商品图片列表
          "https://example.com/clothes1.jpg",
          "https://example.com/clothes2.jpg"
        ],
        "group_type": 3,
        "group_price": 69000,
        "single_price": 89000,
        "participants": 234,
        "max_participants": 10000,
        "success_rate": 98.5,     // number，拼团成功率
        "rating": 4.7,
        "review_count": 156,
        "sales": 1234,
        "tags": ["新品", "包邮", "退换无忧"],
        "specs": [                // array，商品规格
          "L码蓝色",
          "M码红色"
        ],
        "shipping_info": {
          "free_shipping": true,   // boolean，是否包邮
          "shipping_time": "1-3天", // string，发货时间
          "location": "胡志明市"   // string，发货地
        },
        "promotion": {
          "type": "limited_time",  // string，促销类型
          "countdown": 7200,       // number，倒计时（秒）
          "stock": 50             // number，剩余库存
        }
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 234,
      "has_more": true
    },
    "filters": {                  // object，当前筛选条件
      "category": "recommended",
      "group_type": 3,
      "active_filters_count": 2
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

#### 详细对接方案：

```javascript
// 页面初始化
async function initHomePage() {
    try {
        showLoading(); // 显示加载状态
    
        const language = getLanguage(); // 获取当前语言设置
        
        // 1. 加载首页数据（不需要认证）
        const homeResponse = await fetch(`/api/v1/home?language=${language}`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const homeData = await homeResponse.json();
        
        if (homeData.code === 0) {
            // 渲染Banner轮播
            renderBanners(homeData.data.banners);
        
            // 渲染分类导航
            renderCategories(homeData.data.categories);
        
            // 渲染初始商品列表
            renderProducts(homeData.data.initial_products);
        }
        
        // 2. 加载用户状态（需要认证，可能失败）
        const token = localStorage.getItem('token');
        if (token) {
            try {
                const userResponse = await fetch('/api/v1/userStatus', {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const userData = await userResponse.json();
                
                if (userData.code === 0) {
                    // 渲染用户状态
                    renderUserStatus(userData.data);
                } else {
                    // 用户未登录或token过期，显示登录状态
                    renderGuestStatus();
                }
            } catch (error) {
                console.log('用户状态获取失败，显示游客状态');
                renderGuestStatus();
            }
        } else {
            // 没有token，显示游客状态
            renderGuestStatus();
        }
        
        // 初始化无限滚动
        initInfiniteScroll();
        
    } catch (error) {
        showError('页面加载失败，请重试');
    } finally {
        hideLoading(); // 隐藏加载状态
    }
}

// 获取用户状态（单独调用）
async function getUserStatus() {
    const token = localStorage.getItem('token');
    if (!token) {
        renderGuestStatus();
        return;
    }
    
    try {
        const response = await fetch('/api/v1/userStatus', {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            renderUserStatus(data.data);
        } else {
            // Token可能过期，清除本地存储
            localStorage.removeItem('token');
            renderGuestStatus();
        }
    } catch (error) {
        console.error('获取用户状态失败:', error);
        renderGuestStatus();
    }
}

// 商品搜索功能
async function searchProducts(query, options = {}) {
    try {
        if (query.length < 2) {
            showError('搜索关键词至少需要2个字符');
            return;
        }
    
        showSearchLoading();
    
        const params = new URLSearchParams({
            q: query,
            type: options.type || 'product',
            page: options.page || 1,
            per_page: options.per_page || 20
        });
    
        // 添加可选筛选参数
        if (options.category) params.append('category', options.category);
        if (options.price_min) params.append('price_min', options.price_min);
        if (options.price_max) params.append('price_max', options.price_max);
        if (options.sort) params.append('sort', options.sort);
    
        const response = await fetch(`/api/v1/search?${params.toString()}`);
        const data = await response.json();
    
        if (data.code === 0) {
            // 渲染搜索结果
            renderSearchResults(data.data.results);
        
            // 渲染搜索建议
            renderSearchSuggestions(data.data.suggestions);
        
            // 更新搜索统计
            updateSearchStats(data.data.search_stats);
        
            // 保存搜索历史
            saveSearchHistory(query);
        
        } else {
            showError(data.msg || data.message);
        }
    } catch (error) {
        showError('搜索失败，请重试');
    } finally {
        hideSearchLoading();
    }
}

// 按分类筛选商品
async function filterProducts(category, resetList = true) {
    try {
        if (resetList) {
            clearProductList(); // 清空当前商品列表
            showLoading();
        }
    
        const response = await fetch(`/api/v1/products?category=${category}&page=1&per_page=20`);
        const data = await response.json();
    
        if (data.code === 0) {
            if (resetList) {
                renderProducts(data.data);
            } else {
                appendProducts(data.data);
            }
        
            // 更新分类标题
            updateCategoryTitle(category);
        
            // 更新筛选状态
            updateFilterButtons(category);
        
            // 保存当前筛选状态
            setCurrentCategory(category);
        
        } else {
            showError(data.msg || data.message);
        }
    } catch (error) {
        showError('加载商品失败，请重试');
    } finally {
        if (resetList) {
            hideLoading();
        }
    }
}

// 按拼团类型筛选
async function switchGroupType(groupType) {
    try {
        showLoading();
        clearProductList();
    
        const currentCategory = getCurrentCategory();
        const params = new URLSearchParams({
            group_type: groupType,
            page: 1,
            per_page: 20
        });
    
        if (currentCategory) {
            params.append('category', currentCategory);
        }
    
        const response = await fetch(`/api/v1/data/products?${params.toString()}`);
        const data = await response.json();
    
        if (data.code === 200) {
            renderProducts(data.data);
        
            // 更新拼团类型标签状态
            updateGroupTypeTabs(groupType);
        
            // 保存当前拼团类型
            setCurrentGroupType(groupType);
        
        } else {
            showError(data.message);
        }
    } catch (error) {
        showError('加载商品失败，请重试');
    } finally {
        hideLoading();
    }
}

// 加载更多商品（分页）
async function loadMoreProducts(page) {
    try {
        showLoadMoreSpinner();
    
        const currentCategory = getCurrentCategory();
        const currentGroupType = getCurrentGroupType();
    
        const params = new URLSearchParams({
            page: page,
            per_page: 20
        });
    
        if (currentCategory) params.append('category', currentCategory);
        if (currentGroupType) params.append('group_type', currentGroupType);
    
        const response = await fetch(`/api/v1/data/products?${params.toString()}`);
        const data = await response.json();
    
        if (data.code === 200) {
            appendProducts(data.data);
        
            // 检查是否还有更多数据
            if (!data.data.pagination.has_more) {
                showNoMoreData();
            }
        
            return data.data.pagination.has_more;
        } else {
            showError(data.message);
            return false;
        }
    } catch (error) {
        showError('加载更多失败，请重试');
        return false;
    } finally {
        hideLoadMoreSpinner();
    }
}

// 工具函数
function renderUserStatus(userStatus) {
    const userInfo = document.getElementById('userInfo');
    if (userStatus.is_logged_in) {
        userInfo.innerHTML = `
            <div class="flex items-center">
                <img src="${userStatus.avatar}" class="w-8 h-8 rounded-full mr-2">
                <span class="text-white text-sm">${userStatus.nickname}</span>
                ${userStatus.unread_messages > 0 ? `<span class="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">${userStatus.unread_messages}</span>` : ''}
            </div>
        `;
    } else {
        userInfo.innerHTML = `
            <button onclick="goToLogin()" class="text-white text-sm">登录/注册</button>
        `;
    }
}

function renderBanners(banners) {
    const bannerContainer = document.getElementById('bannerContainer');
    let currentBanner = 0;
  
    // 初始化Banner轮播
    initBannerCarousel(banners);
  
    // 自动轮播
    setInterval(() => {
        currentBanner = (currentBanner + 1) % banners.length;
        switchBanner(currentBanner);
    }, 5000);
}

function renderProducts(productData) {
    const productGrid = document.getElementById('productGrid');
  
    productData.items.forEach(product => {
        const productCard = createProductCard(product);
        productGrid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded-2xl shadow-sm overflow-hidden product-card transition-transform duration-200';
    card.onclick = () => goToProductDetail(product.id);
  
    card.innerHTML = `
        <div class="relative">
            <img src="${product.image}" class="w-full h-32 object-cover">
            ${product.promotion.type === 'limited_time' ? `
                <div class="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                    限时${Math.floor(product.promotion.countdown / 3600)}小时
                </div>
            ` : ''}
            <div class="absolute bottom-2 right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs px-2 py-1 rounded-full">
                ${product.group_type}人团
            </div>
        </div>
        <div class="p-3">
            <h4 class="font-medium text-gray-800 mb-2 text-sm line-clamp-2">${product.title}</h4>
            <div class="flex items-center justify-between mb-2">
                <div class="flex flex-col">
                    <span class="text-red-500 font-bold text-lg">${formatCurrency(product.group_price)}</span>
                    <span class="text-gray-400 text-xs line-through">${formatCurrency(product.original_price)}</span>
                </div>
                <div class="text-right">
                    <div class="flex items-center text-yellow-400 text-xs">
                        ${'★'.repeat(Math.floor(product.rating))}
                        <span class="text-gray-500 ml-1">${product.rating}</span>
                    </div>
                    <div class="text-gray-500 text-xs">${product.sales}人购买</div>
                </div>
            </div>
            <div class="text-xs text-gray-500 mb-2">
                ${product.participants}人已参团 · 成功率${product.success_rate}%
            </div>
            <div class="flex flex-wrap gap-1">
                ${product.tags.map(tag => `<span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">${tag}</span>`).join('')}
            </div>
        </div>
    `;
  
    return card;
}

// 状态管理函数
let currentCategory = 'recommended';
let currentGroupType = 3;
let currentPage = 1;

function getCurrentCategory() { return currentCategory; }
function setCurrentCategory(category) { currentCategory = category; }
function getCurrentGroupType() { return currentGroupType; }
function setCurrentGroupType(groupType) { currentGroupType = groupType; }

// 导航函数
function goToProductDetail(productId) {
    window.location.href = `product_detail.html?id=${productId}`;
}

function goToLogin() {
    window.location.href = 'login.html';
}
```

---

### 3. 分类页面 (category.html)

#### 功能模块及对接接口：

| 功能模块               | 对接接口                    | 方法 | 说明             |
| ---------------------- | --------------------------- | ---- | ---------------- |
| **分类数据加载** | `/api/v1/data/categories` | GET  | 获取所有分类数据 |
| **分类搜索**     | `/api/v1/search`          | GET  | 搜索分类         |
| **热门分类**     | `/api/v1/data/categories` | GET  | 获取热门分类     |

#### 详细对接方案：

```javascript
// 加载分类页面
async function initCategoryPage() {
    const response = await fetch('/api/v1/data/categories?language=zh-CN');
    const data = await response.json();
  
    renderHotCategories(data.data.hot_categories);
    renderAllCategories(data.data.all_categories);
}

// 分类搜索
async function searchCategories(query) {
    const response = await fetch(`/api/v1/search?q=${encodeURIComponent(query)}&type=category`);
    const data = await response.json();
    renderSearchResults(data.data.results);
}

// 点击分类跳转到商品列表
function goToCategoryProducts(categoryId) {
    window.location.href = `home.html?category=${categoryId}`;
}
```

---

### 4. 商品详情页 (product_detail.html)

#### 功能模块及对接接口：

| 功能模块                | 对接接口                         | 方法 | 说明               |
| ----------------------- | -------------------------------- | ---- | ------------------ |
| **商品详情加载**  | `/api/v1/products/{id}`        | GET  | 获取商品完整信息   |
| **收藏/取消收藏** | `/api/v1/products/{id}/action` | POST | 商品收藏操作       |
| **获取商品评价**  | `/api/v1/products/{id}/action` | POST | 获取商品评价列表   |
| **获取正在拼团**  | `/api/v1/products/{id}/action` | POST | 获取正在进行的拼团 |
| **发起拼团**      | `/api/v1/groups/action`        | POST | 创建新拼团         |
| **参与拼团**      | `/api/v1/groups/action`        | POST | 加入现有拼团       |

#### 详细接口说明：

##### 4.1 商品详情获取接口

**接口地址**: `GET /api/v1/products/{id}`

**路径参数**:

```
id: string (必需) - 商品ID
```

**返回数据**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "prod_123",
    "title": "智能电子产品",
    "subtitle": "最新款智能设备，功能强大",
    "description": "详细的商品描述内容...",
    "price": 299000,
    "original_price": 399000,
    "discount": 25,
    "images": [
      "https://example.com/product1.jpg",
      "https://example.com/product2.jpg",
      "https://example.com/product3.jpg"
    ],
    "video": "https://example.com/product_video.mp4",
    "group_buy_rules": {
      "group_type_3": {
        "min_participants": 3,
        "max_participants": 1000,
        "price": 249000,
        "save_amount": 50000,
        "success_rate": 98.5
      },
      "group_type_10": {
        "min_participants": 10,
        "max_participants": 1000,
        "price": 199000,
        "save_amount": 100000,
        "success_rate": 95.2
      }
    },
    "specifications": [
      {
        "name": "颜色",
        "options": [
          {"value": "红色", "price_diff": 0, "stock": 50},
          {"value": "蓝色", "price_diff": 0, "stock": 30},
          {"value": "黑色", "price_diff": 5000, "stock": 20}
        ]
      },
      {
        "name": "尺寸",
        "options": [
          {"value": "标准版", "price_diff": 0, "stock": 80},
          {"value": "升级版", "price_diff": 30000, "stock": 40}
        ]
      }
    ],
    "stock_info": {
      "total_stock": 200,
      "available_stock": 150,
      "warning_threshold": 10
    },
    "shipping_info": {
      "free_shipping": true,
      "shipping_time": "1-3天",
      "location": "胡志明市",
      "shipping_fee": 0,
      "cod_available": true
    },
    "seller_info": {
      "id": "seller_123",
      "name": "官方旗舰店",
      "rating": 4.9,
      "response_rate": 98,
      "response_time": "2小时内"
    },
    "rating": 4.8,
    "review_count": 1256,
    "sales": 3456,
    "is_favorited": false,
    "preview_reviews": [
      {
        "id": "review_1",
        "user": {
          "nickname": "用户***123",
          "avatar": "https://example.com/avatar1.jpg"
        },
        "rating": 5,
        "content": "商品质量很好，物流也很快！",
        "images": ["https://example.com/review1.jpg"],
        "created_at": "2024-12-10T10:00:00Z",
        "specs": "红色 标准版"
      }
    ],
    "active_groups": [
      {
        "id": "group_456",
        "creator": {
          "nickname": "团长***456",
          "avatar": "https://example.com/avatar2.jpg"
        },
        "participants": 2,
        "max_participants": 3,
        "remaining_time": 7200,
        "created_at": "2024-12-15T08:00:00Z"
      }
    ],
    "related_products": [
      {
        "id": "prod_124",
        "title": "相关商品1",
        "price": 199000,
        "image": "https://example.com/related1.jpg",
        "rating": 4.6
      }
    ],
    "promotional_activities": [
      {
        "type": "coupon",
        "title": "新用户专享优惠券",
        "discount": 20000,
        "condition": "满200000可用",
        "valid_until": "2024-12-31T23:59:59Z"
      }
    ]
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 4.2 商品操作接口

**接口地址**: `POST /api/v1/products/{id}/action`

**路径参数**:

```
id: string (必需) - 商品ID
```

**请求参数**:

```json
{
  "action": "favorite",          // 必需，string，操作类型
  "params": {}                  // 可选，object，操作参数
}
```

**支持的操作类型**:

1. **收藏商品** - `action: "favorite"`
2. **取消收藏** - `action: "unfavorite"`
3. **获取评价** - `action: "get_reviews"`
4. **获取正在拼团** - `action: "get_groups"`

**获取评价操作参数**:

```json
{
  "action": "get_reviews",
  "params": {
    "page": 1,                   // number，页码
    "per_page": 10,              // number，每页数量
    "rating_filter": "all",      // string，评分筛选（all/5/4/3/2/1）
    "has_image": false,          // boolean，是否只看有图评价
    "sort": "newest"             // string，排序（newest/oldest/helpful）
  }
}
```

**获取正在拼团操作参数**:

```json
{
  "action": "get_groups",
  "params": {
    "status": "active",          // string，拼团状态（active/all）
    "group_type": 3,             // number，拼团类型筛选
    "page": 1,                   // number，页码
    "per_page": 20               // number，每页数量
  }
}
```

**返回数据示例**:

1. **收藏/取消收藏返回**:

```json
{
  "code": 200,
  "message": "收藏成功",
  "data": {
    "is_favorited": true,
    "total_favorites": 1256
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

2. **获取评价返回**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "reviews": [
      {
        "id": "review_123",
        "user": {
          "nickname": "用户***789",
          "avatar": "https://example.com/avatar3.jpg",
          "level": 3
        },
        "rating": 5,
        "content": "非常满意的购物体验，商品质量超出预期！",
        "images": [
          "https://example.com/review_img1.jpg",
          "https://example.com/review_img2.jpg"
        ],
        "specs": "红色 标准版",
        "created_at": "2024-12-14T15:30:00Z",
        "helpful_count": 23,
        "is_verified_purchase": true,
        "seller_reply": {
          "content": "感谢您的好评！",
          "replied_at": "2024-12-14T16:00:00Z"
        }
      }
    ],
    "statistics": {
      "total_reviews": 1256,
      "average_rating": 4.8,
      "rating_distribution": {
        "5": 856,
        "4": 234,
        "3": 89,
        "2": 45,
        "1": 32
      },
      "has_image_count": 567
    },
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 1256,
      "has_more": true
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 4.3 拼团操作接口

**接口地址**: `POST /api/v1/groups/action`

**请求参数**:

1. **发起拼团**:

```json
{
  "action": "create",
  "product_id": "prod_123",      // 必需，string，商品ID
  "params": {
    "group_type": 3,             // 必需，number，拼团人数
    "specs": {                   // 必需，object，选择的规格
      "颜色": "红色",
      "尺寸": "标准版"
    },
    "quantity": 1,               // 必需，number，购买数量
    "address_id": "addr_123"     // 必需，string，收货地址ID
  }
}
```

2. **参与拼团**:

```json
{
  "action": "join",
  "group_id": "group_456",       // 必需，string，拼团ID
  "params": {
    "quantity": 1,               // 必需，number，购买数量
    "address_id": "addr_123"     // 必需，string，收货地址ID
  }
}
```

**返回数据**:

1. **发起拼团返回**:

```json
{
  "code": 200,
  "message": "拼团创建成功",
  "data": {
    "group_id": "group_789",
    "order_id": "order_123",
    "payment_amount": 249000,
    "group_info": {
      "participants": 1,
      "max_participants": 3,
      "remaining_time": 86400,
      "share_url": "https://app.example.com/group/789"
    },
    "next_action": "payment"      // string，下一步操作（payment/waiting）
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

2. **参与拼团返回**:

```json
{
  "code": 200,
  "message": "参团成功",
  "data": {
    "group_id": "group_456",
    "order_id": "order_124",
    "payment_amount": 249000,
    "group_info": {
      "participants": 3,
      "max_participants": 3,
      "status": "success",
      "remaining_time": 0
    },
    "next_action": "payment"
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

#### 详细对接方案：

```javascript
// 加载商品详情
async function initProductDetail(productId) {
    const response = await fetch(`/api/v1/products/${productId}`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
    });
    const data = await response.json();
  
    renderProductInfo(data.data);
    renderPriceInfo(data.data.price_info);
    renderGroupRules(data.data.group_buy_rules);
    renderPreviewReviews(data.data.preview_reviews);
}

// 收藏商品
async function toggleFavorite(productId, isFavorited) {
    const action = isFavorited ? 'unfavorite' : 'favorite';
    const response = await fetch(`/api/v1/products/${productId}/action`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: action,
            params: {}
        })
    });
}

// 获取正在拼团列表
async function loadActiveGroups(productId) {
    const response = await fetch(`/api/v1/products/${productId}/action`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: 'get_groups',
            params: { status: 'active' }
        })
    });
    const data = await response.json();
    renderActiveGroups(data.data.groups);
}

// 发起拼团
async function createGroup(productId) {
    const response = await fetch('/api/v1/groups/action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: 'create',
            product_id: productId,
            params: {
                group_type: 2,
                price: 39.00
            }
        })
    });
    const data = await response.json();
    window.location.href = `group_buying_confirmation.html?group_id=${data.data.group_id}`;
}
```

---

### 5. 拼团相关页面 (group_buying_confirmation.html, group_waiting.html, settlement_failure.html)

#### 功能模块及对接接口：

| 功能模块           | 对接接口                  | 方法 | 说明               |
| ------------------ | ------------------------- | ---- | ------------------ |
| **拼团详情** | `/api/v1/groups/{id}`   | GET  | 获取拼团状态和详情 |
| **分享拼团** | `/api/v1/groups/action` | POST | 生成分享链接       |
| **取消拼团** | `/api/v1/groups/action` | POST | 取消拼团           |
| **支付拼团** | `/api/v1/payment`       | POST | 支付拼团费用       |

#### 详细对接方案：

```javascript
// 加载拼团详情
async function initGroupDetail(groupId) {
    const response = await fetch(`/api/v1/groups/${groupId}`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
    });
    const data = await response.json();
    renderGroupInfo(data.data);
}

// 分享拼团
async function shareGroup(groupId) {
    const response = await fetch('/api/v1/groups/action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: 'share',
            group_id: groupId
        })
    });
    const data = await response.json();
    if (data.code === 200) {
        showSuccess('拼团分享成功');
    } else {
        showError(data.message || '分享失败');
    }
}

// 取消拼团
async function cancelGroup(groupId) {
    const response = await fetch('/api/v1/groups/action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: 'cancel',
            group_id: groupId
        })
    });
    const data = await response.json();
    if (data.code === 200) {
        showSuccess('拼团取消成功');
    } else {
        showError(data.message || '取消失败');
    }
}

// 支付拼团
async function payGroup(groupId) {
    const response = await fetch('/api/v1/payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            action: 'pay',
            group_id: groupId
        })
    });
    const data = await response.json();
    if (data.code === 200) {
        showSuccess('拼团支付成功');
    } else {
        showError(data.message || '支付失败');
    }
}
```

---

### 13. 活动页面 (home.html - 活动功能模块)

#### 功能模块及对接接口：

| 功能模块                   | 对接接口                      | 方法 | 说明                     |
| -------------------------- | ----------------------------- | ---- | ------------------------ |
| **获取全部活动列表** | `/api/v1/activities`        | GET  | 获取所有活动信息         |
| **获取拼团类型活动** | `/api/v1/activities?name=*` | GET  | 按拼团类型筛选活动       |

#### 详细接口说明：

##### 13.1 活动列表接口

**接口地址**: `GET /api/v1/activities`

**请求参数**:

```
无需参数，无需认证
```

**返回数据**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "activities": [
      {
        "id": "activity_001",            // string，活动ID
        "name": "3人团专场",             // string，活动名称
        "type": "group_buying",          // string，活动类型
        "group_type": 3,                 // number，拼团人数
        "title": "3人成团，立享优惠",    // string，活动标题
        "subtitle": "邀请好友一起拼团",  // string，活动副标题
        "description": "活动详细描述...", // string，活动描述
        "banner_image": "https://example.com/banner.jpg", // string，活动横幅图片
        "icon": "https://example.com/icon.jpg", // string，活动图标
        "start_time": "2024-12-01T00:00:00Z", // string，活动开始时间
        "end_time": "2024-12-31T23:59:59Z",   // string，活动结束时间
        "status": "active",              // string，活动状态（active/inactive/expired）
        "participant_count": 1256,       // number，参与人数
        "product_count": 89,             // number，活动商品数量
        "discount_info": {
          "type": "percentage",          // string，优惠类型（percentage/fixed）
          "value": 20,                   // number，优惠值
          "max_discount": 50000          // number，最大优惠金额（越南盾）
        },
        "rules": [                       // array，活动规则
          "每人限参与一次",
          "活动期间有效",
          "不可与其他优惠叠加"
        ]
      },
      {
        "id": "activity_002",
        "name": "10人团专场",
        "type": "group_buying",
        "group_type": 10,
        "title": "10人成团，更大优惠",
        "subtitle": "人多力量大，优惠更给力",
        "description": "10人拼团活动详细描述...",
        "banner_image": "https://example.com/banner2.jpg",
        "icon": "https://example.com/icon2.jpg",
        "start_time": "2024-12-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "status": "active",
        "participant_count": 2341,
        "product_count": 156,
        "discount_info": {
          "type": "percentage",
          "value": 30,
          "max_discount": 100000
        },
        "rules": [
          "每人限参与一次",
          "活动期间有效",
          "不可与其他优惠叠加"
        ]
      }
    ],
    "total_count": 2,                    // number，活动总数
    "active_count": 2                    // number，进行中的活动数
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

##### 13.2 拼团类型活动接口

**接口地址**: `GET /api/v1/activities?name={groupTypeName}`

**请求参数**:

```
name: string (必需) - 拼团类型名称（3人团/5人团/10人团/限时团）
```

**返回数据**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "activities": [
      {
        "id": "activity_001",
        "name": "3人团专场",
        "type": "group_buying",
        "group_type": 3,
        "title": "3人成团，立享优惠",
        "subtitle": "邀请好友一起拼团",
        "description": "3人拼团活动，商品价格更优惠，快邀请朋友一起参与吧！",
        "banner_image": "https://example.com/3person_banner.jpg",
        "icon": "https://example.com/3person_icon.jpg",
        "start_time": "2024-12-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "status": "active",
        "participant_count": 1256,
        "product_count": 89,
        "discount_info": {
          "type": "percentage",
          "value": 20,
          "max_discount": 50000
        },
        "featured_products": [           // array，活动推荐商品
          {
            "id": "prod_001",
            "title": "智能手机",
            "image": "https://example.com/phone.jpg",
            "original_price": 5000000,
            "group_price": 4000000,
            "discount": 20,
            "participants": 234
          },
          {
            "id": "prod_002", 
            "title": "蓝牙耳机",
            "image": "https://example.com/earphone.jpg",
            "original_price": 1500000,
            "group_price": 1200000,
            "discount": 20,
            "participants": 156
          }
        ],
        "rules": [
          "每人限参与一次",
          "活动期间有效",
          "3人成团即可享受优惠价格",
          "拼团失败自动退款"
        ]
      }
    ],
    "matched_count": 1,                  // number，匹配的活动数量
    "group_type_info": {                 // object，拼团类型信息
      "name": "3人团",
      "min_participants": 3,
      "description": "3人成团，价格优惠",
      "success_rate": 95.8,              // number，成团成功率
      "avg_completion_time": 2.5         // number，平均成团时间（小时）
    }
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

**错误响应示例**:

```json
{
  "code": 400,
  "message": "不支持的拼团类型",
  "data": {
    "supported_types": ["3人团", "5人团", "10人团", "限时团"]
  },
  "timestamp": "2024-12-15T10:00:00Z"
}
```

#### 详细对接方案：

```javascript
// 获取全部活动列表
async function loadAllActivities() {
    try {
        const response = await fetch('/api/v1/activities', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.code === 200) {
            renderActivitiesList(data.data.activities);
            updateActivityStats(data.data.total_count, data.data.active_count);
            console.log('✅ 活动列表加载成功:', data.data.activities.length, '个活动');
        } else {
            showError(data.message || '获取活动列表失败');
        }
    } catch (error) {
        console.error('❌ 获取活动列表失败:', error);
        showError('网络错误，请重试');
    }
}

// 按拼团类型获取活动
async function loadActivitiesByGroupType(groupTypeName) {
    try {
        const response = await fetch(`/api/v1/activities?name=${encodeURIComponent(groupTypeName)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.code === 200) {
            renderGroupTypeActivities(data.data.activities);
            updateGroupTypeInfo(data.data.group_type_info);
            
            // 渲染推荐商品
            if (data.data.activities.length > 0 && data.data.activities[0].featured_products) {
                renderFeaturedProducts(data.data.activities[0].featured_products);
            }
            
            console.log('✅ 拼团类型活动加载成功:', groupTypeName, data.data.matched_count, '个活动');
        } else {
            showError(data.message || '获取拼团活动失败');
        }
    } catch (error) {
        console.error('❌ 获取拼团活动失败:', error);
        showError('网络错误，请重试');
    }
}

// 切换拼团类型标签
function switchGroupTypeTab(groupTypeName) {
    // 更新标签状态
    updateTabActiveState(groupTypeName);
    
    // 加载对应类型的活动
    loadActivitiesByGroupType(groupTypeName);
    
    // 保存当前选择的类型
    localStorage.setItem('currentGroupType', groupTypeName);
}

// 渲染活动列表
function renderActivitiesList(activities) {
    const container = document.getElementById('activitiesContainer');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-gray-400 text-4xl mb-4">🎯</div>
                <p class="text-gray-500">暂无活动</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-card bg-white rounded-2xl p-4 shadow-sm mb-4" data-activity-id="${activity.id}">
            <div class="flex items-center mb-3">
                <img src="${activity.icon}" class="w-12 h-12 rounded-full mr-3" alt="${activity.name}">
                <div class="flex-1">
                    <h3 class="font-bold text-gray-800">${activity.title}</h3>
                    <p class="text-sm text-gray-500">${activity.subtitle}</p>
                </div>
                <div class="text-right">
                    <div class="text-orange-500 font-bold">${activity.discount_info.value}% OFF</div>
                    <div class="text-xs text-gray-400">${activity.participant_count}人参与</div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-3 mb-3">
                <img src="${activity.banner_image}" class="w-full h-32 object-cover rounded-lg" alt="${activity.title}">
            </div>
            
            <div class="flex items-center justify-between text-sm">
                <div class="text-gray-600">
                    <span class="text-blue-600 font-medium">${activity.product_count}</span> 件商品参与
                </div>
                <div class="text-gray-400">
                    ${formatDateTime(activity.end_time)} 截止
                </div>
            </div>
            
            <button onclick="goToActivityDetail('${activity.id}')" 
                    class="w-full mt-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 rounded-xl font-medium">
                立即参与
            </button>
        </div>
    `).join('');
}

// 渲染拼团类型活动
function renderGroupTypeActivities(activities) {
    const container = document.getElementById('groupTypeActivitiesContainer');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-gray-400 text-4xl mb-4">👥</div>
                <p class="text-gray-500">该类型暂无活动</p>
            </div>
        `;
        return;
    }
    
    // 渲染活动信息和推荐商品
    activities.forEach(activity => {
        renderSingleGroupActivity(activity);
    });
}

// 渲染推荐商品
function renderFeaturedProducts(products) {
    const container = document.getElementById('featuredProductsContainer');
    
    container.innerHTML = `
        <div class="mb-4">
            <h4 class="text-lg font-bold text-gray-800 mb-3">🔥 活动推荐商品</h4>
            <div class="grid grid-cols-2 gap-3">
                ${products.map(product => `
                    <div class="bg-white rounded-xl p-3 shadow-sm" onclick="goToProductDetail('${product.id}')">
                        <img src="${product.image}" class="w-full h-32 object-cover rounded-lg mb-2" alt="${product.title}">
                        <h5 class="font-medium text-sm text-gray-800 mb-1 line-clamp-2">${product.title}</h5>
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-red-500 font-bold text-sm">${formatCurrency(product.group_price)}</div>
                                <div class="text-gray-400 text-xs line-through">${formatCurrency(product.original_price)}</div>
                            </div>
                            <div class="text-xs text-gray-500">${product.participants}人已拼</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// 更新拼团类型信息
function updateGroupTypeInfo(groupTypeInfo) {
    const infoContainer = document.getElementById('groupTypeInfo');
    
    if (groupTypeInfo) {
        infoContainer.innerHTML = `
            <div class="bg-blue-50 rounded-xl p-3 mb-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-bold text-blue-800">${groupTypeInfo.name}</h4>
                        <p class="text-sm text-blue-600">${groupTypeInfo.description}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-blue-800 font-bold">${groupTypeInfo.success_rate}%</div>
                        <div class="text-xs text-blue-600">成团率</div>
                    </div>
                </div>
                <div class="mt-2 text-xs text-blue-600">
                    平均 ${groupTypeInfo.avg_completion_time} 小时成团
                </div>
            </div>
        `;
    }
}

// 跳转到活动详情
function goToActivityDetail(activityId) {
    window.location.href = `activity_detail.html?id=${activityId}`;
}

// 工具函数
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        minimumFractionDigits: 0
    }).format(amount);
}

// 页面初始化时加载活动数据
document.addEventListener('DOMContentLoaded', function() {
    // 加载全部活动列表
    loadAllActivities();
    
    // 获取上次选择的拼团类型，默认为3人团
    const savedGroupType = localStorage.getItem('currentGroupType') || '3人团';
    switchGroupTypeTab(savedGroupType);
});
```

---

**文档版本**: v1.2
**最后更新**: 2024-12-15
**维护人员**: 开发团队

**更新日志**:

- v1.2: 更新活动相关接口，添加活动列表和拼团类型活动接口的详细说明和对接方案
- v1.1: 添加详细的接口参数说明、返回数据示例、错误代码对照表、数据验证规范、性能监控指标和测试用例模板
- v1.0: 初始版本，包含基础API对接方案