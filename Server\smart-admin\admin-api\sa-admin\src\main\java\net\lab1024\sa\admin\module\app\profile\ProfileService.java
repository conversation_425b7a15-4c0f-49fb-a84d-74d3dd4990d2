package net.lab1024.sa.admin.module.app.profile;

import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.wallets.domain.form.WalletTransactionsQueryForm;
import net.lab1024.sa.admin.module.business.wallets.domain.vo.WalletTransactionsVO;
import net.lab1024.sa.admin.module.business.wallets.domain.vo.WalletsVO;
import net.lab1024.sa.admin.module.business.wallets.service.WalletsService;
import net.lab1024.sa.admin.module.business.withdrawals.domain.form.WithdrawalsAddForm;
import net.lab1024.sa.admin.module.business.withdrawals.domain.form.WithdrawalsQueryForm;
import net.lab1024.sa.admin.module.business.withdrawals.service.WithdrawalsService;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.common.util.SmartStringUtil;
import net.lab1024.sa.base.module.support.config.ConfigKeyEnum;
import net.lab1024.sa.base.module.support.config.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class ProfileService {
    @Resource
    private WalletsService walletsService;

    @Resource
    private WithdrawalsService withdrawalsService;
    @Autowired
    private ConfigService configService;
    
    @Resource
    private EmployeeService employeeService;

    public ResponseDTO<Object> wallet(String type, String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        Long userId = SmartRequestUtil.getRequestUserId();

        Map<String, Object> balance = new LinkedHashMap<>();
        WalletsVO userWallet = walletsService.queryById(userId);
        balance.put("totalBalance", userWallet.getBalance().add(userWallet.getExperienceBalance()));
        balance.put("balance", userWallet.getBalance());
        balance.put("experienceBalance", userWallet.getExperienceBalance());
        balance.put("points", userWallet.getPoints());
        balance.put("totalRecharge", userWallet.getTotalRecharge());
        balance.put("totalWithdraw", userWallet.getTotalWithdraw());
        balance.put("totalEarnings", userWallet.getTotalEarnings());
        balance.put("status", userWallet.getStatus());
        
        // 获取用户实名认证状态
        try {
            boolean hasReal = employeeService.getHasRealByUserId(userId);
            balance.put("hasReal", hasReal);
        } catch (Exception e) {
            // 如果获取失败，默认为false
            balance.put("hasReal", false);
        }

        WalletTransactionsQueryForm walletTransactionsQueryForm = new WalletTransactionsQueryForm();
        walletTransactionsQueryForm.setSortItemList(new ArrayList<>());
        PageParam.SortItem si = new PageParam.SortItem();
        si.setColumn("id");
        si.setIsAsc(false);
        walletTransactionsQueryForm.getSortItemList().add(si);
        walletTransactionsQueryForm.setPageNum(pageNum);
        walletTransactionsQueryForm.setPageSize(pageSize);
        walletTransactionsQueryForm.setUserId(userId);
        if(type != null && !type.isEmpty()){
            walletTransactionsQueryForm.setType(type.toUpperCase());
        }
        if (Objects.equals(sort, "createTime_desc")) {
            si = new PageParam.SortItem();
            si.setColumn("create_time");
            si.setIsAsc(false);
            walletTransactionsQueryForm.getSortItemList().add(si);
        }
        if (Objects.equals(sort, "createTime_asc")) {
            si = new PageParam.SortItem();
            si.setColumn("create_time");
            si.setIsAsc(true);
            walletTransactionsQueryForm.getSortItemList().add(si);
        }
        PageResult<WalletTransactionsVO> transactions =  walletsService.transactionsQueryPage(walletTransactionsQueryForm);

        Map<String, Object> out = new LinkedHashMap<>();
        out.put("balance", balance);
        out.put("transactions", transactions);
        return ResponseDTO.ok(out);
    }

    public ResponseDTO<Object> withdrawalsApply(WithdrawalsAddForm withdrawalsAddForm) {
        Long userId = SmartRequestUtil.getRequestUserId();

        WalletsVO wallet = walletsService.queryById(userId);

        //判断最低提现额
        int lowestWithdraw = configService.getConfigValueInteger(ConfigKeyEnum.LOWEST_WITHDRAW);
        if(BigDecimal.valueOf(lowestWithdraw).compareTo(wallet.getBalance()) > 0){
            return ResponseDTO.userErrorParam("balance less than " + SmartStringUtil.formatWithCommas(lowestWithdraw, "."));
        }

        if(withdrawalsAddForm.getAmount().compareTo(wallet.getBalance()) > 0){
            return ResponseDTO.userErrorParam("balance not enough");
        }

        withdrawalsAddForm.setUserId(userId);
        withdrawalsAddForm.setStatus("pending");
        withdrawalsService.add(withdrawalsAddForm);

        return ResponseDTO.ok();
    }

    public ResponseDTO<Object> withdrawals(String status, String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        Long userId = SmartRequestUtil.getRequestUserId();

        WithdrawalsQueryForm withdrawalsQueryForm = new WithdrawalsQueryForm();
        withdrawalsQueryForm.setSortItemList(new ArrayList<>());
        withdrawalsQueryForm.setUserId(userId);
        withdrawalsQueryForm.setPageNum(pageNum);
        withdrawalsQueryForm.setPageSize(pageSize);
        PageParam.SortItem si = new PageParam.SortItem();
        si.setColumn("id");
        si.setIsAsc(false);
        withdrawalsQueryForm.getSortItemList().add(si);
        if(status != null && !status.isEmpty()){
            withdrawalsQueryForm.setStatus(status);
        }
        return ResponseDTO.ok(withdrawalsService.queryPage(withdrawalsQueryForm));
    }
}
