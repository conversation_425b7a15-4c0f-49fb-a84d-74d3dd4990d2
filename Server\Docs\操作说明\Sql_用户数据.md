### 连接远程服务器

  ssh root@47.76.164.209

### 连接MySQL（需要数据库用户名和密码）

  mysql -u root -p

### 选择数据库（根据项目配置，可能是 smart_admin 或其他名称）

  USE  tgw_pp;

列出表

show tables

### 执行软删除操作

  首先查看用户信息（确认要删除的用户）：
  -- 查看指定用户信息
  SELECT employee_id, login_name, actual_name, phone, deleted_flag, inviter_id, create_time, update_time
  FROM t_employee
  WHERE employee_id = 用户ID;

  -- 或根据登录名查找
  SELECT employee_id, login_name, actual_name, phone, deleted_flag, create_time, update_time
  FROM t_employee
  WHERE login_name = '用户名';

  执行软删除：
  -- 开启事务（安全操作）
  START TRANSACTION;

  -- 执行软删除
  UPDATE t_employee
  SET deleted_flag = 1, update_time = NOW()
  WHERE employee_id = 用户ID;

  -- 查看影响的行数
  SELECT ROW_COUNT() as affected_rows;

  -- 如果确认无误，提交事务
  COMMIT;

  -- 如果有问题，可以回滚
  -- ROLLBACK;
