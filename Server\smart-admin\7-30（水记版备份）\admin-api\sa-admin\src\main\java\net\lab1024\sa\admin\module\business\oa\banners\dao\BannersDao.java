package net.lab1024.sa.admin.module.business.oa.banners.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.oa.banners.domain.entity.BannersEntity;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersQueryForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.vo.BannersVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 横幅管理 Dao
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Mapper
public interface BannersDao extends BaseMapper<BannersEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<BannersVO> queryPage(Page page, @Param("queryForm") BannersQueryForm queryForm);

}
