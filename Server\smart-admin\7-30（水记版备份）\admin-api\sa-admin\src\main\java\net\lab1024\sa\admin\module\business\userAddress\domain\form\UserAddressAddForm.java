package net.lab1024.sa.admin.module.business.userAddress.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户收货地址表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 15:09:41
 * @Copyright -
 */

@Data
public class UserAddressAddForm {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "用户ID 不能为空")
    private Long userId;

    @Schema(description = "收件人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "收件人姓名 不能为空")
    private String recipientName;

    @Schema(description = "收件人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "收件人电话 不能为空")
    private String phoneNumber;

    @Schema(description = "省份", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotBlank(message = "省份 不能为空")
    private String province;

    @Schema(description = "城市", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotBlank(message = "城市 不能为空")
    private String city;

    @Schema(description = "区县", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotBlank(message = "区县 不能为空")
    private String district;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "详细地址 不能为空")
    private String addressLine;

    @Schema(description = "是否默认地址", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "是否默认地址 不能为空")
    private Integer isDefault;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}