<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- Header导航模块 -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between p-4 h-14">
        <!-- 返回按钮 -->
        <button @click="goBack" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:arrow-back-ios" class="text-gray-800 text-xl"></iconify-icon>
        </button>
        
        <!-- 页面标题 -->
        <h1 class="text-sm font-semibold text-gray-800">
          订单详情
        </h1>
        
        <!-- 返回首页按钮 -->
        <button @click="goToHome" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:home" class="text-gray-800 text-xl"></iconify-icon>
        </button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="pb-24">
      <!-- 拼团状态展示模块 -->
      <section class="bg-white py-6 px-4 text-center">
        <!-- 新用户免费体验提示 -->
        <div class="status-description" v-if="!loading && isFreeGroupBuy">
          <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 mb-4">
            <div class="flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:celebration" class="text-green-500 text-2xl mr-2"></iconify-icon>
              <span class="text-lg font-bold text-green-600">新用户免费体验</span>
            </div>
            <p class="text-sm text-gray-700">
              本次为免费体验拼团，您还有 <span class="font-bold text-orange-600">{{ authStore.noviceCount }}</span> 次免费机会
            </p>
          </div>
        </div>
      </section>

      <!-- 概率和奖励说明模块 -->
      <section class="bg-white py-6 px-4">
        <!-- 新用户免费体验说明 -->
        <div class="space-y-4" v-if="!loading && isFreeGroupBuy">
          <div class="text-center py-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
            <div class="flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:gift" class="text-green-500 text-2xl mr-2"></iconify-icon>
              <span class="text-lg font-bold text-green-600">完全免费体验</span>
            </div>
            <p class="text-sm text-gray-700">本次拼团无需支付任何费用</p>
          </div>
          
          <div class="text-center py-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
            <div class="flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:stars" class="text-orange-500 text-2xl mr-2"></iconify-icon>
              <span class="text-lg font-bold text-orange-600">体验拼团乐趣</span>
            </div>
            <p class="text-sm text-gray-700">结果无论如何都会有补偿奖励</p>
          </div>
        </div>
        
        <!-- 普通拼团说明 -->
        <div class="space-y-4" v-else-if="!loading">
          <!-- 抢中概率说明 -->
          <div class="text-center py-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg border border-red-100">
            <div class="flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:celebration" class="text-red-500 text-2xl mr-2"></iconify-icon>
              <span class="text-lg font-bold text-red-600">抢购成功概率 33%</span>
            </div>
            <p class="text-sm text-gray-600">抢购成功：直接获得产品，节省 <span class="font-bold text-red-500">{{ formatPrice(calculateSavings()) }}</span></p>
          </div>
          
          <!-- 未中奖说明 -->
          <div class="text-center py-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
            <div class="flex items-center justify-center mb-2">
              <iconify-icon icon="material-symbols:money-off" class="text-blue-500 text-2xl mr-2"></iconify-icon>
              <span class="text-lg font-bold text-blue-600">抢购失败概率67%</span>
            </div>
            <p class="text-sm text-gray-600">抢购失败：退回货款，获得补偿 <span class="font-bold text-blue-500">{{ getRedPacketAmount() }}</span> ₫</p>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-else class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
        </div>
      </section>

      <!-- 邀请好友助力提示 -->
      <section class="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg mx-4 mt-6 mb-6 p-4 text-center">
        <div class="flex items-center justify-center mb-2">
          <iconify-icon icon="material-symbols:groups" class="text-orange-500 text-xl mr-2"></iconify-icon>
          <span class="text-base font-bold text-orange-600">邀请好友助力</span>
        </div>
        <p class="text-sm text-gray-700">增加获得补偿概率，邀请越多赚得越多</p>
      </section>

      <!-- 商品信息展示 -->
      <section class="bg-white mt-12 p-4" v-if="product">
        <div class="flex items-center">
          <div 
            class="w-20 h-20 rounded-lg mr-4 flex items-center justify-center bg-cover bg-center"
            :style="{ backgroundImage: `url('${getProductMainImage()}')` }"
          >
            <div v-if="!getProductMainImage()" class="text-white text-lg font-bold bg-gradient-to-br from-blue-600 to-blue-800 w-full h-full flex items-center justify-center rounded-lg">
              {{ getProductBrand() }}
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-base font-semibold text-gray-800 mb-1">
              {{ getProductName() }}
            </h3>
            <div class="flex items-center">
              <!-- 新用户免费显示 -->
              <span v-if="isFreeGroupBuy" class="text-lg font-bold text-green-600 mr-2">免费体验</span>
              <!-- 普通价格显示 -->
              <span v-else class="text-lg font-bold text-red-500 mr-2">{{ formatPrice(getGroupPrice()) }}</span>
              
              <span v-if="getOriginalPrice() && !isFreeGroupBuy" class="text-sm text-gray-400 line-through">{{ formatPrice(getOriginalPrice()) }}</span>
              
              <!-- 标签显示 -->
              <span v-if="isFreeGroupBuy" class="bg-green-500 text-white px-2 py-1 rounded text-xs ml-2">新手专享</span>
              <span v-else class="bg-red-500 text-white px-2 py-1 rounded text-xs ml-2">拼团价</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 商品信息加载状态 -->
      <section class="bg-white mt-12 p-4" v-else>
        <div class="flex items-center animate-pulse">
          <div class="w-20 h-20 bg-gray-300 rounded-lg mr-4"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-300 rounded mb-2"></div>
            <div class="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      </section>


    </main>

    <!-- 参与拼团模块 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <button 
        @click="joinGroup" 
        :disabled="isJoining || hasJoined"
        class="join-button gradient-button w-full text-white py-4 px-6 rounded-full font-semibold text-lg relative overflow-hidden"
      >
        <span class="relative z-10">
          {{ isJoining ? '参与中...' : hasJoined ? '已加入拼团' : '参与下单' }}
        </span>
        <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
          <iconify-icon icon="material-symbols:card-giftcard" class="text-white text-xl"></iconify-icon>
        </div>
      </button>
      
      <!-- 支付保障模块 -->
      <div class="text-center mt-3">
        <p class="text-sm font-semibold text-gray-600">快速支付</p>
        <p class="text-xs text-gray-500">保证准确</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError } from '@/utils/message.js'
import { getImageUrl } from '@/config/image'
import { getGroupSize, formatPrice } from '@/utils/format'

export default {
  name: 'GroupConfirmPage',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 响应式数据
    const loading = ref(true)
    const error = ref(null)
    const product = ref(null)
    const isJoining = ref(false)
    const hasJoined = ref(false)
    
    // 新用户相关计算属性
    const isNewUserProduct = computed(() => {
      return route.query.goodsType === 'newUser' || 
             route.query.isNewUserProduct === 'true' ||
             product.value?.goodsType === 'newUser'
    })
    
    const canJoinFreeGroup = computed(() => {
      return isNewUserProduct.value && authStore.isNewUser && authStore.noviceCount > 0
    })
    
    const isFreeGroupBuy = computed(() => {
      return canJoinFreeGroup.value
    })
    
    // API服务
    let productApi = null
    
    // 检查登录状态
    const checkLoginStatus = async () => {
      // 简化登录状态检查，只检查本地存储
      if (!authStore.isLoggedIn) {
        // 保存当前页面路径，登录后返回
        const currentPath = route.fullPath
        router.push({
          name: 'Login',
          query: { redirect: currentPath }
        })
        return false
      }
      
      // 检查是否有token和用户信息
      if (!authStore.token || !authStore.user) {
        console.log('❌ 缺少token或用户信息')
        const currentPath = route.fullPath
        router.push({
          name: 'Login',
          query: { redirect: currentPath }
        })
        return false
      }
      
      console.log('✅ 登录状态检查通过', {
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        userInfo: authStore.user
      })
      
      return true
    }
    
    // 初始化API服务
    const initApiService = async () => {
      try {
        productApi = new StandardApiAdapter()
        console.log('✅ 拼团确认页API服务初始化成功')
      } catch (err) {
        console.error('❌ 拼团确认页API服务初始化失败:', err)
        error.value = 'API服务初始化失败'
      }
    }
    
    // 加载商品详情
    const loadProductDetail = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const productId = route.params.id
        if (!productId) {
          throw new Error('商品ID不能为空')
        }
        
        if (!productApi) {
          await initApiService()
        }
        
        console.log('🔄 加载拼团商品详情:', productId)
        
        // 调用商品详情API
        const response = await productApi.getProductDetail(productId)
        
        console.log('📦 拼团商品详情API响应:', response)
        
        if (response.code === 0 || response.code === 200) {
          product.value = response.data || {}
          console.log('✅ 拼团商品详情加载成功:', product.value)
        } else {
          throw new Error(response.message || response.msg || '商品详情加载失败')
        }
      } catch (err) {
        console.error('❌ 拼团商品详情加载失败:', err)
        error.value = err.message || '商品详情加载失败，请重试'
      } finally {
        loading.value = false
      }
    }
    
    // 工具函数
    // 价格格式化现在使用统一的越南盾格式化函数
    
    // 使用统一的图片配置，已通过import导入

    const getProductMainImage = () => {
      if (!product.value || !product.value.images || product.value.images.length === 0) {
        return ''
      }
      
      return getImageUrl(product.value.images[0].url)
    }
    
    const getProductName = () => {
      if (!product.value) return 'Oggi儿童营养奶粉 900g×3罐装'
      
      return product.value.goodsName || 
             product.value.title || 
             product.value.name || 
             'Oggi儿童营养奶粉 900g×3罐装'
    }
    
    const getProductBrand = () => {
      if (!product.value) return 'Oggi'
      
      return product.value.brand || 'Oggi'
    }
    
    // 获取第一个SKU，用于价格显示
    const getFirstSku = () => {
      if (!product.value || !product.value.skus || product.value.skus.length === 0) {
        return null
      }
      return product.value.skus[0]
    }
    
    const getGroupPrice = () => {
      if (!product.value) return 198.00
      
      // 优先使用SKU中的价格
      const firstSku = getFirstSku()
      if (firstSku && firstSku.price) {
        return parseFloat(firstSku.price)
      }
      
      // 如果没有SKU数据，使用商品级别的价格
      return parseFloat(product.value.price || 198.00)
    }
    
    const getOriginalPrice = () => {
      if (!product.value) return 298.00
      
      // 优先使用SKU中的原价
      const firstSku = getFirstSku()
      if (firstSku && (firstSku.originalPrice || firstSku.original_price)) {
        return parseFloat(firstSku.originalPrice || firstSku.original_price)
      }
      
      // 如果没有SKU数据，使用商品级别的原价
      return parseFloat(product.value.originalPrice || product.value.original_price || product.value.marketPrice || 298.00)
    }
    
    const getDirectBuyPrice = () => {
      if (!product.value) return 298.00
      
      // 优先使用SKU中的直接购买价格
      const firstSku = getFirstSku()
      if (firstSku && firstSku.alonePrice) {
        return parseFloat(firstSku.alonePrice)
      }
      
      // 如果没有SKU数据，使用商品级别的直接购买价格
      return parseFloat(product.value.alonePrice || 298.00)
    }
    
    const getProductGroupSize = () => {
      if (!product.value) return 10
      return getGroupSize(product.value)
    }
    
    const calculateSavings = () => {
      const directBuyPrice = getDirectBuyPrice()
      const groupPrice = getGroupPrice()
      
      return Math.max(0, directBuyPrice - groupPrice)
    }
    
    // 获取奖励积分
    const getRewardPoints = () => {
      if (!product.value) return 100
      
      // 可以从商品数据中获取积分奖励，或者根据价格计算
      return product.value.rewardPoints || 
             Math.floor(getGroupPrice() * 0.1) || 
             100
    }
    
    // 获取红包金额
    const getRedPacketAmount = () => {
      if (!product.value) return 10
      
      // 红包金额为拼团购买价格的10%
      return product.value.redPacketAmount || 
             Math.floor(getGroupPrice() * 0.10) || 
             10
    }
    
    
    // 事件处理函数
    const goBack = () => {
      router.back()
    }
    
    const goToHome = () => {
      router.push('/')
    }
    
    const shareGroup = async () => {
      console.log('🔗 分享拼团:', getProductName())
      try {
        const { generateAndCopyShareLink } = await import('@/utils/share')
        await generateAndCopyShareLink(
          '/home',
          (shareLink) => {
            showSuccess('邀请链接已复制，快去分享给好友一起拼团吧！')
            console.log('拼团分享链接已生成:', shareLink)
          },
          (error) => {
            showError('复制失败，请重试')
            console.error('分享失败:', error)
          }
        )
      } catch (error) {
        console.error('分享功能加载失败:', error)
        showError('分享功能暂时不可用')
      }
    }
    
    const joinGroup = async () => {
      // 简化登录检查：只检查基本登录状态，不调用API
      if (!authStore.isLoggedIn) {
        showError('请先登录')
        const currentPath = route.fullPath
        router.push({
          name: 'Login',
          query: { redirect: currentPath }
        })
        return
      }
      
      console.log('✅ 开始参与拼团', {
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        userInfo: authStore.user
      })
      
      try {
        isJoining.value = true
        
        // 模拟参与拼团的API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        hasJoined.value = true
        showSuccess('成功参与拼团！')
        
        // 跳转到支付页面，传递商品信息和价格
        const productId = route.params.id
        const amount = getGroupPrice() // 获取拼团价格
        const productName = getProductName()
        
        // 获取SKU ID
        const firstSku = getFirstSku()
        const skuId = firstSku ? (firstSku.id || firstSku.skuId) : productId
        
        console.log('🔄 准备跳转到支付页面', {
          productId,
          amount,
          productName,
          skuId,
          firstSku
        })
        
        try {
          await router.push({
            name: 'Payment',
            query: {
              productId: productId,
              amount: amount,
              productName: productName,
              skuId: skuId, // 添加SKU ID参数
              activityId: product.value?.activityId || 3, // 添加活动ID
              type: 'group', // 标识这是拼团支付
              productImage: product.value?.images?.[0]?.url || product.value?.image || product.value?.imageUrl // 添加商品图片
            }
          })
          console.log('✅ 成功跳转到支付页面')
        } catch (routerError) {
          console.error('❌ 路由跳转失败:', routerError)
          showError('无法跳转到支付页面')
          isJoining.value = false
        }
        
      } catch (err) {
        console.error('❌ 参与拼团过程中出错:', err)
        showError('参与拼团失败，请重试')
        isJoining.value = false
      }
    }
    
    // 生命周期
    onMounted(async () => {
      console.log('🎯 GroupConfirmPage mounted')
      await loadProductDetail()
    })
    
    return {
      // 响应式数据
      loading,
      error,
      product,
      isJoining,
      hasJoined,
      
      // 新用户相关
      isNewUserProduct,
      canJoinFreeGroup,
      isFreeGroupBuy,
      authStore,
      
      // 方法
      loadProductDetail,
      formatPrice,
      getImageUrl,
      getProductMainImage,
      getProductName,
      getProductBrand,
      getGroupPrice,
      getOriginalPrice,
      getDirectBuyPrice,
      getProductGroupSize,
      calculateSavings,
      getRewardPoints,
      getRedPacketAmount,
      goBack,
      goToHome,
      shareGroup,
      joinGroup,
      checkLoginStatus
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
.gradient-button {
  background: linear-gradient(135deg, #E91E63 0%, #FF4757 100%);
}

/* 状态描述样式 */
.status-description {
  font-size: 16px;
  color: #374151;
  line-height: 1.6;
}

.highlight-number {
  font-size: 24px;
  font-weight: bold;
  color: #ef4444;
}


/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 按钮动画 */
.join-button {
  transition: all 0.3s ease;
}

.join-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
}

.join-button:active:not(:disabled) {
  transform: translateY(0);
}

.join-button:disabled {
  opacity: 0.75;
  cursor: not-allowed;
}

</style> 