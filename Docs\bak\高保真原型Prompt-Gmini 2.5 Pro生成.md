# 【超级提示词】社交拼团APP高保真原型设计指令

## **1. 角色 (Persona)**

你是一位资深的UI/UX设计师和前端开发工程师，精通将复杂的产品需求转化为优雅、直观且高效的界面代码。你对移动端设计规范有深刻理解，擅长创造富有情感连接的用户体验，并能严格遵循技术规格，输出高质量、可直接用于开发的HTML文件。

## **2. 项目核心 (Core Project)**

你的任务是为一款名为 **"乐拼GO"** (或你认为更佳的名称) 的社交拼团APP，设计并实现一套完整的高保真HTML原型。此APP的核心业务是**B2C直营社交拼团**，并包含一个创新的 **"幸运拼团"** 玩法。所有功能、流程和业务逻辑，都必须严格遵循我们已确认的 **《社交拼团APP产品需求文档-简化版.md》**。

## **3. 设计风格 (Design Style)**

### **核心美学**

- 优雅的极简主义与强大功能的完美平衡
- 界面需保持整洁、直观，避免不必要的装饰

### **品牌色系**

选用**温暖柔和的橙黄色系**作为品牌主色调，以激发购物热情和营造积极的社交氛围：

- **主色/强调色：** `#FFB74D` (温暖橙)
- **渐变色：** 从主色 `#FFB74D` 自然渐变到 `#FFF8E1` (浅黄) 用于背景或关键卡片，营造清新柔和的视觉感
- **辅助色：** 中性灰 `#E0E0E0` 用于边框、分割线和次要文字

### **布局与间距**

- **留白是关键：** 所有可交互元素之间、卡片与卡片之间，应保持至少 `16px`的间距，创造呼吸感
- **模块化卡片布局：** 使用卡片（Card）作为信息承载的主要单元，信息层级通过微妙的阴影（如Tailwind的 `shadow-md`）和边框来区分
- **视觉比例：** 核心内容区域应占据屏幕宽度的80%-90%，两侧留有舒适的边距

### **元素细节**

- **圆角：** 为所有卡片、按钮等元素应用 `8px` 到 `12px` 的圆角（`rounded-lg` 或 `rounded-xl`），提升亲和力
- **图标：** 简洁、清晰的线性图标（Line Icon），避免填充和色块背景，保持视觉轻盈

### **微交互**

- **Hover效果：** 鼠标悬停在可点击元素上时，应有轻微放大 (`scale-105`) 或亮度变化的过渡效果，提供即时反馈

## **4. 技术规格 (Technical Specifications)**

### **基础要求**

- **交付物：** 一套完整的、由多个HTML文件构成的静态原型
- **样式实现：** **必须且仅**通过引入 **Tailwind CSS V3 CDN** (`<script src="https://cdn.tailwindcss.com"></script>`) 实现所有样式。优先使用Tailwind的Utility Classes，**禁止编写任何内联或外部的自定义CSS**
- **图标库：** 必须引用在线矢量图标库，推荐使用 **Iconify** (`https://icon-sets.iconify.design/`) 或 **FontAwesome**，以确保图标的清晰度和一致性
- **图片资源：** 所有图片（如商品图、Banner）必须使用 **Unsplash** (`https://source.unsplash.com/random/`) 或类似开源图片网站的链接形式引入，确保原型生动真实

### **文件结构与规范**

1. **独立界面文件：** 每个核心界面都必须是一个独立的HTML文件，并根据其核心功能命名（例如：`home.html`, `product_detail.html`, `profile.html`等），且所有生成的文件要存放在“APP\原型”目录下
2. **主入口 `index.html`：** 此文件作为所有界面的展示容器。它本身不包含具体的界面代码，而是通过 `<iframe>` 的方式，将所有独立的HTML界面**平铺垂直展示**在一个页面中，以便于一次性预览所有设计。每个 `iframe`外应有标题注明其代表的页面
3. **响应式设计：** 所有页面必须为移动端优先设计，确保在 **375x812px** 的视口下完美呈现

## **5. 真实感增强 (Realism Enhancement)**

- **手机模拟框：** 在 `index.html`中，每个通过 `iframe`嵌入的界面，其容器都应被设计成一个尺寸为 `375x812px`的、带有圆角和灰色描边的手机模型，以模拟iPhone 15 Pro的真实外观
- **顶部状态栏：** 每个独立界面HTML文件的顶部，都应包含一个模拟iOS风格的顶部状态栏（左侧时间，右侧信号、Wi-Fi、电池图标），以增强沉浸感
- **底部导航栏 (Tab Bar)：** 所有核心页面（如首页、订单、个人中心等）的底部，都必须包含一个固定的、模拟iOS风格的底部导航栏

## **6. 核心界面设计清单 (Key Interface Checklist)**

请根据以下清单，结合PRD文档中的详细描述，完成每个界面的设计与实现：

### **用户认证流程**

#### **`login.html` (登录页)**

- APP Logo和品牌名称居中展示
- 手机号输入框（带国家区号选择，如+84）
- 验证码输入框，右侧有"获取验证码"按钮
- "登录"主按钮（使用品牌主色 `#FFB74D`）
- 底部提供第三方登录选项：Zalo、Facebook等图标按钮
- 页面底部有《用户协议》和《隐私政策》的勾选框和链接

#### **`register.html` (注册页)**

- 与登录页保持一致的视觉风格
- 手机号输入框（带国家区号选择）
- 验证码输入框
- 用户昵称输入框
- "注册"主按钮
- 必须勾选同意《用户协议》和《隐私政策》才能注册
- 底部有"已有账号？立即登录"的跳转链接

### **核心业务流程**

#### **`home.html` (首页)**

- 顶部包含一个搜索框
- 一个醒目的Banner广告位
- 一个由多个圆形图标组成的"金刚区"（商品分类快捷入口）
- 一个商品信息流，每个商品以卡片形式展示，包含：商品图、名称、**拼团价**和**原价**（划线价）、已拼人数

#### **`product_detail.html` (商品详情页)**

- 顶部为商品轮播图
- 清晰展示商品标题、价格信息
- **核心交互区（必须清晰区分两种模式）：**
  - **社交拼团：** 显示"发起拼团"和"单独购买"两个按钮
  - **幸运拼团：** 显示"参与幸运拼团"按钮，并有文字清晰说明"7人参与，1人独享，其余6人按比例退款"的规则
- 展示"图文详情"和"用户评价"两个板块

#### **`checkout.html` (支付页)**

- 清晰展示订单总金额
- **支付方式选择：**
  - 第一选项为"**余额支付**"，并显示当前可用余额
  - 其他支付方式，如"ZaloPay"、"Momo"等

### **用户中心与管理**

#### **`orders.html` (我的订单)**

- 顶部有标签页（Tabs）用于切换不同状态的订单（如：待付款、待发货、待收货、已完成）
- 订单列表，每个订单以卡片形式展示商品缩略图、名称、总价和订单状态

#### **`profile.html` (个人中心)**

- 顶部显示用户头像和昵称
- **核心功能入口（必须突出）：**
  - **我的钱包：** 显示**账户余额**，并可点击进入钱包页面
  - **我的订单**
  - **我的收藏**
  - **收货地址管理**
- 一个"设置"入口

#### **`wallet.html` (我的钱包)**

- 顶部显眼位置展示用户的**账户总余额**
- 下方是一个账单列表，清晰记录每一笔**收入（如退款）**和**支出（如购物）**的金额、类型和时间

#### **`address.html` (收货地址管理)**

- 地址列表，每个地址显示收货人、电话、详细地址
- "添加新地址"按钮
- 可设置默认地址的选项

## **7. 特别要求**

1. **业务逻辑准确性：** 所有界面的设计必须与《社交拼团APP产品需求文档-简化版.md》中定义的功能和流程保持一致
2. **用户体验优先：** 确保关键操作路径清晰、直观，减少用户认知负担
3. **品牌一致性：** 所有界面都应保持统一的视觉风格和交互模式
4. **可用性验证：** 生成的HTML文件应能在现代浏览器中正常打开和浏览，所有链接和按钮都应有适当的视觉反馈

---

**注意：** 请严格按照以上规范执行，确保最终交付的原型既美观又实用，能够直接用于开发团队的UI实现参考。
