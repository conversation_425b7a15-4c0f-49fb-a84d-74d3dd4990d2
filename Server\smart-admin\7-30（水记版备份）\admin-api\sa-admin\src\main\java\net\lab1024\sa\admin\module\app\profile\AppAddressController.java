package net.lab1024.sa.admin.module.app.profile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressAddForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressUpdateForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "APP:个人地址")
public class AppAddressController {
    @Resource
    AppAddressService appAddressService;

    @Operation(summary = "收货地址列表")
    @GetMapping("/app/v1/address")
    public ResponseDTO<Object> appAddress(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize
    ) {
        return appAddressService.query(name, sort, pageNum, pageSize);
    }

    @Operation(summary = "添加收货地址")
    @PostMapping("/app/v1/address/add")
    public ResponseDTO<Object> appAddressAdd(@Valid @RequestBody UserAddressAddForm userAddressAddForm) {
        return appAddressService.add(userAddressAddForm);
    }

    @Operation(summary = "修改收货地址")
    @PostMapping("/app/v1/address/update")
    public ResponseDTO<Object> appAddressUpdate(@Valid @RequestBody UserAddressUpdateForm userAddressUpdateForm) {
        return appAddressService.update(userAddressUpdateForm);
    }

    @Operation(summary = "删除收货地址")
    @GetMapping("/app/v1/address/delete/{addressId}")
    public ResponseDTO<Object> appAddressDelete(@PathVariable Long addressId) {
        return appAddressService.delete(addressId);
    }

}
