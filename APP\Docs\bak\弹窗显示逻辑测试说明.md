# 首页弹窗显示逻辑修改说明

## 修改内容

根据用户需求，已将首页的弹出广告窗显示逻辑修改为：**只对符合以下条件的用户弹出**

### 弹窗显示条件
1. **未登录用户** - 所有未登录的访客都会看到弹窗
2. **已登录但订单数量小于3单的用户** - 已登录但购买经验较少的用户会看到弹窗

### 弹窗不显示条件
- **已登录且订单数量>=3单的用户** - 老用户不会看到弹窗

## 技术实现

### 1. 新增方法

#### `getUserOrderCount()` - 获取用户订单数量
```javascript
const getUserOrderCount = async () => {
  try {
    // 调用订单列表API获取订单总数
    const response = await homeApi.getOrders({
      page: 1,
      per_page: 1, // 只需要获取数量，不需要具体数据
      status: 'all'
    })
    
    // 从响应中获取订单总数
    const totalOrders = response.data?.pagination?.total || 
                      response.data?.total || 
                      response.data?.count || 0
    
    return totalOrders
  } catch (error) {
    // 错误处理：认证错误按未登录处理，其他错误返回0
    return 0
  }
}
```

#### `shouldShowSubsidyModal()` - 修改弹窗显示判断逻辑
```javascript
const shouldShowSubsidyModal = async () => {
  try {
    // 检查用户是否登录
    const token = localStorage.getItem('access_token') || 
                 sessionStorage.getItem('access_token') ||
                 localStorage.getItem('token')
    
    // 未登录用户：显示弹窗
    if (!token) {
      return true
    }
    
    // 已登录用户：检查订单数量
    const orderCount = await getUserOrderCount()
    
    // 订单数量小于3单：显示弹窗
    if (orderCount < 3) {
      return true
    }
    
    // 订单数量>=3单：不显示弹窗
    return false
    
  } catch (error) {
    // 出错时，为了保险起见，不显示弹窗
    return false
  }
}
```

### 2. 修改页面初始化逻辑

在`onMounted`中将同步调用改为异步调用：

```javascript
// 检查是否需要显示平台补贴弹窗
if (await shouldShowSubsidyModal()) {
  setTimeout(() => {
    showSubsidyModal()
  }, 2000)
}
```

## 使用的API接口

### 1. 订单列表接口
- **路径**: `/api/v1/orders`
- **方法**: GET
- **参数**: 
  - `page: 1` - 页码
  - `per_page: 1` - 每页数量（只需要获取总数）
  - `status: 'all'` - 所有状态的订单
- **响应**: 包含订单总数的分页信息

### 2. 用户状态接口
- **路径**: `/api/v1/userStatus`
- **方法**: GET
- **说明**: 用于验证用户登录状态

## 测试方法

### 1. 未登录用户测试
1. 清除浏览器所有存储（localStorage、sessionStorage）
2. 刷新首页
3. **预期结果**: 2秒后显示弹窗

### 2. 已登录新用户测试（订单数<3）
1. 登录账号（确保该账号订单数量少于3单）
2. 刷新首页
3. **预期结果**: 2秒后显示弹窗

### 3. 已登录老用户测试（订单数>=3）
1. 登录账号（确保该账号订单数量>=3单）
2. 刷新首页
3. **预期结果**: 不显示弹窗

### 4. API错误处理测试
1. 断开网络连接
2. 刷新首页
3. **预期结果**: 不显示弹窗（保守处理）

## 控制台日志

为了便于调试，系统会在控制台输出详细的判断过程：

```
🔍 用户未登录，显示弹窗
🔍 用户已登录，检查订单数量...
📊 用户订单总数: 2
🔍 订单数量小于3单，显示弹窗
```

或

```
🔍 用户已登录，检查订单数量...
📊 用户订单总数: 5
🔍 订单数量>=3单，不显示弹窗
```

## 弹窗内容

弹窗显示"平台补贴"相关内容，包括：
- 标题：平台补贴
- 副标题：每日领取
- 金额显示：+10.000₫
- 操作按钮：去收集
- 说明文字：通过每日签到奖励和分享返利来获得更多收益

点击"去收集"按钮会跳转到活动页面(`/activity`)。

## 注意事项

1. **性能优化**: 只在需要时调用订单API，避免不必要的网络请求
2. **错误处理**: 完善的错误处理机制，确保API错误不影响用户体验
3. **兼容性**: 支持多种token存储方式，兼容现有的认证系统
4. **调试友好**: 详细的控制台日志，便于开发和调试

## 相关文件

- `APP/src/views/home/<USER>
- `APP/src/api/standardAdapter.js` - API适配器（订单接口）
- `APP/src/api/order.js` - 订单API封装
- `APP/test-public-api.html` - API测试工具（参考接口文档） 