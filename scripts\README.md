# 自动化PR摘要生成系统

## 🚀 功能特性

- **自动记录修改**: 每次提交自动记录文件变更信息
- **智能摘要生成**: 基于Git历史生成详细的PR摘要
- **分类变更**: 自动将修改按功能分类（新增功能、页面优化、API接口等）
- **GitHub Actions集成**: 自动更新PR描述和评论
- **一键创建PR**: 通过脚本快速创建包含详细摘要的PR

## 📦 安装和设置

### 1. 设置Git Hooks

```bash
# 运行设置脚本
./scripts/setup-hooks.sh
```

### 2. 安装依赖

```bash
# 确保Node.js环境
npm install

# 安装GitHub CLI（可选，用于自动创建PR）
# macOS
brew install gh

# Ubuntu/Debian
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
sudo apt update && sudo apt install gh

# 认证GitHub CLI
gh auth login
```

## 🔧 使用方法

### 方法1: 使用自动化脚本（推荐）

```bash
# 创建新功能分支
git checkout -b feature/new-feature

# 进行开发和提交
git add .
git commit -m "实现新功能"

# 一键创建PR（自动生成摘要）
./scripts/create-pr.sh
```

### 方法2: 手动生成摘要

```bash
# 生成PR摘要
node scripts/generate-pr-summary.js

# 查看生成的摘要
cat .git/pr-summary-$(git branch --show-current).md

# 手动创建PR
gh pr create --title "功能更新" --body-file .git/pr-summary-$(git branch --show-current).md
```

### 方法3: 使用GitHub Actions自动化

当您推送分支并创建PR时，GitHub Actions会：
1. 自动分析代码变更
2. 生成详细的摘要
3. 更新PR描述
4. 添加分析评论

## 📊 生成的摘要包含

### 基本信息
- 分支名称和目标分支
- 提交数量和文件变更统计
- 代码行数变更统计

### 变更分类
- **新增功能**: 新增的组件和功能
- **页面优化**: 页面和视图的改进
- **API接口**: 接口相关的修改
- **样式更新**: CSS/SCSS样式修改
- **配置修改**: 配置文件的变更
- **文档更新**: 文档和说明的更新

### 详细信息
- 提交历史列表
- 文件变更统计
- 检查清单
- 注意事项

## 📝 自定义配置

### 修改分类规则

编辑 `scripts/generate-pr-summary.js` 中的 `categorizeChanges` 方法：

```javascript
categorizeChanges(changes) {
  const categories = {
    '自定义分类': [],
    // 添加更多分类...
  };
  
  // 自定义分类逻辑
  changes.forEach(change => {
    // 分类逻辑...
  });
  
  return categories;
}
```

### 修改摘要模板

编辑 `scripts/generate-pr-summary.js` 中的 `generateSummary` 方法来自定义摘要格式。

### 配置GitHub Actions

编辑 `.github/workflows/auto-pr-summary.yml` 来自定义自动化行为。

## 🔍 文件结构

```
scripts/
├── generate-pr-summary.js  # 摘要生成脚本
├── create-pr.sh           # PR创建脚本
├── setup-hooks.sh         # Git hooks设置脚本
└── README.md             # 使用说明

.githooks/
├── pre-commit            # 提交前钩子
└── post-commit           # 提交后钩子

.github/workflows/
└── auto-pr-summary.yml   # GitHub Actions配置
```

## 🛠️ 故障排除

### 常见问题

1. **脚本权限问题**
   ```bash
   chmod +x scripts/*.sh
   chmod +x scripts/generate-pr-summary.js
   ```

2. **GitHub CLI未认证**
   ```bash
   gh auth login
   ```

3. **Node.js版本问题**
   ```bash
   # 确保Node.js版本 >= 14
   node --version
   ```

4. **Git hooks未生效**
   ```bash
   # 重新设置hooks
   ./scripts/setup-hooks.sh
   ```

### 调试模式

```bash
# 启用调试模式
DEBUG=true node scripts/generate-pr-summary.js

# 查看Git hooks日志
git config --global core.hooksPath .githooks
```

## 🎯 最佳实践

1. **提交频率**: 建议小而频繁的提交，便于生成更准确的摘要
2. **分支命名**: 使用有意义的分支名，如 `feature/user-auth`、`fix/login-bug`
3. **提交信息**: 写清晰的提交信息，会被包含在摘要中
4. **定期更新**: 定期更新脚本和模板以适应项目需求

## 📈 高级功能

### 集成外部工具

可以集成其他工具来增强摘要生成：

```javascript
// 集成代码复杂度分析
const complexity = analyzeComplexity(changes);

// 集成测试覆盖率
const coverage = getTestCoverage();

// 集成性能分析
const performance = analyzePerformance();
```

### 自定义通知

可以添加Slack、邮件等通知方式：

```yaml
# 在GitHub Actions中添加通知步骤
- name: Send Slack Notification
  uses: 8398a7/action-slack@v3
  with:
    status: success
    text: "PR摘要已生成: ${{ github.event.pull_request.html_url }}"
```

## 📞 支持

如有问题或建议，请：
1. 查看故障排除部分
2. 检查GitHub Actions日志
3. 提交Issue到项目仓库