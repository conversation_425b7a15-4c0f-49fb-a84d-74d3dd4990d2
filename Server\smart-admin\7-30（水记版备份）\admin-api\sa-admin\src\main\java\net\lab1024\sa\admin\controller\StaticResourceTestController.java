package net.lab1024.sa.admin.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 静态资源测试控制器
 * 用于调试静态资源配置问题
 */
@RestController
public class StaticResourceTestController {
    
    @GetMapping("/test/static-config")
    public Map<String, Object> testStaticConfig() {
        Map<String, Object> result = new HashMap<>();
        
        String uploadPath = "D:/Dev/团购网/Server/smart-admin/upload/";
        File uploadDir = new File(uploadPath);
        
        result.put("uploadPath", uploadPath);
        result.put("uploadDirExists", uploadDir.exists());
        result.put("uploadDirIsDirectory", uploadDir.isDirectory());
        result.put("uploadDirCanRead", uploadDir.canRead());
        
        // 测试具体文件
        String testFilePath = uploadPath + "public/common/1da01dd176184f7a8336590b7903ad55_20250708163233.jpg";
        File testFile = new File(testFilePath);
        
        result.put("testFilePath", testFilePath);
        result.put("testFileExists", testFile.exists());
        result.put("testFileCanRead", testFile.canRead());
        result.put("testFileSize", testFile.length());
        
        return result;
    }
}