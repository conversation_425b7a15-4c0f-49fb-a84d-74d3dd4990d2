# 团购网项目说明文档

欢迎来到团购网项目说明文档！本目录包含了项目的完整技术和产品文档。

## 📁 文档目录

### 📋 产品文档
- **[产品需求说明书.md](./产品需求说明书.md)** - 完整的产品需求和功能规格
- **[功能规格说明书.md](./功能规格说明书.md)** - 详细的功能设计和业务流程
- **[项目总体说明文档.md](./项目总体说明文档.md)** - 项目整体概述和商业价值

### 🏗️ 技术文档
- **[技术架构文档.md](./技术架构文档.md)** - 系统架构设计和技术选型
- **[开发指南.md](./开发指南.md)** - 开发环境搭建和编码规范
- **[API接口规范文档.md](./API接口规范文档.md)** - API接口设计规范和示例

### 🚀 运维文档
- **[部署运维文档.md](./部署运维文档.md)** - 系统部署、监控和运维指南

### 📊 数据文档
- **[表结构说明.md](../表结构说明.md)** - 数据库表结构详细说明
- **[表结构.md](../表结构.md)** - 原始数据库表结构

## 🎯 项目概述

团购网是一个基于社交推荐和抽奖机制的移动端团购平台，采用Vue 3 + Spring Boot 3的现代化技术栈开发。平台通过独特的"抽奖式购物"体验，结合社交裂变营销，为用户提供超值购物体验。

### 核心特性
- 🎲 **抽奖购物** - 独特的抽奖机制，用户有机会以极低价格获得商品
- 👥 **社交裂变** - 邀请好友参与获得丰厚奖励，快速用户增长
- 📱 **移动优先** - 专为移动端优化的流畅用户体验
- 💰 **多元钱包** - 支持余额、体验金、积分等多种资金类型
- 🏆 **游戏化运营** - 等级系统、积分奖励等游戏化元素

### 技术亮点
- **前端**: Vue 3 + Composition API + Vite + Pinia + Vant UI
- **后端**: Spring Boot 3.3.1 + SA-Token + MyBatis Plus + MySQL 8.0
- **架构**: 微服务架构 + 读写分离 + Redis集群 + CDN加速
- **安全**: 多层安全防护 + 数据加密 + 风控系统

## 📖 如何使用这些文档

### 👨‍💻 开发人员
1. 从 **[开发指南.md](./开发指南.md)** 开始，搭建开发环境
2. 阅读 **[技术架构文档.md](./技术架构文档.md)** 了解系统架构
3. 参考 **[API接口规范文档.md](./API接口规范文档.md)** 进行接口开发
4. 查看 **[表结构说明.md](../表结构说明.md)** 了解数据库设计

### 🏗️ 架构师
1. 阅读 **[项目总体说明文档.md](./项目总体说明文档.md)** 了解整体架构
2. 研究 **[技术架构文档.md](./技术架构文档.md)** 的架构设计思路
3. 参考 **[部署运维文档.md](./部署运维文档.md)** 的部署架构

### 🎨 产品经理
1. 详读 **[产品需求说明书.md](./产品需求说明书.md)** 了解产品定位
2. 查看 **[功能规格说明书.md](./功能规格说明书.md)** 了解功能设计
3. 参考 **[项目总体说明文档.md](./项目总体说明文档.md)** 的商业价值分析

### 🔧 运维工程师
1. 重点阅读 **[部署运维文档.md](./部署运维文档.md)** 
2. 了解 **[技术架构文档.md](./技术架构文档.md)** 中的基础设施设计
3. 参考监控和告警配置

### 🏢 项目经理
1. 从 **[项目总体说明文档.md](./项目总体说明文档.md)** 开始全面了解项目
2. 阅读各个文档的项目管理相关章节
3. 关注里程碑和风险管理内容

## 🔍 文档特色

### 📋 全面性
- 覆盖产品、技术、运维等各个方面
- 从需求到实现的完整链路文档
- 包含最佳实践和经验总结

### 🎯 实用性
- 提供可执行的操作指南
- 包含详细的代码示例
- 涵盖常见问题和解决方案

### 📈 系统性
- 文档间相互关联，形成体系
- 层次分明，便于查找
- 持续更新，与项目同步

### 🔧 技术深度
- 深入分析技术选型原因
- 详细说明架构设计思路
- 提供性能优化建议

## 📊 项目数据

### 代码规模
- **前端代码**: Vue 3项目，包含移动端APP和管理后台
- **后端代码**: Spring Boot项目，约50+个接口
- **数据库**: 32个表，596个字段
- **文档**: 8个核心文档，约10万字

### 功能模块
- ✅ **已实现**: 用户认证、商品管理、团购拼团、订单支付、钱包系统
- 🔄 **开发中**: 优惠券系统、评价系统、客服系统
- 📋 **规划中**: 直播带货、智能推荐、数据分析

### 技术指标
- **响应时间**: API响应 < 1秒，页面加载 < 3秒
- **并发能力**: 支持1000人同时在线
- **可用性**: 目标99.9%服务可用性
- **安全性**: 多层安全防护，通过安全审计

## 🤝 贡献指南

### 文档更新
1. 文档与代码同步更新
2. 重大变更需要更新相关文档
3. 定期review文档的准确性
4. 欢迎提出文档改进建议

### 反馈渠道
- 技术问题：提交Issue到项目仓库
- 文档建议：联系技术负责人
- 产品建议：联系产品经理

## 📞 联系方式

- **技术团队**: 负责技术架构和开发
- **产品团队**: 负责产品设计和运营
- **运维团队**: 负责系统部署和维护

## 📄 许可证

本项目文档采用 [MIT License](LICENSE) 许可证。

---

**最后更新**: 2025年1月  
**文档版本**: V1.0  
**维护团队**: 技术产品团队

希望这些文档能帮助您更好地理解和使用团购网项目！🚀