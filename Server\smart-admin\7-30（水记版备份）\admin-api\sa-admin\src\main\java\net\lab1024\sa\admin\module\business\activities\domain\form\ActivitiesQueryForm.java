package net.lab1024.sa.admin.module.business.activities.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 抽奖活动表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class ActivitiesQueryForm extends PageParam {

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "活动开始时间")
    private LocalDate startTimeBegin;

    @Schema(description = "活动开始时间")
    private LocalDate startTimeEnd;

    @Schema(description = "活动类型")
    private String type;

    @Schema(description = "活动类型")
    private Integer deletedFlag;

}
