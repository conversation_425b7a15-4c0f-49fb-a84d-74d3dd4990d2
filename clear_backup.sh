#!/bin/bash

# 清晰提示的备份脚本
# 使用方法: ./clear_backup.sh [数据库名]

SERVER_IP="*************"
DATABASE_NAME=${1:-"tgw_pp"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_${TIMESTAMP}.sql"
LOCAL_BACKUP_DIR="DataBackup"

echo "=== MySQL 数据库备份脚本 ==="
echo "数据库: $DATABASE_NAME"
echo "服务器: $SERVER_IP"
echo "备份文件: $BACKUP_FILENAME"
echo ""

# 创建本地备份目录
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "创建备份目录: $LOCAL_BACKUP_DIR"
    mkdir -p "$LOCAL_BACKUP_DIR"
fi

# 步骤1: 检查服务器连接
echo "步骤1: 检查服务器连接..."
echo "提示: 请输入服务器 root 用户的 SSH 密码"
ssh -o ConnectTimeout=10 root@$SERVER_IP "echo '服务器连接成功'" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到服务器 $SERVER_IP"
    echo "请检查:"
    echo "1. 服务器IP是否正确"
    echo "2. SSH密码是否正确"
    echo "3. 网络连接是否正常"
    exit 1
fi

# 步骤2: 检查数据库是否存在
echo "步骤2: 检查数据库..."
echo "提示: 请输入 MySQL root 用户的数据库密码"
DB_CHECK=$(ssh root@$SERVER_IP "mysql -u root -p -e 'SHOW DATABASES LIKE \"$DATABASE_NAME\";' 2>/dev/null | grep '$DATABASE_NAME'")
if [ -z "$DB_CHECK" ]; then
    echo "错误: 数据库 '$DATABASE_NAME' 不存在"
    echo "提示: 请输入 MySQL root 用户的数据库密码查看可用数据库"
    ssh root@$SERVER_IP "mysql -u root -p -e 'SHOW DATABASES;' 2>/dev/null"
    exit 1
fi
echo "数据库 '$DATABASE_NAME' 存在"

# 步骤3: 执行备份
echo "步骤3: 执行数据库备份..."
echo "提示: 接下来需要输入 MySQL root 用户的数据库密码来执行备份"

# 使用更详细的错误检测
ssh root@$SERVER_IP "
set -e
echo '开始备份数据库...'
echo '提示: 请输入 MySQL root 用户的数据库密码'
mysqldump -u root -p --single-transaction --routines --triggers $DATABASE_NAME > /root/$BACKUP_FILENAME 2>/tmp/backup_error.log
if [ \$? -eq 0 ]; then
    FILE_SIZE=\$(stat -c%s /root/$BACKUP_FILENAME)
    if [ \$FILE_SIZE -gt 0 ]; then
        echo \"备份成功，文件大小: \$(ls -lh /root/$BACKUP_FILENAME | awk '{print \$5}')\"
        echo \"备份文件路径: /root/$BACKUP_FILENAME\"
    else
        echo \"错误: 备份文件为空\"
        echo \"可能的原因:\"
        echo \"1. MySQL 密码错误\"
        echo \"2. 数据库权限不足\"
        echo \"3. 数据库为空\"
        cat /tmp/backup_error.log
        exit 1
    fi
else
    echo \"错误: mysqldump 执行失败\"
    echo \"错误详情:\"
    cat /tmp/backup_error.log
    exit 1
fi
"

if [ $? -ne 0 ]; then
    echo "备份失败，请检查上面的错误信息"
    echo ""
    echo "常见问题解决方案:"
    echo "1. 如果提示密码错误，请确认 MySQL root 密码"
    echo "2. 如果提示权限不足，请检查 MySQL 用户权限"
    echo "3. 如果数据库为空，备份文件可能很小但不为0"
    exit 1
fi

# 步骤4: 验证远程文件
echo "步骤4: 验证远程备份文件..."
echo "提示: 请输入服务器 root 用户的 SSH 密码"
REMOTE_FILE_INFO=$(ssh root@$SERVER_IP "ls -lh /root/$BACKUP_FILENAME 2>/dev/null")
if [ -z "$REMOTE_FILE_INFO" ]; then
    echo "错误: 远程备份文件不存在"
    exit 1
fi
echo "远程文件信息: $REMOTE_FILE_INFO"

# 步骤5: 下载文件
echo "步骤5: 下载备份文件..."
echo "提示: 请输入服务器 root 用户的 SSH 密码"
scp root@$SERVER_IP:/root/$BACKUP_FILENAME "$LOCAL_BACKUP_DIR/"

if [ $? -ne 0 ]; then
    echo "错误: 文件下载失败"
    echo "可能的原因:"
    echo "1. SSH 密码错误"
    echo "2. 网络连接问题"
    echo "3. 本地目录权限问题"
    exit 1
fi

# 步骤6: 验证本地文件
LOCAL_FILE_PATH="$LOCAL_BACKUP_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    LOCAL_FILE_SIZE=$(stat -c%s "$LOCAL_FILE_PATH" 2>/dev/null || stat -f%z "$LOCAL_FILE_PATH" 2>/dev/null)
    if [ "$LOCAL_FILE_SIZE" -gt 0 ]; then
        echo "本地文件验证成功:"
        ls -lh "$LOCAL_FILE_PATH"
        
        # 显示文件前几行以确认内容
        echo ""
        echo "备份文件内容预览:"
        head -5 "$LOCAL_FILE_PATH"
        echo ""
        echo "备份文件看起来正常！"
    else
        echo "错误: 本地文件为空 (大小: $LOCAL_FILE_SIZE 字节)"
        echo "这通常意味着备份过程中出现了问题"
        exit 1
    fi
else
    echo "错误: 本地文件不存在"
    exit 1
fi

# 步骤7: 清理远程文件
echo ""
read -p "是否删除服务器上的备份文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "提示: 请输入服务器 root 用户的 SSH 密码"
    ssh root@$SERVER_IP "rm -f /root/$BACKUP_FILENAME"
    echo "远程备份文件已删除"
else
    echo "远程备份文件保留在: /root/$BACKUP_FILENAME"
fi

echo ""
echo "=== 备份完成 ==="
echo "本地文件: $LOCAL_FILE_PATH"
echo "文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"

# 显示最近的备份文件
echo ""
echo "=== 最近的备份文件 ==="
ls -lt "$LOCAL_BACKUP_DIR"/*.sql 2>/dev/null | head -5

echo ""
echo "备份成功完成！"
echo ""
echo "密码说明:"
echo "- SSH 密码: 服务器 root 用户的登录密码"
echo "- MySQL 密码: 数据库 root 用户的密码"
echo "如果不确定密码，请联系服务器管理员"
