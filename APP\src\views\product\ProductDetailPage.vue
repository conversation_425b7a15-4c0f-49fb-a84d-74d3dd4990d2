<template>
  <div class="bg-gray-100">
    <!-- 顶部导航 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <iconify-icon 
          icon="material-symbols:arrow-back" 
          class="text-2xl text-gray-700 cursor-pointer" 
          @click="goBack"
        ></iconify-icon>
        <iconify-icon 
          icon="material-symbols:share" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="shareProduct"
          title="分享商品"
        ></iconify-icon>
      </div>
      <h1 class="text-lg font-semibold text-gray-800">商品详情</h1>
      <div class="flex space-x-4">
        <iconify-icon 
          icon="material-symbols:favorite-border" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="toggleFavorite"
        ></iconify-icon>
        <iconify-icon 
          icon="material-symbols:home" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="goToHome"
        ></iconify-icon>
      </div>
    </div>

    <div class="pb-24">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">正在加载商品详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <iconify-icon icon="material-symbols:error-outline" class="text-red-500 text-5xl mb-4"></iconify-icon>
        <p class="text-gray-500 text-sm mb-4">{{ error }}</p>
        <button 
          @click="loadProductDetail()" 
          class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
        >
          重新加载
        </button>
      </div>

      <!-- 商品详情内容 -->
      <div v-else-if="product">
        <!-- 商品轮播图 -->
        <div class="bg-white">
          <div class="w-full relative">
            <img 
              :src="getProductMainImage()" 
              :alt="getProductName()"
              class="w-full h-auto object-contain"
              style="min-height: 250px; max-height: 500px;"
              @error="handleImageError"
              @load="handleImageLoad"
            />
            <div class="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              1/5
            </div>
          </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="bg-white px-4 py-4 mt-2">
          <div class="flex items-center mb-3">
            <!-- 新用户专享标识 -->
            <span 
              v-if="isNewUserProduct" 
              class="bg-red-500 text-white text-xs px-2 py-1 rounded mr-2 animate-pulse"
            >
              新手专享
            </span>
            <!-- 普通拼团标识 -->
            <span 
              v-else
              class="bg-red-500 text-white text-xs px-2 py-1 rounded mr-2"
            >
              {{ getProductGroupTypeText() }}
            </span>
            <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded">包邮</span>
            <!-- 免费体验标识 -->
            <span 
              v-if="canJoinFreeGroup" 
              class="bg-green-500 text-white text-xs px-2 py-1 rounded ml-2"
            >
              免费体验
            </span>
          </div>
          <h1 class="text-sm font-bold text-gray-800 mb-3">
            {{ getProductName() }}
          </h1>
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <!-- 新用户免费价格显示 -->
              <span 
                v-if="canJoinFreeGroup" 
                class="text-green-600 font-bold text-lg"
              >
                免费体验
              </span>
              <!-- 普通价格显示 -->
              <span 
                v-else
                class="text-red-500 font-bold text-sm"
              >
                {{ formatPrice(getGroupPrice()) }}
              </span>
              <!-- 原价（划掉） -->
              <span 
                v-if="getOriginalPrice() && (canJoinFreeGroup || !isNewUserProduct)" 
                class="text-gray-400 text-xs line-through ml-2"
              >
                {{ formatPrice(getOriginalPrice()) }}
              </span>
            </div>
            <div class="text-right" v-if="!canJoinFreeGroup">
              <div class="text-xs text-gray-500" style="font-size: 10px;">单独购买</div>
              <div class="text-gray-600 font-medium text-xs">
                {{ formatPrice(getSinglePrice()) }}
              </div>
            </div>
            <!-- 新用户引导文案 -->
            <div v-if="canJoinFreeGroup" class="text-right text-xs text-green-600">
              <div>新用户专享</div>
              <div>完全免费体验</div>
            </div>
          </div>
          <div class="flex items-center justify-between text-xs text-gray-600">
            <span>已售{{ getSalesCount() }}+件</span>
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:star" class="text-yellow-400 mr-1"></iconify-icon>
              <span>{{ getRating() }}分 ({{ getReviewCount() }}评价)</span>
            </div>
          </div>
        </div>

        <!-- 新用户专享说明 -->
        <div v-if="isNewUserProduct" class="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl px-4 py-4 mt-2">
          <div class="flex items-center mb-2">
            <iconify-icon icon="material-symbols:celebration" class="text-orange-500 text-xl mr-2"></iconify-icon>
            <h3 class="font-bold text-orange-700 text-sm">新用户专享说明</h3>
          </div>
          <div class="text-sm text-gray-700 space-y-1">
            <p v-if="canJoinFreeGroup">
              • 您还有 <span class="font-bold text-orange-600">{{ authStore.noviceCount }}</span> 次免费拼团机会
            </p>
            <p>• 新用户可完全免费体验拼团乐趣</p>
            <p>• 本次拼团无需支付任何费用</p>
            <p v-if="!isUserLoggedIn" class="text-orange-600">
              • 请先登录以享受新用户专享优惠
            </p>
          </div>
        </div>

        <!-- 商品详情 -->
        <div class="bg-white px-4 py-4 mt-2">
          <h3 class="font-bold text-gray-800 mb-3 text-sm">商品详情</h3>
          <div class="space-y-4">
            <!-- 商品描述文字 -->
            <div v-if="getProductDescription()" class="text-gray-700 text-sm leading-relaxed">
              <p class="whitespace-pre-wrap">{{ getProductDescription() }}</p>
            </div>
            
            <!-- 详情图片 -->
            <div 
              v-for="(image, index) in getProductDetailImages()" 
              :key="index"
              class="w-full bg-gray-100 rounded overflow-hidden"
            >
              <img 
                :src="image" 
                :alt="`商品详情图片${index + 1}`"
                class="w-full h-auto object-contain"
                style="min-height: 200px; max-height: 600px;"
                @error="handleImageError"
                @load="handleImageLoad"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t px-4 py-3">
      <div class="w-full">
        <!-- 新用户免费拼团按钮 -->
        <button 
          v-if="canJoinFreeGroup"
          @click="startGroupBuying()" 
          class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-full font-medium text-sm hover:from-green-600 hover:to-green-700 transition-all transform hover:scale-[1.02] flex items-center justify-center"
        >
          <iconify-icon icon="material-symbols:rocket-launch" class="mr-2"></iconify-icon>
          免费参与拼团
        </button>
        <!-- 普通拼团按钮 -->
        <button 
          v-else
          @click="startGroupBuying()" 
          class="w-full bg-red-500 text-white py-3 rounded-full font-medium text-sm hover:bg-red-600 transition-colors"
        >
          发起拼团 {{ formatPrice(getGroupPrice()) }}
        </button>
      </div>
    </div>

  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError } from '@/utils/message.js'
import { getImageUrl } from '@/config/image'
import { getGroupSize, getGroupTypeText, formatPrice } from '@/utils/format'

export default {
  name: 'ProductDetailPage',
  components: {
    
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 响应式数据
    const loading = ref(true)
    const error = ref(null)
    const product = ref(null)
    
    // 新用户相关计算属性
    const isNewUserProduct = computed(() => {
      return route.query.goodsType === 'newUser' || 
             route.query.isNewUserProduct === 'true' ||
             product.value?.goodsType === 'newUser'
    })
    
    const canJoinFreeGroup = computed(() => {
      return isNewUserProduct.value && authStore.isNewUser && authStore.noviceCount > 0
    })
    
    const isUserLoggedIn = computed(() => {
      return authStore.isLoggedIn
    })
    
    // API服务
    let productApi = null
    
    // 初始化API服务
    const initApiService = async () => {
      try {
        productApi = new StandardApiAdapter()
        console.log('✅ 商品详情API服务初始化成功')
      } catch (err) {
        console.error('❌ 商品详情API服务初始化失败:', err)
        error.value = 'API服务初始化失败'
      }
    }
    
    // 加载商品详情
    const loadProductDetail = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const productId = route.params.id
        if (!productId) {
          throw new Error('商品ID不能为空')
        }
        
        if (!productApi) {
          await initApiService()
        }
        
        console.log('🔄 加载商品详情:', productId)
        
        // 调用商品详情API
        const response = await productApi.getProductDetail(productId)
        
        console.log('📦 商品详情API响应:', response)
        
        if (response.code === 0 || response.code === 200) {
          product.value = response.data || {}
          console.log('✅ 商品详情加载成功:', product.value)
          
          // 添加详细的数据结构调试信息
          console.log('🔍 商品数据结构分析:')
          console.log('  - 商品名称 (goodsName):', product.value.goodsName)
          console.log('  - 商品类型 (goodsType):', product.value.goodsType)
          console.log('  - 价格 (price):', product.value.price)
          console.log('  - 商品描述 (description):', product.value.description)
          console.log('  - 活动信息 (activity):', product.value.activity)
          console.log('  - 图片信息 (images):', product.value.images)
          console.log('  - 详情图片 (detailImages):', product.value.detailImages)
          console.log('  - SKU信息 (skus):', product.value.skus)
          console.log('  - 所有字段:', Object.keys(product.value))
          
          // 详细的SKU数据分析
          if (product.value.skus && product.value.skus.length > 0) {
            console.log('💰 SKU数据分析:')
            product.value.skus.forEach((sku, index) => {
              console.log(`  - SKU${index + 1}:`, {
                id: sku.id,
                name: sku.name || sku.title,
                price: sku.price,
                originalPrice: sku.originalPrice || sku.original_price,
                singlePrice: sku.singlePrice || sku.single_price,
                stock: sku.stock || sku.inventory,
                specs: sku.specs || sku.specifications,
                allFields: Object.keys(sku)
              })
            })
          } else {
            console.log('💰 SKU数据: 空数组或不存在')
          }
          
          // 详细的图片数据分析
          if (product.value.images && product.value.images.length > 0) {
            console.log('📸 主图数据:')
            product.value.images.forEach((img, index) => {
              console.log(`  - 图片${index + 1}:`, img.url)
            })
          }
          
          if (product.value.detailImages && product.value.detailImages.length > 0) {
            console.log('📋 详情图片数据:')
            product.value.detailImages.forEach((img, index) => {
              console.log(`  - 详情图片${index + 1}:`, typeof img === 'string' ? img : img.url)
            })
          } else {
            console.log('📋 详情图片数据: 空数组或不存在')
          }
        } else {
          throw new Error(response.message || response.msg || '商品详情加载失败')
        }
      } catch (err) {
        console.error('❌ 商品详情加载失败:', err)
        error.value = err.message || '商品详情加载失败，请重试'
      } finally {
        loading.value = false
      }
    }
    
    // 工具函数
    // 价格格式化现在使用统一的越南盾格式化函数
    
    // 使用统一的图片URL处理函数

    const getProductMainImage = () => {
      if (!product.value || !product.value.images || product.value.images.length === 0) {
        return ''
      }
      
      return getImageUrl(product.value.images[0].url)
    }
    
    const getProductImages = () => {
      if (!product.value) return []
      
      // 只使用API返回的真实图片数据
      if (product.value.images && product.value.images.length > 0) {
        return product.value.images.map(img => getImageUrl(img.url))
      }
      
      return []
    }
    
    const getProductDetailImages = () => {
      console.log('🖼️ getProductDetailImages 被调用')
      console.log('  - product.value:', product.value)
      console.log('  - product.value.detailImages:', product.value?.detailImages)
      
      if (!product.value || !product.value.detailImages || product.value.detailImages.length === 0) {
        console.log('  - 返回空数组，原因：', {
          hasProduct: !!product.value,
          hasDetailImages: !!(product.value?.detailImages),
          detailImagesLength: product.value?.detailImages?.length || 0
        })
        return []
      }
      
      const result = product.value.detailImages.map(img => {
        const imageUrl = typeof img === 'string' ? img : img.url
        const fullUrl = getImageUrl(imageUrl)
        console.log('  - 处理图片:', { original: img, imageUrl, fullUrl })
        return fullUrl
      })
      
      console.log('  - 最终返回的详情图片数组:', result)
      return result
    }
    
    const getProductName = () => {
      if (!product.value) return ''
      
      return product.value.goodsName || 
             product.value.title || 
             product.value.name || 
             ''
    }
    
    // 拼团相关函数现在使用统一的工具函数
    const getProductGroupTypeText = () => {
      if (!product.value) return ''
      return getGroupTypeText(product.value)
    }
    
    const getProductGroupSize = () => {
      if (!product.value) return 0
      return getGroupSize(product.value)
    }
    
    // 获取第一个SKU，用于价格显示
    const getFirstSku = () => {
      if (!product.value || !product.value.skus || product.value.skus.length === 0) {
        return null
      }
      return product.value.skus[0]
    }
    
    const getGroupPrice = () => {
      if (!product.value) return 0
      
      // 优先使用SKU中的价格
      const firstSku = getFirstSku()
      if (firstSku && firstSku.price) {
        return parseFloat(firstSku.price)
      }
      
      // 如果没有SKU数据，使用商品级别的价格
      return parseFloat(product.value.price || 0)
    }
    
    const getOriginalPrice = () => {
      if (!product.value) return 0
      
      // 优先使用SKU中的原价
      const firstSku = getFirstSku()
      if (firstSku && (firstSku.originalPrice || firstSku.original_price)) {
        return parseFloat(firstSku.originalPrice || firstSku.original_price)
      }
      
      // 如果没有SKU数据，使用商品级别的原价
      return parseFloat(product.value.originalPrice || product.value.original_price || product.value.marketPrice || 0)
    }
    
    const getSinglePrice = () => {
      if (!product.value) return 0
      
      // 优先使用SKU中的直购价格
      const firstSku = getFirstSku()
      if (firstSku && (firstSku.alonePrice || firstSku.alone_price)) {
        return parseFloat(firstSku.alonePrice || firstSku.alone_price)
      }
      
      // 如果没有SKU数据，使用商品级别的直购价格 - 与商品卡保持一致
      return parseFloat(product.value.alonePrice || 0)
    }
    
    // 检查是否可以直接购买 - 与商品卡保持一致
    const canDirectBuy = () => {
      if (!product.value) return false
      return product.value.aloneFlag === 1 || product.value.aloneFlag === true
    }
    
    const getSalesCount = () => {
      if (!product.value) return 0
      
      const count = product.value.salesCount || product.value.sales || product.value.participants || 0
      return count >= 1000 ? Math.floor(count / 1000) + 'k' : count
    }
    
    const getRating = () => {
      if (!product.value) return ''
      
      return product.value.rating || ''
    }
    
    const getReviewCount = () => {
      if (!product.value) return ''
      
      return product.value.reviewCount || ''
    }
    
    const calculateSavings = () => {
      const original = getOriginalPrice()
      const group = getGroupPrice()
      
      return Math.max(0, original - group)
    }
    
    // 事件处理函数
    const goBack = () => {
      router.back()
    }
    
    const goToHome = () => {
      router.push('/')
    }
    
    const shareProduct = () => {
      if (navigator.share) {
        navigator.share({
          title: getProductName(),
          text: '快来看看这个商品！',
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href)
        showSuccess('链接已复制到剪贴板')
      }
    }
    
    const toggleFavorite = async () => {
      try {
        showSuccess('收藏功能待实现')
      } catch (err) {
        showError('收藏操作失败')
      }
    }
    
    
    const startGroupBuying = () => {
      // 跳转到拼团确认页面
      router.push(`/group/confirm/${product.value?.id || route.params.id}`)
    }
    
    // 图片加载处理函数
    const handleImageError = (event) => {
      console.warn('⚠️ 图片加载失败:', event.target.src)
      // 可以设置默认图片或隐藏图片
      event.target.style.display = 'none'
    }
    
    const handleImageLoad = (event) => {
      console.log('✅ 图片加载成功:', event.target.src)
    }
    
    const getProductDescription = () => {
      if (!product.value) return ''
      
      // 只使用API返回的真实描述数据
      return product.value.description || ''
    }
    
    // 生命周期
    onMounted(async () => {
      console.log('🎯 ProductDetailPage mounted')
      await loadProductDetail()
    })
    
    return {
      // 响应式数据
      loading,
      error,
      product,
      
      // 新用户相关
      isNewUserProduct,
      canJoinFreeGroup,
      isUserLoggedIn,
      authStore,
      
      // 方法
      loadProductDetail,
      formatPrice,
      getImageUrl,
      getProductMainImage,
      getProductImages,
      getProductDetailImages,
      getProductName,
      getProductGroupTypeText,
      getProductGroupSize,
      getGroupPrice,
      getOriginalPrice,
      getSinglePrice,
      canDirectBuy,
      getSalesCount,
      getRating,
      getReviewCount,
      calculateSavings,
      goBack,
      shareProduct,
      toggleFavorite,
      startGroupBuying,
      handleImageError,
      handleImageLoad,
      getProductDescription
    }
  }
}
</script>

<style scoped>
/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 确保图片加载失败时的样式 */
.bg-cover {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
</style> 