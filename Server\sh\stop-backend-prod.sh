#!/bin/bash

echo "🛑 停止生产环境后端服务..."

PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs/prod"

# 停止后端生产服务
if [ -f "$LOG_DIR/backend-prod.pid" ]; then
    BACKEND_PID=$(cat "$LOG_DIR/backend-prod.pid")
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端生产服务已停止 (PID: $BACKEND_PID)"
        
        # 等待进程停止
        sleep 3
        
        # 如果进程仍然存在，强制杀死
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill -9 $BACKEND_PID
            echo "⚡ 强制停止后端服务"
        fi
    fi
    rm -f "$LOG_DIR/backend-prod.pid"
fi

# 停止通过jar包启动的后端服务（兼容原有方式）
PID=$(ps -ef | grep tgw-pp.jar | grep -v grep | awk '{ print $2 }')
if [ ! -z "$PID" ]; then
    echo "🛑 停止JAR包启动的后端服务 (PID: $PID)"
    kill -9 $PID
    echo "✅ JAR包后端服务已停止"
fi

echo "🎉 生产环境后端服务已停止！"