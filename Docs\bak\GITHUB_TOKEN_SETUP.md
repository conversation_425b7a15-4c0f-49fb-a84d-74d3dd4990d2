# GitHub Token设置指南

## 🎯 目标

为了能够推送GitHub Actions workflow文件，需要创建一个具有 `workflow` 权限的GitHub Personal Access Token。

## 📋 步骤详解

### 1. 创建GitHub Personal Access Token

#### 访问Token设置页面
1. 打开 https://github.com/settings/tokens
2. 或者通过：头像 → Settings → Developer settings → Personal access tokens → Tokens (classic)

#### 创建新Token
1. 点击 "Generate new token" → "Generate new token (classic)"
2. 填写基本信息：
   - **Note**: `PingTuan项目开发 - 完整权限`
   - **Expiration**: 选择90天或No expiration

#### 选择权限范围（必须全选）
```
✅ repo (完整仓库权限)
   ✅ repo:status
   ✅ repo_deployment  
   ✅ public_repo
   ✅ repo:invite
   ✅ security_events

✅ workflow (GitHub Actions权限) ← 关键权限

✅ write:packages (包写入权限)
✅ read:packages (包读取权限)
✅ delete:packages (包删除权限)

✅ admin:repo_hook (webhook权限)
✅ write:discussion (讨论写入权限)
✅ read:discussion (讨论读取权限)
```

#### 生成Token
1. 点击 "Generate token"
2. **立即复制Token** (格式: `ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`)
3. 保存到安全的地方

### 2. 配置本地Git

#### 方法1: 使用配置脚本（推荐）
```bash
# 使用你的新Token替换 YOUR_TOKEN
./scripts/setup-github-token.sh YOUR_TOKEN
```

#### 方法2: 手动配置
```bash
# 设置远程URL
git remote set-url origin https://mengdong88:<EMAIL>/mengdong88/PingTuan.git

# 配置凭据存储
git config --global credential.helper store

# 验证配置
git remote -v
```

### 3. 验证Token权限

```bash
# 检查Token权限
node scripts/check-token-permissions.js YOUR_TOKEN

# 预期输出包含：
# ✅ repo
# ✅ workflow
# ✅ write:packages
# ✅ 仓库访问权限
# ✅ Workflow权限
```

### 4. 推送workflow文件

```bash
# 添加GitHub Actions文件
git add .github/workflows/auto-pr-summary.yml

# 提交
git commit -m "feat: 添加GitHub Actions自动化workflow"

# 推送（现在应该成功）
git push origin master
```

### 5. 验证GitHub Actions

1. 访问 https://github.com/mengdong88/PingTuan/actions
2. 查看是否有workflow文件
3. 创建测试PR验证自动化功能

## 🔧 故障排除

### 常见错误及解决方案

#### 1. "refusing to allow a Personal Access Token to create or update workflow"
```bash
# 原因：Token缺少workflow权限
# 解决：重新创建Token，确保勾选workflow权限
```

#### 2. "Authentication failed"
```bash
# 原因：Token过期或无效
# 解决：检查Token是否正确，是否过期
node scripts/check-token-permissions.js YOUR_TOKEN
```

#### 3. "remote: Permission denied"
```bash
# 原因：Token权限不足
# 解决：确保Token有repo权限
```

### 调试命令

```bash
# 查看当前远程URL
git remote -v

# 查看Git配置
git config --global --list

# 测试GitHub API连接
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user

# 测试仓库访问
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/repos/mengdong88/PingTuan
```

## 🚀 完成后测试

### 1. 创建测试分支
```bash
git checkout -b test/token-verification
echo "# Token Test" > test-file.md
git add test-file.md
git commit -m "test: 验证Token权限"
```

### 2. 推送并创建PR
```bash
# 推送分支
git push origin test/token-verification

# 创建PR（使用自动化脚本）
./scripts/create-pr.sh
```

### 3. 验证自动化功能
1. 查看PR是否自动生成摘要
2. 检查GitHub Actions是否运行
3. 验证PR描述是否自动更新

## 🔐 安全建议

1. **定期更新Token**: 建议每90天更新一次
2. **最小权限原则**: 只授予必需的权限
3. **安全存储**: 不要在代码中硬编码Token
4. **撤销不用的Token**: 及时撤销不需要的Token
5. **监控使用**: 定期检查Token使用情况

## 📞 支持

如果遇到问题：
1. 运行权限检查脚本：`node scripts/check-token-permissions.js YOUR_TOKEN`
2. 查看Token设置页面：https://github.com/settings/tokens
3. 检查GitHub Actions日志：https://github.com/mengdong88/PingTuan/actions

---

**重要提醒**: 完成设置后，请删除或安全存储Token，不要在终端历史或代码中留下Token信息。