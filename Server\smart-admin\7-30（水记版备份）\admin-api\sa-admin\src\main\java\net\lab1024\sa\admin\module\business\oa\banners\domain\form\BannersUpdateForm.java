package net.lab1024.sa.admin.module.business.oa.banners.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 横幅管理 更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Data
public class BannersUpdateForm extends BannersAddForm {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "ID 不能为空")
    private Integer id;

}