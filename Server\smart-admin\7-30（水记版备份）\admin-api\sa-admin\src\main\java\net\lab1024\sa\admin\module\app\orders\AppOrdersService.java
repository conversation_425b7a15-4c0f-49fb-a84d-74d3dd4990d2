package net.lab1024.sa.admin.module.app.orders;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.constant.WalletConst;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesEntity;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsDao;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsSkusDao;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsEntity;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.entity.InvitationRecordsEntity;
import net.lab1024.sa.admin.module.business.invitationRecords.service.InvitationRecordsService;
import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersEntity;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersQueryForm;
import net.lab1024.sa.admin.module.business.orders.domain.vo.OrdersVO;
import net.lab1024.sa.admin.module.business.orders.service.OrdersService;
import net.lab1024.sa.admin.module.business.wallets.domain.vo.WalletsVO;
import net.lab1024.sa.admin.module.business.wallets.service.WalletsService;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.admin.module.system.login.domain.RequestEmployee;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartEnumUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.config.ConfigKeyEnum;
import net.lab1024.sa.base.module.support.config.ConfigService;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Objects;

@Slf4j
@Service
public class AppOrdersService {

    @Resource
    private OrdersService ordersService;

    @Resource
    private GoodsSkusDao goodsSkusDao;

    @Resource
    private GoodsDao goodsDao;

    @Resource
    private WalletsService walletsService;

    @Resource
    private ActivitiesService activitiesService;

    @Resource
    private InvitationRecordsService invitationRecordsService;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private ConfigService configService;

    @Resource
    private EmployeeService employeeService;

    /**
     * 订单详情
     */
    public ResponseDTO<Object> orderDetail(Long orderId) {
        QueryWrapper<OrdersEntity> Q1 = new QueryWrapper<>();
        Q1.eq("id", orderId)
                .eq("user_id", SmartRequestUtil.getRequestUserId())
                .eq("deleted_flag",0);
        OrdersVO ov = ordersService.selectOne(Q1);
        if(ov == null) { return ResponseDTO.userErrorParam("not data"); }
        return ResponseDTO.ok(ov);
    }

    /**
     * 订单列表
     */
    public PageResult<OrdersVO> orders(String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }
        OrdersQueryForm ordersQueryForm = new OrdersQueryForm();
        ordersQueryForm.setSortItemList(new ArrayList<>());
        ordersQueryForm.setDeletedFlag(0);
        ordersQueryForm.setUserId(SmartRequestUtil.getRequestUserId());
        ordersQueryForm.setPageNum(pageNum);
        ordersQueryForm.setPageSize(pageSize);
        if(Objects.equals(sort, "id_asc")){
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("id");
            si.setIsAsc(true);
        } else if(Objects.equals(sort, "id_desc")){
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("id");
            si.setIsAsc(false);
        }
        return ordersService.queryPage(ordersQueryForm);
    }

    /**
     * 下单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> placeOrder(AppOrderParamForm appOrderParamForm) {
        GoodsSkusEntity sku = goodsSkusDao.selectById(appOrderParamForm.getSkuId());
        if(sku == null){
            return ResponseDTO.userErrorParam("未到到商品Sku");
        }

        //强制购买数量是1
        appOrderParamForm.setQuantity(1);

        RequestEmployee user = AdminRequestUtil.getRequestUser();

        Long userId = user.getUserId();

        WalletsVO userWallet = walletsService.queryById(userId);

        GoodsEntity goods = goodsDao.selectById(sku.getGoodsId());

        Integer paymentMethod = appOrderParamForm.getUseCurrency();
        LocalDateTime now = LocalDateTime.now();
        OrdersEntity ordersEntity = new OrdersEntity();

        int orderAloneFlag = (appOrderParamForm.getAloneFlag() != null)? appOrderParamForm.getAloneFlag() : 0;

        BigDecimal amount = sku.getPrice().multiply(BigDecimal.valueOf(appOrderParamForm.getQuantity()));
        if(orderAloneFlag == 1){
            amount = sku.getAlonePrice().multiply(BigDecimal.valueOf(appOrderParamForm.getQuantity()));
        }

        if(paymentMethod == null) {
            if (Objects.equals(goods.getGoodsCurrency(), "cash")) {
                paymentMethod = WalletConst.PayMode.BALANCE;
            } else if (Objects.equals(goods.getGoodsCurrency(), "points")) {
                paymentMethod = WalletConst.PayMode.POINTS;
            }
        }

        if(paymentMethod != WalletConst.PayMode.POINTS && paymentMethod != WalletConst.PayMode.BALANCE){
            return ResponseDTO.userErrorParam("payment method not support");
        }

        if(paymentMethod == WalletConst.PayMode.BALANCE){
            if (amount.compareTo(userWallet.getBalance()) > 0) {
                return ResponseDTO.userErrorParam("balance not enough");
            }
        }else if(paymentMethod == WalletConst.PayMode.POINTS){
            if (amount.compareTo(BigDecimal.valueOf(userWallet.getPoints())) > 0) {
                return ResponseDTO.userErrorParam("points not enough");
            }
        }


        ActivitiesEntity activitiesEntity =  activitiesService.selectById(goods.getActivityId());
        String activitiesType = activitiesEntity.getType();

        SerialNumberIdEnum serialNumberIdEnum = SmartEnumUtil.getEnumByValue(3, SerialNumberIdEnum.class);
        String orderSn = serialNumberService.generate(serialNumberIdEnum, 1).get(0);

        ordersEntity.setUserId(userId);
        ordersEntity.setOrderSn(orderSn);
        ordersEntity.setActivityId(goods.getActivityId());

        Integer drawResult = 0;
        //判断是否单独购买
        if(orderAloneFlag == 1){
            drawResult = 1;
        }else {
            log.info("activitiesType:{}", activitiesType);
            if (!Objects.equals(goods.getGoodsType(), "newUser")) {
                //按中奖几率开奖
                drawResult = ordersService.getWinPercentage(activitiesType);
                log.info("中奖:{}", drawResult);
            } else {
                int noviceGroupLimit = configService.getConfigValueInteger(ConfigKeyEnum.NOVICE_GROUP_LIMIT);
                if(user.getNoviceCount() >= noviceGroupLimit){
                    return ResponseDTO.userErrorParam("you can`t used novice group");
                }
                //试新次数+1(为前端判断)
                employeeService.incrementalNoviceCount(userId);
            }
        }
        ordersEntity.setAloneFlag(orderAloneFlag == 1);
        ordersEntity.setDrawResult(drawResult);
        ordersEntity.setDrawTime(now);

//        String status = "";
//        if(winPercentage == 1) {
//            status = "WON";
//        } else {
//            status = "LOST";
//        }
        ordersEntity.setStatus("COMPLETED");

        ordersEntity.setGoodsId(sku.getGoodsId());
        ordersEntity.setSkuId(sku.getId());

        if (paymentMethod == WalletConst.PayMode.BALANCE) {
            ordersEntity.setAmountPaid(amount);
        } else if(paymentMethod == WalletConst.PayMode.POINTS) {
            ordersEntity.setPointsPaid(amount.intValue());
        } else {
            if(Objects.equals(goods.getGoodsCurrency(), "cash")){
               ordersEntity.setAmountPaid(amount);
            } else if(Objects.equals(goods.getGoodsCurrency(), "points")) {
               ordersEntity.setPointsPaid(amount.intValue());
            }
        }
        ordersEntity.setPayableAmount(amount);
        ordersEntity.setShippingAddressId(appOrderParamForm.getShippingAddressId());
        ordersEntity.setPaymentMethod(String.valueOf(paymentMethod));

        ordersEntity.setPaymentTime(now);
        ordersEntity.setCompleteTime(now);
        //标记已结算
        ordersEntity.setSettleFlag(1);

        ordersService.insert(ordersEntity);

        Long orderId = ordersEntity.getId();

        //付费:操作钱包
        walletsService.rechargeOrWithdraw(userId, amount, paymentMethod,  WalletConst.TransactionsStatus.PAYMENT, orderId, null, "购物支付", true);

        //累加活动数据
        activitiesService.incrementCount(userId, activitiesType, amount);


        //记录首次下单时间
        InvitationRecordsEntity invitationRecordsEntity = invitationRecordsService.queryByInviteeId(userId);
        if(invitationRecordsEntity.getFirstOrderTime() == null){
            invitationRecordsEntity.setFirstOrderTime(now);
            invitationRecordsService.update(invitationRecordsEntity);

            if(user.getInviterId() != null) {
                //首次消费上级奖励
                int firstReward = configService.getConfigValueInteger(ConfigKeyEnum.FIRST_REWARD);
                //奖励:操作钱包
                walletsService.rechargeOrWithdraw(user.getInviterId() , BigDecimal.valueOf(firstReward), WalletConst.PayMode.POINTS, WalletConst.TransactionsStatus.FIRST_REWARD, orderId, user.getUserId(), "首次消费上级奖励", true);
            }
        }

        //操作上级奖励
        if(user.getInviterId() != null) {
            //上级获得团队奖励
            int teamReward = configService.getConfigValueInteger(ConfigKeyEnum.TEAM_REWARD);
            //计算奖励：消费额x奖励百分比
            BigDecimal rewardAmount = amount.multiply(BigDecimal.valueOf(teamReward/100)).setScale(0, RoundingMode.UP);
            //奖励:操作钱包
            walletsService.rechargeOrWithdraw(user.getInviterId() , rewardAmount, WalletConst.PayMode.POINTS, WalletConst.TransactionsStatus.TEAM_REWARD, orderId, user.getUserId(), "团队奖励", true);
        }

        //修改库存和销售数量
        goodsSkusDao.salesCount(sku.getId());

        //不中奖操作
        if(drawResult == 0){
            if(paymentMethod == WalletConst.PayMode.BALANCE) {
                BigDecimal award = new BigDecimal(0);
                if (Objects.equals(goods.getGoodsType(), "newUser")) {
                    Integer noviceRefundWin = configService.getConfigValueInteger(ConfigKeyEnum.NOVICE_REFUND_WIN);
                    award = award.add(BigDecimal.valueOf(noviceRefundWin));
                } else {
                    BigDecimal returnRatio = BigDecimal.valueOf(activitiesEntity.getReturnRatio());
                    MathContext mc = new MathContext(2, RoundingMode.HALF_UP);
                    BigDecimal ratio = returnRatio.divide(BigDecimal.valueOf(100), mc);
                    award = amount.multiply(ratio);
                    log.info("{}, 未中奖奖励: {} x {} = {}", ordersEntity.getId(), ratio, amount, award);
                }
                walletsService.rechargeOrWithdraw(userId, award, WalletConst.PayMode.BALANCE, WalletConst.TransactionsStatus.REFUND_WIN, orderId, null, "未中奖励", true);
                walletsService.rechargeOrWithdraw(userId, amount, WalletConst.PayMode.BALANCE, WalletConst.TransactionsStatus.REFUND_LOSS, orderId, null, "未中返还", true);

                //填入返奖金额
                ordersEntity.setRewardAmount(award);
                ordersService.save(ordersEntity);
            }
        }

        return ResponseDTO.ok(orderId.toString());
    }

    public ResponseDTO<Object> Logistics(Long orderId) {
        OrdersEntity oe = ordersService.getId(orderId);
        if(oe == null || !Objects.equals(oe.getUserId(), SmartRequestUtil.getRequestUserId())){
            return ResponseDTO.userErrorParam("no right");
        }
        return ResponseDTO.ok(ordersService.detailLogisticsByOrderId(orderId).getData());
    }
}
