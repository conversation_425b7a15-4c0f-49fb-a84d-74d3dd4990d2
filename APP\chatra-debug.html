<!DOCTYPE html>
<html>
<head>
    <title>Chatra客服调试工具</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; background: white; padding: 20px; border-radius: 8px; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        .status { background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .log { background: #f3f4f6; padding: 10px; margin: 10px 0; border-radius: 4px; white-space: pre-wrap; font-size: 12px; height: 200px; overflow-y: auto; }
        .success { color: #2e7d32; }
        .error { color: #d32f2f; }
        .warning { color: #f57c00; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Chatra客服调试工具</h1>
        
        <div class="status" id="status">
            <h3>📊 当前状态</h3>
            <div id="statusContent">正在检查...</div>
        </div>
        
        <div>
            <h3>🚀 操作按钮</h3>
            <button onclick="checkStatus()">🔍 检查状态</button>
            <button onclick="loadChatra()">📥 加载Chatra</button>
            <button onclick="showChatra()">👁️ 显示Chatra</button>
            <button onclick="openChat()">💬 打开聊天</button>
            <button onclick="hideChatra()">🙈 隐藏Chatra</button>
            <button onclick="findChatraElements()">🔎 查找元素</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        const chatId = '4gbJiKfsXHorvTHm8';
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateStatus() {
            const statusEl = document.getElementById('statusContent');
            const hasChatra = typeof window.Chatra === 'function';
            const hasChatraID = !!window.ChatraID;
            const chatraScript = document.querySelector('script[src*="chatra"]');
            
            statusEl.innerHTML = `
                <p><strong>✓ window.Chatra:</strong> ${hasChatra ? '✅ 已加载' : '❌ 未加载'}</p>
                <p><strong>✓ window.ChatraID:</strong> ${hasChatraID ? '✅ ' + window.ChatraID : '❌ 未设置'}</p>
                <p><strong>✓ Chatra脚本:</strong> ${chatraScript ? '✅ 已注入' : '❌ 未找到'}</p>
                <p><strong>✓ 预期ChatraID:</strong> ${chatId}</p>
            `;
        }
        
        function checkStatus() {
            log('🔍 检查Chatra状态...');
            updateStatus();
            findChatraElements();
            
            if (typeof window.Chatra === 'function') {
                log('✅ Chatra函数可用', 'success');
                
                // 尝试获取Chatra状态
                try {
                    window.Chatra('isOnline', (online) => {
                        log(`📡 Chatra在线状态: ${online ? '在线' : '离线'}`, online ? 'success' : 'warning');
                    });
                } catch (e) {
                    log(`⚠️ 无法获取在线状态: ${e.message}`, 'warning');
                }
            } else {
                log('❌ Chatra函数不可用', 'error');
            }
        }
        
        function loadChatra() {
            log('📥 开始加载Chatra...');
            
            // 设置ChatraID
            window.ChatraID = chatId;
            
            // 创建Chatra函数队列
            window.Chatra = window.Chatra || function() {
                (window.Chatra.q = window.Chatra.q || []).push(arguments);
            };
            
            // 加载脚本
            const script = document.createElement('script');
            script.async = true;
            script.src = 'https://call.chatra.io/chatra.js';
            
            script.onload = () => {
                log('✅ Chatra脚本加载成功', 'success');
                updateStatus();
                
                // 等待Chatra完全初始化
                setTimeout(() => {
                    checkStatus();
                }, 2000);
            };
            
            script.onerror = () => {
                log('❌ Chatra脚本加载失败', 'error');
            };
            
            document.head.appendChild(script);
            log('📡 Chatra脚本已添加到页面');
        }
        
        function showChatra() {
            if (typeof window.Chatra === 'function') {
                log('👁️ 显示Chatra组件...');
                window.Chatra('show');
                
                setTimeout(() => {
                    findChatraElements();
                }, 1000);
            } else {
                log('❌ Chatra未加载，无法显示', 'error');
            }
        }
        
        function hideChatra() {
            if (typeof window.Chatra === 'function') {
                log('🙈 隐藏Chatra组件...');
                window.Chatra('hide');
                
                setTimeout(() => {
                    findChatraElements();
                }, 1000);
            } else {
                log('❌ Chatra未加载，无法隐藏', 'error');
            }
        }
        
        function openChat() {
            if (typeof window.Chatra === 'function') {
                log('💬 尝试打开聊天窗口...');
                
                // 尝试多种方式
                try {
                    window.Chatra('openChat', true);
                    log('✅ 使用openChat(true)', 'success');
                } catch (e) {
                    log(`⚠️ openChat失败: ${e.message}`, 'warning');
                }
                
                try {
                    window.Chatra('expandWidget');
                    log('✅ 使用expandWidget', 'success');
                } catch (e) {
                    log(`⚠️ expandWidget失败: ${e.message}`, 'warning');
                }
                
                setTimeout(() => {
                    findChatraElements();
                }, 1500);
            } else {
                log('❌ Chatra未加载，无法打开聊天', 'error');
            }
        }
        
        function findChatraElements() {
            log('🔎 查找Chatra DOM元素...');
            
            const selectors = [
                '[data-chatra]',
                '.chatra',
                '#chatra',
                'iframe[src*="chatra"]',
                '[class*="chatra"]',
                '[id*="chatra"]'
            ];
            
            let found = false;
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    found = true;
                    elements.forEach((el, index) => {
                        const visible = el.style.display !== 'none' && el.offsetParent !== null;
                        log(`✅ 找到元素 ${selector}[${index}]: ${visible ? '可见' : '隐藏'} - ${el.tagName}`, 'success');
                        
                        if (el.tagName === 'IFRAME') {
                            log(`   iframe src: ${el.src}`);
                        }
                    });
                }
            });
            
            if (!found) {
                log('❌ 未找到任何Chatra元素', 'error');
            }
            
            // 检查shadow DOM
            const shadowHosts = document.querySelectorAll('*');
            let shadowFound = false;
            shadowHosts.forEach(host => {
                if (host.shadowRoot) {
                    const shadowChatra = host.shadowRoot.querySelectorAll('[class*="chatra"], [id*="chatra"]');
                    if (shadowChatra.length > 0) {
                        shadowFound = true;
                        log(`✅ 在Shadow DOM中找到Chatra元素: ${shadowChatra.length}个`, 'success');
                    }
                }
            });
            
            if (!shadowFound) {
                log('ℹ️ Shadow DOM中未找到Chatra元素');
            }
        }
        
        // 页面加载时自动检查
        window.onload = () => {
            updateStatus();
            log('🚀 Chatra调试工具已加载');
            log('💡 建议操作顺序: 检查状态 → 加载Chatra → 显示Chatra → 打开聊天');
        };
    </script>
</body>
</html>