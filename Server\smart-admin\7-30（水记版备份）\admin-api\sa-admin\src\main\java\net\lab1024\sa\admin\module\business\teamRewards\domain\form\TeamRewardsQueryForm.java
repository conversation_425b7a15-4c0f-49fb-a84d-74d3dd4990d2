package net.lab1024.sa.admin.module.business.teamRewards.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 团队奖励表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class TeamRewardsQueryForm extends PageParam {

    @Schema(description = "用户Id")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "创建时间")
    private LocalDate createTimeBegin;

    @Schema(description = "创建时间")
    private LocalDate createTimeEnd;

}
