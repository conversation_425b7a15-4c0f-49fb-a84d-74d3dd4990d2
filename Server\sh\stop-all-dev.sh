#!/bin/bash

echo "🛑 停止所有服务..."

PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs"

# 停止后端服务
if [ -f "$LOG_DIR/backend.pid" ]; then
    BACKEND_PID=$(cat "$LOG_DIR/backend.pid")
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止 (PID: $BACKEND_PID)"
    fi
    rm -f "$LOG_DIR/backend.pid"
fi

# 停止前端服务
if [ -f "$LOG_DIR/frontend.pid" ]; then
    FRONTEND_PID=$(cat "$LOG_DIR/frontend.pid")
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止 (PID: $FRONTEND_PID)"
    fi
    rm -f "$LOG_DIR/frontend.pid"
fi

# 停止管理端服务
if [ -f "$LOG_DIR/admin-web.pid" ]; then
    ADMIN_PID=$(cat "$LOG_DIR/admin-web.pid")
    if kill -0 $ADMIN_PID 2>/dev/null; then
        kill $ADMIN_PID
        echo "✅ 管理端已停止 (PID: $ADMIN_PID)"
    fi
    rm -f "$LOG_DIR/admin-web.pid"
fi

# 强制停止相关进程（备用方案）
echo "🔍 检查残留进程..."
pkill -f "spring-boot:run"
pkill -f "vite"
pkill -f "node.*vite"

# 停止通过jar包启动的后端服务（兼容现有stop.sh）
PID=$(ps -ef | grep tgw-pp.jar | grep -v grep | awk '{ print $2 }')
if [ ! -z "$PID" ]; then
    echo "🛑 停止JAR包启动的后端服务 (PID: $PID)"
    kill -9 $PID
    echo "✅ JAR包后端服务已停止"
fi

echo "🎉 所有服务已停止！"