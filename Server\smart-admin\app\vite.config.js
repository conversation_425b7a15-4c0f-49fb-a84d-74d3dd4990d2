import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 将 iconify-icon 视为自定义元素
          isCustomElement: (tag) => tag === 'iconify-icon'
        }
      }
    })
  ],
  
  // 开发服务器配置
  server: {
    port: 3000,
    open: false, // 不自动打开浏览器，由启动脚本控制
    host: true, // 允许外部访问
    cors: true,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8686/app',
        changeOrigin: true,
        secure: false,
        timeout: 30000,
        rewrite: (path) => {
          console.log('路径重写:', path, '->', path);
          return path;
        },
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.error('代理错误:', err.message);
            if (!res.headersSent) {
              res.writeHead(503, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
              });
              res.end(JSON.stringify({ 
                error: '服务暂时不可用',
                code: 'PROXY_ERROR',
                message: '无法连接到API服务器'
              }));
            }
          });
          
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔄 代理请求:', req.method, req.url, '-> 目标:', 'https://pp.kongzhongkouan.com/api' + req.url);
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
            proxyReq.setHeader('Accept', 'application/json, text/plain, */*');
            proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
            proxyReq.setTimeout(25000);
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url);
            
            // 强制设置CORS头部，解决跨域问题
            proxyRes.headers['access-control-allow-origin'] = '*';
            proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
            proxyRes.headers['access-control-allow-headers'] = 'Content-Type, Authorization, X-Requested-With';
            proxyRes.headers['access-control-allow-credentials'] = 'true';
            
            // 移除可能冲突的安全头
            delete proxyRes.headers['x-frame-options'];
            delete proxyRes.headers['content-security-policy'];
          });
        }
      },
      
      // 图片代理配置 - 禁用，让Nginx直接处理
      // '/upload': {
      //   target: 'http://127.0.0.1',
      //   changeOrigin: true,
      //   secure: false,
      //   configure: (proxy, options) => {
      //     proxy.on('proxyReq', (proxyReq, req, res) => {
      //       console.log('🖼️ 图片代理请求:', req.method, req.url);
      //     });
      //   }
      // }
    }
  },
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@api': resolve(__dirname, 'src/api'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@store': resolve(__dirname, 'src/store'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  
  // CSS配置
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer
      ]
    }
  },
  
  // 环境变量配置
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    // 🚫 Mock功能已完全禁用
    'import.meta.env.VITE_USE_MOCK': JSON.stringify('false'), // 强制禁用Mock
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify('/api/v1') // 修正：使用正确的API路径
  }
}) 