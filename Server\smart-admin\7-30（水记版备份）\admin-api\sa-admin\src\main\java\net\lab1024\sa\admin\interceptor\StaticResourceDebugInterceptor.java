package net.lab1024.sa.admin.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 静态资源调试拦截器
 * 用于调试静态资源请求处理流程
 */
@Slf4j
@Component
public class StaticResourceDebugInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        if (requestURI.startsWith("/upload/")) {
            System.out.println("=== 静态资源请求调试 ===");
            System.out.println("请求URI: " + requestURI);
            System.out.println("请求方法: " + request.getMethod());
            System.out.println("Handler类型: " + handler.getClass().getSimpleName());
            System.out.println("Handler: " + handler);
            System.out.println("======================");
        }
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String requestURI = request.getRequestURI();
        
        if (requestURI.startsWith("/upload/")) {
            System.out.println("=== 静态资源响应调试 ===");
            System.out.println("响应状态: " + response.getStatus());
            System.out.println("Content-Type: " + response.getContentType());
            if (ex != null) {
                System.out.println("异常: " + ex.getMessage());
            }
            System.out.println("======================");
        }
    }
}