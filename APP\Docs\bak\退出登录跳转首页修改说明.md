# 退出登录跳转首页修改说明

## 修改内容

### 问题描述
用户在"我的"页面中退出登录后，需要跳转回到首页，而不是登录页面。

### 修改位置
文件：`APP/src/views/user/ProfilePage.vue`

### 修改详情

#### 退出登录方法（logout）
```javascript
// 修改前：跳转到登录页
setTimeout(() => {
  router.push({
    path: '/login',
    query: { from: 'logout' }
  })
}, 1000)

// 修改后：跳转到首页
setTimeout(() => {
  router.push({
    path: '/',
    query: { from: 'logout' }
  })
}, 1000)
```

### 功能流程
1. 用户点击"退出登录"按钮
2. 显示确认对话框
3. 用户确认后，执行退出登录流程：
   - 显示加载提示"正在退出..."
   - 调用后端登出API（如果可用）
   - 清除本地认证状态
   - 清除相关本地存储数据
   - 重置页面状态
   - 显示成功提示"已退出登录"
   - **延迟1秒后跳转到首页**

### 其他跳转逻辑
以下跳转逻辑保持不变，因为它们是在不同场景下的正确处理：

1. **认证失败处理**（`handleAuthFailure`）：跳转到登录页
   - 用于处理token过期、认证失败等情况
   
2. **未登录状态处理**（`initializeData`）：跳转到登录页
   - 用于处理用户未登录时访问个人中心的情况

### 测试验证
1. 登录系统，进入"我的"页面
2. 点击"退出登录"按钮
3. 确认退出操作
4. 验证显示"已退出登录"提示
5. 验证1秒后自动跳转到首页
6. 验证首页正常显示，且处于未登录状态

### 技术细节
- 跳转路径：从 `/login` 改为 `/`
- 保留了 `query: { from: 'logout' }` 参数，便于首页识别用户来源
- 延迟时间：1秒（保持不变）
- 其他退出登录逻辑保持不变

### 相关文件
- `APP/src/views/user/ProfilePage.vue` - 个人中心页面（已修改）
- `APP/src/views/home/<USER>