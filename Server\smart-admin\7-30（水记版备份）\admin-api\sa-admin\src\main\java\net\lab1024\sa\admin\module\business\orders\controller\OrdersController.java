package net.lab1024.sa.admin.module.business.orders.controller;

import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersLogisticsEntity;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersAddForm;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersQueryForm;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersUpdateForm;
import net.lab1024.sa.admin.module.business.orders.domain.vo.OrdersVO;
import net.lab1024.sa.admin.module.business.orders.service.OrdersService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 订单表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@RestController
@Tag(name = "订单表")
public class OrdersController {

    @Resource
    private OrdersService ordersService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/orders/queryPage")
    @SaCheckPermission("orders:query")
    public ResponseDTO<PageResult<OrdersVO>> queryPage(@RequestBody @Valid OrdersQueryForm queryForm) {
        return ResponseDTO.ok(ordersService.queryPage(queryForm));
    }

    @Operation(summary = "查询详细 <AUTHOR>
    @GetMapping("/orders/detail/{id}")
    @SaCheckPermission("orders:query")
    public ResponseDTO<OrdersVO> detail(@PathVariable Long id) {
        return ordersService.detail(id);
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/orders/add")
    @SaCheckPermission("orders:add")
    public ResponseDTO<String> add(@RequestBody @Valid OrdersAddForm addForm) {
        return ordersService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/orders/update")
    @SaCheckPermission("orders:update")
    public ResponseDTO<String> update(@RequestBody @Valid OrdersUpdateForm updateForm) {
        return ordersService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/orders/batchDelete")
    @SaCheckPermission("orders:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return ordersService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/orders/delete/{id}")
    @SaCheckPermission("orders:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
        return ordersService.delete(id);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/orders/detailLogisticsByOrderId/{orderId}")
    @SaCheckPermission("orders:query")
    public ResponseDTO<OrdersLogisticsEntity> detailLogisticsByOrderId(@PathVariable Long orderId) {
        return ordersService.detailLogisticsByOrderId(orderId);
    }


    @Operation(summary = "添加物流信息 <AUTHOR>
    @PostMapping("/orders/addLogistics")
    @SaCheckPermission("orders:add")
    public ResponseDTO<String> addLogistics(@RequestBody @Valid OrdersLogisticsEntity addForm) {
        return ordersService.addLogistics(addForm);
    }

    @Operation(summary = "更新物流信息 <AUTHOR>
    @PostMapping("/orders/updateLogistics")
    @SaCheckPermission("orders:update")
    public ResponseDTO<String> update(@RequestBody @Valid OrdersLogisticsEntity updateForm) {
        return ordersService.updateLogistics(updateForm);
    }
}
