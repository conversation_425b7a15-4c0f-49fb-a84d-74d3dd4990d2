-- ========================================
-- 汇总奖励功能数据库迁移脚本
-- 日期: 2025-07-31
-- 功能: 添加total_earnings字段到t_wallets表
-- 使用说明: mysql -u root -p tgw_pp < migration_total_earnings.sql
-- ========================================

-- 显示开始信息
SELECT '开始执行汇总奖励功能数据库迁移...' as message;

-- 检查当前数据库
SELECT DATABASE() as current_database;

-- 检查字段是否已存在
SET @exist := (SELECT COUNT(*) 
               FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_SCHEMA = DATABASE() 
               AND TABLE_NAME = 't_wallets' 
               AND COLUMN_NAME = 'total_earnings');

SELECT CASE 
    WHEN @exist = 0 THEN '字段total_earnings不存在，将执行添加操作'
    ELSE '字段total_earnings已存在，将跳过添加操作'
END as field_check_result;

-- 只有当字段不存在时才添加
SET @sql := IF(@exist = 0, 
    'ALTER TABLE t_wallets ADD COLUMN total_earnings DECIMAL(15,2) DEFAULT 0.00 COMMENT "总收益（未中奖励汇总）"',
    'SELECT "字段total_earnings已存在，跳过添加" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT '验证字段添加结果:' as step;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 't_wallets' 
AND COLUMN_NAME = 'total_earnings';

-- 显示当前t_wallets表结构
SELECT '当前t_wallets表结构:' as step;
DESCRIBE t_wallets;

-- 显示完成信息
SELECT '数据库迁移脚本执行完成！请继续执行历史数据初始化脚本。' as message;