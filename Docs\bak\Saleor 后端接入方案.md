# 现有 Vue 前端应用接入 Saleor 后端方案

## 核心设计思想

我们的核心战略是：**用 GraphQL 替换掉现有的 RESTful API 层**。您现有的业务逻辑和视图组件可以最大程度地复用，我们主要改造的是数据请求和状态管理的部分。Saleor 的 Headless 特性与您现有的前后端分离架构是完美匹配的。

---

## 方案总览：四阶段迁移计划

| 阶段 | 任务 | 核心目标 | 主要涉及文件/目录 | 引入的新概念 |
| :--- | :--- | :--- | :--- | :--- |
| **阶段 0** | **环境准备与工具引入** | 搭建 Saleor 后端，为 Vue 项目引入 GraphQL 客户端。 | `package.json`, `main.js` | Saleor Docker, Apollo Client |
| **阶段 1** | **核心功能迁移** | 将用户认证、商品展示等核心功能对接到 Saleor。 | `src/api/`, `src/store/`, `src/router/`, `src/views/` | GraphQL Queries & Mutations |
| **阶段 2** | **下单与支付流程改造** | 对接 Saleor 复杂的多步结账 (Checkout) 流程。 | `src/api/order.js`, `src/views/order/`, `src/views/cart/` | Saleor Checkout Flow |
| **阶段 3** | **实现“拼团”高级功能** | 利用 Saleor 的扩展性，设计并实现拼团业务逻辑。 | 自定义 Saleor App, `src/api/group.js` | Saleor App, Webhooks, Metadata |

---

## 阶段 0: 环境准备与工具引入

在动工之前，我们需要准备好必要的工具和环境。

1.  **部署 Saleor 后端**:
    *   最简单的方式是使用 Docker。按照 Saleor 官方文档，通过 `docker-compose` 在您的本地或服务器上快速启动 Saleor 核心、数据库、Nginx 等全套服务。
    *   启动后，您应该可以访问 Saleor Dashboard (后台管理) 和 GraphQL Playground (API 调试工具)。

2.  **为 Vue 项目引入 GraphQL 客户端**:
    *   您现有的项目使用 `axios` 请求 REST API。我们需要引入一个 GraphQL 客户端来与 Saleor 通信。`@vue/apollo-composable` 是 Vue 3 + Composition API 的最佳选择。
    *   **安装依赖**:
        ```bash
        npm install --save @vue/apollo-composable graphql graphql-tag
        ```

3.  **配置 Apollo Client**:
    *   在 `src/` 目录下创建一个新文件 `apollo.js` (或 `utils/apollo.js`)：
        ```javascript
        // src/apollo.js
        import { ApolloClient, createHttpLink, InMemoryCache } from '@apollo/client/core';
        import { setContext } from '@apollo/client/link/context';

        const httpLink = createHttpLink({
          // 这是你的 Saleor GraphQL API 地址
          uri: 'http://localhost:8000/graphql/', 
        });

        const authLink = setContext((_, { headers }) => {
          // 从 localStorage 获取认证 token
          const token = localStorage.getItem('auth-token');
          return {
            headers: {
              ...headers,
              authorization: token ? `Bearer ${token}` : "",
            }
          }
        });

        const cache = new InMemoryCache();

        export const apolloClient = new ApolloClient({
          link: authLink.concat(httpLink),
          cache,
        });
        ```
    *   在您的 `main.js` 中集成 Apollo Provider：
        ```javascript
        // src/main.js
        import { createApp, provide, h } from 'vue'
        import { DefaultApolloClient } from '@vue/apollo-composable'
        import { apolloClient } from './apollo' // 引入我们刚创建的 client
        import App from './App.vue'
        // ... 其他 imports

        const app = createApp({
          setup () {
            provide(DefaultApolloClient, apolloClient)
          },
          render: () => h(App),
        })

        // ...
        app.mount('#app')
        ```

---

## 阶段 1: 核心功能迁移

现在，我们将逐个替换 `src/api/` 中的模块。建议创建一个新的目录 `src/graphql/` 来存放所有的 GraphQL 查询语句。

#### a. 用户认证 (`src/api/auth.js`)

*   **旧逻辑**: `login(username, password)` -> `axios.post('/login', ...)`
*   **新逻辑 (Saleor)**:
    *   **登录**: 使用 Saleor 的 `tokenCreate` mutation。
    *   **存储 Token**: 登录成功后，将返回的 `token` 存入 `localStorage` (就像上面 `authLink` 配置中预期的那样)。
    *   **状态管理**: 更新 `src/store/auth.js` 和 `src/store/user.js` 中的用户状态。可以调用 Saleor 的 `me` query 来获取当前登录用户的详细信息。
    *   **路由守卫**: `src/router/index.js` 中的路由守卫逻辑基本不变，只是验证 token 的方式可能需要调整为调用 Saleor 的 `tokenVerify` mutation。

#### b. 商品展示 (`src/api/home.js`, `src/api/product.js`)

*   **旧逻辑**: `getProducts()`, `getProductDetails(id)`
*   **新逻辑 (Saleor)**:
    *   **获取商品列表**: 在 `HomePage.vue` 中，使用 `@vue/apollo-composable` 的 `useQuery` 来执行 Saleor 的 `products` query。您可以轻松实现分页、筛选（按分类/collection）、排序等。
    *   **获取商品详情**: 在 `ProductDetailPage.vue` 中，使用 `useQuery` 执行 `product` query (通过 ID 或 slug)。Saleor 的 `product` query 可以一次性返回所有变体 (variants)、属性、图片、描述等，非常高效。

---

## 阶段 2: 下单与支付流程改造 (`src/api/order.js`)

这是最复杂的部分，因为 Saleor 的结账流程是多步骤、状态化的，非常严谨和灵活。

*   **旧逻辑**: 可能是一个 `createOrder(cartItems)` 的单一请求。
*   **新逻辑 (Saleor Checkout Flow)**:
    1.  **创建结账会话**: 当用户进入购物车或点击“去结算”时，调用 `checkoutCreate` mutation 创建一个 checkout 对象，您会得到一个 `checkoutId`。
    2.  **添加商品**: 使用 `checkoutLinesAdd` mutation 将购物车中的商品逐个添加到 checkout 中。
    3.  **更新配送地址**: 使用 `checkoutShippingAddressUpdate` mutation。
    4.  **选择配送方式**: 使用 `checkoutShippingMethodUpdate` mutation。
    5.  **创建支付**: 使用 `checkoutPaymentCreate` mutation，与具体的支付网关集成。
    6.  **完成订单**: 当支付成功后，调用 `checkoutComplete` mutation，此时 Saleor 才会在后台真正地创建一个 `Order`。

您需要将您现有的 `ConfirmPage.vue` 和 `PaymentPage.vue` 的逻辑完全重构，以适配这个新的、更强大的流程。

---

## 阶段 3: 实现“拼团”高级功能 (`src/api/group.js`)

这是您应用的核心特色，Saleor 本身没有这个功能，但其强大的扩展性恰好为此而生。**我们不修改 Saleor 核心代码，而是创建一个独立的“Saleor App”**。

#### 设计方案:

1.  **数据标记 (Metadata)**:
    *   在 Saleor Dashboard 中，为需要参与拼团的商品添加**元数据 (Metadata)**。这是一个键值对存储。
    *   例如，在一个商品上添加:
        ```json
        {
          "isGroupBuy": "true",
          "groupPrice": "99.99",
          "requiredMembers": "3"
        }
        ```
    *   您的前端在查询商品时，可以一并把 `metadata` 查询出来，从而判断是否显示“发起拼团”按钮。

2.  **创建拼团逻辑微服务 (Saleor App)**:
    *   这是一个独立的、可以用任何语言（如 Node.js, Python/Flask）编写的微服务。
    *   这个微服务需要一个简单的数据库来记录所有正在进行中的拼团活动（如：`{ group_id, product_id, members: [user1, user2], status: 'pending', expires_at }`）。
    *   它需要监听 Saleor 发出的 **Webhook**。关键是 `ORDER_CONFIRMED` 或 `PAYMENT_CONFIRMED` 事件。

3.  **完整流程**:
    *   **发起拼团**: 用户 A 在前端点击“发起拼团”并完成支付。
    *   **Webhook 触发**: Saleor 确认订单后，向您的微服务 URL 发送一个 Webhook 请求，其中包含订单详情。
    *   **微服务处理**: 您的微服务收到请求，检查订单中的商品是否包含 `isGroupBuy` 元数据。如果是，就在自己的数据库里创建一个新的拼团活动，并将用户 A 添加进去。
    *   **用户参与**: 用户 B 通过分享链接进入，同样下单支付。您的微服务再次收到 Webhook，并将用户 B 添加到对应的拼团活动中。
    *   **前端状态更新**: 前端页面（如 `GroupWaitingPage.vue`）需要轮询您的微服务（而不是 Saleor）来获取当前拼团的状态（例如，还差几人）。
    *   **成团/失败**: 微服务中的定时任务会检查超时的团。
        *   **成功**: 人数凑齐，拼团成功。如果原价和拼团价有差价，微服务可以通过 Saleor API 为所有参与者执行退款或发放优惠券。
        *   **失败**: 时间到了人数未满，拼团失败。微服务通过 Saleor API 自动取消这些订单并全额退款。

通过这种方式，您将复杂的、非标准的业务逻辑（拼团）与核心的、标准的电商逻辑（商品、订单、支付）完全解耦，保证了 Saleor 核心的纯净和可升级性。

---

## 方案完善补充

### 性能优化策略

#### GraphQL 查询优化
```javascript
// 使用 Fragment 减少重复查询
const PRODUCT_FRAGMENT = gql`
  fragment ProductInfo on Product {
    id
    name
    description
    thumbnail { url }
    pricing {
      priceRange {
        start { gross { amount currency } }
      }
    }
  }
`

// 批量查询优化
const GET_PRODUCTS_WITH_VARIANTS = gql`
  query GetProductsWithVariants($first: Int!) {
    products(first: $first) {
      edges {
        node {
          ...ProductInfo
          variants {
            id
            name
            pricing { price { gross { amount } } }
          }
        }
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`
```

#### Apollo Client 缓存策略
```javascript
// 改进的 Apollo Client 配置
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client/core'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { RetryLink } from '@apollo/client/link/retry'

const httpLink = createHttpLink({
  uri: process.env.VITE_SALEOR_API_URL || 'http://localhost:8000/graphql/',
})

// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(`GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`)
    })
  }
  if (networkError) {
    console.error(`Network error: ${networkError}`)
    // 可以在这里添加用户友好的错误提示
  }
})

// 重试链接
const retryLink = new RetryLink({
  delay: {
    initial: 300,
    max: Infinity,
    jitter: true
  },
  attempts: {
    max: 3,
    retryIf: (error, _operation) => !!error
  }
})

// 认证链接
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('auth-token')
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  }
})

// 缓存配置
const cache = new InMemoryCache({
  typePolicies: {
    Product: {
      fields: {
        variants: {
          merge(existing = [], incoming) {
            return [...existing, ...incoming]
          }
        }
      }
    },
    Query: {
      fields: {
        products: {
          keyArgs: ["filter", "sortBy"],
          merge(existing, incoming) {
            return {
              ...incoming,
              edges: [...(existing?.edges || []), ...(incoming?.edges || [])]
            }
          }
        }
      }
    }
  }
})

export const apolloClient = new ApolloClient({
  link: from([errorLink, retryLink, authLink, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-and-network'
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first'
    }
  }
})
```

### 数据迁移策略

#### 迁移前数据评估
1. **数据审计脚本**
   ```bash
   # 创建数据迁移脚本目录
   mkdir -p scripts/migration
   ```

2. **用户数据迁移**
   - 用户基本信息 → Saleor User
   - 用户地址 → Saleor Address
   - 用户钱包余额 → Saleor Store Credit (Gift Cards)

3. **商品数据迁移**
   - 商品基本信息 → Saleor Product
   - 商品分类 → Saleor Category/Collection
   - 商品变体 → Saleor ProductVariant
   - 库存信息 → Saleor Stock

4. **订单历史迁移**
   - 历史订单数据迁移到 Saleor Order
   - 订单状态映射表制定

### 测试策略

#### 阶段性测试计划
```javascript
// 1. 单元测试示例 (Jest + Vue Test Utils)
import { mount } from '@vue/test-utils'
import { useQuery } from '@vue/apollo-composable'
import ProductCard from '@/components/ProductCard.vue'

jest.mock('@vue/apollo-composable')

describe('ProductCard', () => {
  it('renders product information correctly', async () => {
    const mockProduct = {
      id: '1',
      name: 'Test Product',
      pricing: {
        priceRange: {
          start: { gross: { amount: 99.99, currency: 'CNY' } }
        }
      }
    }

    useQuery.mockReturnValue({
      result: { value: { product: mockProduct } },
      loading: { value: false },
      error: { value: null }
    })

    const wrapper = mount(ProductCard, {
      props: { productId: '1' }
    })

    expect(wrapper.text()).toContain('Test Product')
    expect(wrapper.text()).toContain('99.99')
  })
})
```

#### 集成测试
```javascript
// 2. GraphQL API 集成测试
import { apolloClient } from '@/apollo'
import { GET_PRODUCTS } from '@/graphql/queries'

describe('GraphQL Integration', () => {
  it('fetches products successfully', async () => {
    const result = await apolloClient.query({
      query: GET_PRODUCTS,
      variables: { first: 10 }
    })
    
    expect(result.data.products).toBeDefined()
    expect(result.data.products.edges).toBeInstanceOf(Array)
  })
})
```

#### E2E测试 (Playwright)
```javascript
// 3. 端到端测试
import { test, expect } from '@playwright/test'

test('complete purchase flow', async ({ page }) => {
  // 1. 访问首页
  await page.goto('/')
  
  // 2. 选择商品
  await page.click('[data-testid="product-card"]:first-child')
  
  // 3. 添加到购物车
  await page.click('[data-testid="add-to-cart"]')
  
  // 4. 进入结账
  await page.click('[data-testid="checkout"]')
  
  // 5. 验证结账流程
  await expect(page.locator('[data-testid="checkout-form"]')).toBeVisible()
})
```

### 安全考虑

#### API 安全
```javascript
// CORS 配置
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || 'http://localhost:3000',
  credentials: true
}))

// Rate limiting
import rateLimit from 'express-rate-limit'
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
app.use('/api/', limiter)
```

#### Webhook 安全验证
```javascript
// 验证 Saleor Webhook 签名
import crypto from 'crypto'

function verifyWebhookSignature(payload, signature, secret) {
  const hmac = crypto.createHmac('sha256', secret)
  hmac.update(payload)
  const computedSignature = hmac.digest('hex')
  
  return crypto.timingSafeEqual(
    Buffer.from(`sha256=${computedSignature}`),
    Buffer.from(signature)
  )
}

// 在 webhook 处理器中使用
app.post('/webhook/saleor', (req, res) => {
  const signature = req.headers['x-saleor-signature']
  if (!verifyWebhookSignature(req.body, signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).send('Unauthorized')
  }
  
  // 处理 webhook
})
```

### 监控和运维

#### 应用监控
```javascript
// OpenTelemetry 集成
import { NodeSDK } from '@opentelemetry/sdk-node'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'

const sdk = new NodeSDK({
  instrumentations: [getNodeAutoInstrumentations()]
})

sdk.start()

// 自定义指标
import { metrics } from '@opentelemetry/api'
const meter = metrics.getMeter('group-buying-app')

const orderCounter = meter.createCounter('orders_total', {
  description: 'Total number of orders'
})

const groupBuyingGauge = meter.createUpDownCounter('active_groups', {
  description: 'Number of active group buying sessions'
})
```

#### 日志配置
```javascript
// Winston 日志配置
import winston from 'winston'

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'group-buying-app' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    ...(process.env.NODE_ENV !== 'production' ? [
      new winston.transports.Console({
        format: winston.format.simple()
      })
    ] : [])
  ]
})
```

### 回滚策略

#### 数据库回滚计划
1. **数据备份**: 每个迁移阶段前完整备份
2. **版本控制**: 数据库 schema 版本化管理
3. **蓝绿部署**: 使用两套环境确保零停机回滚
4. **功能开关**: 通过配置快速切换新旧功能

```javascript
// 功能开关实现
class FeatureFlags {
  static flags = {
    useSaleorAPI: process.env.USE_SALEOR_API === 'true',
    useNewCheckout: process.env.USE_NEW_CHECKOUT === 'true'
  }
  
  static isEnabled(flag) {
    return this.flags[flag] || false
  }
}

// 在组件中使用
export default {
  setup() {
    const useSaleor = FeatureFlags.isEnabled('useSaleorAPI')
    
    if (useSaleor) {
      // 使用 Saleor API
      return useSaleorProducts()
    } else {
      // 使用原有 REST API
      return useRestProducts()
    }
  }
}
```

### 成本评估

#### 基础设施成本
- **服务器**: Saleor 后端 + PostgreSQL + Redis
- **CDN**: 图片和静态资源分发
- **监控**: 日志存储和分析服务
- **备份**: 数据备份存储成本

#### 开发成本
- **学习成本**: 团队 GraphQL 和 Saleor 培训 (2-3周)
- **开发时间**: 预估 3-4 个月完成完整迁移
- **测试成本**: 额外 1 个月的测试和优化

#### 维护成本
- **运维复杂度**: 从单体应用变为微服务架构
- **技术债务**: GraphQL 查询优化和缓存管理
- **版本升级**: Saleor 版本更新和兼容性维护

### 团队培训计划

#### Week 1-2: GraphQL 基础
- GraphQL 概念和语法
- Apollo Client 使用
- Vue Composition API 集成

#### Week 3-4: Saleor 深入
- Saleor 架构理解
- 商品、订单、用户模型
- Webhook 和扩展开发

#### Week 5-6: 实战练习
- 搭建开发环境
- 完成一个完整功能模块
- 代码 Review 和最佳实践分享

---

## 总结

这个迁移方案整体设计合理，但需要在以下方面加强：

1. **完善技术细节**：增加错误处理、性能优化和安全考虑
2. **制定详细的测试策略**：确保迁移过程中的质量保证
3. **准备应急预案**：包括回滚策略和风险控制
4. **评估总体成本**：包括开发、运维和培训成本
5. **制定团队培训计划**：确保团队能力匹配技术要求

建议先进行小规模的 POC (概念验证) 项目，验证关键技术点后再全面实施。 