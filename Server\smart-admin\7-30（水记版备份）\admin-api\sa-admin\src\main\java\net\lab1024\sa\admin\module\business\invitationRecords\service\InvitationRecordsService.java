package net.lab1024.sa.admin.module.business.invitationRecords.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.invitationRecords.dao.InvitationRecordsDao;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.entity.InvitationRecordsEntity;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.form.InvitationRecordsQueryForm;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.vo.InvitationRecordsVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 邀请记录表 Service
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@Service
public class InvitationRecordsService {

    @Resource
    private InvitationRecordsDao invitationRecordsDao;

    /**
     * 分页查询
     */
    public PageResult<InvitationRecordsVO> queryPage(InvitationRecordsQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<InvitationRecordsVO> list = invitationRecordsDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }


    public void create(InvitationRecordsEntity invitationRecordsEntity) {
        invitationRecordsDao.insert(invitationRecordsEntity);
    }

    public void update(InvitationRecordsEntity updateEntity) {
        InvitationRecordsEntity invitationRecordsEntity = SmartBeanUtil.copy(updateEntity, InvitationRecordsEntity.class);
        invitationRecordsDao.updateById(invitationRecordsEntity);
    }

    public InvitationRecordsEntity queryByInviteeId(Long userId) {
        InvitationRecordsEntity invitationRecordsEntity = invitationRecordsDao.selectByInviteeId(userId);
        if (invitationRecordsEntity == null) {
            invitationRecordsEntity = new InvitationRecordsEntity();
            invitationRecordsEntity.setInviteeId(userId);
            invitationRecordsDao.insert(invitationRecordsEntity);
        }
        return invitationRecordsEntity;
    }
}
