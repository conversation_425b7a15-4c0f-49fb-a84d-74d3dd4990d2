<template>
  <div class="test-hmr" style="background: #4ecdc4; padding: 20px; margin: 10px; border-radius: 8px; color: white;">
    <h3>简单HMR测试 - 蓝绿色背景</h3>
    <p>时间: {{ currentTime }}</p>
    <button @click="count++" style="background: #4ecdc4; color: white; border: none; padding: 10px; border-radius: 4px;">
      点击: {{ count }}
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const count = ref(0)
const currentTime = ref('')

onMounted(() => {
  const updateTime = () => {
    currentTime.value = new Date().toLocaleTimeString()
  }
  updateTime()
  setInterval(updateTime, 1000)
  
  console.log('✅ 简单HMR测试组件已挂载')
})

// HMR保持状态
if (import.meta.hot) {
  console.log('✅ HMR已启用')
  if (import.meta.hot.data.count) {
    count.value = import.meta.hot.data.count
  }
  import.meta.hot.dispose((data) => {
    data.count = count.value
  })
}
</script>