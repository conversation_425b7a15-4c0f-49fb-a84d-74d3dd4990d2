# Vue 前端应用接入 Saleor 后端优化方案

基于 **优化计划（7-14）** 和 **优化需求（7-14）** 的完整 Saleor 接入实施方案

## 核心设计思想

采用 **GraphQL + 微服务混合架构**，用 Saleor 作为电商核心，通过自定义 App 实现业务特色功能。现有 Vue 3 应用最大程度复用，主要改造数据层和状态管理。

---

## 方案总览：五阶段迁移计划

| 阶段 | 任务 | 核心目标 | 新增功能覆盖 | 预估时间 |
| :--- | :--- | :--- | :--- | :--- |
| **阶段 0** | **环境搭建与基础集成** | Saleor 部署，GraphQL 客户端集成 | - | 1-2 周 |
| **阶段 1** | **核心电商功能迁移** | 用户、商品、基础订单功能对接 | 新用户激励、界面优化 | 3-4 周 |
| **阶段 2** | **增强购买与支付流程** | 实现直接购买、积分支付、简化拼团 | 直接购买、积分系统 | 4-5 周 |
| **阶段 3** | **用户中心与团队系统** | 实名认证、团队管理、提现优化 | 实名认证、团队功能 | 3-4 周 |
| **阶段 4** | **拼团微服务与业务完善** | 完整拼团逻辑、风控规则 | 拼团优化、风控系统 | 4-5 周 |

---

## 阶段 0: 环境搭建与基础集成

### Saleor 部署配置

```yaml
# docker-compose.yml - 针对拼团应用的 Saleor 配置
version: '3.8'
services:
  api:
    image: ghcr.io/saleor/saleor:3.15
    environment:
      - DATABASE_URL=********************************/saleor
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - DEFAULT_CURRENCY=CNY
      - ALLOWED_HOSTS=localhost,127.0.0.1
      - ENABLE_ACCOUNT_CONFIRMATION_BY_EMAIL=false
      - ENABLE_SSL=false
    depends_on:
      - db
      - redis
    ports:
      - "8000:8000"
    volumes:
      - ./media:/app/media

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=saleor
      - POSTGRES_USER=saleor
      - POSTGRES_PASSWORD=saleor
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine

volumes:
  postgres_data:
```

### Vue 3 + Apollo Client 集成

```javascript
// src/apollo/client.js - 优化的 Apollo Client 配置
import { ApolloClient, createHttpLink, InMemoryCache, from } from '@apollo/client/core'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { RetryLink } from '@apollo/client/link/retry'

const httpLink = createHttpLink({
  uri: import.meta.env.VITE_SALEOR_API_URL || 'http://localhost:8000/graphql/',
})

// 错误处理
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(`[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`)
      // 集成到现有的错误处理系统
      if (message.includes('Invalid token')) {
        // 触发重新登录
        window.location.href = '/login'
      }
    })
  }
  
  if (networkError) {
    console.error(`[Network error]: ${networkError}`)
  }
})

// 认证处理
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('auth-token')
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  }
})

// 重试机制
const retryLink = new RetryLink({
  delay: { initial: 300, max: Infinity, jitter: true },
  attempts: { max: 3, retryIf: (error, _operation) => !!error }
})

// 缓存策略 - 针对拼团应用优化
const cache = new InMemoryCache({
  typePolicies: {
    Product: {
      fields: {
        metadata: {
          merge(existing, incoming) {
            return { ...existing, ...incoming }
          }
        }
      }
    },
    User: {
      fields: {
        privateMetadata: {
          merge(existing, incoming) {
            return { ...existing, ...incoming }
          }
        }
      }
    }
  }
})

export const apolloClient = new ApolloClient({
  link: from([errorLink, retryLink, authLink, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: { errorPolicy: 'all', fetchPolicy: 'cache-and-network' },
    query: { errorPolicy: 'all', fetchPolicy: 'cache-first' }
  }
})
```

```javascript
// src/main.js - 集成到现有 Vue 应用
import { createApp, provide, h } from 'vue'
import { DefaultApolloClient } from '@vue/apollo-composable'
import { apolloClient } from './apollo/client'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

const app = createApp({
  setup() {
    provide(DefaultApolloClient, apolloClient)
  },
  render: () => h(App),
})

app.use(createPinia())
app.use(router)
app.mount('#app')
```

---

## 阶段 1: 核心电商功能迁移

### 1.1 新用户激励机制实现

#### Saleor 数据结构设计

```python
# 通过 Saleor Metadata 存储新用户激励配置
# 管理后台设置全局元数据
GLOBAL_METADATA = {
    "newUserFreeChances": "1",           # 新用户免费机会次数
    "newUserRewardAmount": "10.00",      # 未中奖现金奖励金额
    "freeGroupBuyEnabled": "true"        # 是否启用新用户激励
}

# 用户私有元数据存储
USER_PRIVATE_METADATA = {
    "freeGroupBuyChances": "1",          # 剩余免费机会
    "isNewUser": "true",                 # 是否为新用户
    "registrationDate": "2024-07-21"     # 注册日期
}
```

#### GraphQL 查询和变更

```javascript
// src/graphql/user.js - 用户相关 GraphQL 操作
import { gql } from 'graphql-tag'

export const GET_USER_PROFILE = gql`
  query GetUserProfile {
    me {
      id
      email
      firstName
      lastName
      privateMetadata {
        key
        value
      }
      metadata {
        key
        value
      }
    }
  }
`

export const UPDATE_USER_METADATA = gql`
  mutation UpdateUserMetadata($id: ID!, $input: [MetadataInput!]!) {
    updatePrivateMetadata(id: $id, input: $input) {
      item {
        id
        privateMetadata {
          key
          value
        }
      }
      errors {
        field
        message
      }
    }
  }
`

export const USER_REGISTER = gql`
  mutation UserRegister($input: AccountRegisterInput!) {
    accountRegister(input: $input) {
      user {
        id
        email
      }
      errors {
        field
        message
      }
    }
  }
`
```

#### 前端实现

```javascript
// src/store/auth.js - 集成新用户激励到认证流程
import { defineStore } from 'pinia'
import { useApolloClient, useMutation } from '@vue/apollo-composable'
import { USER_REGISTER, UPDATE_USER_METADATA } from '@/graphql/user'

export const useAuthStore = defineStore('auth', () => {
  const { mutate: registerUser } = useMutation(USER_REGISTER)
  const { mutate: updateUserMetadata } = useMutation(UPDATE_USER_METADATA)
  
  // 注册新用户并设置激励
  const register = async (userData) => {
    try {
      const { data } = await registerUser({
        input: {
          email: userData.email,
          password: userData.password,
          firstName: userData.firstName,
          redirectUrl: `${window.location.origin}/account-confirm/`
        }
      })
      
      if (data.accountRegister.user) {
        // 为新用户设置免费拼团机会
        await updateUserMetadata({
          id: data.accountRegister.user.id,
          input: [
            { key: 'freeGroupBuyChances', value: '1' },
            { key: 'isNewUser', value: 'true' },
            { key: 'registrationDate', value: new Date().toISOString().split('T')[0] }
          ]
        })
        
        return { success: true, user: data.accountRegister.user }
      }
    } catch (error) {
      console.error('Registration failed:', error)
      return { success: false, error: error.message }
    }
  }
  
  return { register }
})
```

### 1.2 首页界面优化适配

```vue
<!-- src/views/home/<USER>
<template>
  <div class="home-page">
    <!-- 弹窗暂时隐藏但保留 -->
    <!-- <WelcomeModal v-if="showWelcomeModal" @close="showWelcomeModal = false" /> -->
    
    <!-- Banner 区域 - 支持配置跳转链接 -->
    <div class="banner-section">
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="banner in banners" :key="banner.id" @click="handleBannerClick(banner)">
          <img :src="banner.image" :alt="banner.title" class="banner-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
    
    <!-- 商品分类 - 硬编码为新分类 -->
    <div class="category-section">
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category.key"
          :class="['category-btn', { active: activeCategory === category.key }]"
          @click="selectCategory(category.key)"
        >
          {{ category.label }}
        </button>
      </div>
    </div>
    
    <!-- 移除拼团类型选择 -->
    
    <!-- 商品列表 - 改为单列布局 -->
    <div class="products-section">
      <div class="products-list single-column">
        <ProductCard 
          v-for="product in filteredProducts" 
          :key="product.id" 
          :product="product"
          class="product-item-wide"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import { GET_PRODUCTS, GET_BANNERS } from '@/graphql/queries'
import ProductCard from '@/components/common/ProductCard.vue'

// 硬编码的商品分类
const categories = ref([
  { key: 'all', label: '全部' },
  { key: 'digital', label: '手机数码' },
  { key: 'luxury', label: '奢饰品' },
  { key: 'points', label: '积分商城' }
])

const activeCategory = ref('all')

// 获取商品列表
const { result: productsResult, loading: productsLoading } = useQuery(GET_PRODUCTS, {
  first: 50,
  filter: computed(() => ({
    categories: activeCategory.value === 'all' ? undefined : [activeCategory.value]
  }))
})

// 获取 Banner 列表
const { result: bannersResult } = useQuery(GET_BANNERS)

const products = computed(() => productsResult.value?.products?.edges?.map(edge => edge.node) || [])
const banners = computed(() => bannersResult.value?.banners || [])

// 根据分类筛选商品
const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') return products.value
  if (activeCategory.value === 'points') {
    return products.value.filter(product => 
      product.metadata?.some(meta => meta.key === 'isPointsOnly' && meta.value === 'true')
    )
  }
  return products.value.filter(product => 
    product.category?.slug === activeCategory.value
  )
})

const selectCategory = (categoryKey) => {
  activeCategory.value = categoryKey
}

const handleBannerClick = (banner) => {
  if (banner.linkUrl) {
    if (banner.linkUrl.startsWith('http')) {
      window.open(banner.linkUrl, '_blank')
    } else {
      router.push(banner.linkUrl)
    }
  }
}
</script>

<style scoped>
.products-list.single-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.product-item-wide {
  width: 100%;
}

.category-tabs {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.category-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #666;
  border-radius: 20px;
  transition: all 0.3s;
}

.category-btn.active {
  background: #ee0a24;
  color: white;
}
</style>
```

### 1.3 底部导航优化

```vue
<!-- src/components/common/BottomNav.vue - 简化的导航栏 -->
<template>
  <van-tabbar v-model="active" :fixed="true" :placeholder="true">
    <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
    <van-tabbar-item icon="orders-o" to="/orders">订单</van-tabbar-item>
    <van-tabbar-item icon="user-o" to="/user">我的</van-tabbar-item>
    <!-- 移除了"活动"和"购物车"选项卡 -->
  </van-tabbar>
</template>

<script setup>
import { ref } from 'vue'

const active = ref(0)
</script>
```

---

## 阶段 2: 增强购买与支付流程

### 2.1 直接购买功能实现

#### Saleor 商品模型扩展

```python
# 通过 Metadata 扩展商品属性
PRODUCT_METADATA_SCHEMA = {
    "allowPointsPurchase": "true|false",    # 是否允许积分购买
    "isPointsOnly": "true|false",           # 是否仅限积分购买
    "directPrice": "199.99",                # 直接购买价格
    "groupPrice": "99.99",                  # 拼团价格
    "isGroupBuy": "true|false",             # 是否支持拼团
    "requiredMembers": "3",                 # 拼团所需人数
    "groupDuration": "24"                   # 拼团持续时间(小时)
}
```

#### GraphQL 查询扩展

```javascript
// src/graphql/product.js - 商品相关查询
import { gql } from 'graphql-tag'

export const GET_PRODUCT_DETAIL = gql`
  query GetProductDetail($id: ID!, $slug: String) {
    product(id: $id, slug: $slug) {
      id
      name
      description
      thumbnail { url }
      images { url }
      category {
        id
        name
      }
      variants {
        id
        name
        sku
        pricing {
          price { gross { amount currency } }
        }
        quantityAvailable
      }
      metadata {
        key
        value
      }
      pricing {
        priceRange {
          start { gross { amount currency } }
          stop { gross { amount currency } }
        }
      }
    }
  }
`

export const CREATE_CHECKOUT = gql`
  mutation CreateCheckout($input: CheckoutCreateInput!) {
    checkoutCreate(input: $input) {
      checkout {
        id
        lines {
          id
          quantity
          variant {
            id
            name
          }
        }
        totalPrice { gross { amount currency } }
      }
      errors {
        field
        message
      }
    }
  }
`
```

#### 商品详情页改造

```vue
<!-- src/views/product/ProductDetailPage.vue - 支持双按钮模式 -->
<template>
  <div class="product-detail">
    <!-- 商品信息展示 -->
    <div class="product-info">
      <van-swipe :autoplay="3000">
        <van-swipe-item v-for="image in product.images" :key="image.url">
          <img :src="image.url" class="product-image" />
        </van-swipe-item>
      </van-swipe>
      
      <div class="product-content">
        <h1 class="product-title">{{ product.name }}</h1>
        <div class="product-price">
          <span v-if="showDirectPrice" class="direct-price">
            直购价: ¥{{ directPrice }}
          </span>
          <span v-if="showGroupPrice" class="group-price">
            拼团价: ¥{{ groupPrice }}
          </span>
        </div>
        <div class="product-description" v-html="product.description"></div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <div class="contact-section">
        <button @click="contactService" class="contact-btn">
          <iconify-icon icon="material-symbols:chat-outline"></iconify-icon>
          <span>客服</span>
        </button>
      </div>
      
      <div class="action-buttons">
        <!-- 直接购买按钮 -->
        <button 
          v-if="showDirectBuy"
          @click="handleDirectBuy" 
          class="direct-buy-btn"
        >
          {{ isPointsOnly ? '积分购买' : '直接购买' }} 
          {{ isPointsOnly ? `${directPrice}积分` : `¥${directPrice}` }}
        </button>
        
        <!-- 拼团按钮 -->
        <button 
          v-if="showGroupBuy"
          @click="handleGroupBuy" 
          class="group-buy-btn"
        >
          发起拼团 ¥{{ groupPrice }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuery } from '@vue/apollo-composable'
import { GET_PRODUCT_DETAIL } from '@/graphql/product'

const route = useRoute()
const router = useRouter()

const { result, loading } = useQuery(GET_PRODUCT_DETAIL, {
  id: route.params.id
})

const product = computed(() => result.value?.product || {})

// 从 metadata 中解析商品属性
const getMetadataValue = (key, defaultValue = null) => {
  const metadata = product.value.metadata || []
  const item = metadata.find(meta => meta.key === key)
  return item ? item.value : defaultValue
}

const allowPointsPurchase = computed(() => getMetadataValue('allowPointsPurchase') === 'true')
const isPointsOnly = computed(() => getMetadataValue('isPointsOnly') === 'true')
const isGroupBuy = computed(() => getMetadataValue('isGroupBuy') === 'true')
const directPrice = computed(() => getMetadataValue('directPrice', '0'))
const groupPrice = computed(() => getMetadataValue('groupPrice', '0'))

// 按钮显示逻辑
const showDirectBuy = computed(() => !isPointsOnly.value || allowPointsPurchase.value)
const showGroupBuy = computed(() => isGroupBuy.value && !isPointsOnly.value)
const showDirectPrice = computed(() => directPrice.value && directPrice.value !== '0')
const showGroupPrice = computed(() => groupPrice.value && groupPrice.value !== '0' && isGroupBuy.value)

const handleDirectBuy = () => {
  router.push({
    name: 'DirectBuyOrder',
    params: { productId: product.value.id },
    query: { 
      type: 'direct',
      price: directPrice.value,
      paymentMethod: isPointsOnly.value ? 'points' : 'balance'
    }
  })
}

const handleGroupBuy = () => {
  router.push({
    name: 'GroupBuyOrder', 
    params: { productId: product.value.id },
    query: { 
      type: 'group',
      price: groupPrice.value
    }
  })
}

const contactService = () => {
  // 集成客服系统
  console.log('Contact customer service')
}
</script>

<style scoped>
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-section {
  display: flex;
  align-items: center;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  font-size: 12px;
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: 8px;
}

.direct-buy-btn, .group-buy-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 24px;
  font-weight: 500;
  color: white;
}

.direct-buy-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.group-buy-btn {
  background: linear-gradient(135deg, #ee0a24, #ff4757);
}

.product-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin: 12px 0;
}

.direct-price {
  font-size: 18px;
  color: #ff6b35;
  font-weight: 600;
}

.group-price {
  font-size: 16px;
  color: #ee0a24;
  font-weight: 500;
}
</style>
```

### 2.2 直接购买订单页面

```vue
<!-- src/views/order/DirectBuyOrderPage.vue - 新建的直接购买页面 -->
<template>
  <div class="direct-buy-order">
    <van-nav-bar title="确认订单" left-arrow @click-left="$router.back()" />
    
    <!-- 商品信息 -->
    <div class="product-section">
      <div class="product-item">
        <img :src="product.thumbnail?.url" class="product-image" />
        <div class="product-info">
          <h3>{{ product.name }}</h3>
          <p class="product-spec">{{ selectedVariant?.name }}</p>
          <div class="product-price">
            {{ isPointsPayment ? `${orderPrice}积分` : `¥${orderPrice}` }}
          </div>
        </div>
        <div class="quantity-selector">
          数量: {{ quantity }}
        </div>
      </div>
    </div>
    
    <!-- 收货地址 -->
    <div class="address-section" @click="selectAddress">
      <div v-if="selectedAddress" class="address-item">
        <div class="address-info">
          <div class="recipient">{{ selectedAddress.firstName }} {{ selectedAddress.lastName }}</div>
          <div class="phone">{{ selectedAddress.phone }}</div>
          <div class="address">{{ formatAddress(selectedAddress) }}</div>
        </div>
        <iconify-icon icon="mdi:chevron-right"></iconify-icon>
      </div>
      <div v-else class="address-placeholder">
        <span>请选择收货地址</span>
        <iconify-icon icon="mdi:chevron-right"></iconify-icon>
      </div>
    </div>
    
    <!-- 支付方式 -->
    <div class="payment-section">
      <h3>支付方式</h3>
      <div v-if="!isPointsOnly" class="payment-options">
        <label class="payment-option">
          <van-radio v-model="paymentMethod" name="balance">余额支付</van-radio>
          <span class="balance-info">余额: ¥{{ userBalance }}</span>
        </label>
        <label v-if="allowPointsPurchase" class="payment-option">
          <van-radio v-model="paymentMethod" name="points">积分支付</van-radio>
          <span class="points-info">积分: {{ userPoints }}</span>
        </label>
      </div>
      <div v-else class="payment-fixed">
        <span>积分支付 (积分: {{ userPoints }})</span>
      </div>
    </div>
    
    <!-- 订单金额 -->
    <div class="amount-section">
      <div class="amount-item">
        <span>商品总价</span>
        <span>{{ isPointsPayment ? `${orderPrice}积分` : `¥${orderPrice}` }}</span>
      </div>
      <div class="amount-item">
        <span>运费</span>
        <span>¥0.00</span>
      </div>
      <div class="amount-total">
        <span>实付款</span>
        <span class="total-price">{{ isPointsPayment ? `${orderPrice}积分` : `¥${orderPrice}` }}</span>
      </div>
    </div>
    
    <!-- 提交订单 -->
    <div class="submit-section">
      <button 
        :disabled="!canSubmit" 
        @click="submitOrder"
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
      >
        {{ submitButtonText }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuery, useMutation } from '@vue/apollo-composable'
import { GET_PRODUCT_DETAIL } from '@/graphql/product'
import { CREATE_CHECKOUT, CHECKOUT_COMPLETE } from '@/graphql/checkout'
import { useUserStore } from '@/store/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const quantity = ref(1)
const paymentMethod = ref('balance')
const selectedAddress = ref(null)

// 获取商品信息
const { result: productResult } = useQuery(GET_PRODUCT_DETAIL, {
  id: route.params.productId
})

const product = computed(() => productResult.value?.product || {})
const selectedVariant = computed(() => product.value.variants?.[0])

// 从 URL 参数获取信息
const orderPrice = computed(() => route.query.price || '0')
const isPointsOnly = computed(() => {
  const metadata = product.value.metadata || []
  return metadata.some(meta => meta.key === 'isPointsOnly' && meta.value === 'true')
})
const allowPointsPurchase = computed(() => {
  const metadata = product.value.metadata || []
  return metadata.some(meta => meta.key === 'allowPointsPurchase' && meta.value === 'true')
})

// 支付方式判断
const isPointsPayment = computed(() => {
  return isPointsOnly.value || paymentMethod.value === 'points'
})

// 用户余额和积分
const userBalance = computed(() => userStore.balance || 0)
const userPoints = computed(() => userStore.points || 0)

// 是否可以提交订单
const canSubmit = computed(() => {
  if (!selectedAddress.value) return false
  
  if (isPointsPayment.value) {
    return userPoints.value >= parseInt(orderPrice.value)
  } else {
    return userBalance.value >= parseFloat(orderPrice.value)
  }
})

const submitButtonText = computed(() => {
  if (!selectedAddress.value) return '请选择收货地址'
  if (!canSubmit.value) {
    return isPointsPayment.value ? '积分不足' : '余额不足'
  }
  return '提交订单'
})

// 创建订单
const { mutate: createCheckout } = useMutation(CREATE_CHECKOUT)
const { mutate: completeCheckout } = useMutation(CHECKOUT_COMPLETE)

const submitOrder = async () => {
  if (!canSubmit.value) return
  
  try {
    // 第一步：创建 checkout
    const { data: checkoutData } = await createCheckout({
      input: {
        lines: [{
          variantId: selectedVariant.value.id,
          quantity: quantity.value
        }],
        shippingAddress: selectedAddress.value,
        metadata: [
          { key: 'orderType', value: 'DIRECT_BUY' },
          { key: 'paymentMethod', value: paymentMethod.value },
          { key: 'originalPrice', value: orderPrice.value }
        ]
      }
    })
    
    if (checkoutData.checkoutCreate.checkout) {
      // 第二步：完成支付和订单
      const { data: completeData } = await completeCheckout({
        id: checkoutData.checkoutCreate.checkout.id,
        paymentData: {
          method: paymentMethod.value,
          amount: orderPrice.value
        }
      })
      
      if (completeData.checkoutComplete.order) {
        // 订单创建成功，跳转到订单详情
        router.push({
          name: 'OrderDetail',
          params: { id: completeData.checkoutComplete.order.id }
        })
      }
    }
  } catch (error) {
    console.error('Order submission failed:', error)
    // 显示错误提示
  }
}

const selectAddress = () => {
  router.push('/user/address')
}

const formatAddress = (address) => {
  return `${address.country} ${address.city} ${address.streetAddress1}`
}

// 初始化支付方式
onMounted(() => {
  if (isPointsOnly.value) {
    paymentMethod.value = 'points'
  }
})
</script>

<style scoped>
.direct-buy-order {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 80px;
}

.product-section, .address-section, .payment-section, .amount-section {
  background: white;
  margin-bottom: 12px;
  padding: 16px;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-info h3 {
  font-size: 16px;
  margin: 0 0 4px 0;
}

.product-spec {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.product-price {
  color: #ee0a24;
  font-weight: 600;
  font-size: 16px;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.submit-btn {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 25px;
  background: #ee0a24;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.submit-btn.disabled {
  background: #ccc;
}

.amount-total {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.total-price {
  color: #ee0a24;
  font-size: 18px;
}
</style>
```

### 2.3 积分系统 Saleor 集成

#### 积分作为 Gift Cards 实现

```javascript
// src/graphql/giftCard.js - 将积分映射为 Saleor Gift Cards
import { gql } from 'graphql-tag'

export const GET_USER_GIFT_CARDS = gql`
  query GetUserGiftCards {
    me {
      giftCards {
        id
        code
        currentBalance {
          amount
          currency
        }
        isActive
        metadata {
          key
          value
        }
      }
    }
  }
`

export const CREATE_GIFT_CARD = gql`
  mutation CreateGiftCard($input: GiftCardCreateInput!) {
    giftCardCreate(input: $input) {
      giftCard {
        id
        code
        currentBalance {
          amount
          currency
        }
      }
      errors {
        field
        message
      }
    }
  }
`

export const USE_GIFT_CARD = gql`
  mutation UseGiftCard($checkoutId: ID!, $giftCardCode: String!) {
    checkoutAddGiftCard(checkoutId: $checkoutId, giftCardCode: $giftCardCode) {
      checkout {
        id
        giftCards {
          id
          code
          currentBalance {
            amount
            currency
          }
        }
        totalPrice {
          gross {
            amount
            currency
          }
        }
      }
      errors {
        field
        message
      }
    }
  }
`
```

#### 积分商城实现

```vue
<!-- src/views/product/PointsMallPage.vue - 积分商城页面 -->
<template>
  <div class="points-mall">
    <van-nav-bar title="积分商城" />
    
    <div class="points-header">
      <div class="points-balance">
        <span class="points-label">我的积分</span>
        <span class="points-amount">{{ userPoints }}</span>
      </div>
    </div>
    
    <div class="products-grid">
      <div 
        v-for="product in pointsProducts" 
        :key="product.id"
        class="points-product-card"
        @click="viewProduct(product)"
      >
        <img :src="product.thumbnail?.url" class="product-image" />
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-points">
            {{ getPointsPrice(product) }}积分
          </div>
          <button class="exchange-btn">立即兑换</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useQuery } from '@vue/apollo-composable'
import { GET_PRODUCTS } from '@/graphql/product'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

// 获取积分商品
const { result } = useQuery(GET_PRODUCTS, {
  first: 50,
  filter: {
    metadata: [{
      key: 'isPointsOnly',
      value: 'true'
    }]
  }
})

const pointsProducts = computed(() => 
  result.value?.products?.edges?.map(edge => edge.node) || []
)

const userPoints = computed(() => userStore.points || 0)

const getPointsPrice = (product) => {
  const metadata = product.metadata || []
  const pointsPrice = metadata.find(meta => meta.key === 'pointsPrice')
  return pointsPrice ? pointsPrice.value : '0'
}

const viewProduct = (product) => {
  router.push({
    name: 'ProductDetail',
    params: { id: product.id },
    query: { type: 'points' }
  })
}
</script>

<style scoped>
.points-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.points-balance {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.points-label {
  font-size: 14px;
  opacity: 0.9;
}

.points-amount {
  font-size: 32px;
  font-weight: bold;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
}

.points-product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.product-points {
  color: #667eea;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.exchange-btn {
  width: 100%;
  padding: 8px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
}
</style>
```

### 2.4 拼团流程简化

```vue
<!-- src/views/order/GroupBuyOrderPage.vue - 简化的拼团下单页 -->
<template>
  <div class="group-buy-order">
    <van-nav-bar title="拼团下单" left-arrow @click-left="$router.back()" />
    
    <!-- 余额不足时显示充值按钮 -->
    <div v-if="!hasEnoughBalance" class="insufficient-balance">
      <div class="balance-warning">
        <iconify-icon icon="mdi:alert-circle" class="warning-icon"></iconify-icon>
        <span>余额不足，请先充值</span>
      </div>
      <button @click="goToRecharge" class="recharge-btn">
        立即充值
      </button>
    </div>
    
    <!-- 正常下单流程 -->
    <div v-else>
      <!-- 商品信息 -->
      <div class="product-section">
        <!-- 商品展示组件 -->
      </div>
      
      <!-- 收货地址 -->
      <div class="address-section">
        <!-- 地址选择组件 -->
      </div>
      
      <!-- 支付信息 - 只显示余额支付 -->
      <div class="payment-section">
        <h3>支付方式</h3>
        <div class="payment-fixed">
          <span>余额支付</span>
          <span class="balance-info">余额: ¥{{ userBalance }}</span>
        </div>
      </div>
      
      <div class="submit-section">
        <button @click="submitGroupOrder" class="submit-btn">
          确认拼团 ¥{{ groupPrice }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const groupPrice = computed(() => parseFloat(route.query.price || '0'))
const userBalance = computed(() => userStore.balance || 0)

const hasEnoughBalance = computed(() => userBalance.value >= groupPrice.value)

const goToRecharge = () => {
  router.push('/user/recharge')
}

const submitGroupOrder = async () => {
  // 拼团下单逻辑，只使用余额支付
  try {
    // 调用拼团下单API
  } catch (error) {
    console.error('Group order failed:', error)
  }
}
</script>

<style scoped>
.insufficient-balance {
  padding: 20px;
  text-align: center;
}

.balance-warning {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #ff6b35;
  margin-bottom: 20px;
  font-size: 16px;
}

.warning-icon {
  font-size: 20px;
}

.recharge-btn {
  width: 200px;
  height: 44px;
  background: #ff6b35;
  color: white;
  border: none;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 600;
}

.payment-fixed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}
</style>
```

---

## 阶段 3: 用户中心与团队系统

### 3.1 实名认证系统

#### 实名认证数据结构

```python
# 通过 Saleor User privateMetadata 存储实名信息
USER_REAL_NAME_METADATA = {
    "isRealNameVerified": "true|false",
    "realName": "张三",
    "idCardNumber": "encrypted_id_card",
    "bankCardNumber": "encrypted_bank_card",
    "bankName": "招商银行",
    "idCardFrontImage": "image_url",
    "idCardBackImage": "image_url",
    "verificationStatus": "pending|approved|rejected",
    "verificationDate": "2024-07-21",
    "rejectionReason": ""
}
```

#### 实名认证页面

```vue
<!-- src/views/user/RealNameVerificationPage.vue -->
<template>
  <div class="real-name-verification">
    <van-nav-bar title="实名认证" left-arrow @click-left="$router.back()" />
    
    <van-form @submit="submitVerification">
      <!-- 基本信息 -->
      <van-cell-group>
        <van-field
          v-model="form.realName"
          label="真实姓名"
          placeholder="请输入身份证上的姓名"
          :rules="[{ required: true, message: '请输入真实姓名' }]"
        />
        <van-field
          v-model="form.idCardNumber"
          label="身份证号"
          placeholder="请输入18位身份证号码"
          :rules="[{ required: true, message: '请输入身份证号码' }]"
        />
      </van-cell-group>
      
      <!-- 身份证照片上传 -->
      <van-cell-group>
        <van-cell title="身份证正面" />
        <van-uploader
          v-model="form.idCardFront"
          :max-count="1"
          :after-read="(file) => handleImageUpload(file, 'front')"
        >
          <div class="upload-placeholder">
            <iconify-icon icon="mdi:camera" size="32"></iconify-icon>
            <p>上传身份证正面</p>
          </div>
        </van-uploader>
        
        <van-cell title="身份证反面" />
        <van-uploader
          v-model="form.idCardBack"
          :max-count="1"
          :after-read="(file) => handleImageUpload(file, 'back')"
        >
          <div class="upload-placeholder">
            <iconify-icon icon="mdi:camera" size="32"></iconify-icon>
            <p>上传身份证反面</p>
          </div>
        </van-uploader>
      </van-cell-group>
      
      <!-- 银行卡信息 -->
      <van-cell-group>
        <van-field
          v-model="form.bankCardNumber"
          label="银行卡号"
          placeholder="请输入银行卡号"
          :rules="[{ required: true, message: '请输入银行卡号' }]"
        />
        <van-field
          v-model="form.bankName"
          label="开户银行"
          placeholder="请输入开户银行"
          :rules="[{ required: true, message: '请输入开户银行' }]"
        />
      </van-cell-group>
      
      <div class="submit-section">
        <van-button type="primary" native-type="submit" block>
          提交认证
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation } from '@vue/apollo-composable'
import { UPDATE_USER_METADATA } from '@/graphql/user'

const router = useRouter()

const form = ref({
  realName: '',
  idCardNumber: '',
  bankCardNumber: '',
  bankName: '',
  idCardFront: [],
  idCardBack: []
})

const frontImageUrl = ref('')
const backImageUrl = ref('')

const { mutate: updateUserMetadata } = useMutation(UPDATE_USER_METADATA)

const handleImageUpload = async (file, type) => {
  // 上传图片到服务器
  const formData = new FormData()
  formData.append('file', file.file)
  
  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })
    const result = await response.json()
    
    if (type === 'front') {
      frontImageUrl.value = result.url
    } else {
      backImageUrl.value = result.url
    }
  } catch (error) {
    console.error('Image upload failed:', error)
  }
}

const submitVerification = async () => {
  try {
    await updateUserMetadata({
      id: 'current-user-id', // 需要获取当前用户ID
      input: [
        { key: 'realName', value: form.value.realName },
        { key: 'idCardNumber', value: encryptData(form.value.idCardNumber) },
        { key: 'bankCardNumber', value: encryptData(form.value.bankCardNumber) },
        { key: 'bankName', value: form.value.bankName },
        { key: 'idCardFrontImage', value: frontImageUrl.value },
        { key: 'idCardBackImage', value: backImageUrl.value },
        { key: 'verificationStatus', value: 'pending' },
        { key: 'verificationDate', value: new Date().toISOString() }
      ]
    })
    
    // 提交成功，返回上一页
    router.back()
  } catch (error) {
    console.error('Verification submission failed:', error)
  }
}

const encryptData = (data) => {
  // 简单的加密处理，实际应用中应使用更安全的加密方法
  return btoa(data)
}
</script>

<style scoped>
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  color: #666;
}

.submit-section {
  padding: 20px;
}
</style>
```

### 3.2 提现页面改造

```vue
<!-- src/views/user/WithdrawPage.vue - 集成实名认证检查 -->
<template>
  <div class="withdraw-page">
    <van-nav-bar title="提现" left-arrow @click-left="$router.back()" />
    
    <!-- 未实名认证提示 -->
    <div v-if="!isRealNameVerified" class="verification-required">
      <div class="verification-notice">
        <iconify-icon icon="mdi:shield-alert" class="notice-icon"></iconify-icon>
        <h3>需要实名认证</h3>
        <p>根据相关法律法规，提现前需要完成实名认证</p>
      </div>
      <van-button type="primary" @click="goToVerification" block>
        立即认证
      </van-button>
    </div>
    
    <!-- 正常提现流程 -->
    <div v-else>
      <div class="balance-info">
        <div class="balance-amount">
          <span class="label">可提现余额</span>
          <span class="amount">¥{{ availableBalance }}</span>
        </div>
      </div>
      
      <van-form @submit="submitWithdraw">
        <van-cell-group>
          <van-field
            v-model="withdrawAmount"
            label="提现金额"
            type="number"
            placeholder="请输入提现金额"
            :rules="withdrawRules"
          />
        </van-cell-group>
        
        <div class="fee-notice">
          <p>提现手续费: 10%</p>
          <p>实际到账: ¥{{ actualAmount }}</p>
        </div>
        
        <div class="submit-section">
          <van-button type="primary" native-type="submit" block>
            确认提现
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const withdrawAmount = ref('')

const isRealNameVerified = computed(() => userStore.isRealNameVerified)
const availableBalance = computed(() => userStore.balance || 0)

// 计算扣除手续费后的实际到账金额
const actualAmount = computed(() => {
  const amount = parseFloat(withdrawAmount.value) || 0
  return (amount * 0.9).toFixed(2) // 扣除10%手续费
})

const withdrawRules = [
  { required: true, message: '请输入提现金额' },
  { 
    validator: (value) => {
      const amount = parseFloat(value)
      if (amount <= 0) return '提现金额必须大于0'
      if (amount > availableBalance.value) return '提现金额不能超过可用余额'
      return true
    }
  }
]

const goToVerification = () => {
  router.push('/user/real-name-verification')
}

const submitWithdraw = async () => {
  try {
    // 调用提现API
    const response = await fetch('/api/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        amount: parseFloat(withdrawAmount.value),
        feeRate: 0.1
      })
    })
    
    if (response.ok) {
      // 提现申请成功
      router.push('/user/withdraw-success')
    }
  } catch (error) {
    console.error('Withdraw failed:', error)
  }
}
</script>

<style scoped>
.verification-required {
  padding: 40px 20px;
  text-align: center;
}

.verification-notice {
  margin-bottom: 30px;
}

.notice-icon {
  font-size: 48px;
  color: #ff6b35;
  margin-bottom: 16px;
}

.verification-notice h3 {
  font-size: 20px;
  margin: 0 0 12px 0;
  color: #333;
}

.verification-notice p {
  color: #666;
  line-height: 1.5;
}

.balance-info {
  background: white;
  padding: 20px;
  margin-bottom: 12px;
}

.balance-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: #666;
}

.amount {
  font-size: 24px;
  font-weight: 600;
  color: #ee0a24;
}

.fee-notice {
  padding: 16px 20px;
  background: #fff7e6;
  margin: 12px 0;
  border-radius: 8px;
}

.fee-notice p {
  margin: 4px 0;
  color: #d48806;
}

.submit-section {
  padding: 20px;
}
</style>
```

### 3.3 团队信息页面

```vue
<!-- src/views/user/TeamInfoPage.vue - 团队管理页面 -->
<template>
  <div class="team-info">
    <van-nav-bar title="我的团队" left-arrow @click-left="$router.back()" />
    
    <!-- 团队概况 -->
    <div class="team-overview">
      <div class="overview-item">
        <span class="value">{{ teamStats.totalMembers }}</span>
        <span class="label">团队成员</span>
      </div>
      <div class="overview-item">
        <span class="value">¥{{ teamStats.totalEarnings }}</span>
        <span class="label">累计收益</span>
      </div>
      <div class="overview-item">
        <span class="value">{{ teamStats.activeMembers }}</span>
        <span class="label">活跃成员</span>
      </div>
    </div>
    
    <!-- 下级列表 -->
    <div class="members-section">
      <h3>下级成员</h3>
      <div class="members-list">
        <div 
          v-for="member in teamMembers" 
          :key="member.id"
          class="member-item"
          :class="{ active: member.hasConsumption }"
        >
          <div class="member-info">
            <div class="member-id">ID: {{ member.id }}</div>
            <div class="member-name">{{ member.name || '未设置昵称' }}</div>
            <div class="member-date">加入时间: {{ formatDate(member.joinDate) }}</div>
          </div>
          <div class="member-stats">
            <div class="contribution">
              贡献: ¥{{ member.contribution }}
            </div>
            <div v-if="member.hasConsumption" class="consumption-badge">
              有消费
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 邀请链接 -->
    <div class="invite-section">
      <h3>邀请新成员</h3>
      <div class="invite-link">
        <input 
          v-model="inviteLink" 
          readonly 
          class="link-input"
          placeholder="邀请链接"
        />
        <button @click="copyInviteLink" class="copy-btn">复制</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuery } from '@vue/apollo-composable'
import { GET_TEAM_INFO, GET_TEAM_MEMBERS } from '@/graphql/team'

// 团队统计信息
const { result: teamStatsResult } = useQuery(GET_TEAM_INFO)
const teamStats = computed(() => teamStatsResult.value?.teamInfo || {
  totalMembers: 0,
  totalEarnings: '0.00',
  activeMembers: 0
})

// 团队成员列表
const { result: membersResult } = useQuery(GET_TEAM_MEMBERS)
const teamMembers = computed(() => membersResult.value?.teamMembers || [])

const inviteLink = ref('')

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const copyInviteLink = async () => {
  try {
    await navigator.clipboard.writeText(inviteLink.value)
    // 显示复制成功提示
  } catch (error) {
    console.error('Copy failed:', error)
  }
}

onMounted(() => {
  // 生成邀请链接
  inviteLink.value = `${window.location.origin}/register?ref=${userStore.userId}`
})
</script>

<style scoped>
.team-overview {
  display: flex;
  background: white;
  padding: 20px;
  margin-bottom: 12px;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.overview-item .value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.overview-item .label {
  font-size: 14px;
  color: #666;
}

.members-section, .invite-section {
  background: white;
  padding: 16px;
  margin-bottom: 12px;
}

.members-section h3, .invite-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.member-item.active {
  background: #fff7e6;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  border-bottom: none;
}

.member-info {
  flex: 1;
}

.member-id {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.member-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.member-date {
  font-size: 12px;
  color: #999;
}

.member-stats {
  text-align: right;
}

.contribution {
  font-size: 14px;
  color: #ee0a24;
  font-weight: 600;
}

.consumption-badge {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-top: 4px;
}

.invite-link {
  display: flex;
  gap: 8px;
}

.link-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #f9f9f9;
}

.copy-btn {
  padding: 12px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
}
</style>
```

---

## 阶段 4: 拼团微服务与业务完善

### 4.1 拼团微服务架构

```javascript
// group-buying-service/src/app.js - 独立的拼团微服务
const express = require('express')
const { createClient } = require('@supabase/supabase-js')
const { GraphQLClient } = require('graphql-request')

const app = express()
app.use(express.json())

// Saleor GraphQL 客户端
const saleorClient = new GraphQLClient(process.env.SALEOR_API_URL, {
  headers: {
    authorization: `Bearer ${process.env.SALEOR_API_TOKEN}`,
  },
})

// 数据库客户端 (使用 Supabase 作为示例)
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
)

// 拼团数据模型
const GROUP_BUYING_SCHEMA = {
  id: 'string',
  product_id: 'string',
  saleor_product_id: 'string',
  required_members: 'number',
  current_members: 'number',
  group_price: 'decimal',
  original_price: 'decimal',
  status: 'enum(pending, active, success, failed)',
  expires_at: 'timestamp',
  created_at: 'timestamp',
  updated_at: 'timestamp'
}

const GROUP_MEMBERS_SCHEMA = {
  id: 'string',
  group_id: 'string',
  user_id: 'string',
  saleor_order_id: 'string',
  join_time: 'timestamp',
  is_leader: 'boolean'
}

// Webhook 处理器 - 监听 Saleor 订单事件
app.post('/webhook/saleor/order-confirmed', async (req, res) => {
  try {
    const { order } = req.body
    
    // 验证 webhook 签名
    if (!verifyWebhookSignature(req)) {
      return res.status(401).send('Unauthorized')
    }
    
    // 检查是否是拼团订单
    const orderMetadata = order.metadata || []
    const orderType = orderMetadata.find(meta => meta.key === 'orderType')
    
    if (orderType?.value === 'GROUP_BUY') {
      await handleGroupBuyOrder(order)
    }
    
    res.status(200).send('OK')
  } catch (error) {
    console.error('Webhook processing failed:', error)
    res.status(500).send('Internal Server Error')
  }
})

// 处理拼团订单
async function handleGroupBuyOrder(order) {
  const productId = order.lines[0].variant.product.id
  const userId = order.user.id
  
  // 检查商品是否支持拼团
  const product = await saleorClient.request(`
    query GetProduct($id: ID!) {
      product(id: $id) {
        metadata {
          key
          value
        }
      }
    }
  `, { id: productId })
  
  const metadata = product.product.metadata || []
  const isGroupBuy = metadata.find(meta => meta.key === 'isGroupBuy')?.value === 'true'
  
  if (!isGroupBuy) return
  
  const requiredMembers = parseInt(metadata.find(meta => meta.key === 'requiredMembers')?.value || '3')
  const groupDuration = parseInt(metadata.find(meta => meta.key === 'groupDuration')?.value || '24')
  
  // 查找是否有进行中的拼团
  const { data: existingGroups } = await supabase
    .from('group_buying')
    .select('*')
    .eq('saleor_product_id', productId)
    .eq('status', 'active')
    .gt('expires_at', new Date().toISOString())
  
  let groupId
  
  if (existingGroups.length > 0) {
    // 加入现有拼团
    groupId = existingGroups[0].id
    
    await supabase
      .from('group_buying')
      .update({ 
        current_members: existingGroups[0].current_members + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', groupId)
  } else {
    // 创建新的拼团
    const { data: newGroup } = await supabase
      .from('group_buying')
      .insert({
        saleor_product_id: productId,
        required_members: requiredMembers,
        current_members: 1,
        group_price: order.total.gross.amount,
        status: 'active',
        expires_at: new Date(Date.now() + groupDuration * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    groupId = newGroup.id
  }
  
  // 添加成员记录
  await supabase
    .from('group_members')
    .insert({
      group_id: groupId,
      user_id: userId,
      saleor_order_id: order.id,
      join_time: new Date().toISOString(),
      is_leader: existingGroups.length === 0 // 第一个加入的是团长
    })
  
  // 检查是否成团
  await checkGroupCompletion(groupId)
}

// 检查拼团是否完成
async function checkGroupCompletion(groupId) {
  const { data: group } = await supabase
    .from('group_buying')
    .select('*')
    .eq('id', groupId)
    .single()
  
  if (group.current_members >= group.required_members) {
    // 拼团成功
    await completeGroupBuying(groupId, 'success')
  }
}

// 完成拼团处理
async function completeGroupBuying(groupId, status) {
  // 更新拼团状态
  await supabase
    .from('group_buying')
    .update({ 
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', groupId)
  
  // 获取所有成员
  const { data: members } = await supabase
    .from('group_members')
    .select('*')
    .eq('group_id', groupId)
  
  if (status === 'success') {
    // 拼团成功，处理后续逻辑
    for (const member of members) {
      await handleGroupBuySuccess(member)
    }
  } else {
    // 拼团失败，退款处理
    for (const member of members) {
      await handleGroupBuyFailure(member)
    }
  }
}

// 拼团成功处理
async function handleGroupBuySuccess(member) {
  // 更新订单状态为已确认
  await saleorClient.request(`
    mutation UpdateOrder($id: ID!, $input: OrderUpdateInput!) {
      orderUpdate(id: $id, input: $input) {
        order {
          id
          status
        }
      }
    }
  `, {
    id: member.saleor_order_id,
    input: {
      metadata: [
        { key: 'groupBuyStatus', value: 'success' },
        { key: 'groupId', value: member.group_id }
      ]
    }
  })
  
  // 发送成功通知
  await sendNotification(member.user_id, {
    type: 'group_buy_success',
    message: '恭喜！拼团成功，商品将为您安排发货'
  })
}

// 拼团失败处理
async function handleGroupBuyFailure(member) {
  // 取消订单并退款
  await saleorClient.request(`
    mutation CancelOrder($id: ID!) {
      orderCancel(id: $id) {
        order {
          id
          status
        }
      }
    }
  `, { id: member.saleor_order_id })
  
  // 发送失败通知
  await sendNotification(member.user_id, {
    type: 'group_buy_failed',
    message: '很遗憾，拼团未成功，款项已原路退回'
  })
}

// 定时任务 - 检查过期的拼团
setInterval(async () => {
  const { data: expiredGroups } = await supabase
    .from('group_buying')
    .select('*')
    .eq('status', 'active')
    .lt('expires_at', new Date().toISOString())
  
  for (const group of expiredGroups) {
    await completeGroupBuying(group.id, 'failed')
  }
}, 60000) // 每分钟检查一次

// API 端点 - 获取拼团状态
app.get('/api/group/:groupId/status', async (req, res) => {
  try {
    const { groupId } = req.params
    
    const { data: group } = await supabase
      .from('group_buying')
      .select(`
        *,
        group_members (
          user_id,
          join_time,
          is_leader
        )
      `)
      .eq('id', groupId)
      .single()
    
    if (!group) {
      return res.status(404).json({ error: 'Group not found' })
    }
    
    res.json({
      id: group.id,
      status: group.status,
      currentMembers: group.current_members,
      requiredMembers: group.required_members,
      timeRemaining: Math.max(0, new Date(group.expires_at) - new Date()),
      members: group.group_members
    })
  } catch (error) {
    console.error('Get group status failed:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.listen(process.env.PORT || 3001, () => {
  console.log('Group buying service started on port', process.env.PORT || 3001)
})
```

### 4.2 拼团状态页面

```vue
<!-- src/views/group/GroupWaitingPage.vue - 拼团等待页面 -->
<template>
  <div class="group-waiting">
    <van-nav-bar title="拼团中" />
    
    <div class="group-info">
      <div class="product-info">
        <img :src="product.thumbnail" class="product-image" />
        <div class="product-details">
          <h3>{{ product.name }}</h3>
          <div class="group-price">拼团价: ¥{{ groupStatus.groupPrice }}</div>
        </div>
      </div>
      
      <div class="progress-section">
        <div class="progress-header">
          <span>拼团进度</span>
          <span class="countdown">{{ formatTimeRemaining }}</span>
        </div>
        
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        
        <div class="progress-text">
          {{ groupStatus.currentMembers }}/{{ groupStatus.requiredMembers }}人
          <span v-if="remainingMembers > 0">
            还差{{ remainingMembers }}人成团
          </span>
          <span v-else class="success-text">
            拼团成功！
          </span>
        </div>
      </div>
      
      <div class="members-list">
        <h4>参团成员</h4>
        <div class="members-grid">
          <div 
            v-for="(member, index) in groupStatus.members" 
            :key="member.userId"
            class="member-avatar"
            :class="{ leader: member.isLeader }"
          >
            <img :src="member.avatar || defaultAvatar" :alt="`成员${index + 1}`" />
            <span v-if="member.isLeader" class="leader-badge">团长</span>
          </div>
          
          <!-- 空位显示 -->
          <div 
            v-for="i in remainingMembers" 
            :key="`empty-${i}`"
            class="member-avatar empty"
          >
            <iconify-icon icon="mdi:account-plus" size="24"></iconify-icon>
          </div>
        </div>
      </div>
    </div>
    
    <div class="actions-section">
      <van-button 
        type="primary" 
        block 
        @click="shareGroup"
        :disabled="groupStatus.status !== 'active'"
      >
        邀请好友参团
      </van-button>
      
      <van-button 
        v-if="groupStatus.status === 'success'"
        type="success" 
        block 
        @click="goToHome"
        class="success-btn"
      >
        拼团成功，返回首页
      </van-button>
    </div>
    
    <!-- 10秒倒计时自动返回 -->
    <div v-if="showAutoReturn" class="auto-return-notice">
      {{ autoReturnCountdown }}秒后自动返回首页
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const groupStatus = ref({
  id: route.params.groupId,
  status: 'active',
  currentMembers: 1,
  requiredMembers: 3,
  timeRemaining: 86400000, // 24小时
  members: []
})

const product = ref({
  name: '',
  thumbnail: '',
  groupPrice: '0'
})

const showAutoReturn = ref(false)
const autoReturnCountdown = ref(10)
const statusCheckInterval = ref(null)
const autoReturnInterval = ref(null)

const progressPercentage = computed(() => {
  return (groupStatus.value.currentMembers / groupStatus.value.requiredMembers) * 100
})

const remainingMembers = computed(() => {
  return Math.max(0, groupStatus.value.requiredMembers - groupStatus.value.currentMembers)
})

const formatTimeRemaining = computed(() => {
  const hours = Math.floor(groupStatus.value.timeRemaining / (1000 * 60 * 60))
  const minutes = Math.floor((groupStatus.value.timeRemaining % (1000 * 60 * 60)) / (1000 * 60))
  return `${hours}:${minutes.toString().padStart(2, '0')}`
})

const defaultAvatar = '/default-avatar.png'

// 获取拼团状态
const fetchGroupStatus = async () => {
  try {
    const response = await fetch(`/api/group/${groupStatus.value.id}/status`)
    const data = await response.json()
    
    const oldStatus = groupStatus.value.status
    groupStatus.value = data
    
    // 如果状态变为成功，启动自动返回倒计时
    if (oldStatus !== 'success' && data.status === 'success') {
      startAutoReturn()
    }
  } catch (error) {
    console.error('Failed to fetch group status:', error)
  }
}

// 开始自动返回倒计时
const startAutoReturn = () => {
  showAutoReturn.value = true
  autoReturnInterval.value = setInterval(() => {
    autoReturnCountdown.value--
    if (autoReturnCountdown.value <= 0) {
      goToHome()
    }
  }, 1000)
}

const shareGroup = () => {
  const shareUrl = `${window.location.origin}/group/${groupStatus.value.id}/join`
  
  if (navigator.share) {
    navigator.share({
      title: '快来参与拼团！',
      text: `${product.value.name} 拼团价仅需¥${product.value.groupPrice}`,
      url: shareUrl,
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(shareUrl)
    // 显示复制成功提示
  }
}

const goToHome = () => {
  if (autoReturnInterval.value) {
    clearInterval(autoReturnInterval.value)
  }
  router.push('/home')
}

onMounted(() => {
  fetchGroupStatus()
  
  // 每5秒更新一次状态
  statusCheckInterval.value = setInterval(fetchGroupStatus, 5000)
})

onUnmounted(() => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }
  if (autoReturnInterval.value) {
    clearInterval(autoReturnInterval.value)
  }
})
</script>

<style scoped>
.group-waiting {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 80px;
}

.group-info {
  background: white;
  margin: 12px;
  border-radius: 12px;
  padding: 20px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.product-details h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.group-price {
  color: #ee0a24;
  font-size: 18px;
  font-weight: 600;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.countdown {
  color: #ee0a24;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #ee0a24, #ff4757);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #666;
}

.success-text {
  color: #52c41a;
  font-weight: 600;
}

.members-list h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.members-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.member-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e0e0e0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-avatar.leader {
  border-color: #ffd700;
}

.member-avatar.empty {
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
}

.leader-badge {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  background: #ffd700;
  color: #333;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
}

.actions-section {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.success-btn {
  background: #52c41a !important;
}

.auto-return-notice {
  text-align: center;
  color: #666;
  padding: 12px;
  background: #fff7e6;
  margin: 0 20px;
  border-radius: 8px;
}
</style>
```

### 4.3 风控系统实现

```javascript
// src/utils/riskControl.js - 风控规则实现
export class RiskControlService {
  // 每日积分获取上限检查
  static async checkDailyPointsLimit(userId) {
    const today = new Date().toISOString().split('T')[0]
    
    try {
      const response = await fetch(`/api/user/${userId}/daily-points?date=${today}`)
      const data = await response.json()
      
      const dailyLimit = 1000 // 每日积分上限
      const currentPoints = data.totalPointsToday || 0
      
      return {
        canEarn: currentPoints < dailyLimit,
        remaining: Math.max(0, dailyLimit - currentPoints),
        limit: dailyLimit
      }
    } catch (error) {
      console.error('Failed to check daily points limit:', error)
      return { canEarn: false, remaining: 0, limit: 0 }
    }
  }
  
  // 积分退单规则
  static calculateRefundAmount(order) {
    const { totalAmount, paymentMethod, pointsUsed, cashUsed } = order
    
    if (paymentMethod === 'points') {
      // 纯积分支付，不退积分，返回0
      return {
        cashRefund: 0,
        pointsRefund: 0,
        message: '积分支付订单取消，积分不予退还'
      }
    } else if (paymentMethod === 'mixed') {
      // 混合支付，只退现金部分
      return {
        cashRefund: cashUsed,
        pointsRefund: 0,
        message: `退还现金 ¥${cashUsed}，积分不予退还`
      }
    } else {
      // 现金支付，全额退还
      return {
        cashRefund: totalAmount,
        pointsRefund: 0,
        message: `全额退还 ¥${totalAmount}`
      }
    }
  }
  
  // 提现手续费计算
  static calculateWithdrawFee(amount) {
    const feeRate = 0.1 // 10%手续费
    const fee = amount * feeRate
    const actualAmount = amount - fee
    
    return {
      originalAmount: amount,
      fee,
      actualAmount,
      feeRate
    }
  }
  
  // 用户行为风险评估
  static async assessUserRisk(userId, action) {
    const riskFactors = []
    
    try {
      // 检查注册时间（新用户风险较高）
      const userInfo = await this.getUserInfo(userId)
      const daysSinceRegistration = (Date.now() - new Date(userInfo.registrationDate)) / (1000 * 60 * 60 * 24)
      
      if (daysSinceRegistration < 1) {
        riskFactors.push({ type: 'new_user', score: 30 })
      }
      
      // 检查频繁操作
      const recentActions = await this.getRecentActions(userId, action, 3600000) // 1小时内
      if (recentActions.length > 10) {
        riskFactors.push({ type: 'frequent_actions', score: 50 })
      }
      
      // 检查异常订单模式
      if (action === 'place_order') {
        const recentOrders = await this.getRecentOrders(userId, 24 * 3600000) // 24小时内
        if (recentOrders.length > 20) {
          riskFactors.push({ type: 'excessive_orders', score: 40 })
        }
      }
      
      const totalRiskScore = riskFactors.reduce((sum, factor) => sum + factor.score, 0)
      
      return {
        riskScore: totalRiskScore,
        riskLevel: this.getRiskLevel(totalRiskScore),
        factors: riskFactors,
        action: totalRiskScore > 80 ? 'block' : totalRiskScore > 50 ? 'review' : 'allow'
      }
    } catch (error) {
      console.error('Risk assessment failed:', error)
      return { riskScore: 0, riskLevel: 'unknown', action: 'allow' }
    }
  }
  
  static getRiskLevel(score) {
    if (score >= 80) return 'high'
    if (score >= 50) return 'medium'
    if (score >= 20) return 'low'
    return 'minimal'
  }
  
  static async getUserInfo(userId) {
    // 从 Saleor 获取用户信息
    const response = await fetch(`/api/users/${userId}`)
    return response.json()
  }
  
  static async getRecentActions(userId, actionType, timeWindow) {
    // 获取用户最近的操作记录
    const response = await fetch(`/api/users/${userId}/actions?type=${actionType}&since=${Date.now() - timeWindow}`)
    return response.json()
  }
  
  static async getRecentOrders(userId, timeWindow) {
    // 获取用户最近的订单
    const response = await fetch(`/api/users/${userId}/orders?since=${Date.now() - timeWindow}`)
    return response.json()
  }
}

// 积分每日限制中间件
export const pointsLimitMiddleware = async (req, res, next) => {
  if (req.path.includes('/earn-points')) {
    const userId = req.user.id
    const limitCheck = await RiskControlService.checkDailyPointsLimit(userId)
    
    if (!limitCheck.canEarn) {
      return res.status(429).json({
        error: 'Daily points limit exceeded',
        message: '今日积分获取已达上限，请明天再试',
        limit: limitCheck.limit
      })
    }
    
    req.pointsRemaining = limitCheck.remaining
  }
  
  next()
}
```

---

## 性能优化与监控

### 缓存策略

```javascript
// src/utils/cache.js - 多层缓存策略
import { apolloClient } from '@/apollo/client'

export class CacheManager {
  // GraphQL 查询缓存优化
  static setupCachePolicy() {
    return {
      // 商品信息缓存较长时间
      'Product': {
        fetchPolicy: 'cache-first',
        nextFetchPolicy: 'cache-and-network',
        maxAge: 300000 // 5分钟
      },
      
      // 用户信息实时更新
      'User': {
        fetchPolicy: 'cache-and-network',
        maxAge: 60000 // 1分钟
      },
      
      // 拼团状态实时查询
      'GroupBuying': {
        fetchPolicy: 'network-only',
        maxAge: 0
      }
    }
  }
  
  // 本地存储缓存
  static setLocalCache(key, data, ttl = 3600000) {
    const item = {
      data,
      timestamp: Date.now(),
      ttl
    }
    localStorage.setItem(`cache_${key}`, JSON.stringify(item))
  }
  
  static getLocalCache(key) {
    try {
      const item = JSON.parse(localStorage.getItem(`cache_${key}`))
      if (!item) return null
      
      if (Date.now() - item.timestamp > item.ttl) {
        localStorage.removeItem(`cache_${key}`)
        return null
      }
      
      return item.data
    } catch {
      return null
    }
  }
  
  // 内存缓存（会话级别）
  static memoryCache = new Map()
  
  static setMemoryCache(key, data, ttl = 300000) {
    const timeout = setTimeout(() => {
      this.memoryCache.delete(key)
    }, ttl)
    
    this.memoryCache.set(key, { data, timeout })
  }
  
  static getMemoryCache(key) {
    const item = this.memoryCache.get(key)
    return item ? item.data : null
  }
}
```

### 监控指标

```javascript
// src/utils/monitoring.js - 应用性能监控
export class MonitoringService {
  static init() {
    // 页面性能监控
    this.setupPerformanceMonitoring()
    
    // API 请求监控
    this.setupAPIMonitoring()
    
    // 错误监控
    this.setupErrorMonitoring()
    
    // 用户行为监控
    this.setupUserActivityMonitoring()
  }
  
  static setupPerformanceMonitoring() {
    // 监控页面加载时间
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0]
      
      this.sendMetric('page_load_time', {
        loadTime: perfData.loadEventEnd - perfData.loadEventStart,
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        firstContentfulPaint: this.getFCP(),
        largestContentfulPaint: this.getLCP(),
        url: window.location.pathname
      })
    })
  }
  
  static setupAPIMonitoring() {
    // 拦截 Apollo Client 请求
    apolloClient.addResolvers({
      Mutation: {
        // 监控变更操作
      },
      Query: {
        // 监控查询操作
      }
    })
  }
  
  static setupErrorMonitoring() {
    window.addEventListener('error', (event) => {
      this.sendError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      })
    })
    
    window.addEventListener('unhandledrejection', (event) => {
      this.sendError('promise_rejection', {
        reason: event.reason?.toString(),
        stack: event.reason?.stack
      })
    })
  }
  
  static setupUserActivityMonitoring() {
    // 监控关键用户行为
    document.addEventListener('click', (event) => {
      const target = event.target
      if (target.matches('[data-track]')) {
        this.sendEvent('user_click', {
          element: target.dataset.track,
          page: window.location.pathname,
          timestamp: Date.now()
        })
      }
    })
  }
  
  static sendMetric(name, data) {
    // 发送指标到监控系统
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ metric: name, data, timestamp: Date.now() })
    }).catch(console.error)
  }
  
  static sendError(type, data) {
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, data, timestamp: Date.now() })
    }).catch(console.error)
  }
  
  static sendEvent(name, data) {
    fetch('/api/events', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ event: name, data, timestamp: Date.now() })
    }).catch(console.error)
  }
  
  static getFCP() {
    const perfEntries = performance.getEntriesByType('paint')
    const fcpEntry = perfEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  }
  
  static getLCP() {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        resolve(lastEntry.startTime)
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    })
  }
}
```

---

## 部署与运维

### Docker 部署配置

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  # Saleor 核心服务
  saleor-api:
    image: ghcr.io/saleor/saleor:3.15
    environment:
      - DATABASE_URL=postgres://saleor:${POSTGRES_PASSWORD}@postgres:5432/saleor
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - DEFAULT_CURRENCY=CNY
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - WEBHOOK_HOST=${WEBHOOK_HOST}
    depends_on:
      - postgres
      - redis
    volumes:
      - saleor_media:/app/media
    networks:
      - saleor-network

  # 拼团微服务
  group-buying-service:
    build: ./group-buying-service
    environment:
      - SALEOR_API_URL=http://saleor-api:8000/graphql/
      - SALEOR_API_TOKEN=${SALEOR_API_TOKEN}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
    depends_on:
      - saleor-api
    networks:
      - saleor-network

  # Vue 前端应用
  frontend:
    build: 
      context: .
      dockerfile: Dockerfile.frontend
    environment:
      - VITE_SALEOR_API_URL=http://saleor-api:8000/graphql/
      - VITE_GROUP_API_URL=http://group-buying-service:3001/api
    networks:
      - saleor-network

  # 数据库
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=saleor
      - POSTGRES_USER=saleor
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - saleor-network

  # 缓存
  redis:
    image: redis:7-alpine
    networks:
      - saleor-network

  # 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - saleor_media:/usr/share/nginx/html/media
    depends_on:
      - saleor-api
      - group-buying-service
      - frontend
    networks:
      - saleor-network

volumes:
  postgres_data:
  saleor_media:

networks:
  saleor-network:
    driver: bridge
```

### CI/CD 流水线

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: |
        npm run test:unit
        npm run test:e2e
    
    - name: Run linting
      run: npm run lint
    
    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /app/group-buying
          git pull origin main
          docker-compose -f docker-compose.production.yml down
          docker-compose -f docker-compose.production.yml up -d --build
          docker system prune -f
```

---

## 总结与建议

### 实施优先级

1. **阶段 0-1 (基础功能)**: 优先实现新用户激励、界面优化、基础购买流程
2. **阶段 2 (核心增强)**: 直接购买、积分系统、支付优化
3. **阶段 3 (用户体验)**: 实名认证、团队功能、提现优化
4. **阶段 4 (业务特色)**: 拼团微服务、风控系统

### 技术选型优势

- **Saleor**: 成熟的电商后端，GraphQL API，强大的扩展性
- **Vue 3 + Apollo**: 现代前端技术栈，响应式数据管理
- **微服务架构**: 业务解耦，独立部署，便于维护和扩展
- **容器化部署**: 环境一致性，便于扩容和管理

### 风险控制

1. **数据迁移风险**: 建议分批迁移，保留原系统作为备份
2. **性能风险**: 通过缓存策略和CDN优化，监控关键指标
3. **业务连续性**: 功能开关控制，灰度发布，快速回滚机制
4. **安全风险**: API权限控制，数据加密，审计日志

### 成本效益

**预期收益**:
- 开发效率提升 40%（GraphQL统一API）
- 系统维护成本降低 30%（标准化架构）
- 功能扩展速度提升 50%（Saleor生态）
- 用户体验改善显著（新功能支持）

**总体评估**: 这是一个技术先进、业务匹配、风险可控的优化方案，建议按阶段逐步实施。
