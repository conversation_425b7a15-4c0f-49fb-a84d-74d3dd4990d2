package net.lab1024.sa.admin.module.business.teamRewards.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 团队奖励表 实体类
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Data
@TableName("t_team_rewards")
public class TeamRewardsEntity {

    /**
     * 奖励ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 奖励日期
     */
    private LocalDate rewardDate;

    /**
     * 达标人数
     */
    private Integer qualifiedCount;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 发放状态
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
