<!DOCTYPE html>
<html>
<head>
    <title>清除认证状态测试</title>
</head>
<body>
    <h1>清除认证状态测试</h1>
    <button onclick="clearAuth()">清除所有认证信息</button>
    <button onclick="checkAuth()">检查认证状态</button>
    <pre id="output"></pre>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\n';
        }

        function clearAuth() {
            // 清除所有可能的认证相关数据
            const keysToRemove = [
                'access_token',
                'token',
                'refresh_token',
                'user_info',
                'token_expire_time',
                'last_login_account',
                'show_newuser_guide'
            ];

            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });

            log('已清除所有认证信息');
            checkAuth();
        }

        function checkAuth() {
            const localStorage_tokens = {
                'access_token': localStorage.getItem('access_token'),
                'token': localStorage.getItem('token'),
                'refresh_token': localStorage.getItem('refresh_token'),
                'user_info': localStorage.getItem('user_info'),
                'token_expire_time': localStorage.getItem('token_expire_time')
            };

            const sessionStorage_tokens = {
                'access_token': sessionStorage.getItem('access_token'),
                'token': sessionStorage.getItem('token'),
                'refresh_token': sessionStorage.getItem('refresh_token'),
                'user_info': sessionStorage.getItem('user_info')
            };

            log('=== localStorage 认证信息 ===');
            Object.entries(localStorage_tokens).forEach(([key, value]) => {
                log(`${key}: ${value ? '存在' : '不存在'}`);
            });

            log('=== sessionStorage 认证信息 ===');
            Object.entries(sessionStorage_tokens).forEach(([key, value]) => {
                log(`${key}: ${value ? '存在' : '不存在'}`);
            });

            // 检查是否还有其他认证相关的键
            log('=== 所有localStorage键 ===');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.includes('token') || key.includes('user') || key.includes('auth')) {
                    log(`发现认证相关键: ${key} = ${localStorage.getItem(key) ? '有值' : '无值'}`);
                }
            }
        }

        // 页面加载时自动检查
        checkAuth();
    </script>
</body>
</html>