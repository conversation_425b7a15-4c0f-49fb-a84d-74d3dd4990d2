-- ========================================
-- 汇总奖励功能数据库迁移脚本
-- 日期: 2025-08-01
-- 功能: 添加total_earnings字段
-- ========================================

-- 检查字段是否已存在
SET @exist := (SELECT COUNT(*) 
               FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_SCHEMA = DATABASE() 
               AND TABLE_NAME = 't_wallets' 
               AND COLUMN_NAME = 'total_earnings');

-- 只有当字段不存在时才添加
SET @sql := IF(@exist = 0, 
    'ALTER TABLE t_wallets ADD COLUMN total_earnings DECIMAL(15,2) DEFAULT 0.00 COMMENT "总收益（未中奖励汇总）"',
    'SELECT "字段total_earnings已存在，跳过添加" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 't_wallets' 
AND COLUMN_NAME = 'total_earnings';

-- ========================================
-- 历史数据初始化：计算所有用户的总收益
-- 基于现有的REFUND_WIN交易记录计算
-- ========================================

-- 更新所有用户的total_earnings字段
UPDATE t_wallets w 
SET total_earnings = (
    SELECT IFNULL(SUM(amount), 0) 
    FROM t_wallet_transactions wt 
    WHERE wt.user_id = w.user_id 
    AND wt.type = 'REFUND_WIN'
),
update_time = CURRENT_TIMESTAMP;

-- 显示更新结果
SELECT 
    '=== 历史数据初始化完成 ===' as status;

SELECT 
    w.user_id,
    w.total_earnings,
    (SELECT COUNT(*) FROM t_wallet_transactions wt 
     WHERE wt.user_id = w.user_id AND wt.type = 'REFUND_WIN') as refund_win_count,
    w.update_time
FROM t_wallets w 
WHERE w.total_earnings > 0
ORDER BY w.total_earnings DESC
LIMIT 10;

-- 统计报告
SELECT 
    '总用户数' as metric,
    COUNT(*) as value
FROM t_wallets
UNION ALL
SELECT 
    '有收益用户数' as metric,
    COUNT(*) as value
FROM t_wallets 
WHERE total_earnings > 0
UNION ALL
SELECT 
    '总收益金额' as metric,
    ROUND(SUM(total_earnings), 2) as value
FROM t_wallets;

-- 数据一致性验证
SELECT 
    '=== 数据一致性验证 ===' as status;

SELECT 
    w.user_id,
    w.total_earnings as stored_earnings,
    IFNULL(SUM(wt.amount), 0) as calculated_earnings,
    ROUND(w.total_earnings - IFNULL(SUM(wt.amount), 0), 2) as difference
FROM t_wallets w
LEFT JOIN t_wallet_transactions wt ON w.user_id = wt.user_id 
    AND wt.type = 'REFUND_WIN'
GROUP BY w.user_id, w.total_earnings
HAVING ABS(difference) > 0.01  -- 检查差异大于1分的记录
ORDER BY ABS(difference) DESC
LIMIT 5;