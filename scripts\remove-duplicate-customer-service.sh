#!/bin/bash

# 移除重复的客服悬浮按钮脚本
echo "🧹 开始移除重复的客服悬浮按钮..."

# 需要处理的文件列表
files=(
  "APP/src/views/group/GroupPage.vue"
  "APP/src/views/order/DirectBuyPage.vue" 
  "APP/src/views/product/ProductDetailPage.vue"
)

for file in "${files[@]}"; do
  if [ -f "/mnt/d/Dev/团购网/$file" ]; then
    echo "📄 处理文件: $file"
    
    # 移除客服悬浮按钮模板代码
    sed -i '/<!-- 客服悬浮按钮 -->/,+1d' "/mnt/d/Dev/团购网/$file"
    
    # 移除CustomerServiceFloat导入
    sed -i '/import CustomerServiceFloat/d' "/mnt/d/Dev/团购网/$file"
    
    # 移除组件声明中的CustomerServiceFloat
    sed -i 's/CustomerServiceFloat,\?//g' "/mnt/d/Dev/团购网/$file"
    sed -i 's/,\s*CustomerServiceFloat//g' "/mnt/d/Dev/团购网/$file"
    
    echo "✅ 已处理: $file"
  else
    echo "⚠️ 文件不存在: $file"
  fi
done

echo "🎉 重复客服按钮移除完成！"