#!/bin/bash

PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs"

# 显示菜单
show_menu() {
    echo ""
    echo "📝 日志查看工具"
    echo "================"
    echo "1. 后端日志 (实时)"
    echo "2. 前端日志 (实时)"
    echo "3. 管理端日志 (实时)"
    echo "4. 生产后端日志 (实时)"
    echo "5. 显示所有日志文件大小"
    echo "6. 清理所有日志文件"
    echo "7. 退出"
    echo ""
}

# 清理日志函数
clean_logs() {
    echo "🧹 清理日志文件..."
    if [ -d "$LOG_DIR" ]; then
        # 备份重要日志
        if [ -f "$LOG_DIR/backend.log" ]; then
            cp "$LOG_DIR/backend.log" "$LOG_DIR/backend.log.bak"
        fi
        if [ -f "$LOG_DIR/backend-prod.log" ]; then
            cp "$LOG_DIR/backend-prod.log" "$LOG_DIR/backend-prod.log.bak"
        fi
        
        # 清理日志文件
        > "$LOG_DIR/backend.log" 2>/dev/null
        > "$LOG_DIR/frontend.log" 2>/dev/null
        > "$LOG_DIR/admin-web.log" 2>/dev/null
        > "$LOG_DIR/backend-prod.log" 2>/dev/null
        
        echo "✅ 日志文件已清理完成"
        echo "💾 重要日志已备份为 .bak 文件"
    else
        echo "❌ 日志目录不存在"
    fi
}

# 显示日志文件大小
show_log_sizes() {
    echo "📊 日志文件状态："
    if [ -d "$LOG_DIR" ]; then
        cd "$LOG_DIR"
        for log_file in *.log; do
            if [ -f "$log_file" ]; then
                SIZE=$(du -h "$log_file" | cut -f1)
                LINES=$(wc -l < "$log_file" 2>/dev/null || echo "0")
                echo "   $log_file: $SIZE ($LINES 行)"
            fi
        done
    else
        echo "   日志目录不存在"
    fi
}

# 主循环
while true; do
    show_menu
    read -p "请选择选项 (1-7): " choice
    
    case $choice in
        1)
            if [ -f "$LOG_DIR/backend.log" ]; then
                echo "📖 查看后端日志 (Ctrl+C 退出)..."
                tail -f "$LOG_DIR/backend.log"
            else
                echo "❌ 后端日志文件不存在"
            fi
            ;;
        2)
            if [ -f "$LOG_DIR/frontend.log" ]; then
                echo "📖 查看前端日志 (Ctrl+C 退出)..."
                tail -f "$LOG_DIR/frontend.log"
            else
                echo "❌ 前端日志文件不存在"
            fi
            ;;
        3)
            if [ -f "$LOG_DIR/admin-web.log" ]; then
                echo "📖 查看管理端日志 (Ctrl+C 退出)..."
                tail -f "$LOG_DIR/admin-web.log"
            else
                echo "❌ 管理端日志文件不存在"
            fi
            ;;
        4)
            if [ -f "$LOG_DIR/backend-prod.log" ]; then
                echo "📖 查看生产后端日志 (Ctrl+C 退出)..."
                tail -f "$LOG_DIR/backend-prod.log"
            else
                echo "❌ 生产后端日志文件不存在"
            fi
            ;;
        5)
            show_log_sizes
            read -p "按回车键继续..."
            ;;
        6)
            read -p "确定要清理所有日志文件吗? (y/N): " confirm
            if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                clean_logs
            else
                echo "❌ 取消清理操作"
            fi
            read -p "按回车键继续..."
            ;;
        7)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            read -p "按回车键继续..."
            ;;
    esac
done