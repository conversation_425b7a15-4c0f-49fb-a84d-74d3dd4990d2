package net.lab1024.sa.admin.module.business.oa.popups.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 弹窗管理 更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Data
public class PopupsUpdateForm extends PopupsAddForm {

    @Schema(description = "弹窗ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "弹窗ID 不能为空")
    private Integer id;

}