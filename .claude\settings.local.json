{"permissions": {"allow": ["Bash(ls:*)", "Bash(npm install:*)", "WebFetch(domain:github.com)", "Bash(npm search mcp)", "Bash(mcp-filesystem-server:*)", "Bash(npx @modelcontextprotocol/server-filesystem:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(git remote add:*)", "Bash(git config:*)", "Bash(git merge:*)", "Bash(git submodule:*)", "Bash(git pull:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git reset:*)", "Bash(bash:*)", "Bash(./scripts/setup-hooks.sh:*)", "Bash(cp:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "Bash(gh pr create:*)", "Bash(npm run dev:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(curl:*)", "Bash(gh:*)", "<PERSON><PERSON>(wget:*)", "Bash(git remote get-url:*)", "<PERSON><PERSON>(cat:*)", "Bash(ping:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(git --version)", "WebFetch(domain:www.superclaude.sh)", "<PERSON><PERSON>(superclaude:*)", "Bash(npm uninstall:*)", "Bash(claude --version)", "<PERSON><PERSON>(sh:*)", "Bash(claude --print \"请为这个团购网项目生成一个智能的git commit消息，说明我正在测试SuperClaude功能\")", "Bash(claude --print \"分析这个Vue 3团购网APP项目，生成一个专业的README.md文档，包括项目介绍、技术栈、安装步骤、使用说明等\")", "<PERSON><PERSON>(claude:*)", "Bash(npx:*)", "Bash(npm search:*)", "Bash(npm run save-login:*)", "<PERSON><PERSON>(dir:*)", "Bash(npm:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:docs.saleor.io)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(wsl:*)", "WebFetch(domain:www.docker.com)", "<PERSON><PERSON>(git clone:*)", "Bash(find:*)", "Bash(telnet:*)", "Bash(openssl s_client:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(cmd /c:*)", "Bash(TASKKILL /PID 13280 /F)", "<PERSON><PERSON>(powershell:*)", "Bash(git init:*)", "Bash(if exist node_modules rmdir /s /q node_modules)", "Bash(if exist package-lock.json del package-lock.json)", "Bash(rm:*)", "Bash(where node)", "Bash(where npm)", "Bash(java:*)", "<PERSON><PERSON>(mysql:*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(winget:*)", "<PERSON><PERSON>(echo:*)", "WebFetch(domain:learn.microsoft.com)", "Bash(sc query:*)", "Bash(where java)", "Bash(\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe\" --version)", "Bash(\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe\" -u root -pkzka20220726 -e \"CREATE DATABASE IF NOT EXISTS tgw_pp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\")", "Bash(\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe\" -u root -pkzka20220726 -e \"SHOW DATABASES;\")", "Bash(\"C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\java.exe\" -version)", "WebFetch(domain:maven.apache.org)", "Bash(set JAVA_HOME=\"C:\\Program Files\\Microsoft\\jdk-*********-hotspot\")", "Bash(set PATH=%JAVA_HOME%bin)", "Bash(%PATH%)", "Bash(\"D:\\apache-maven-3.9.11\\bin\\mvn.cmd\" --version)", "Bash(\"D:\\Dapache-maven-3.9.11\\bin\\mvn.cmd\" --version)", "Bash(set JAVA_HOME=C:Program FilesMicrosoftjdk-*********-hotspot)", "Bash(set MAVEN_HOME=D:Dapache-maven-3.9.11)", "Bash(%MAVEN_HOME%bin)", "Bash(\"D:\\Dapache-maven-3.9.11\\bin\\mvn.cmd\" -version)", "Bash(\"D:\\Dapache-maven-3.9.11\\bin\\mvn\" -version)", "Bash(D:/build-project.bat)", "Bash(\"D:\\Dapache-maven-3.9.11\\bin\\mvn.cmd\" clean package -DskipTests)", "Ba<PERSON>(D:build-complete.bat)", "<PERSON><PERSON>(start:*)", "Bash(./mvnw spring-boot:run:*)", "Bash(where mvn)", "Bash(git fetch:*)", "Bash(git ls-tree:*)", "Bash(\"C:\\Program Files\\Java\\jdk-17\\bin\\java.exe\" -jar tgw-pp.jar --spring.profiles.active=dev --server.port=8686)", "Bash(\"D:\\apache-maven-3.9.11\\bin\\mvn.cmd\" clean package -DskipTests)", "Bash(\"D:\\apache-maven-3.9.11\\bin\\mvn\" clean package -DskipTests)", "Bash(D:/build-backend-fixed.bat)", "Bash(cmd //c:*)", "<PERSON><PERSON>(apt:*)", "Bash(apt install:*)", "Bash(systemctl status:*)", "Bash(sudo systemctl start:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo systemctl:*)", "<PERSON><PERSON>(sudo journalctl:*)", "<PERSON><PERSON>(sudo cat:*)", "<PERSON>sh(sudo cp:*)", "Bash(sudo sed:*)", "<PERSON><PERSON>(sudo redis-server:*)", "Bash(sudo chown:*)", "<PERSON>sh(redis-server:*)", "Bash(sudo mysqld:*)", "Bash(sudo tail:*)", "<PERSON><PERSON>(sudo:*)", "Bash(pgrep:*)", "<PERSON><PERSON>(go:*)", "Bash(set HTTP_PROXY=http://127.0.0.1:10810)", "Bash(set HTTPS_PROXY=http://127.0.0.1:10810)", "<PERSON><PERSON>(setx:*)", "<PERSON><PERSON>(net stop:*)", "<PERSON><PERSON>(net start:*)", "Bash(nginx:*)", "Bash(whereis:*)", "Bash(service:*)", "<PERSON><PERSON>(ss -tuln)", "Bash(update-java-alternatives:*)", "Bash(dpkg:*)", "Bash(systemctl start:*)", "Bash(systemctl:*)", "<PERSON><PERSON>(journalctl:*)", "Bash(/usr/lib/jvm/java-1.8.0-openjdk-amd64/bin/java -version)", "Bash(ss:*)", "<PERSON><PERSON>(pkill:*)", "Bash(DESCRIBE t_wallets)", "Bash(ln:*)", "Bash(kill:*)", "Ba<PERSON>(unzip:*)", "Bash(jar:*)", "<PERSON><PERSON>(touch:*)", "Bash(./diagnose-api-issue.sh:*)", "Bash(./start-backend-dev.sh:*)", "<PERSON><PERSON>(env)", "Bash(sysctl:*)"], "deny": []}}