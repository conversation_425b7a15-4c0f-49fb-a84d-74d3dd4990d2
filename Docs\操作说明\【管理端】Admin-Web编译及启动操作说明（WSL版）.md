# 【管理端】Admin-Web编译及启动操作说明（WSL版）

## 项目信息
- **项目路径**: `/mnt/d/Dev/团购网/Server/smart-admin/admin-web`
- **技术栈**: Vue 3 + Vite + Ant Design Vue
- **开发端口**: 5173
- **生产构建目录**: `dist/`

## 环境检查

### 检查Node.js版本
```bash
node --version
npm --version
```

### 安装依赖（首次运行或package.json更新后）
```bash
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web
npm install
```

## 手动编译命令

### 进入项目目录
```bash
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web
```

### 开发环境编译
```bash
# 启动开发服务器（热重载）
npm run dev
```

### 生产环境编译
```bash
# 构建生产版本
npm run build

# 构建并分析包大小
npm run build:analyze

# 预览生产构建
npm run preview
```

## 手动启动命令

### 开发模式启动
```bash
# 进入项目目录
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-web

# 启动开发服务器
npm run dev
```

### 生产模式启动
```bash
# 先构建项目
npm run build

# 预览生产版本
npm run preview
```

## 检查启动状态

### 检查端口5173是否被占用
```bash
ss -tlnp | grep :5173
```

### 检查Node进程
```bash
ps aux | grep node | grep vite
```

### 访问测试
如果启动成功，您可以访问：
- 开发环境：http://localhost:5173
- 预览环境：http://localhost:4173

## 手动停止命令

### 方式1：使用Ctrl+C停止
如果在前台运行，直接按 `Ctrl+C` 停止服务

### 方式2：查找并终止进程
```bash
# 查找Node.js/Vite进程
ps aux | grep node | grep vite

# 终止指定进程（替换PID为实际进程ID）
kill -9 <PID>

# 或者直接终止所有相关进程
pkill -f "vite"
pkill -f "npm run dev"
```

### 方式3：按端口查找并终止
```bash
# 查找占用5173端口的进程
lsof -ti:5173

# 终止占用5173端口的进程
lsof -ti:5173 | xargs kill -9
```

## 配置说明

### 开发环境配置
管理端需要连接到后端API服务：
- **后端API地址**: http://localhost:8686
- **配置文件**: `src/config/app-config.js`

### API代理配置
开发环境下，Vite会代理API请求到后端服务：
```javascript
// vite.config.js 中的代理配置
proxy: {
  '/api': {
    target: 'http://localhost:8686',
    changeOrigin: true
  }
}
```

## 常见问题

### 依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 端口被占用
```bash
# 修改开发端口（在vite.config.js中）
# 或者终止占用端口的进程
lsof -ti:5173 | xargs kill -9
```

### API请求失败
1. 确保后端API服务（端口8686）已启动
2. 检查API代理配置
3. 检查网络连接和防火墙设置

### 热重载不工作
```bash
# 检查文件监听限制
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 部署说明

### 生产环境部署
```bash
# 构建生产版本
npm run build

# 将dist目录部署到Web服务器
# 例如：nginx、apache等
```

### Docker部署（可选）
```bash
# 如果有Dockerfile
docker build -t admin-web .
docker run -p 80:80 admin-web
```