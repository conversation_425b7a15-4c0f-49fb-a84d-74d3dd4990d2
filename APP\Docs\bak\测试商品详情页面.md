# 商品详情页面测试指南

## 快速测试

### 1. 启动开发服务器
```bash
cd APP
npm run dev
```

### 2. 访问测试页面
在浏览器中访问：`http://localhost:5173/product/1`

## 功能测试清单

### 基础功能
- [ ] 页面正常加载
- [ ] 商品图片轮播可以手动切换
- [ ] 商品信息正确显示（价格、销量、评分）
- [ ] 拼团规则说明清晰展示

### 交互功能
- [ ] 点击收藏按钮切换收藏状态
- [ ] 点击分享按钮触发分享功能
- [ ] 点击参团按钮可以参与拼团
- [ ] 点击发起拼团按钮可以创建拼团
- [ ] 点击客服按钮显示相应提示

### 数据展示
- [ ] 正在拼团列表正确显示
- [ ] 用户评价信息正确展示
- [ ] 商品详情图片正常加载
- [ ] Toast消息正常显示

### 导航功能
- [ ] 返回按钮正常工作
- [ ] 跳转到拼团确认页面
- [ ] 查看全部评价/拼团功能

## 测试数据
页面使用Mock数据，包含：
- 商品名称：精选优质纯棉T恤 舒适透气 多色可选 男女同款
- 拼团价格：¥39
- 原价：¥89
- 单独购买价：¥79
- 拼团类型：2人团
- 销量：999+件
- 评分：4.9分 (2.1k评价)

## 预期行为
1. 页面加载时显示加载动画
2. 数据加载完成后显示完整的商品信息
3. 所有按钮点击都有相应的反馈
4. 错误情况下显示友好的错误提示
5. 操作成功时显示成功提示

## 移动端测试
在开发者工具中切换到移动设备模式，验证：
- [ ] 页面布局适配正常
- [ ] 触摸操作响应良好
- [ ] 文字大小合适
- [ ] 按钮尺寸适中 