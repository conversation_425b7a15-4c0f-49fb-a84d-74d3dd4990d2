# 生产环境升级部署说明 - 汇总奖励功能

## 📋 升级概述

**功能名称**: 汇总奖励功能 - 方案1：数据库字段扩展实现
**升级日期**: 2025-07-31
**版本**: v1.1 (汇总奖励功能)
**升级类型**: 功能增强 (非破坏性升级)

## ✅ 实现状态验证

经过全面代码审查，**汇总奖励功能已100%实现并可投产**：

### 数据库层面 ✅
- **字段添加**: `t_wallets.total_earnings` 字段已创建
- **数据类型**: `DECIMAL(15,2) DEFAULT 0.00`
- **现有数据**: 已有用户数据 (User 1: 225.00, User 2: 200.00)

### 后端代码 ✅
- **WalletsEntity.java**: 添加 `totalEarnings` 字段
- **WalletsVO.java**: 添加相应的API返回字段
- **AppOrdersService.java**: 第296行 `REFUND_WIN` 后调用 `updateTotalEarnings`
- **WalletsDao.java**: 添加 `updateTotalEarnings` 方法
- **WalletsMapper.xml**: 实现SQL更新逻辑
- **ProfileService.java**: 用户信息接口返回 `totalEarnings`

### 前端代码 ✅
- **ProfilePage.vue**: "我的"页面显示总收益金额
- **用户体验**: 醒目的黄色渐变背景突出显示
- **API适配**: 正确获取和显示 `totalEarnings` 数据

## 🚀 生产环境部署步骤

### 预备工作

1. **备份当前数据库**
   ```bash
   mysqldump -u root -p tgw_pp > tgw_pp_backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **确认服务状态**
   ```bash
   # 检查服务运行状态
   systemctl status mysql nginx
   # 检查端口监听
   ss -tlnp | grep -E ':(80|443|3306|8080)'
   ```

### 第一步：数据库迁移 (核心步骤)

#### 1.1 开发环境数据库结构导出

**在开发环境执行**：
```bash
# 连接到开发环境数据库
mysql -u root -p'kzka20220726' tgw_pp

# 导出t_wallets表结构（只导出结构，不导出数据）
mysqldump -u root -p'kzka20220726' --no-data --single-transaction tgw_pp t_wallets > t_wallets_structure.sql

# 查看导出的结构文件
cat t_wallets_structure.sql
```

#### 1.2 生成数据库迁移脚本

**创建迁移脚本** `migration_total_earnings.sql`：
```sql
-- ========================================
-- 汇总奖励功能数据库迁移脚本
-- 日期: 2025-07-31
-- 功能: 添加total_earnings字段
-- ========================================

-- 检查字段是否已存在
SET @exist := (SELECT COUNT(*) 
               FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_SCHEMA = DATABASE() 
               AND TABLE_NAME = 't_wallets' 
               AND COLUMN_NAME = 'total_earnings');

-- 只有当字段不存在时才添加
SET @sql := IF(@exist = 0, 
    'ALTER TABLE t_wallets ADD COLUMN total_earnings DECIMAL(15,2) DEFAULT 0.00 COMMENT "总收益（未中奖励汇总）"',
    'SELECT "字段total_earnings已存在，跳过添加" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 't_wallets' 
AND COLUMN_NAME = 'total_earnings';
```

#### 1.3 历史数据初始化脚本

**创建历史数据更新脚本** `init_total_earnings.sql`：
```sql
-- ========================================
-- 初始化所有用户的总收益数据
-- 基于现有的REFUND_WIN交易记录计算
-- ========================================

-- 更新所有用户的total_earnings字段
UPDATE t_wallets w 
SET total_earnings = (
    SELECT IFNULL(SUM(amount), 0) 
    FROM t_wallet_transactions wt 
    WHERE wt.user_id = w.user_id 
    AND wt.type = 'REFUND_WIN'
),
update_time = CURRENT_TIMESTAMP;

-- 显示更新结果
SELECT 
    w.user_id,
    w.total_earnings,
    (SELECT COUNT(*) FROM t_wallet_transactions wt 
     WHERE wt.user_id = w.user_id AND wt.type = 'REFUND_WIN') as refund_win_count,
    w.update_time
FROM t_wallets w 
WHERE w.total_earnings > 0
ORDER BY w.total_earnings DESC;

-- 统计报告
SELECT 
    '总用户数' as metric,
    COUNT(*) as value
FROM t_wallets
UNION ALL
SELECT 
    '有收益用户数' as metric,
    COUNT(*) as value
FROM t_wallets 
WHERE total_earnings > 0
UNION ALL
SELECT 
    '总收益金额' as metric,
    SUM(total_earnings) as value
FROM t_wallets;
```

#### 1.4 生产环境数据库迁移执行

**在生产服务器执行**：

1. **备份生产数据库**
   ```bash
   # 完整备份
   mysqldump -u root -p --single-transaction --routines --triggers tgw_pp > tgw_pp_full_backup_$(date +%Y%m%d_%H%M%S).sql
   
   # 只备份t_wallets表
   mysqldump -u root -p --single-transaction tgw_pp t_wallets > t_wallets_backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **连接生产数据库**
   ```bash
   # 使用生产环境数据库密码连接
   mysql -u root -p tgw_pp
   ```

3. **执行迁移脚本**
   ```bash
   # 方法1: 直接执行SQL文件 (推荐)
   mysql -u root -p tgw_pp < /path/to/migration_total_earnings.sql
   
   # 方法2: 在MySQL命令行中执行
   mysql -u root -p tgw_pp
   # 然后在MySQL中执行:
   # SOURCE /path/to/migration_total_earnings.sql;
   
   # 检查执行结果
   mysql -u root -p tgw_pp -e "DESCRIBE t_wallets;"
   ```

4. **初始化历史数据**
   ```bash
   # 方法1: 直接执行SQL文件 (推荐)
   mysql -u root -p tgw_pp < /path/to/init_total_earnings.sql
   
   # 方法2: 在MySQL命令行中执行
   mysql -u root -p tgw_pp
   # 然后在MySQL中执行:
   # SOURCE /path/to/init_total_earnings.sql;
   
   # 验证数据正确性
   mysql -u root -p tgw_pp -e "SELECT user_id, total_earnings FROM t_wallets WHERE total_earnings > 0 LIMIT 10;"
   ```
   
   **📁 迁移脚本文件位置**：
   - `migration_total_earnings.sql` - 数据库字段添加脚本
   - `init_total_earnings.sql` - 历史数据初始化脚本
   
   这两个文件位于：`/mnt/d/Dev/团购网/Docs/修复与优化/` 目录下

5. **数据一致性验证**
   ```sql
   -- 验证计算准确性
   SELECT 
       w.user_id,
       w.total_earnings as stored_earnings,
       IFNULL(SUM(wt.amount), 0) as calculated_earnings,
       (w.total_earnings - IFNULL(SUM(wt.amount), 0)) as difference
   FROM t_wallets w
   LEFT JOIN t_wallet_transactions wt ON w.user_id = wt.user_id 
       AND wt.type = 'REFUND_WIN'
   GROUP BY w.user_id, w.total_earnings
   HAVING ABS(difference) > 0.01  -- 检查差异大于1分的记录
   ORDER BY ABS(difference) DESC
   LIMIT 10;
   ```

#### 1.5 迁移验证清单

**必须验证的项目**：
- [ ] `total_earnings` 字段已成功添加
- [ ] 字段类型为 `DECIMAL(15,2)`，默认值为 `0.00`
- [ ] 所有现有用户的 `total_earnings` 已正确计算
- [ ] 计算结果与 `REFUND_WIN` 交易记录一致
- [ ] 没有数据丢失或损坏
- [ ] 数据库性能正常，无锁表现象

#### 1.6 迁移回滚脚本 (应急使用)

**如果迁移出现问题，使用此脚本回滚**：
```sql
-- ========================================
-- 紧急回滚脚本 (谨慎使用)
-- ========================================

-- 1. 恢复备份 (推荐方法)
-- mysql -u root -p tgw_pp < tgw_pp_full_backup_YYYYMMDD_HHMMSS.sql

-- 2. 或者只删除新添加的字段 (会丢失total_earnings数据)
-- ALTER TABLE t_wallets DROP COLUMN total_earnings;

-- 验证回滚结果
DESCRIBE t_wallets;
```

### 第二步：后端服务升级

1. **停止后端服务**
   ```bash
   # 根据你的部署方式选择
   systemctl stop your-backend-service
   # 或者
   pkill -f java.*admin-api
   ```

2. **部署新版本**
   ```bash
   # 备份当前版本
   cp -r /path/to/current/backend /path/to/backup/backend_$(date +%Y%m%d)
   
   # 部署新代码
   cp -r /path/to/new/Server/smart-admin/admin-api/* /path/to/production/backend/
   
   # 重新编译 (如果需要)
   cd /path/to/production/backend
   mvn clean package -DskipTests
   ```

3. **启动后端服务**
   ```bash
   systemctl start your-backend-service
   # 检查启动状态
   systemctl status your-backend-service
   ```

### 第三步：前端服务升级

1. **备份当前前端**
   ```bash
   cp -r /path/to/current/frontend /path/to/backup/frontend_$(date +%Y%m%d)
   ```

2. **构建新版本**
   ```bash
   cd /path/to/new/APP
   npm run build
   ```

3. **部署前端文件**
   ```bash
   # 停止nginx (如果需要)
   systemctl stop nginx
   
   # 部署新文件
   cp -r /path/to/new/APP/dist/* /var/www/html/
   # 或者你的nginx根目录
   
   # 启动nginx
   systemctl start nginx
   ```

### 第四步：服务验证

1. **API接口测试**
   ```bash
   # 测试用户信息接口
   curl -X GET "http://your-domain/api/app/profile/info" \
        -H "Authorization: Bearer YOUR_TOKEN"
   
   # 检查返回结果中是否包含totalEarnings字段
   ```

2. **前端功能验证**
   - 访问"我的"页面
   - 确认总收益金额正确显示
   - 验证黄色背景样式正常

3. **数据一致性检查**
   ```sql
   -- 验证总收益计算正确性
   SELECT 
       w.user_id,
       w.total_earnings,
       IFNULL(SUM(wt.amount), 0) as calculated_earnings
   FROM t_wallets w
   LEFT JOIN t_wallet_transactions wt ON w.user_id = wt.user_id 
       AND wt.type = 'REFUND_WIN'
   GROUP BY w.user_id, w.total_earnings;
   ```

## 📊 关键监控指标

### 系统监控
- **数据库连接数**: 正常范围内
- **API响应时间**: /api/app/profile/info < 500ms
- **内存使用**: 后端服务内存稳定
- **CPU使用**: 前后端服务CPU正常

### 业务监控
- **总收益统计准确性**: 与交易记录一致
- **新订单收益更新**: 未中奖后自动更新
- **用户界面显示**: 总收益正确展示

## 🔄 回滚计划

如出现问题，按以下步骤回滚：

### 数据库回滚 (谨慎操作)
```sql
-- 如果需要移除字段 (不推荐，数据会丢失)
-- ALTER TABLE t_wallets DROP COLUMN total_earnings;
```

### 后端回滚
```bash
systemctl stop your-backend-service
cp -r /path/to/backup/backend_YYYYMMDD/* /path/to/production/backend/
systemctl start your-backend-service
```

### 前端回滚
```bash
systemctl stop nginx
cp -r /path/to/backup/frontend_YYYYMMDD/* /var/www/html/
systemctl start nginx
```

## 📝 升级后验证清单

### 数据库验证
- [ ] `total_earnings`字段已成功添加到`t_wallets`表
- [ ] 字段类型为`DECIMAL(15,2)`，默认值`0.00`
- [ ] 所有现有用户的`total_earnings`数据已正确初始化
- [ ] 历史收益计算与`REFUND_WIN`交易记录完全一致
- [ ] 数据库备份文件已安全保存
- [ ] 没有数据丢失或表结构损坏

### 后端服务验证
- [ ] 后端服务正常启动，日志无错误
- [ ] `updateTotalEarnings`方法正常工作
- [ ] 新订单未中奖后总收益正确自动更新
- [ ] API接口返回包含`totalEarnings`字段

### 前端功能验证
- [ ] 前端页面正常访问
- [ ] "我的"页面显示总收益金额
- [ ] 总收益显示样式正确（黄色渐变背景）
- [ ] 数据实时更新正常

### 系统整体验证
- [ ] 服务器资源使用正常
- [ ] 所有原有功能正常运行
- [ ] 新功能与现有功能无冲突
- [ ] 用户体验无异常

## 🚨 注意事项

1. **备份优先**: 升级前必须备份数据库和代码
2. **逐步升级**: 建议先升级后端，测试通过后再升级前端
3. **监控告警**: 升级后持续监控系统状态24小时
4. **用户通知**: 如有计划停机，提前通知用户
5. **数据验证**: 升级完成后验证总收益数据准确性

## 📞 技术支持

- **升级负责人**: 开发团队
- **紧急联系**: 技术负责人
- **监控告警**: 运维团队
- **回滚决策**: 项目经理

---

**升级状态**: ✅ 准备就绪，可立即部署
**风险等级**: 🟢 低风险 (功能增强，非破坏性)
**预计停机时间**: 5-10分钟
**完成时间**: 2025-07-31 制作完成