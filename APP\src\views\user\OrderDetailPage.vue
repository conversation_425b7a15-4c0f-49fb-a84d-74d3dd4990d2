<template>
  <div class="bg-gray-100 min-h-screen">
    <!-- 顶部导航 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-b">
      <iconify-icon 
        icon="material-symbols:arrow-back" 
        class="text-2xl text-gray-700 cursor-pointer" 
        @click="goBack"
      ></iconify-icon>
      <h1 class="text-lg font-bold text-gray-800">订单详情</h1>
      <div class="w-6"></div> <!-- 占位元素保持居中 -->
    </div>

    <!-- 主要内容区域 -->
    <div class="pb-20" v-if="orderDetail">
      
      <!-- 订单状态卡片 -->
      <div class="bg-white mx-4 mt-4 rounded-2xl p-4 shadow-sm">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-2">
            <iconify-icon 
              :icon="getStatusIcon(orderDetail.status)" 
              :class="['text-2xl', getStatusIconColor(orderDetail.status)]"
            ></iconify-icon>
            <div>
              <h2 class="text-lg font-bold text-gray-800">{{ getStatusTitle(orderDetail.status) }}</h2>
              <p class="text-sm text-gray-500">{{ getStatusDescription(orderDetail.status) }}</p>
            </div>
          </div>
          <div class="text-right">
            <span 
              v-if="getStatusText(orderDetail.status)"
              :class="[
                'text-xs px-3 py-1 rounded-full font-medium',
                getStatusBadgeClass(orderDetail.status)
              ]"
            >
              {{ getStatusText(orderDetail.status) }}
            </span>
          </div>
        </div>
        
        <!-- 时间信息 -->
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">下单时间：</span>
              <span class="text-gray-800">{{ formatTime(orderDetail.createTime) }}</span>
            </div>
            <div v-if="orderDetail.paymentTime" class="flex justify-between">
              <span class="text-gray-600">支付时间：</span>
              <span class="text-gray-800">{{ formatTime(orderDetail.paymentTime) }}</span>
            </div>
            <div v-if="!isDirectBuyOrder(orderDetail) && orderDetail.drawTime" class="flex justify-between">
              <span class="text-gray-600">开奖时间：</span>
              <span class="text-gray-800">{{ formatTime(orderDetail.drawTime) }}</span>
            </div>
            <div v-if="orderDetail.completeTime" class="flex justify-between">
              <span class="text-gray-600">完成时间：</span>
              <span class="text-gray-800">{{ formatTime(orderDetail.completeTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单信息卡片 -->
      <div class="bg-white mx-4 mt-4 rounded-2xl p-4 shadow-sm">
        <h3 class="text-lg font-bold text-gray-800 mb-3">订单信息</h3>
        
        <div class="space-y-3 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">订单号：</span>
            <span class="text-gray-800 font-mono">{{ orderDetail.orderSn }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">订单类型：</span>
            <span :class="getOrderTypeClass(orderDetail)">
              {{ getOrderTypeText(orderDetail) }}
            </span>
          </div>
          
          <div class="flex justify-between" v-if="orderDetail.activityId && !isDirectBuyOrder(orderDetail)">
            <span class="text-gray-600">拼团类型：</span>
            <span :class="getGroupTypeClass(orderDetail.activityId)">
              {{ getGroupTypeText(orderDetail.activityId) }}
            </span>
          </div>
          
          <div class="flex justify-between" v-if="!isDirectBuyOrder(orderDetail) && orderDetail.drawResult !== null && orderDetail.status === 'COMPLETED'">
            <span class="text-gray-600">拼团结果：</span>
            <span :class="orderDetail.drawResult === 1 ? 'text-green-600 font-bold' : 'text-gray-600'">
              {{ orderDetail.drawResult === 1 ? '已拼中' : '未拼中' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 商品信息卡片 -->
      <div class="bg-white mx-4 mt-4 rounded-2xl p-4 shadow-sm">
        <h3 class="text-lg font-bold text-gray-800 mb-3">商品信息</h3>
        
        <div class="flex">
          <div 
            class="w-20 h-20 bg-cover bg-center rounded-lg mr-4 bg-gray-200"
            :style="getProductImageStyle(orderDetail)"
          ></div>
          
          <div class="flex-1">
            <h4 class="font-medium text-gray-800 mb-2">{{ getProductName(orderDetail) }}</h4>
            <div class="text-sm text-gray-500 mb-2">商品ID: {{ orderDetail.goodsId }}</div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-red-500 font-bold text-lg">{{ formatPrice(getOrderPrice(orderDetail)) }}</span>
                <span v-if="orderDetail.sku?.originalPrice" class="text-gray-400 text-sm line-through">
                  {{ formatPrice(orderDetail.sku.originalPrice) }}
                </span>
              </div>
              <span class="text-gray-600 text-sm">x1</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 支付信息卡片 -->
      <div class="bg-white mx-4 mt-4 rounded-2xl p-4 shadow-sm">
        <h3 class="text-lg font-bold text-gray-800 mb-3">支付信息</h3>
        
        <div class="space-y-3 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">支付方式：</span>
            <span class="text-gray-800">{{ getPaymentMethodText(orderDetail.paymentMethod) }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">商品金额：</span>
            <span class="text-gray-800">{{ formatPrice(getOrderPrice(orderDetail)) }}</span>
          </div>
          
          <div class="flex justify-between" v-if="orderDetail.experiencePaid > 0">
            <span class="text-gray-600">体验金抵扣：</span>
            <span class="text-green-600">-{{ formatPrice(orderDetail.experiencePaid) }}</span>
          </div>
          
          <div class="flex justify-between" v-if="orderDetail.subsidyPaid > 0">
            <span class="text-gray-600">补贴金额：</span>
            <span class="text-green-600">-{{ formatPrice(orderDetail.subsidyPaid) }}</span>
          </div>
          
          <!-- 退单信息显示 -->
          <div v-if="orderDetail.status === 'REFUNDED'" class="bg-orange-50 border border-orange-200 rounded-lg p-3 mt-3">
            <div class="flex items-center mb-2">
              <iconify-icon icon="material-symbols:info" class="text-orange-600 text-sm mr-1"></iconify-icon>
              <span class="text-sm font-medium text-orange-700">退单信息</span>
            </div>
            <div class="space-y-2 text-xs">
              <div class="flex justify-between">
                <span class="text-gray-600">原订单金额：</span>
                <span class="text-gray-800">{{ formatPrice(getOrderPrice(orderDetail)) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">违约金 ({{ getBreachFeeRate() }}%)：</span>
                <span class="text-red-600">-{{ formatPrice(getBreachFeeAmount(orderDetail)) }}</span>
              </div>
              <div class="flex justify-between font-medium border-t border-orange-200 pt-2">
                <span class="text-orange-700">实际退款：</span>
                <span class="text-green-600">{{ formatPrice(getRefundAmount(orderDetail)) }}</span>
              </div>
              <div v-if="orderDetail.refundTime" class="flex justify-between">
                <span class="text-gray-600">退单时间：</span>
                <span class="text-gray-800">{{ formatTime(orderDetail.refundTime) }}</span>
              </div>
              <div v-if="orderDetail.refundReason" class="border-t border-orange-200 pt-2">
                <span class="text-gray-600">退单原因：</span>
                <span class="text-gray-800 block mt-1">{{ orderDetail.refundReason }}</span>
              </div>
            </div>
          </div>
          
          <div class="border-t pt-3 mt-3">
            <div class="flex justify-between font-bold">
              <span class="text-gray-800">{{ getPaymentAmountLabel(orderDetail) }}：</span>
              <span class="text-red-500 text-lg">{{ formatPrice(getOrderPrice(orderDetail)) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 收货地址卡片 -->
      <div class="bg-white mx-4 mt-4 rounded-2xl p-4 shadow-sm" v-if="orderDetail.shippingAddressId">
        <h3 class="text-lg font-bold text-gray-800 mb-3">收货地址</h3>
        <div class="text-sm text-gray-700">
          <div v-if="getShippingAddress(orderDetail)" class="space-y-2">
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:person" class="text-gray-500 mr-2"></iconify-icon>
              <span class="font-medium">{{ getShippingAddress(orderDetail).name }}</span>
              <span class="ml-2 text-gray-500">{{ getShippingAddress(orderDetail).phone }}</span>
            </div>
            <div class="flex items-start">
              <iconify-icon icon="material-symbols:location-on" class="text-gray-500 mr-2 mt-0.5"></iconify-icon>
              <span class="flex-1">{{ getShippingAddress(orderDetail).fullAddress }}</span>
            </div>
          </div>
          <div v-else class="text-gray-500">
            <div class="flex items-center mb-2">
              <iconify-icon icon="material-symbols:info" class="mr-2"></iconify-icon>
              <span>正在获取地址信息...</span>
            </div>
            <p class="text-xs">地址ID: {{ orderDetail.shippingAddressId }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">正在加载订单详情...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="flex items-center justify-center h-64">
      <div class="text-center">
        <iconify-icon icon="material-symbols:error" class="text-gray-400 text-6xl mb-4"></iconify-icon>
        <p class="text-gray-500 text-sm mb-4">订单数据获取失败</p>
        <button 
          class="bg-blue-500 text-white py-2 px-4 rounded-full text-sm hover:bg-blue-600 transition-colors"
          @click="goBack"
        >
          返回订单列表
        </button>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <BottomNav current="user" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError } from '@/utils/message.js'
import BottomNav from '@/components/common/BottomNav.vue'
import { getImageUrl } from '@/config/image'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { formatPrice, getGroupTypeText } from '@/utils/format'
import { useQuickAuthCheck } from '@/composables/useAuthGuard'

const router = useRouter()
const route = useRoute()

// 快速登录检查
const { isAuthenticated, requireAuth } = useQuickAuthCheck()

// 页面状态
const loading = ref(false)
const orderDetail = ref(null)
const shippingAddresses = ref([])

// API实例
let apiAdapter = null

// 初始化API适配器
const initApiAdapter = async () => {
  if (!apiAdapter) {
    apiAdapter = new StandardApiAdapter()
  }
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  return new Date(timeString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化价格 - 使用统一的越南盾格式化函数

// 获取订单价格
const getOrderPrice = (order) => {
  // 如果是直接购买订单，使用pointsPaid
  if (isDirectBuyOrder(order)) {
    return order.pointsPaid || order.amountPaid || 0
  }
  // 拼团订单使用amountPaid
  return order.amountPaid || 0
}

// 获取支付金额标签文本
const getPaymentAmountLabel = (order) => {
  // 如果是直接购买订单，需要根据实际支付金额判断
  if (isDirectBuyOrder(order)) {
    // 如果积分支付金额大于0，说明是积分支付
    if (order.pointsPaid > 0 && order.amountPaid === 0) {
      return '实付积分'
    }
  }
  return '实付金额'
}

// 价格显示现在使用统一的越南盾格式化函数

// 判断是否为直接购买订单
const isDirectBuyOrder = (order) => {
  return order.aloneFlag === true || order.aloneFlag === 1
}

// 获取订单类型文本
const getOrderTypeText = (order) => {
  return isDirectBuyOrder(order) ? '直接购买' : '拼团购买'
}

// 获取订单类型样式
const getOrderTypeClass = (order) => {
  return isDirectBuyOrder(order)
    ? 'text-blue-600 font-medium' 
    : 'text-orange-600 font-medium'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const icons = {
    PENDING: 'material-symbols:hourglass-empty',
    COMPLETED: 'material-symbols:check-circle',
    FAILED: 'material-symbols:cancel',
    SHIPPING: 'material-symbols:local-shipping',
    EXPIRED: 'material-symbols:schedule',
    REFUNDED: 'material-symbols:undo'
  }
  return icons[status] || 'material-symbols:help'
}

// 获取状态图标颜色
const getStatusIconColor = (status) => {
  const colors = {
    PENDING: 'text-blue-500',
    COMPLETED: 'text-green-500',
    FAILED: 'text-gray-500',
    SHIPPING: 'text-purple-500',
    EXPIRED: 'text-red-500',
    REFUNDED: 'text-orange-500'
  }
  return colors[status] || 'text-gray-500'
}

// 获取状态标题
const getStatusTitle = (status) => {
  const titles = {
    PENDING: '订单进行中',
    COMPLETED: '订单已完成',
    FAILED: '订单未成功',
    SHIPPING: '商品配送中',
    EXPIRED: '订单已过期',
    REFUNDED: '订单已退单'
  }
  return titles[status] || '未知状态'
}

// 获取状态描述
const getStatusDescription = (status) => {
  // 根据订单类型返回不同的状态描述
  if (isDirectBuyOrder(orderDetail.value)) {
    const directBuyDescriptions = {
      PENDING: '订单支付成功，正在处理中',
      COMPLETED: '订单已成功完成',
      FAILED: '订单处理失败，款项已退回',
      SHIPPING: '商品正在配送途中',
      EXPIRED: '订单已超时失效',
      REFUNDED: '已申请退单，扣除违约金后退款已到账'
    }
    return directBuyDescriptions[status] || ''
  } else {
    const groupBuyDescriptions = {
      PENDING: '请耐心等待开奖结果',
      COMPLETED: '订单已成功完成',
      FAILED: '恭喜！获得补偿奖励红包，本金已退回',
      SHIPPING: '商品正在配送途中',
      EXPIRED: '订单已超时失效',
      REFUNDED: '已申请退单，扣除违约金后退款已到账'
    }
    return groupBuyDescriptions[status] || ''
  }
}

// 获取状态文本
const getStatusText = (status) => {
  // 根据订单类型返回不同的状态文本
  if (isDirectBuyOrder(orderDetail.value)) {
    const directBuyTexts = {
      PENDING: '处理中',
      COMPLETED: '已完成',
      FAILED: '已失败',
      SHIPPING: '待收货',
      EXPIRED: '已失效',
      CANCELLED: '已取消',
      REFUNDED: '' // 移除"已退单"文字，不显示状态标签
    }
    return directBuyTexts[status] !== undefined ? directBuyTexts[status] : '未知'
  } else {
    const groupBuyTexts = {
      PENDING: '待开奖',
      COMPLETED: '已完成',
      FAILED: '获奖励',
      SHIPPING: '待收货',
      EXPIRED: '已失效',
      CANCELLED: '已取消',
      REFUNDED: '' // 移除"已退单"文字，不显示状态标签
    }
    return groupBuyTexts[status] !== undefined ? groupBuyTexts[status] : '未知'
  }
}

// 获取状态徽章样式
const getStatusBadgeClass = (status) => {
  const classes = {
    PENDING: 'bg-blue-100 text-blue-600',
    COMPLETED: 'bg-green-100 text-green-600',
    FAILED: 'bg-gray-100 text-gray-600',
    SHIPPING: 'bg-purple-100 text-purple-600',
    EXPIRED: 'bg-red-100 text-red-600',
    CANCELLED: 'bg-gray-100 text-gray-600',
    REFUNDED: 'bg-orange-100 text-orange-600'
  }
  return classes[status] || 'bg-gray-100 text-gray-600'
}

// 获取团类型样式
const getGroupTypeClass = (activityId) => {
  const classes = {
    3: 'text-orange-500 font-medium',
    4: 'text-red-500 font-medium',
    5: 'text-purple-500 font-medium',
    6: 'text-green-500 font-medium',
    7: 'text-blue-500 font-medium',
    8: 'text-pink-500 font-medium'
  }
  return classes[activityId] || 'text-gray-500 font-medium'
}

// 拼团类型文本现在使用统一的工具函数

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  // 支付方式映射 - 根据实际后端返回的值调整
  const methods = {
    '0': '余额支付',
    '1': '支付宝',
    '2': '微信支付',
    '3': '银行卡支付',
    'wechat': '微信支付',
    'alipay': '支付宝',
    'balance': '余额支付',
    'bank': '银行卡支付'
  }
  
  // 处理数字和字符串类型
  const methodKey = String(method)
  const paymentText = methods[methodKey] || methods[method]
  
  if (paymentText) {
    return paymentText
  }
  
  // 如果都没匹配到，显示原始值和调试信息
  console.log('🔍 未识别的支付方式:', method, typeof method)
  return `支付方式: ${method}`
}

// 获取商品名称
const getProductName = (order) => {
  // 优先从缓存的商品数据中获取
  try {
    const homeData = localStorage.getItem('homeData')
    if (homeData) {
      const data = JSON.parse(homeData)
      if (data.products && Array.isArray(data.products)) {
        const cachedProduct = data.products.find(product => 
          product.goodsId === order.goodsId
        )
        if (cachedProduct) {
          return cachedProduct.goodsName || cachedProduct.name || cachedProduct.title
        }
      }
    }
  } catch (error) {
    console.warn('⚠️ 从缓存获取商品名称失败:', error)
  }
  
  // 备用：从订单中获取
  return order.sku?.goodsName || order.goodsName || `商品 ${order.goodsId}`
}

// 获取商品规格
const getProductSpec = (order) => {
  return order.sku?.spec || '标准规格'
}

// 获取商品图片样式
const getProductImageStyle = (order) => {
  let imageUrl = null
  
  // 优先从缓存的商品数据中获取图片
  try {
    const homeData = localStorage.getItem('homeData')
    if (homeData) {
      const data = JSON.parse(homeData)
      if (data.products && Array.isArray(data.products)) {
        const cachedProduct = data.products.find(product => 
          product.goodsId === order.goodsId
        )
        if (cachedProduct) {
          imageUrl = cachedProduct.image || cachedProduct.imageUrl || cachedProduct.mainImage
        }
      }
    }
  } catch (error) {
    console.warn('⚠️ 从缓存获取商品图片失败:', error)
  }
  
  // 备用：从订单中获取
  if (!imageUrl) {
    imageUrl = order.sku?.image || order.image
  }
  
  if (imageUrl) {
    const fullUrl = getImageUrl(imageUrl)
    return `background-image: url('${fullUrl}')`
  }
  return 'background-color: #f3f4f6'
}

// 获取收货地址信息
const getShippingAddress = (order) => {
  if (!order?.shippingAddressId) return null
  
  // 从本地缓存中查找地址信息
  const address = shippingAddresses.value.find(addr => 
    addr.id === order.shippingAddressId
  )
  
  if (address) {
    return {
      name: address.recipientName || address.receiverName || address.name || '收货人',
      phone: address.phoneNumber || address.receiverPhone || address.phone || '',
      fullAddress: getFullAddressText(address)
    }
  }
  
  return null
}

// 获取完整地址文本（参考AddressPage的实现）
const getFullAddressText = (address) => {
  const parts = []
  if (address.province) parts.push(address.province)
  if (address.city) parts.push(address.city)
  if (address.district) parts.push(address.district)
  if (address.addressLine) parts.push(address.addressLine)
  if (!address.addressLine && address.address) parts.push(address.address)
  if (!address.addressLine && !address.address && address.detailAddress) parts.push(address.detailAddress)
  return parts.join(' ')
}

// 获取违约金比例（默认10%）
const getBreachFeeRate = () => {
  // 可以从订单数据中获取，或者使用默认值
  return orderDetail.value?.breachFeeRate || 10
}

// 计算违约金金额
const getBreachFeeAmount = (order) => {
  if (!order || order.status !== 'REFUNDED') return 0
  
  // 如果订单中直接包含违约金金额，使用该值
  if (order.breachFeeAmount !== undefined && order.breachFeeAmount !== null) {
    return order.breachFeeAmount
  }
  
  // 否则根据比例计算
  const orderAmount = getOrderPrice(order)
  const feeRate = getBreachFeeRate() / 100
  return Math.round(orderAmount * feeRate)
}

// 计算实际退款金额
const getRefundAmount = (order) => {
  if (!order || order.status !== 'REFUNDED') return 0
  
  // 如果订单中直接包含退款金额，使用该值
  if (order.refundAmount !== undefined && order.refundAmount !== null) {
    return order.refundAmount
  }
  
  // 否则根据公式计算：订单金额 - 违约金
  const orderAmount = getOrderPrice(order)
  const breachFee = getBreachFeeAmount(order)
  return Math.max(0, orderAmount - breachFee)
}

// 加载收货地址数据
const loadShippingAddresses = async () => {
  try {
    // 检查登录状态
    const authStore = useAuthStore()
    if (!authStore.isLoggedIn || !authStore.token) {
      console.warn('❌ 未登录，无法获取收货地址')
      return
    }
    
    console.log('🔄 获取收货地址列表...')
    
    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${authStore.token}`
    }
    
    // 调用地址列表API - 使用与AddressPage相同的接口
    const response = await fetch('/api/v1/address', {
      method: 'GET',
      headers: headers,
      mode: 'cors',
      credentials: 'omit'
    })
    
    const result = await response.json()
    console.log('📍 收货地址API响应:', result)
    
    if (response.ok && result.code === 0) {
      const data = result.data || {}
      shippingAddresses.value = data.list || []
      console.log('✅ 收货地址数据加载成功:', shippingAddresses.value.length, '个地址')
    } else {
      console.warn('⚠️ 收货地址获取失败:', result.message || result.msg)
    }
    
  } catch (error) {
    console.error('❌ 加载收货地址失败:', error)
    // 不显示错误提示，因为这不是关键功能
  }
}

// 获取订单详情数据
const getOrderDetail = async () => {
  const orderId = route.params.id
  let orderData = null
  
  console.log('🔍 开始获取订单详情，订单ID:', orderId)
  
  // 方式1: 从路由state中获取
  if (history.state?.orderData) {
    orderData = history.state.orderData
    console.log('✅ 从history.state获取订单数据')
  } else {
    console.log('❌ history.state中没有orderData:', history.state)
  }
  
  // 方式2: 从路由meta中获取
  if (!orderData && route.meta?.orderData) {
    orderData = route.meta.orderData
    console.log('✅ 从route.meta获取订单数据')
  } else if (!orderData) {
    console.log('❌ route.meta中没有orderData:', route.meta)
  }
  
  // 方式3: 从sessionStorage中获取
  if (!orderData) {
    const storageKey = `orderDetail_${orderId}`
    const storedData = sessionStorage.getItem(storageKey)
    console.log('🔍 尝试从sessionStorage获取数据，key:', storageKey, 'data:', storedData)
    
    if (storedData) {
      try {
        orderData = JSON.parse(storedData)
        console.log('✅ 从sessionStorage获取订单数据成功')
      } catch (error) {
        console.error('❌ sessionStorage数据解析失败:', error)
      }
    } else {
      console.log('❌ sessionStorage中没有找到数据')
    }
  }
  
  // 方式4: 如果所有方式都失败，尝试从订单列表页面的缓存中查找
  if (!orderData) {
    console.log('🔄 尝试从订单列表缓存中查找数据...')
    try {
      // 尝试从localStorage中的订单列表数据中查找
      const ordersData = localStorage.getItem('allOrders')
      if (ordersData) {
        const orders = JSON.parse(ordersData)
        orderData = orders.find(order => 
          String(order.id) === String(orderId) || 
          String(order.orderSn) === String(orderId)
        )
        if (orderData) {
          console.log('✅ 从localStorage的订单列表中找到数据')
        }
      }
    } catch (error) {
      console.error('❌ 从localStorage获取订单数据失败:', error)
    }
  }
  
  // 方式5: 最后的备选方案 - 重新调用API
  if (!orderData) {
    console.log('🚨 所有本地数据获取方式都失败，准备调用API...')
    await loadOrderFromAPI(orderId)
    return
  }
  
  if (orderData) {
    orderDetail.value = orderData
    console.log('✅ 订单详情数据设置成功:', orderDetail.value)
    
    // 清理sessionStorage中的临时数据
    const storageKey = `orderDetail_${orderId}`
    sessionStorage.removeItem(storageKey)
    console.log('🗑️ 已清理sessionStorage中的临时数据:', storageKey)
    
    return
  }
}

// 从API加载订单数据的备选方案
const loadOrderFromAPI = async (orderId) => {
  try {
    loading.value = true
    await initApiAdapter()
    
    console.log('🔄 开始从API加载订单详情:', orderId)
    
    // 首先尝试获取所有订单，然后找到对应的订单
    const response = await apiAdapter.request('GET', '/orders', { status: 'all' })
    
    if (response.code === 0 || response.code === 200) {
      const orders = response.data?.list || []
      const targetOrder = orders.find(order => 
        String(order.id) === String(orderId) || 
        String(order.orderSn) === String(orderId)
      )
      
      if (targetOrder) {
        orderDetail.value = targetOrder
        console.log('✅ 从API获取订单详情成功:', targetOrder)
        
        // 同时更新localStorage缓存，避免下次再次调用API
        localStorage.setItem('allOrders', JSON.stringify(orders))
        console.log('💾 已更新订单列表缓存')
      } else {
        throw new Error('未找到对应的订单')
      }
    } else {
      throw new Error(response.message || response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('❌ 从API加载订单详情失败:', error)
    showError('订单详情加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 页面初始化
onMounted(async () => {
  console.log('🎯 OrderDetailPage mounted, orderId:', route.params.id)
  await getOrderDetail()
  
  // 如果订单有收货地址，加载地址信息
  if (orderDetail.value?.shippingAddressId) {
    await loadShippingAddresses()
  }
})
</script>

<style scoped>
/* 自定义样式 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 卡片悬停效果 */
.shadow-sm:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>