<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.wallets.dao.WalletTransactionsDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_wallet_transactions.id,
        t_wallet_transactions.user_id,
        t_wallet_transactions.amount,
        t_wallet_transactions.balance_after,
        t_wallet_transactions.experience_balance_after,
        t_wallet_transactions.points_after,
        t_wallet_transactions.mode,
        t_wallet_transactions.type,
        t_wallet_transactions.related_id,
        t_wallet_transactions.related_user_id,
        t_wallet_transactions.description,
        t_wallet_transactions.create_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.wallets.domain.vo.WalletTransactionsVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_wallet_transactions
        <where>
            <!--用户ID-->
            <if test="queryForm.userId != null">
                AND t_wallet_transactions.user_id = #{queryForm.userId}
            </if>
            <!--用户ID-->
            <if test="queryForm.type != null">
                AND t_wallet_transactions.type = #{queryForm.type}
            </if>
        </where>
    </select>

    <select id="getUserReward" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(t_wallet_transactions.amount),0)
        FROM t_wallet_transactions
        WHERE t_wallet_transactions.user_id=#{userId}
          AND t_wallet_transactions.type in ("FIRST_REWARD","TEAM_REWARD")
    </select>

    <select id="getUserContribution" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(t_wallet_transactions.amount),0)
        FROM t_wallet_transactions
        WHERE t_wallet_transactions.related_user_id=#{userId}
          AND t_wallet_transactions.type in ("FIRST_REWARD","TEAM_REWARD")
    </select>

    <select id="getUserConsume" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(t_wallet_transactions.amount),0)
        FROM t_wallet_transactions
        WHERE t_wallet_transactions.user_id=#{userId}
          AND mode=0
          AND t_wallet_transactions.type in ("PAYMENT")
    </select>

</mapper>
