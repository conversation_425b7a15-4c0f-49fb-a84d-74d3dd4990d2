# 拼团系统对接Smart-Admin认证方案

## 概述

基于拼团项目的产品需求和Smart-Admin的Sa-Token认证体系，设计一套完整的用户认证对接方案，实现C端用户和B端管理员的统一认证管理。

## 1. 用户角色映射

### 1.1 拼团系统用户角色
根据需求文档，拼团系统包含以下用户角色：

| 拼团系统角色 | 描述 | Smart-Admin映射 |
|-------------|------|----------------|
| 消费者(未登录) | 浏览商品，无法下单 | 匿名用户 |
| 消费者(已登录) | 完整购物功能 | customer用户 |
| 平台管理员 | 后台管理系统 | admin/super_admin |

### 1.2 接口分离策略

**未登录用户可访问的公开接口：**
- `/api/app/public/**` - 公开信息接口
- `/api/app/auth/login/**` - 登录相关接口
- `/api/app/auth/register` - 注册接口
- `/api/app/products/**` - 商品浏览接口（只读）

**已登录用户专用接口：**
- `/api/app/user/**` - 用户中心功能
- `/api/app/group/**` - 拼团功能
- `/api/app/order/**` - 订单管理
- `/api/app/wallet/**` - 钱包功能

**管理员专用接口：**
- `/api/admin/**` - 后台管理功能

## 2. 数据库设计方案

### 2.1 用户表扩展

```sql
-- 创建拼团APP用户表
CREATE TABLE `app_users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_id` bigint NULL COMMENT '关联Smart-Admin员工ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `country_code` varchar(10) DEFAULT '+84' COMMENT '国家区号',
  `nickname` varchar(50) NULL COMMENT '昵称',
  `avatar` varchar(500) NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date NULL COMMENT '生日',
  `user_type` enum('customer','vip_customer','admin','super_admin') DEFAULT 'customer' COMMENT '用户类型',
  `user_status` tinyint DEFAULT 1 COMMENT '用户状态：0-禁用，1-正常',
  `register_source` varchar(20) DEFAULT 'app' COMMENT '注册来源',
  `last_login_time` datetime NULL COMMENT '最后登录时间',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `total_consumed` decimal(10,2) DEFAULT 0.00 COMMENT '累计消费',
  `invitation_code` varchar(20) NULL COMMENT '邀请码',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`, `country_code`),
  UNIQUE KEY `uk_employee_id` (`employee_id`)
) COMMENT='拼团APP用户表';
```

### 2.2 权限设计

```sql
-- 拼团系统权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, permission_code) VALUES
-- C端用户权限
('APP用户中心', 1, 0, 1, 'app:user'),
('个人资料', 2, 1, 1, 'app:user:profile'),
('我的订单', 2, 1, 2, 'app:user:orders'),
('我的收藏', 2, 1, 3, 'app:user:favorites'),
('我的钱包', 2, 1, 4, 'app:user:wallet'),

-- 拼团功能权限
('拼团功能', 1, 0, 2, 'app:group'),
('发起拼团', 2, 2, 1, 'app:group:create'),
('参与拼团', 2, 2, 2, 'app:group:join'),
('分享拼团', 2, 2, 3, 'app:group:share'),

-- B端管理权限
('拼团管理', 1, 0, 3, 'admin:group'),
('用户管理', 2, 3, 1, 'admin:user:manage'),
('商品管理', 2, 3, 2, 'admin:product:manage'),
('订单管理', 2, 3, 3, 'admin:order:manage'),
('营销管理', 2, 3, 4, 'admin:marketing:manage'),
('幸运拼团', 2, 3, 5, 'admin:lucky:manage');
```

## 3. 后端接口设计

### 3.1 公开接口（无需登录）

```java
@RestController
@RequestMapping("/api/app/public")
public class AppPublicController {
    
    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    public ResponseResult sendSmsCode(@RequestBody SendSmsRequest request) {
        // 发送验证码逻辑
        return ResponseResult.success("验证码发送成功");
    }
    
    /**
     * 商品列表（游客可浏览）
     */
    @GetMapping("/products")
    public ResponseResult getProducts(@RequestParam(defaultValue = "1") int page,
                                    @RequestParam(defaultValue = "20") int size) {
        // 返回商品列表，但不显示用户相关信息（如收藏状态）
        return ResponseResult.success(productService.getPublicProductList(page, size));
    }
    
    /**
     * 商品详情（游客可查看）
     */
    @GetMapping("/products/{productId}")
    public ResponseResult getProductDetail(@PathVariable Long productId) {
        // 返回商品详情，但隐藏用户相关功能
        return ResponseResult.success(productService.getPublicProductDetail(productId));
    }
    
    /**
     * 获取应用配置信息
     */
    @GetMapping("/config")
    public ResponseResult getAppConfig() {
        // 返回APP公开配置信息
        return ResponseResult.success(configService.getPublicConfig());
    }
}
```

### 3.2 认证接口

```java
@RestController
@RequestMapping("/api/app/auth")
public class AppAuthController {
    
    @Autowired
    private AppAuthService appAuthService;
    
    /**
     * 手机号登录/注册（公开接口）
     */
    @PostMapping("/login/phone")
    public ResponseResult loginByPhone(@RequestBody PhoneLoginRequest request) {
        return appAuthService.loginByPhone(request);
    }
    
    /**
     * 第三方授权登录（公开接口）
     */
    @PostMapping("/login/oauth")
    public ResponseResult oauthLogin(@RequestBody OAuthLoginRequest request) {
        return appAuthService.oauthLogin(request);
    }
    
    /**
     * 刷新Token（需要登录）
     */
    @PostMapping("/refresh")
    @SaCheckLogin
    public ResponseResult refreshToken() {
        StpUtil.renewTimeout(7 * 24 * 60 * 60); // 续期7天
        return ResponseResult.success(StpUtil.getTokenInfo());
    }
    
    /**
     * 退出登录（需要登录）
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public ResponseResult logout() {
        StpUtil.logout();
        return ResponseResult.success("退出成功");
    }
}
```

### 3.3 用户功能接口（需要登录）

```java
@RestController
@RequestMapping("/api/app/user")
@SaCheckLogin  // 整个控制器都需要登录
public class AppUserController {
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @SaCheckPermission("app:user:profile")
    public ResponseResult getUserProfile() {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser user = appUserService.getById(userId);
        return ResponseResult.success(user);
    }
    
    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @SaCheckPermission("app:user:profile")
    public ResponseResult updateProfile(@RequestBody UpdateProfileRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        appUserService.updateProfile(userId, request);
        return ResponseResult.success("更新成功");
    }
    
    /**
     * 我的订单列表
     */
    @GetMapping("/orders")
    @SaCheckPermission("app:user:orders")
    public ResponseResult getMyOrders(@RequestParam(required = false) String status,
                                    @RequestParam(defaultValue = "1") int page) {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(orderService.getUserOrders(userId, status, page));
    }
    
    /**
     * 我的钱包
     */
    @GetMapping("/wallet")
    @SaCheckPermission("app:user:wallet")
    public ResponseResult getWallet() {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(walletService.getUserWallet(userId));
    }
    
    /**
     * 收藏商品（需要登录才能收藏）
     */
    @PostMapping("/favorites/{productId}")
    @SaCheckPermission("app:user:favorites")
    public ResponseResult addFavorite(@PathVariable Long productId) {
        Long userId = StpUtil.getLoginIdAsLong();
        favoriteService.addFavorite(userId, productId);
        return ResponseResult.success("收藏成功");
    }
}
```

### 3.4 拼团功能接口（需要登录）

```java
@RestController
@RequestMapping("/api/app/group")
@SaCheckLogin
public class AppGroupController {
    
    /**
     * 发起拼团
     */
    @PostMapping("/create")
    @SaCheckPermission("app:group:create")
    public ResponseResult createGroup(@RequestBody CreateGroupRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(groupService.createGroup(userId, request));
    }
    
    /**
     * 参与拼团
     */
    @PostMapping("/join/{groupId}")
    @SaCheckPermission("app:group:join")
    public ResponseResult joinGroup(@PathVariable Long groupId) {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(groupService.joinGroup(userId, groupId));
    }
    
    /**
     * 参与幸运拼团
     */
    @PostMapping("/lucky/join/{activityId}")
    @SaCheckPermission("app:group:join")
    public ResponseResult joinLuckyGroup(@PathVariable Long activityId) {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(luckyGroupService.joinLuckyGroup(userId, activityId));
    }
    
    /**
     * 我的拼团
     */
    @GetMapping("/my")
    @SaCheckPermission("app:group:join")
    public ResponseResult getMyGroups(@RequestParam(defaultValue = "1") int page) {
        Long userId = StpUtil.getLoginIdAsLong();
        return ResponseResult.success(groupService.getUserGroups(userId, page));
    }
}
```

## 4. 认证服务实现

### 4.1 认证服务

```java
@Service
@Slf4j
public class AppAuthService {
    
    @Autowired
    private AppUserService appUserService;
    
    @Autowired
    private SmsService smsService;
    
    /**
     * 手机号验证码登录/注册
     */
    public ResponseResult<LoginResponse> loginByPhone(PhoneLoginRequest request) {
        try {
            // 1. 验证验证码
            if (!smsService.verifyCode(request.getPhone(), request.getVerificationCode())) {
                return ResponseResult.error(40002, "验证码错误或已过期");
            }
            
            // 2. 查找或创建用户
            AppUser user = appUserService.findOrCreateByPhone(
                request.getCountryCode(), 
                request.getPhone()
            );
            
            // 3. 检查用户状态
            if (user.getUserStatus() == 0) {
                return ResponseResult.error(40003, "账户已被禁用，请联系客服");
            }
            
            // 4. 执行Sa-Token登录
            StpUtil.login(user.getId(), SaLoginConfig
                .setTimeout(7 * 24 * 60 * 60) // 7天
                .setDevice("app")
                .setExtra("userType", user.getUserType())
                .setExtra("phone", user.getPhone())
            );
            
            // 5. 更新登录信息
            appUserService.updateLoginInfo(user.getId(), request.getClientIp());
            
            // 6. 构建响应
            LoginResponse response = LoginResponse.builder()
                .token(StpUtil.getTokenValue())
                .tokenName(StpUtil.getTokenName())
                .expiresIn(StpUtil.getTokenTimeout())
                .user(buildUserResponse(user))
                .build();
                
            return ResponseResult.success(response);
            
        } catch (Exception e) {
            log.error("手机号登录失败: phone={}, error={}", request.getPhone(), e.getMessage());
            return ResponseResult.error(50001, "登录失败，请稍后重试");
        }
    }
}
```

### 4.2 权限验证实现

```java
@Component
public class AppStpInterfaceImpl implements StpInterface {
    
    @Autowired
    private AppUserService appUserService;
    
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        Long userId = Long.valueOf(loginId.toString());
        AppUser user = appUserService.getById(userId);
        
        if (user == null) {
            return Collections.emptyList();
        }
        
        List<String> permissions = new ArrayList<>();
        
        // 根据用户类型分配基础权限
        switch (user.getUserType()) {
            case "customer":
                permissions.addAll(getCustomerPermissions());
                break;
            case "vip_customer":
                permissions.addAll(getVipCustomerPermissions());
                break;
            case "admin":
                permissions.addAll(getAdminPermissions());
                break;
            case "super_admin":
                permissions.add("*"); // 超级管理员拥有所有权限
                break;
        }
        
        return permissions;
    }
    
    private List<String> getCustomerPermissions() {
        return Arrays.asList(
            "app:user:profile",
            "app:user:orders",
            "app:user:favorites",
            "app:user:wallet",
            "app:group:create",
            "app:group:join",
            "app:group:share"
        );
    }
}
```

## 5. 路由安全配置

### 5.1 Sa-Token路由拦截

```java
@Configuration
public class SecurityConfig {
    
    @Bean
    public SaServletFilter getSaServletFilter() {
        return new SaServletFilter()
            .addInclude("/**")
            .setAuth(obj -> {
                // 公开接口放行
                SaRouter.match("/api/app/public/**").stop();
                SaRouter.match("/api/app/auth/login/**").stop();
                SaRouter.match("/api/app/auth/register").stop();
                
                // 认证接口部分需要登录
                SaRouter.match("/api/app/auth/refresh", () -> StpUtil.checkLogin());
                SaRouter.match("/api/app/auth/logout", () -> StpUtil.checkLogin());
                
                // 用户功能接口需要登录
                SaRouter.match("/api/app/user/**", () -> StpUtil.checkLogin());
                SaRouter.match("/api/app/group/**", () -> StpUtil.checkLogin());
                SaRouter.match("/api/app/order/**", () -> StpUtil.checkLogin());
                SaRouter.match("/api/app/wallet/**", () -> StpUtil.checkLogin());
                
                // 管理后台接口需要管理员权限
                SaRouter.match("/api/admin/**", () -> {
                    StpUtil.checkLogin();
                    StpUtil.checkRoleOr("admin", "super_admin");
                });
            })
            .setError(e -> {
                return ResponseResult.error(40001, "认证失败：" + e.getMessage());
            });
    }
}
```

## 6. 前端集成方案

### 6.1 认证状态管理

```javascript
// store/auth.js
import { defineStore } from 'pinia'
import { appApi } from '@/api/app'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('app_token') || '')
  const user = ref(null)
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  
  // 手机号登录
  const loginByPhone = async (loginData) => {
    const response = await appApi.auth.loginByPhone(loginData)
    
    if (response.code === 200) {
      token.value = response.data.token
      user.value = response.data.user
      
      localStorage.setItem('app_token', response.data.token)
      localStorage.setItem('user_info', JSON.stringify(response.data.user))
      
      return response
    } else {
      throw new Error(response.message || '登录失败')
    }
  }
  
  // 退出登录
  const logout = async () => {
    try {
      await appApi.auth.logout()
    } catch (error) {
      console.warn('退出登录接口调用失败:', error)
    } finally {
      token.value = ''
      user.value = null
      localStorage.removeItem('app_token')
      localStorage.removeItem('user_info')
    }
  }
  
  return {
    token,
    user,
    isLoggedIn,
    loginByPhone,
    logout
  }
})
```

### 6.2 API分层调用

```javascript
// api/app.js
import request from '@/utils/request'

export const appApi = {
  // 公开接口
  public: {
    getProducts: (params) => request.get('/api/app/public/products', { params }),
    getProductDetail: (id) => request.get(`/api/app/public/products/${id}`),
    sendSms: (data) => request.post('/api/app/public/sms/send', data),
    getConfig: () => request.get('/api/app/public/config')
  },
  
  // 认证接口
  auth: {
    loginByPhone: (data) => request.post('/api/app/auth/login/phone', data),
    oauthLogin: (data) => request.post('/api/app/auth/login/oauth', data),
    logout: () => request.post('/api/app/auth/logout'),
    refresh: () => request.post('/api/app/auth/refresh')
  },
  
  // 用户接口（需要登录）
  user: {
    getProfile: () => request.get('/api/app/user/profile'),
    updateProfile: (data) => request.put('/api/app/user/profile', data),
    getOrders: (params) => request.get('/api/app/user/orders', { params }),
    getWallet: () => request.get('/api/app/user/wallet'),
    addFavorite: (productId) => request.post(`/api/app/user/favorites/${productId}`)
  },
  
  // 拼团接口（需要登录）
  group: {
    create: (data) => request.post('/api/app/group/create', data),
    join: (groupId) => request.post(`/api/app/group/join/${groupId}`),
    joinLucky: (activityId) => request.post(`/api/app/group/lucky/join/${activityId}`),
    getMyGroups: (params) => request.get('/api/app/group/my', { params })
  }
}
```

### 6.3 路由权限控制

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'

const routes = [
  // 公开页面
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/products',
    name: 'ProductList',
    component: () => import('@/views/ProductList.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/products/:id',
    name: 'ProductDetail',
    component: () => import('@/views/ProductDetail.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  
  // 需要登录的页面
  {
    path: '/user',
    name: 'UserCenter',
    component: () => import('@/views/UserCenter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/orders',
    name: 'MyOrders',
    component: () => import('@/views/MyOrders.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/wallet',
    name: 'MyWallet',
    component: () => import('@/views/MyWallet.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/group/create',
    name: 'CreateGroup',
    component: () => import('@/views/CreateGroup.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    // 需要登录但未登录，跳转到登录页
    next('/login')
  } else {
    next()
  }
})

export default router
```

## 7. 配置文件

### 7.1 Sa-Token配置

```yaml
# application.yml
sa-token:
  # token名称
  token-name: satoken
  # token有效期，7天
  timeout: 604800
  # 30分钟内无操作过期
  activity-timeout: 1800
  # 允许并发登录
  is-concurrent: true
  # 每次登录新建token
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出日志
  is-log: true
  # 移动端不使用cookie
  is-read-cookie: false
  # 从header读取token
  is-read-head: true
  # 从请求体读取token
  is-read-body: true
```

## 8. 总结

### 8.1 接口分离策略

**公开接口特点：**
- 无需token验证
- 提供基础浏览功能
- 不显示用户相关信息
- 支持游客体验

**已登录接口特点：**
- 需要token验证
- 提供完整功能
- 显示个性化信息
- 支持用户操作

### 8.2 优势

1. **安全性**：明确区分公开和私有接口，保护用户隐私
2. **用户体验**：游客可以浏览商品，降低使用门槛
3. **权限控制**：基于Sa-Token的细粒度权限管理
4. **扩展性**：便于后续添加新功能和用户类型
5. **统一认证**：C端和B端使用同一套认证体系

该方案完美契合拼团项目的需求，实现了游客浏览、用户登录、权限控制的完整闭环。 
