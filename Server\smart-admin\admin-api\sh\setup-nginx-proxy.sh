#!/bin/bash

echo "🔧 设置Nginx代理配置..."

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本"
    echo "用法: sudo ./setup-nginx-proxy.sh"
    exit 1
fi

# 安装Nginx
echo "📦 安装Nginx..."
apt update && apt install nginx -y

if [ $? -ne 0 ]; then
    echo "❌ Nginx安装失败"
    exit 1
fi

# 创建Nginx配置文件
echo "📝 创建Nginx代理配置..."
cat > /etc/nginx/sites-available/tgw-proxy << 'EOF'
# 社交拼团APP代理配置
server {
    listen 80;
    server_name localhost;
    
    # 日志配置
    access_log /var/log/nginx/tgw-access.log;
    error_log /var/log/nginx/tgw-error.log;

    # 代理前端应用 (/)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 代理API请求 (/app/*)
    location /app/ {
        proxy_pass http://localhost:8686/app/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Accept application/json;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # CORS头设置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        
        # 处理预检请求
        if ($request_method = OPTIONS) {
            return 204;
        }
    }

    # 代理后端健康检查
    location /actuator/ {
        proxy_pass http://localhost:8686/actuator/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 代理Swagger文档
    location /doc.html {
        proxy_pass http://localhost:8686/doc.html;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源代理（图片、文件等）
    location /upload/ {
        proxy_pass http://localhost:8686/upload/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /file/ {
        proxy_pass http://localhost:8686/file/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 启用配置
echo "🔗 启用Nginx配置..."
ln -sf /etc/nginx/sites-available/tgw-proxy /etc/nginx/sites-enabled/

# 删除默认配置（如果存在）
if [ -f /etc/nginx/sites-enabled/default ]; then
    rm /etc/nginx/sites-enabled/default
    echo "🗑️  删除默认Nginx配置"
fi

# 测试配置
echo "🧪 测试Nginx配置..."
nginx -t

if [ $? -ne 0 ]; then
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 启动Nginx
echo "🚀 启动Nginx服务..."
systemctl enable nginx
systemctl start nginx

# 检查状态
sleep 2
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx已成功启动"
else
    echo "❌ Nginx启动失败"
    systemctl status nginx
    exit 1
fi

# 显示配置信息
echo ""
echo "🎉 Nginx代理配置完成！"
echo ""
echo "📍 访问地址："
echo "   前端应用:  http://localhost/"
echo "   API接口:   http://localhost/app/v1/..."
echo "   健康检查:  http://localhost/actuator/health"
echo "   API文档:   http://localhost/doc.html"
echo ""
echo "📊 端口映射："
echo "   80 -> 3000 (前端)"
echo "   80/app/* -> 8686/app/* (后端API)"
echo ""
echo "📝 配置文件位置："
echo "   /etc/nginx/sites-available/tgw-proxy"
echo ""
echo "🔧 管理命令："
echo "   sudo systemctl status nginx    # 查看状态"
echo "   sudo systemctl restart nginx  # 重启服务"
echo "   sudo nginx -t                 # 测试配置"
echo "   sudo tail -f /var/log/nginx/tgw-access.log  # 查看访问日志"