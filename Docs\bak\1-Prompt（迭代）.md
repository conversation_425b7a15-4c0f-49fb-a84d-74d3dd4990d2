我们再来重新梳理一下这个拼团APP的拼团模式、商品分类和流程：

一、拼团模式有3种：

    1.新手团（无充值记录的用户）

    2.三人团（所有用户）

    3.五人团（所有用户）

二、商品分2类：

    1.普通商品（标价500元以下）

    2.VIP商品（标价500元以上）

    3.活动商品（管理员指定的商品）

三、流程

    1.新手团流程

    首页 → 新手团弹窗 → 商品确认页 → 拼团确认页 → 支付页 → 5人团等待页 → 成功/失败弹窗 → 弹窗引导分享

    2.三/五人团流程

    首页 → 商品列表页(分开3人、5人两个标签页)→商品详情页 → 商品确认页 → 拼团确认页 → 支付页 → 3/5人团等待页 → 成功/失败弹窗 → 弹窗引导分享

    3.活动团流程

    首页 → 活动弹窗广告 → 活动说明页 → 活动商品列表页(分开3人、5人两个标签页) → 商品确认页 → 拼团确认页 → 支付页 → 3/5人团等待页 → 成功/失败弹窗 → 弹窗引导分享

请你根据以上情况并按照原型 **@/原型2**   来修改   **@/src**  所有页面，完全地实现以上要求

---

新手用户（没有充值记录）要添加一个体验金的字段，数据要从API获取，新用户默认有10元

新手团的所有商品价格都是10元

=========

请按以下需求来修改、优化完善

用户分3种：

1.新用户（无充值记录）

2.老用户（有充值记录）

3.未登录用户（按新用户处理）

商品分4类：

1.普通商品

2.特惠商品（平台补贴）

3.热门商品（销售量排行前20）

4.推荐商品（平台推荐）

=========

1、主页（home)

默认显示默认显示推荐类商品列表

如果是新手用户或者未登录用户有，则弹出一个弹窗广告，主要内容：

* "Nền tảng trợ cấp" = "平台补贴"
* "Mỗi ngày nhận" = "每日领取"
* "+10.000₫" = "+10,000越南盾"
* "Hãy chia sẻ ngay cho bạn bè" = "立即分享给朋友"
* "Đi để thu thập" = "去收集"

  这是一个新手奖励弹窗，告诉用户这是一个补贴平台，每天可以领取10,000越南盾的奖励，并鼓励用户分享给朋友。底部的按钮是"去收集"，引导用户点击领取奖励。

  这是典型的推广拉新活动，通过每日签到奖励和分享返利来吸引新用户注册和老用户推广。

  点击按钮"去收集"跳转到活动页面

=========

请按以下需求来生成

2、拉新活动商品列表页

活动页面的UI风格要严格按照 来设计以保持UI风格一致性，它有两个标签页面，分别3人团和10人团，标签页内是这两种团的活动商品列表（商品是否参与活动，由管理员在管理后台设置）

活动页面对新手用户弹窗广告：

* "Tham gia hoạt động" = "参与活动"
* "Nhận được tiền mặt miễn phí" = "获得免费现金"
* "Tiền mặt" = "现金"
* "30.000₫" = "30,000越南盾"
* "Nhấn để nhận" = "点击领取"（弹窗底部的按钮）

  引导新用户参与一个赢取30,000越南盾现金的拼团活动

本页面按浏览器语言自动切换中文和越南语，文件保存到 \app\原型3 目录下， 完成后要链接到首页的弹窗广告中的“去收集”按钮

=========

请按以下需求来生成

3、拼团订单详情页面，这个页面直接从app\原型2 目录中复制group_buying_confirmation.html这个文件过来放到app\原型3中，然后做以下小改动：

活动页面的UI风格要严格按照 原来的设计以保持UI风格一致性，

页面标题：* "Chi tiết đơn hàng" = "订单详情"

主要内容：* "7 người tham gia ghép nhóm ngẫu nhiên, chỉ có 1 người may mắn có thể nhận được sản phẩm này" = "7人参与随机拼团，只有1人幸运者可以获得此商品"

拼团状态说明：* "Đánh (×) Rất tiếc người dùng ghép nhóm thất bại, có thể nhận thưởng 2%~16%" = "标记(×)很遗憾用户拼团失败，可以获得2%~16%奖励"

重要提示（红色文字）：* "Sản phẩm ghép nhóm không hỗ trợ đổi trả, vui lòng xác nhận lại trước khi đặt hàng" = "拼团商品不支持退换货，请在下单前再次确认"

按钮文字：* "Tham gia đặt hàng" = "参与下单"

底部说明：

    "Thanh toán nhanh chóng" = "快速支付"

    "Bảo đảm chính xác" = "保证准确"

本页面根据浏览器语言自动切换中文和越南语，文件保存到 \app\原型3 目录下， 完成后要链接到拉新活动商品列表页中的弹窗广告中"点击领取"按钮

=========

请按以下需求来生成

4、支付页面，这个页面直接从app\原型1 目录中复制 payment.html这个文件过来放到app\原型3中，然后做以下小改动：

    页面标题：* "Phương thức thanh toán" = "支付方式"

    订单信息：* 金额: "10.000₫" = "10,000越南盾"

* 商品: "Mua hàng hóa: Nhanh tay giành 30K tiền mặt – Ghép nhóm thành công là nhận ngay" = "购买商品：快速抢30K现金 - 拼团成功即可获得"

  支付方式选项：

1. Ví Leshop Pay (LH钱包)

* "Số dư hiện tại: 24.000₫" = "当前余额：24,000越南盾"
* "Chọn" = "选择"

1. VietQR Pay (越南二维码支付)

* "Vietcombank、BIDV、VIETINBANK....." = "越南外贸银行、越南投资发展银行、越南工商银行等"

  底部信息：* "Leshop" (平台名称)
* "Mô hình bán lẻ mới tích hợp các sản phẩm chất lượng cao và dịch vụ đa dạng" = "整合高品质产品和多样化服务的新零售模式"

  确认按钮：* "Xác nhận thanh toán" = "确认支付"

  总结：这是一个参与拼团活动的支付页面。用户需要支付10,000越南盾来参加一个赢取30,000越南盾现金的拼团活动。页面提供了两种支付方式：使用余额为24,000越南盾的平台钱包（LM Pay），或者通过VietQR扫描银行二维码进行支付。

本页面根据浏览器语言自动切换中文和越南语，文件保存到 \app\原型3 目录下， 完成后要链接到拼团订单详情页面中的弹窗广告中按钮"参与下单"

=========

请按以下需求来生成

5、拼团倒计时页面，这个页面直接从app\原型2 目录中复制 group_waiting.html 这个文件过来放到app\原型3中，然后做以下小改动：

参照  上的

改为有10个参与者，所以头像数量和位置排列要做相应修改

本页面根据浏览器语言自动切换中文和越南语，文件保存到 \app\原型3 目录下， 完成后要链接到支付页面中的"确认支付"按钮

=========

请按以下需求来生成

6、拼团结果显示页，这个页面直接从app\原型2 目录中复制group_failure_modal.html个文件过来放到app\原型3中，然后做以下改动：

    显示10个参与者中只有1人（右上角打勾的粉色头像）获得了商品，其他9人（标记×）拼团失败，但可以获得1,000越南盾的安慰奖。页面提供"再试一次"按钮鼓励用户继续参与。

    页面标题：* "Chi tiết đơn hàng" = "订单详情"

    拼团说明：* "10 người tham gia ghép nhóm ngẫu nhiên, chỉ có 1 người may mắn có thể nhận được sản phẩm này" = "10人参与随机拼团，只有1人幸运者可以获得此商品"

    拼团结果：* "Kết quả ghép nhóm" = "拼团结果"

* "Đánh (×) Rất tiếc người dùng ghép nhóm thất bại, có thể nhận thưởng 1.000₫" = "标记(×)很遗憾用户拼团失败，可以获得1,000越南盾奖励"

  操作按钮：* "Thử một lần nữa" = "再试一次"

  底部说明：* "Thanh toán nhanh chóng" = "快速支付"
* "Bảo đảm chính xác" = "保证准确"

  提现信息：* "096055 Rút thành công!" = "096055 提现成功！"

  右下角标识：* "Chia sẻ và kiếm VNĐ" = "分享赚越南盾"

弹出一个弹窗广告，弹窗主要内容：

* "Rất tiếc, bạn đã ghép nhóm thất bại, không nhận được sản phẩm, phần thưởng cho người dùng thất bại" = "很遗憾，您拼团失败了，没有获得商品，失败用户奖励"
* "+1.000₫" = "+1,000越南盾"

按钮选项：* "Thử một lần nữa" = "再试一次"

* "Tiếp tục nhận phần thưởng" = "继续领取奖励"
* "Chia sẻ cho bạn bè" = "分享给朋友"
* "Cùng nhau kiếm tiền" = "一起赚钱"

其他信息：* "Xem hồ sơ chi tiết" = "查看详细资料"

* "Ấn để nhận thêm tiền" = "点击获得更多钱"

底部显示：* "Giao dịch thành công" = "交易成功"

* "41,589,000 VND" = "41,589,000越南盾"

这是一个拼团失败后的弹窗广告，给用户1,000越南盾的安慰奖，同时鼓励用户再次尝试或分享给朋友来获得更多收益。弹窗还显示了一个大额交易成功的信息来刺激用户继续参与。

本页面要根据浏览器语言自动切换中文和越南语，文件保存到 \app\原型3 目录下， 完成后要修改 拼团倒计时页面，在倒计时结束时直接跳转到本页面

=========

7、点击弹窗中的“继续领取奖励”跳回活动页面

弹出一个弹窗广告，主要内容：

* 顶部标题: "Chia sẻ cho bạn bè, mỗi lần nhận được thêm 10.000VND"
* 翻译: "分享给朋友，每次可额外获得10,000越南盾"
* 中间大字: "10.000 VND"
* 翻译: "10,000越南盾"
* 中间小字: "Hãy nhanh chóng chia sẻ cho bạn bè"
* 翻译: "请快速分享给朋友"
* 余额显示: "LM Pay : 24.000₫"
* 翻译: "LM钱包：24,000越南盾"

按钮文字：

* 红色标签: "Mời nhiều, nhận nhiều"
* 翻译: "邀请越多，获得越多"
* 主按钮: "Mời bạn bè ngay bây giờ"
* 翻译: "立即邀请朋友"

整体含义：

这是一个典型的邀请好友返利活动弹窗。它鼓励用户立即分享给朋友，每次成功邀请都可以获得10,000越南盾的现金奖励，并强调“邀请越多，奖励越多”。弹窗还显示了用户在平台钱包（LM Pay）中的当前余额。

=======

8、商品详情页：

    把 APP/原型2/product_detail.htm (商品详情页)复制到 APP/原型3 目录中，修改 APP/原型3/home.html 选中商品时，跳转到这个商品详情页

---

## 前后端对接

### 思路

1、用html高保真原型来明确前端功能需求

2、根据html高保真原型来分析出API需求及相应的Mock数据及调试框架

3、根据html高保真原型来生成真正的前端（VUE 及其他框架）

4、将生成的前端程序与mock 数据的API对接

5、将已完成与mock 数据API对接的前端改为接真实API

### 前端与后端对接的prompt

1  -  请根据 **@社交拼团APP产品需求文档-简化版3.md** 和**@API对接实施方案.md** 中的方案，来对**@login.html**实现VUE重构，要求完全按原有的布局式样来实现，同时参照 login.vue 以解耦合的方式实现API对接，实现好的文件请输出到 src\这个目录下对应的目录

1.1  -  请根据 **@社交拼团APP产品需求文档-简化版3.md** 和**@API对接实施方案.md** 中的方案，来对** **@group_buying_confirmation.html** **实现VUE重构，要求完全按原有的布局式样来实现，同时参照   **@Login.vue** 以解耦合的方式实现API对接，实现好的文件请输出到 src\这个目录下对应的目录

2  -  接下来你参照   的mock实现方式，再根据 **@社交拼团APP产品需求文档-简化版3.md** 和**@API对接实施方案.md** 中的方案，用mock数据来调试好** **@home.html**  **这个页面的API接口

========

接下来你参照 **@Login.vue **  的mock实现方式，再根据 **@社交拼团APP产品需求文档-简化版3.md** 和**@API对接实施方案.md** 中的方案，用mock数据来调试好刚才生成的** **@/home** **这个页面的API接口，但mock 部分与其他部分要做到解耦合，以便在迁移到生产环境时可以无感知无障碍地去掉mock来对接真实API

那现在请你在现在已有的mock数据基础上，根据**@API对接实施方案.md** 中的需求，把缺少的mock数据全部补充完整，以便我可以调试所有的页面API接入

---

现在请你严格按照**@group_buying_confirmation.html** 来构建对应的下单vue页面，此vue页面中的拼团人数来自于**@/group** 商品详情页中的拼团人数，并链接到商品详情页中的“发起拼团”按钮


请按照**@test-public-api.html** 中钱包信息接口/api/v1/wallet的使用方式来修正支付页面（包括但不限于余额等所有相关数据）

---
