package net.lab1024.sa.admin.module.business.goods.domain.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.goods.constant.GoodsStatusEnum;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.base.common.json.deserializer.DictDataDeserializer;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class GoodsAddDetailForm {

    @Schema(description = "商品分类")
    @NotNull(message = "商品分类不能为空")
    private Long categoryId;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    @Schema(description = "商品类型")
    private String goodsType;

    @Schema(description = "货币类型")
    private String goodsCurrency;

    @SchemaEnum(GoodsStatusEnum.class)
    @CheckEnum(message = "商品状态错误", value = GoodsStatusEnum.class, required = true)
    private Integer goodsStatus;

    @Schema(description = "产地")
    @NotBlank(message = "产地 不能为空 ")
    @JsonDeserialize(using = DictDataDeserializer.class)
    private String place;

    @Schema(description = "付款模式")
    @NotNull(message = "付款模式 不能为空 ")
    private Integer payMode;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品图片数组")
    private List<Map<String, Object>> images;

    @Schema(description = "详情图片数组")
    private List<Map<String, Object>> detailImages;

    @Schema(description = "商品价格")
    //@NotNull(message = "商品价格不能为空")
    //@DecimalMin(value = "0", message = "商品价格最低0")
    private String price;

    @Schema(description = "上架状态")
    @NotNull(message = "上架状态不能为空")
    private Boolean shelvesFlag;

    @Schema(description = "是否单买")
    private Boolean aloneFlag;

    @Schema(description = "单买价格")
    private String alonePrice;

    @Schema(description = "备注|可选")
    private String remark;

    @Schema(description = "SKU")
    private List<GoodsSkusEntity> skus;
}
