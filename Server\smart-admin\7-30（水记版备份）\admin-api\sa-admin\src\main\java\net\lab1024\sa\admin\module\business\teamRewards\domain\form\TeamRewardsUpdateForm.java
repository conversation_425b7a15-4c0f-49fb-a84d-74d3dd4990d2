package net.lab1024.sa.admin.module.business.teamRewards.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 团队奖励表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Data
public class TeamRewardsUpdateForm {

    @Schema(description = "奖励ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "奖励ID 不能为空")
    private Long id;

}