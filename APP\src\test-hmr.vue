<template>
  <div class="hmr-test">
    <h2>HMR功能测试组件</h2>
    <p>当前时间: {{ currentTime }}</p>
    <p>计数器: {{ counter }}</p>
    <button @click="counter++" class="test-button">点击增加</button>
    <div class="status-info">
      <p>✅ 如果修改这个组件的样式或内容后保存，页面应该热更新而不刷新</p>
      <p>✅ 计数器的值应该保持不变</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const counter = ref(0)
const currentTime = ref('')

onMounted(() => {
  const updateTime = () => {
    currentTime.value = new Date().toLocaleTimeString()
  }
  updateTime()
  setInterval(updateTime, 1000)
})

// HMR相关代码
if (import.meta.hot) {
  console.log('✅ HMR功能已启用')
  
  // 保持计数器状态
  if (import.meta.hot.data.counter) {
    counter.value = import.meta.hot.data.counter
  }
  
  // 在热更新前保存状态
  import.meta.hot.dispose((data) => {
    data.counter = counter.value
  })
} else {
  console.log('❌ HMR功能未启用')
}
</script>

<style scoped>
.hmr-test {
  padding: 20px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  margin: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-button {
  background: #10b981;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin: 10px 0;
}

.test-button:hover {
  background: #059669;
}

.status-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
}

.status-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>