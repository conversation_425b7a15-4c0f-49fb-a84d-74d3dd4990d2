package net.lab1024.sa.admin.module.app.orders;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.base.module.support.captcha.domain.CaptchaForm;
import org.hibernate.validator.constraints.Length;

@Data
public class AppOrderParamForm {

    @Schema(description = "SkuId")
    @NotNull(message = "skuId not be blank")
    private Long skuId;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "收货地址Id")
    private Long shippingAddressId;

    @Schema(description = "单独购买标记")
    private Integer aloneFlag;

    @Schema(description = "支付货币0现金2积分")
    private Integer useCurrency;
}
