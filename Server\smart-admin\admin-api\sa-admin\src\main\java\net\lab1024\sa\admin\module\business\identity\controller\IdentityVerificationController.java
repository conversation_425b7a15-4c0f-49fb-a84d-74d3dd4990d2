package net.lab1024.sa.admin.module.business.identity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAddForm;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationStatusVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationVO;
import net.lab1024.sa.admin.module.business.identity.service.IdentityVerificationService;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 实名认证用户端控制器
 * 
 * 用户端API，用于用户提交和查看自己的实名认证信息
 *
 * <AUTHOR>
 * @Date 2025-01-27
 */
@Slf4j
@Tag(name = "实名认证用户端")
@RestController
@RequestMapping("/app/v1/identity-verification")
@Validated
public class IdentityVerificationController {

    @Resource
    private IdentityVerificationService identityVerificationService;

    /**
     * 提交实名认证申请
     */
    @Operation(summary = "提交实名认证申请")
    @PostMapping(value = "/submit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseDTO<String> submitVerification(@Valid @ModelAttribute IdentityVerificationAddForm addForm) {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.submitVerification(addForm, userId);
    }

    /**
     * 重新提交实名认证申请
     */
    @Operation(summary = "重新提交实名认证申请")
    @PostMapping(value = "/resubmit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseDTO<String> resubmitVerification(@Valid @ModelAttribute IdentityVerificationAddForm addForm) {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.resubmitVerification(addForm, userId);
    }

    /**
     * 获取当前用户的认证状态
     */
    @Operation(summary = "获取当前用户认证状态")
    @GetMapping("/status")
    public ResponseDTO<IdentityVerificationStatusVO> getUserVerificationStatus() {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.getUserVerificationStatus(userId);
    }

    /**
     * 获取当前用户的认证详情
     */
    @Operation(summary = "获取当前用户认证详情")
    @GetMapping("/detail")
    public ResponseDTO<IdentityVerificationVO> getUserVerificationDetail() {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.getUserVerificationDetail(userId);
    }

    /**
     * 撤销认证申请（仅限待审核状态）
     */
    @Operation(summary = "撤销认证申请")
    @PostMapping("/cancel")
    public ResponseDTO<String> cancelVerification() {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.cancelVerification(userId);
    }

    /**
     * 检查身份证是否已被使用
     */
    @Operation(summary = "检查身份证是否已被使用")
    @GetMapping("/check-idcard")
    public ResponseDTO<Boolean> checkIdCardExists(@RequestParam("idCard") String idCard) {
        Long userId = SmartRequestUtil.getRequestUserId();
        boolean exists = identityVerificationService.isIdCardExists(idCard, userId);
        return ResponseDTO.ok(exists);
    }

    /**
     * 检查银行卡是否已被使用
     */
    @Operation(summary = "检查银行卡是否已被使用")
    @GetMapping("/check-bankcard")
    public ResponseDTO<Boolean> checkBankCardExists(@RequestParam("bankCard") String bankCard) {
        Long userId = SmartRequestUtil.getRequestUserId();
        boolean exists = identityVerificationService.isBankCardExists(bankCard, userId);
        return ResponseDTO.ok(exists);
    }

    /**
     * 验证用户是否可以提交认证
     */
    @Operation(summary = "验证用户是否可以提交认证")
    @GetMapping("/validate")
    public ResponseDTO<String> validateUserCanVerify() {
        Long userId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.validateUserCanVerify(userId);
    }

    /**
     * 批量查询用户认证状态（用于其他模块调用）
     */
    @Operation(summary = "批量查询用户认证状态")
    @PostMapping("/batch-status")
    public ResponseDTO<List<IdentityVerificationStatusVO>> batchGetUserVerificationStatus(
            @RequestBody @Valid @NotNull List<Long> userIds) {
        return identityVerificationService.batchGetUserVerificationStatus(userIds);
    }

    /**
     * 检查当前用户是否已通过实名认证
     */
    @Operation(summary = "检查当前用户是否已通过实名认证")
    @GetMapping("/is-verified")
    public ResponseDTO<Boolean> isCurrentUserVerified() {
        Long userId = SmartRequestUtil.getRequestUserId();
        boolean verified = identityVerificationService.isUserVerified(userId);
        return ResponseDTO.ok(verified);
    }
}