<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 路由视图 -->
    <router-view />
    
    <!-- 全局客服悬浮按钮 -->
    <CustomerServiceFloat 
      v-if="shouldShowCustomerService"
      :bottom-offset="getBottomOffset"
    />
  </div>
</template>

<script>
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import CustomerServiceFloat from '@/components/common/CustomerServiceFloat.vue'

export default {
  name: 'App',
  components: {
    CustomerServiceFloat
  },
  setup() {
    const route = useRoute()
    
    // 不显示客服按钮的页面列表
    const excludePages = [
      '/login',           // 登录页面
      '/order/payment',   // 支付页面
      '/api-test'         // API测试页面
    ]
    
    // 判断是否应该显示客服按钮
    const shouldShowCustomerService = computed(() => {
      const currentPath = route.path
      
      // 检查是否在排除列表中
      const isExcluded = excludePages.some(excludePath => 
        currentPath === excludePath || currentPath.startsWith(excludePath)
      )
      
      return !isExcluded
    })
    
    // 根据当前页面调整底部偏移量
    const getBottomOffset = computed(() => {
      const currentPath = route.path
      
      // 有底部导航栏的页面需要更高的偏移量
      const pagesWithBottomNav = [
        '/home',
        '/category', 
        '/activity',
        '/user'
      ]
      
      const hasBottomNav = pagesWithBottomNav.some(navPath => 
        currentPath === navPath || currentPath.startsWith(navPath)
      )
      
      return 150  // 所有页面统一偏移150px
    })
    
    onMounted(() => {
      // 输出启动信息
      console.log('🎉 社交拼团APP已启动 (REAL API ONLY)')
      console.log('📱 当前环境:', import.meta.env.MODE)
      console.log('🔧 调试模式:', import.meta.env.DEV ? '开启' : '关闭')
      console.log('🚫 Mock功能已完全禁用')
      console.log('🎧 全局客服悬浮按钮已启用')
    })
    
    return {
      shouldShowCustomerService,
      getBottomOffset
    }
  }
}
</script>

<style scoped>
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style> 