package net.lab1024.sa.admin.module.business.orders.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Data
public class OrdersUpdateForm extends OrdersAddForm {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单ID 不能为空")
    private Long id;

}