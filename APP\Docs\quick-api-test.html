<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速API测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .alert-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f4f6;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 5px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速API测试工具</h1>
        
        <div class="alert alert-warning">
            <strong>⚠️ CORS跨域问题说明：</strong><br>
            由于浏览器安全策略，直接从本地HTML访问HTTPS API可能会遇到CORS错误。<br>
            解决方案：使用代理服务器或安装CORS浏览器扩展。
        </div>

        <div class="form-group">
            <label for="apiUrl">API地址：</label>
            <input type="text" id="apiUrl" value="https://pp.kongzhongkouan.com/api/v1/captcha">
        </div>

        <div class="form-group">
            <label for="testMode">测试模式：</label>
            <select id="testMode">
                <option value="direct">直接请求（可能遇到CORS）</option>
                <option value="proxy" selected>代理请求（推荐）</option>
                <option value="iframe">iframe方式</option>
            </select>
        </div>

        <div style="margin-bottom: 20px;">
            <button class="btn btn-success" onclick="testApi()">🔐 测试验证码接口</button>
            <button class="btn btn-warning" onclick="testApiProxy()">🌐 代理测试</button>
            <button class="btn" onclick="clearResult()">🗑️ 清除结果</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let currentTest = null;

        function showResult(status, message, data = null) {
            const resultDiv = document.getElementById('result');
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 'status-loading';
            
            let content = `<div class="status ${statusClass}">`;
            if (status === 'loading') {
                content += `<span class="loading"></span>`;
            }
            content += `${message}</div>`;
            
            if (data) {
                content += `\n${JSON.stringify(data, null, 2)}`;
            }
            
            resultDiv.innerHTML = content;
        }

        async function testApi() {
            const apiUrl = document.getElementById('apiUrl').value;
            const testMode = document.getElementById('testMode').value;
            
            showResult('loading', '正在测试API接口...');
            
            try {
                const startTime = performance.now();
                let response;
                
                if (testMode === 'proxy') {
                    response = await testWithProxy(apiUrl);
                } else if (testMode === 'iframe') {
                    response = await testWithIframe(apiUrl);
                } else {
                    response = await fetch(apiUrl, {
                        method: 'GET',
                        mode: 'cors',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                }
                
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 检查业务逻辑是否成功
                    if (data.code === 0) {
                        showResult('success', `✅ 测试成功！响应时间: ${responseTime}ms`, data);
                    } else {
                        showResult('error', `❌ 业务逻辑失败！状态码: ${data.code}, 消息: ${data.msg}`, data);
                    }
                } else {
                    showResult('error', `❌ HTTP请求失败！状态码: ${response.status}`, {
                        status: response.status,
                        statusText: response.statusText,
                        responseTime: responseTime
                    });
                }
                
            } catch (error) {
                let errorMsg = error.message;
                let solution = '';
                
                if (errorMsg.includes('CORS') || errorMsg.includes('Failed to fetch')) {
                    solution = '\n\n解决方案:\n1. 点击"代理测试"按钮\n2. 安装CORS浏览器扩展\n3. 使用本地HTTP服务器';
                }
                
                showResult('error', `❌ 请求异常: ${errorMsg}${solution}`);
            }
        }

        async function testWithProxy(url) {
            // 使用多个代理服务尝试
            const proxies = [
                'https://api.allorigins.win/raw?url=',
                'https://cors-anywhere.herokuapp.com/',
                'https://api.codetabs.com/v1/proxy?quest='
            ];
            
            for (let proxy of proxies) {
                try {
                    console.log(`尝试代理: ${proxy}`);
                    const response = await fetch(proxy + encodeURIComponent(url));
                    if (response.ok) {
                        return response;
                    }
                } catch (e) {
                    console.log(`代理失败: ${proxy}`);
                    continue;
                }
            }
            
            throw new Error('所有代理服务都不可用');
        }

        async function testWithIframe(url) {
            return new Promise((resolve, reject) => {
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = url;
                
                iframe.onload = function() {
                    try {
                        const content = iframe.contentDocument || iframe.contentWindow.document;
                        const text = content.body.textContent;
                        const data = JSON.parse(text);
                        
                        resolve({
                            ok: true,
                            json: () => Promise.resolve(data)
                        });
                    } catch (e) {
                        reject(new Error('无法解析iframe内容'));
                    } finally {
                        document.body.removeChild(iframe);
                    }
                };
                
                iframe.onerror = function() {
                    document.body.removeChild(iframe);
                    reject(new Error('iframe加载失败'));
                };
                
                document.body.appendChild(iframe);
                
                // 5秒超时
                setTimeout(() => {
                    if (document.body.contains(iframe)) {
                        document.body.removeChild(iframe);
                        reject(new Error('iframe加载超时'));
                    }
                }, 5000);
            });
        }

        async function testApiProxy() {
            const apiUrl = document.getElementById('apiUrl').value;
            showResult('loading', '正在通过代理测试...');
            
            try {
                const response = await testWithProxy(apiUrl);
                const data = await response.json();
                
                if (data.code === 0) {
                    showResult('success', '✅ 代理测试成功！', data);
                } else {
                    showResult('error', `❌ 业务逻辑失败！状态码: ${data.code}`, data);
                }
            } catch (error) {
                showResult('error', `❌ 代理测试失败: ${error.message}`);
            }
        }

        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }

        // 页面加载完成后自动测试一次
        window.onload = function() {
            console.log('页面加载完成，可以开始测试API');
        };
    </script>
</body>
</html> 