# 团购网API接口测试工具使用说明

## 功能概述

这是一个完整的API接口测试工具，支持团购网项目的所有API接口测试，包括：
- 🌐 **公开接口**：不需要登录即可访问的接口
- 🔐 **认证接口**：需要用户登录后才能访问的接口

## 主要功能

### 1. 服务器配置
- 设置API服务器地址
- 测试服务器连接状态
- 支持HTTP和HTTPS协议

### 2. 用户认证管理
- 用户登录功能
- Token自动管理
- 登录状态持久化
- 退出登录和Token刷新

### 3. 接口测试功能
- 单个接口测试
- 批量接口测试
- 实时响应显示
- 详细错误信息

### 4. 测试统计
- 总测试次数
- 成功/失败次数
- 成功率计算
- 平均响应时间
- 最后测试时间

## 支持的API接口

### 公开接口（无需登录）
1. **验证码获取** - `/api/v1/captcha`
2. **首页数据** - `/api/v1/data/home`
3. **轮播图数据** - `/api/v1/data/banners`
4. **商品分类** - `/api/v1/data/categories`
5. **商品列表** - `/api/v1/data/products`
6. **商品搜索** - `/api/v1/search`
7. **商品详情** - `/api/v1/products/{id}`
8. **客服支持** - `/api/v1/support`
9. **活动详情** - `/api/v1/activities/{id}`

### 认证接口（需要登录）
1. **用户中心** - `/api/v1/user/dashboard`
2. **订单列表** - `/api/v1/orders`
3. **钱包信息** - `/api/v1/wallet`
4. **收藏列表** - `/api/v1/user/favorites`
5. **收货地址** - `/api/v1/user/addresses`
6. **用户设置** - `/api/v1/user/settings`
7. **商品收藏操作** - `/api/v1/products/{id}/action`
8. **拼团操作** - `/api/v1/groups/action`

## 使用步骤

### 1. 配置API服务器
```
1. 在"API基础地址"输入框中输入后端服务器地址
2. 点击"更新配置"按钮
3. 点击"测试连接"验证服务器是否正常
```

### 2. 测试公开接口
```
1. 直接点击任意公开接口的测试按钮
2. 或点击"测试公开接口"批量测试所有公开接口
```

### 3. 测试认证接口
```
1. 在"用户认证"区域输入手机号和密码
2. 点击"登录"按钮进行身份验证
3. 登录成功后，点击认证接口的测试按钮
4. 或点击"测试认证接口"批量测试所有认证接口
```

## 响应状态说明

### 成功状态
- ✅ **请求成功**：HTTP状态码正常且业务逻辑成功
- 显示完整的响应数据和响应时间

### 失败状态
- ❌ **业务逻辑失败**：HTTP正常但业务状态码异常
- ❌ **认证失败**：Token无效或权限不足
- ❌ **HTTP请求失败**：网络或服务器错误
- ❌ **请求异常**：CORS、网络连接等问题

## 故障排除

### 1. CORS跨域问题
```
解决方案：
- 安装CORS浏览器扩展
- 后端配置CORS头部
- 使用代理服务器
- 本地启动HTTP服务器
```

### 2. 网络请求失败
```
检查项目：
- 后端服务是否启动
- API地址是否正确
- 网络连接是否正常
- 防火墙设置
```

### 3. 认证失败
```
解决方案：
- 检查用户名密码是否正确
- 确认Token是否过期
- 重新登录获取新Token
- 检查用户权限
```

## 技术特性

- 🎨 **现代化UI**：响应式设计，支持移动端
- 🔄 **实时更新**：动态显示测试结果和统计信息
- 💾 **状态持久化**：自动保存登录状态到localStorage
- 🚀 **高性能**：支持并发测试和批量操作
- 🔍 **详细日志**：完整的请求响应信息和错误提示

## 开发者信息

- 支持HTTP/HTTPS协议
- 自动处理JSON响应
- 智能错误处理和提示
- 完整的认证流程管理
- 可扩展的接口测试框架

## 注意事项

1. 请确保后端服务正常运行
2. 认证接口需要先登录获取Token
3. 测试数据仅用于开发调试
4. 生产环境请谨慎使用批量测试功能
5. 建议在开发环境中使用此工具 