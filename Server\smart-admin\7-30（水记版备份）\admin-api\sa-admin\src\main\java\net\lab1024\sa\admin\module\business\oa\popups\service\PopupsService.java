package net.lab1024.sa.admin.module.business.oa.popups.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.oa.popups.dao.PopupsDao;
import net.lab1024.sa.admin.module.business.oa.popups.domain.entity.PopupsEntity;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsAddForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsQueryForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsUpdateForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.vo.PopupsVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.base.module.support.file.service.IFileStorageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 弹窗管理 Service
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Service
public class PopupsService {

    @Resource
    private PopupsDao popupsDao;

    @Resource
    private IFileStorageService fileStorageService;
    /**
     * 分页查询
     */
    public PageResult<PopupsVO> queryPage(PopupsQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PopupsVO> list = popupsDao.queryPage(page, queryForm);
        for (PopupsVO vo : list) {
            ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(vo.getImageUrl());
            if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                vo.setImageUrl(getFileUrl.getData());
            }
        }
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PopupsAddForm addForm) {
        PopupsEntity popupsEntity = SmartBeanUtil.copy(addForm, PopupsEntity.class);
        popupsDao.insert(popupsEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     */
    public ResponseDTO<String> update(PopupsUpdateForm updateForm) {
        PopupsEntity popupsEntity = SmartBeanUtil.copy(updateForm, PopupsEntity.class);
        popupsDao.updateById(popupsEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        popupsDao.deleteBatchIds(idList);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        popupsDao.deleteById(id);
        return ResponseDTO.ok();
    }
}
