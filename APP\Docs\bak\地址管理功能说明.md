# 收货地址管理功能

## 功能概述

根据 `地址接口.txt` 文档，已完成收货地址管理功能的开发，包含以下核心功能：

### 📍 主要功能

1. **地址列表管理** - 查看所有收货地址
2. **添加新地址** - 添加新的收货地址
3. **编辑地址** - 修改现有地址信息
4. **删除地址** - 删除不需要的地址
5. **设置默认地址** - 设置常用的默认收货地址

### 🛠️ 技术实现

#### 页面结构
- `AddressPage.vue` - 地址列表页面
- `AddAddressPage.vue` - 添加地址页面
- `EditAddressPage.vue` - 编辑地址页面

#### 路由配置
- `/user/address` - 地址列表
- `/user/address/add` - 添加地址
- `/user/address/edit/:id` - 编辑地址

#### API 接口对接
- **GET** `/api/v1/address` - 获取地址列表
- **POST** `/api/v1/address/add` - 添加新地址
- **POST** `/api/v1/address/update` - 更新地址信息
- **GET** `/api/v1/address/delete/{id}` - 删除地址

### 🎯 核心特性

#### 1. 地址列表页面
- ✅ 显示所有收货地址
- ✅ 默认地址标识
- ✅ 手机号脱敏显示
- ✅ 完整地址信息展示
- ✅ 编辑、删除、设为默认操作
- ✅ 空状态处理

#### 2. 添加地址页面
- ✅ 收货人信息填写（姓名、手机号）
- ✅ 地址信息填写（省市区、详细地址）
- ✅ 设为默认地址选项
- ✅ 表单验证
- ✅ 提交状态处理

#### 3. 编辑地址页面
- ✅ 加载现有地址信息
- ✅ 修改地址信息
- ✅ 保存修改
- ✅ 表单验证

#### 4. 用户体验优化
- ✅ 加载状态显示
- ✅ 错误处理
- ✅ 成功提示
- ✅ 确认删除弹窗
- ✅ 响应式设计

### 🔧 数据结构

#### 地址信息字段
```javascript
{
  id: 8,                           // 地址ID
  userId: 81,                      // 用户ID
  userName: "13833883388",         // 用户名
  recipientName: "张二",           // 收货人姓名
  phoneNumber: "865542141",        // 手机号码
  province: "广西区",              // 省份
  city: "南宁市",                  // 城市
  district: "青秀区",              // 区县
  addressLine: "唠三叨四涌腾泉在哪里", // 详细地址
  isDefault: 1,                    // 是否默认地址（1是0否）
  createTime: "2025-07-06 16:25:36" // 创建时间
}
```

### 🚀 使用流程

#### 访问地址管理
1. 进入个人中心页面
2. 点击"收货地址"选项
3. 进入地址列表页面

#### 添加新地址
1. 在地址列表页面点击"+"按钮
2. 填写收货人信息和地址信息
3. 选择是否设为默认地址
4. 点击"保存地址"

#### 编辑地址
1. 在地址列表中点击"编辑"按钮
2. 修改地址信息
3. 点击"保存地址"

#### 删除地址
1. 在地址列表中点击"删除"按钮
2. 确认删除操作

#### 设置默认地址
1. 在地址列表中点击"设为默认"按钮
2. 系统自动设置该地址为默认地址

### 🔐 权限控制

- 所有地址管理功能需要用户登录
- 使用 JWT Token 进行身份验证
- 用户只能管理自己的地址信息

### 📱 移动端适配

- 完全响应式设计
- 移动端友好的触摸操作
- 优化的表单输入体验
- 适配各种屏幕尺寸

### 🎨 UI 设计

- 遵循应用整体设计风格
- 使用 Tailwind CSS 样式系统
- Iconify 图标库
- 现代化的卡片式布局
- 清晰的视觉层次

### 🔄 状态管理

- 完整的加载状态处理
- 错误状态显示
- 成功操作反馈
- 数据实时更新

### 🧪 测试建议

1. **功能测试**
   - 地址列表加载
   - 添加地址功能
   - 编辑地址功能
   - 删除地址功能
   - 设置默认地址

2. **边界测试**
   - 空地址列表
   - 网络异常处理
   - 权限验证
   - 表单验证

3. **用户体验测试**
   - 页面加载速度
   - 操作流畅性
   - 错误提示友好性
   - 移动端适配

### 📝 注意事项

1. 省市区字段为非必填项，可以为空
2. 手机号在列表中会进行脱敏显示
3. 删除地址需要用户确认
4. 设置默认地址会自动取消其他地址的默认状态
5. 所有操作都有相应的成功/失败提示

### 🔮 后续优化

1. 添加地址选择器（省市区联动）
2. 地址定位功能
3. 地址验证功能
4. 常用地址推荐
5. 地址标签功能（如：家、公司等） 