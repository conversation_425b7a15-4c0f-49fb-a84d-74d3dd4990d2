{"name": "social-group-buying-app", "version": "1.0.0", "description": "社交拼团APP前端项目", "type": "module", "scripts": {"dev": "CHOKIDAR_USEPOLLING=true vite", "dev:mock": "vite --mode development", "dev:real": "cross-env VITE_USE_MOCK=false VITE_API_BASE_URL=http://pp.oripicks.com vite", "build": "vite build", "preview": "vite preview", "serve": "vite", "test:mock": "echo '启动Mock测试模式' && npm run dev:mock", "test:real": "echo '启动真实API测试模式' && npm run dev:real", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "save-login": "node scripts/save-login-state.js", "scrape": "node scripts/scrape-showdoc.js", "enter-password": "node scripts/enter-password.js", "scrape-manual": "node scripts/scrape-manual-fixed.js"}, "dependencies": {"axios": "^1.5.0", "iconify-icon": "^3.0.0", "pinia": "^2.1.6", "vant": "^4.9.20", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@playwright/mcp": "^0.0.31", "@playwright/test": "^1.54.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.3.4", "autoprefixer": "^10.4.15", "cross-env": "^7.0.3", "postcss": "^8.4.28", "sass": "^1.89.2", "tailwindcss": "^3.3.3", "terser": "^5.43.1", "vite": "^4.4.9"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "social", "group-buying", "mobile-app", "e-commerce"], "author": "开发团队", "license": "MIT"}