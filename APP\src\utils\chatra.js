/**
 * Chatra 客服系统集成工具
 * 
 * 用于动态加载和控制 Chatra 客服聊天窗口
 */

// Chatra 配置
const CHATRA_CONFIG = {
  chatId: '4gbJiKfsXHorvTHm8', // 您的 Chatra ID
  scriptUrl: 'https://call.chatra.io/chatra.js',
  loaded: false,
  loading: false
}

/**
 * 动态加载 Chatra 脚本
 */
export const loadChatra = () => {
  return new Promise((resolve, reject) => {
    // 如果已经加载过，直接返回
    if (CHATRA_CONFIG.loaded && window.Chatra) {
      resolve(window.Chatra)
      return
    }

    // 如果正在加载中，等待加载完成
    if (CHATRA_CONFIG.loading) {
      const checkLoaded = () => {
        if (CHATRA_CONFIG.loaded && window.Chatra) {
          resolve(window.Chatra)
        } else {
          setTimeout(checkLoaded, 100)
        }
      }
      checkLoaded()
      return
    }

    CHATRA_CONFIG.loading = true

    try {
      // 设置 ChatraID
      window.ChatraID = CHATRA_CONFIG.chatId
      
      // 创建脚本标签
      const script = document.createElement('script')
      
      // 创建 Chatra 函数队列
      window.Chatra = window.Chatra || function() {
        (window.Chatra.q = window.Chatra.q || []).push(arguments)
      }
      
      // 注意：不要在这里隐藏Chatra，而是在初始化完成后再处理显示状态
      
      script.async = true
      script.src = CHATRA_CONFIG.scriptUrl
      
      // 监听加载完成
      script.onload = () => {
        CHATRA_CONFIG.loaded = true
        CHATRA_CONFIG.loading = false
        console.log('✅ [Chatra] 脚本加载成功')
        
        // 等待Chatra完全初始化
        setTimeout(() => {
          console.log('🔧 [Chatra] 初始化完成，隐藏默认悬浮按钮')
          // 现在隐藏默认的悬浮按钮
          if (window.Chatra && typeof window.Chatra === 'function') {
            window.Chatra('hide')
          }
          resolve(window.Chatra)
        }, 1000)
      }
      
      // 监听加载失败
      script.onerror = () => {
        CHATRA_CONFIG.loading = false
        console.error('Failed to load Chatra script')
        reject(new Error('Failed to load Chatra script'))
      }
      
      // 添加脚本到页面
      if (document.head) {
        document.head.appendChild(script)
      } else {
        document.body.appendChild(script)
      }
    } catch (error) {
      CHATRA_CONFIG.loading = false
      reject(error)
    }
  })
}

/**
 * 打开 Chatra 聊天窗口
 */
export const openChatraChat = async () => {
  try {
    // 确保 Chatra 已加载
    await loadChatra()
    
    if (window.Chatra && typeof window.Chatra === 'function') {
      console.log('🔄 [Chatra] 尝试打开聊天窗口')
      
      // 先显示Chatra组件（如果之前被隐藏）
      window.Chatra('show')
      
      // 等待一小段时间确保Chatra初始化完成
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 尝试多种API调用方式打开聊天窗口
      try {
        // 方式1: 标准的openChat API
        window.Chatra('openChat', true)
        console.log('✅ [Chatra] 使用openChat API')
      } catch (e1) {
        console.warn('⚠️ [Chatra] openChat API失败:', e1)
        try {
          // 方式2: 展开聊天窗口
          window.Chatra('expandWidget')
          console.log('✅ [Chatra] 使用expandWidget API')
        } catch (e2) {
          console.warn('⚠️ [Chatra] expandWidget API失败:', e2)
          try {
            // 方式3: 直接显示并激活
            window.Chatra('show')
            window.Chatra('expandWidget', true)
            console.log('✅ [Chatra] 使用show + expandWidget组合')
          } catch (e3) {
            console.error('❌ [Chatra] 所有API调用都失败:', e3)
            throw e3
          }
        }
      }
      
      // 验证聊天窗口是否真的打开了
      setTimeout(() => {
        const chatraWidget = document.querySelector('[data-chatra]') || 
                           document.querySelector('.chatra') ||
                           document.querySelector('#chatra')
        
        if (chatraWidget) {
          console.log('✅ [Chatra] 聊天窗口DOM元素已找到')
        } else {
          console.warn('⚠️ [Chatra] 未找到聊天窗口DOM元素，可能未正确显示')
        }
      }, 1000)
      
      return true
    } else {
      throw new Error('Chatra not available')
    }
  } catch (error) {
    console.error('❌ [Chatra] 打开聊天窗口失败:', error)
    return false
  }
}

/**
 * 关闭 Chatra 聊天窗口
 */
export const closeChatraChat = async () => {
  try {
    if (window.Chatra && typeof window.Chatra === 'function') {
      window.Chatra('openChat', false)
      return true
    }
  } catch (error) {
    console.error('Failed to close Chatra chat:', error)
  }
  return false
}

/**
 * 设置 Chatra 用户信息
 * @param {Object} userInfo - 用户信息
 */
export const setChatraUserInfo = async (userInfo) => {
  try {
    await loadChatra()
    
    if (window.Chatra && typeof window.Chatra === 'function') {
      const chatraUserData = {}
      
      if (userInfo.name) chatraUserData.name = userInfo.name
      if (userInfo.email) chatraUserData.email = userInfo.email
      if (userInfo.phone) chatraUserData.phone = userInfo.phone
      if (userInfo.userId) chatraUserData.userId = userInfo.userId
      
      window.Chatra('updateIntegrationData', chatraUserData)
      return true
    }
  } catch (error) {
    console.error('Failed to set Chatra user info:', error)
  }
  return false
}

/**
 * 隐藏 Chatra 默认的悬浮按钮
 * (因为我们使用自定义的悬浮按钮)
 */
export const hideChatraWidget = async () => {
  try {
    await loadChatra()
    
    if (window.Chatra && typeof window.Chatra === 'function') {
      // 使用正确的API隐藏默认悬浮按钮
      window.Chatra('hide')
      return true
    }
  } catch (error) {
    console.error('Failed to hide Chatra widget:', error)
  }
  return false
}

/**
 * 检查 Chatra 是否可用
 */
export const isChatraAvailable = () => {
  return CHATRA_CONFIG.loaded && window.Chatra && typeof window.Chatra === 'function'
}

/**
 * 初始化 Chatra（在应用启动时调用）
 */
export const initChatra = async (options = {}) => {
  try {
    await loadChatra()
    
    // 隐藏默认悬浮按钮
    if (options.hideWidget !== false) {
      await hideChatraWidget()
      // 再次确保隐藏
      setTimeout(() => {
        if (window.Chatra) {
          window.Chatra('hide')
        }
      }, 1000)
    }
    
    // 设置用户信息
    if (options.userInfo) {
      await setChatraUserInfo(options.userInfo)
    }
    
    console.log('Chatra initialized successfully')
    return true
  } catch (error) {
    console.error('Failed to initialize Chatra:', error)
    return false
  }
}

export default {
  loadChatra,
  openChatraChat,
  closeChatraChat,
  setChatraUserInfo,
  hideChatraWidget,
  isChatraAvailable,
  initChatra
}