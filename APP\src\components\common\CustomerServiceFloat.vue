<template>
  <div 
    v-if="visible"
    class="customer-service-float" 
    @click="handleClick"
    @contextmenu.prevent="showDebugMenu"
    :style="{ bottom: bottomOffset + 'px' }"
  >
    <div class="service-icon">
      <iconify-icon icon="material-symbols:headset-mic" class="text-xl" />
    </div>
    <div class="service-text">客服</div>
    
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showToast, showDialog } from 'vant'
import { useRouter } from 'vue-router'

const props = defineProps({
  // 是否显示悬浮按钮
  visible: {
    type: Boolean,
    default: true
  },
  // 底部偏移量，用于避开底部导航栏
  bottomOffset: {
    type: Number,
    default: 150
  },
  // 自定义点击处理
  onClick: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['click'])
const router = useRouter()

// 开发环境检查
const isDev = import.meta.env.DEV

// 客服联系方式配置
const serviceConfig = {
  phone: '************',
  wechat: 'service_wechat',
  qq: '123456789',
  workTime: '9:00-18:00（工作日）'
}

// 处理点击事件
const handleClick = async () => {
  // 如果有自定义点击处理，优先使用
  if (props.onClick) {
    props.onClick()
    emit('click')
    return
  }

  console.log('🎯 [CustomerServiceFloat] 点击客服按钮')
  
  // 直接跳转到客服页面，简化流程
  try {
    router.push('/user/customer-service')
    console.log('✅ [CustomerServiceFloat] 已跳转到客服页面')
    emit('click')
  } catch (error) {
    console.error('❌ [CustomerServiceFloat] 跳转客服页面失败:', error)
    // 如果跳转失败，显示备用的联系方式弹窗
    showFallbackContactDialog()
  }
}

// 显示备用联系方式弹窗
const showFallbackContactDialog = () => {
  showDialog({
    title: '联系客服',
    message: `
      <div style="text-align: left; padding: 10px;">
        <p style="margin-bottom: 12px; color: #ff6b35;">
          <strong>多种联系方式，为您服务</strong>
        </p>
        <p style="margin-bottom: 12px;"><strong>客服热线：</strong>${serviceConfig.phone}</p>
        <p style="margin-bottom: 12px;"><strong>微信客服：</strong>${serviceConfig.wechat}</p>
        <p style="margin-bottom: 12px;"><strong>QQ客服：</strong>${serviceConfig.qq}</p>
        <p style="color: #999; font-size: 12px; margin-top: 15px; margin-bottom: 0;">
          工作时间：${serviceConfig.workTime}
        </p>
        <p style="color: #999; font-size: 11px; margin-top: 8px;">
          建议优先拨打客服热线获得更快响应
        </p>
      </div>
    `,
    allowHtml: true,
    showCancelButton: true,
    confirmButtonText: '拨打电话',
    cancelButtonText: '关闭',
    confirmButtonColor: '#ff6b35',
    cancelButtonColor: '#666',
    closeOnClickOverlay: true,
    beforeClose: (action) => {
      if (action === 'confirm') {
        // 拨打客服电话
        callService()
      }
      return true
    }
  })

  emit('click')
}

// 显示调试菜单（开发环境，右键点击）
const showDebugMenu = () => {
  if (!isDev) return
  
  showDialog({
    title: '客服调试信息',
    message: `
      <div style="text-align: left; padding: 10px; font-family: monospace; font-size: 12px;">
        <p><strong>客服系统状态:</strong></p>
        <p>✓ 客服页面路径: /user/customer-service</p>
        <p>✓ 客服热线: ${serviceConfig.phone}</p>
        <p>✓ 微信客服: ${serviceConfig.wechat}</p>
        <p>✓ QQ客服: ${serviceConfig.qq}</p>
        <p style="margin-top: 15px;"><strong>调试信息:</strong></p>
        <p>• 点击客服按钮将跳转到客服页面</p>
        <p>• 如果跳转失败，将显示联系方式弹窗</p>
        <p>• 右键点击客服按钮查看此调试信息</p>
        <p style="margin-top: 15px; color: #999;">
          当前策略: 客服页面 → 联系方式弹窗
        </p>
      </div>
    `,
    allowHtml: true,
    confirmButtonText: '测试拨号',
    cancelButtonText: '关闭',
    showCancelButton: true,
    beforeClose: async (action) => {
      if (action === 'confirm') {
        // 测试拨号功能
        showToast('测试拨号功能...')
        try {
          callService()
          showToast('拨号功能正常')
        } catch (error) {
          showToast('拨号测试出错: ' + error.message)
        }
      }
      return true
    }
  })
}

// 拨打客服电话
const callService = () => {
  try {
    window.location.href = `tel:${serviceConfig.phone}`
  } catch (error) {
    showToast('拨号失败，请手动拨打客服电话')
  }
}

// 页面滚动时的显示/隐藏逻辑
const scrollY = ref(0)
const isScrolling = ref(false)
let scrollTimer = null

const handleScroll = () => {
  scrollY.value = window.scrollY
  isScrolling.value = true
  
  clearTimeout(scrollTimer)
  scrollTimer = setTimeout(() => {
    isScrolling.value = false
  }, 150)
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})
</script>

<style lang="scss" scoped>
.customer-service-float {
  position: fixed;
  right: 20px;
  bottom: 150px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f42 100%);
  border-radius: 28px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0); /* 开启硬件加速 */

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
  }

  &:active {
    transform: translateY(0) scale(0.95);
    transition: all 0.1s ease;
  }

  .service-icon {
    color: white;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .service-text {
    color: white;
    font-size: 10px;
    font-weight: 500;
    line-height: 1;
  }

  .debug-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: rgba(255, 193, 7, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .customer-service-float {
    right: 16px;
    width: 52px;
    height: 52px;
    border-radius: 26px;
    
    .service-icon {
      font-size: 22px;
    }
    
    .service-text {
      font-size: 9px;
    }
  }
}

/* 减少动画效果（针对低性能设备） */
@media (prefers-reduced-motion: reduce) {
  .customer-service-float {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .customer-service-float {
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    
    &:hover {
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }
  }
}
</style>
