7-28

1.新用户

   注册成功后直接自动登录

    改进后的注册流程：

1. 用户完成注册 → 提交注册信息
2. 自动登录 → 如果后端返回token，系统会自动设置登录状态
3. 优化的用户反馈：

   - 显示 "注册成功！已自动为您登录" （如果有token）
   - 1秒后显示体验金奖励信息
   - 1.5秒后自动跳转到首页
4. 无缝体验 → 用户无需再次输入密码即可使用应用

   改为：

- 显示 "注册成功！已自动为您登录" （如果有token）
- 1秒后显示新用户免费拼团奖励信息（改为：“恭喜你！获得3次免费拼团机会！”“是您首次登录后专享的福利，马上发起拼团并获取利润吧”）***（问题：这3次免费拼团只限定一个新手商品，这个商品首页第1条，3次免费机会用完后就看不到这个新手商品了）***
- 1.5秒后自动跳转到首页

  且每次登录后，如果还没用完3次免费拼团机会，就会弹窗提醒用户，这个弹窗只有一个确定按钮，点击后跳到首页，首页商品列表的第1条固定是仅限新用户可见的商品，此商品仅有拼团购买，拼团费用为0，用户参与这个商品的拼团（下单）时，后端判断免费次数大于0，则拼团失败（必不中），同时发放固定金额的奖励。拼团失败页面提示：本次拼团不发货，同时会有补偿奖励包XX元。

  现在要实现一个新用户引导功能，每个新用户会有3次的免费拼团机会（/api/v1/auth接口中的noviceCount为剩余免费拼团次数），当用户登录成功后，noviceCount>0，就跳到首页，同时弹窗提示用户还有x次免费拼团机会，首页商品列表的第1条固定是仅限新用户可见的商品（api/v1/home接口中"goodsType": "newUser")，此商品仅有拼团购买，拼团费用为0，用户参与这个商品的拼团（下单）后，在拼团失败时跳转一个新用户专用的拼团失败页面，提示：本次拼团不发货，同时会有补偿奖励包XX元。

 现在我已经为您添加了完整的调试功能。请按照以下步骤测试：

  方法一：使用调试函数直接测试

1. 打开浏览器控制台（F12）
2. 在控制台中输入以下命令来直接显示弹窗：
   triggerNewUserGuide()

  方法二：模拟完整的新用户登录流程

1. 在控制台中输入以下命令来模拟localStorage标记：
   setNewUserGuide(3)

  方法三：检查当前状态

1. 在控制台中输入以下命令来检查当前状态：
   checkNewUserGuide()

   继续优化：

   登录后自动跳转首页

   弹窗缩小；

   /api/v1/auth只会调用一次，如何刷新noviceCount？

   新手专享团的拼团失败页面要单独一个页面，不能与正常拼团的失败页面共用（因为要有新手团不发货的文字描述）

2.中了产品，两个选项，一个拿产品，一个不要产品，扣违约金

  违约金扣多少？

  涉及到的有：拼中后的流程、订单内容、流水内容、

 3、拼不中的页面要把哭脸改成笑脸，同时增加显示获得多少的现金奖励，这个金额要很明显

4、订单要增加拼不中时的内容（奖励之类的内容）已经出了一版

5、团队中的拉新奖励规则说明（要在哪里呈现？呈现的内容是什么？）

    目前拉新不按阶梯来计算奖励

***6、在“我的”页面中，要非常明显地显示用户的总收益（即该用户所有未拼中订单中获得的奖励的汇总金额）***

    实现思路：用户的拼团购买时所有未拼中时会获得奖励，增加一个统计用户所有未拼中时获得的奖励的累计汇总金额的字段，并在用户信息接口中返回给前端

    问题：这个总收益需要把违约金减掉吗？违约金需要汇总和显示吗？

***7、在拼团购买且拼中时，增加流程：用户可以选择收货或者不收货，选择收货，则提示确认收货地址（目前的流程），如果选择不收货，则在已支付金额中扣除违约金后退还剩余的款项***

    实现思路：订单接口中增加一个退单功能，调用这个接口可以对指定的订单进行扣除违约金（比例在后台设置）后退款处理，要求把购买支付金额、违约金的比例、违约金的金额也记录下来并返回给前端，前端展现出来），相应的流水记录中也要对应有扣违约金的记录和退款记录。这个违约金也要有个累计汇总金额，送给前端。

8、更多的新手引导、指引

    问题：具体的内容、展示方式（比如弹窗？页面内的详细文字介绍？）

9、【Bug】消费后对上级的积分贡献计算不对，系统设置的是5%，所以要用拼团价X0.05来计算积分贡献。

    问题：直接购买会产生积分贡献吗？

10、【Bug】新注册用户的免费拼团次数不对（返回"noviceCount": 0,） 没有按管理台后中的novice_group_limit（新手团参加次数）来设置noviceCount     【OK】

11、拉新分阶梯奖励

12、代理后台
