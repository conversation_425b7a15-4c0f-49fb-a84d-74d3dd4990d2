#!/bin/bash

echo "🛑 停止生产环境服务..."

PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs/prod"

# 停止后端生产服务
if [ -f "$LOG_DIR/backend-prod.pid" ]; then
    BACKEND_PID=$(cat "$LOG_DIR/backend-prod.pid")
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端生产服务已停止 (PID: $BACKEND_PID)"
        
        # 等待进程完全停止
        sleep 3
        
        # 如果进程仍然存在，强制杀死
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill -9 $BACKEND_PID
            echo "⚡ 强制停止后端服务"
        fi
    fi
    rm -f "$LOG_DIR/backend-prod.pid"
fi

# 停止通过jar包启动的后端服务（兼容原有方式）
PID=$(ps -ef | grep tgw-pp.jar | grep -v grep | awk '{ print $2 }')
if [ ! -z "$PID" ]; then
    echo "🛑 停止JAR包启动的后端服务 (PID: $PID)"
    kill -9 $PID
    echo "✅ JAR包后端服务已停止"
fi

# 检查所有相关进程
JAVA_PROCESSES=$(ps aux | grep "tgw-pp.jar\|AdminApplication" | grep -v grep | wc -l)
if [ $JAVA_PROCESSES -gt 0 ]; then
    echo "🔍 清理残留Java进程..."
    pkill -f "tgw-pp.jar"
    pkill -f "AdminApplication"
fi

echo "🎉 生产环境服务已完全停止！"

echo ""
echo "📊 服务状态检查："
if netstat -tlnp 2>/dev/null | grep -q :8686; then
    echo "⚠️  端口8686仍被占用，可能需要手动处理"
else
    echo "✅ 端口8686已释放"
fi