package net.lab1024.sa.admin.module.business.orders.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersEntity;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersQueryForm;
import net.lab1024.sa.admin.module.business.orders.domain.vo.OrdersVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 订单表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Mapper
public interface OrdersDao extends BaseMapper<OrdersEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<OrdersVO> queryPage(Page page, @Param("queryForm") OrdersQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
