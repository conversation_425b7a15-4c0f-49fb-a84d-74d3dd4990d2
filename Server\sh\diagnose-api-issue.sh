#!/bin/bash

echo "🔍 API问题诊断工具"
echo "=================="

PROJECT_ROOT="/mnt/d/Dev/团购网"

echo ""
echo "📊 当前系统状态："
echo "=================="

# 检查Nginx状态
echo "🔧 Nginx状态:"
if command -v nginx >/dev/null 2>&1; then
    if systemctl is-active --quiet nginx; then
        echo "  ✅ Nginx已安装且正在运行"
        nginx_running=true
    else
        echo "  ⚠️  Nginx已安装但未运行"
        nginx_running=false
    fi
else
    echo "  ❌ Nginx未安装"
    nginx_running=false
fi

# 检查端口占用
echo ""
echo "📡 端口占用情况:"
echo "  端口80 (Nginx前端代理):"
if netstat -tlnp 2>/dev/null | grep -q :80; then
    echo "    ✅ 已占用"
    netstat -tlnp | grep :80
else
    echo "    ❌ 未占用"
fi

echo "  端口3000 (前端开发服务器):"
if netstat -tlnp 2>/dev/null | grep -q :3000; then
    echo "    ✅ 已占用"
    netstat -tlnp | grep :3000
else
    echo "    ❌ 未占用"
fi

echo "  端口8686 (后端API服务):"
if netstat -tlnp 2>/dev/null | grep -q :8686; then
    echo "    ✅ 已占用"
    netstat -tlnp | grep :8686
else
    echo "    ❌ 未占用"
fi

# 检查服务进程
echo ""
echo "🚀 服务进程:"
echo "  前端进程:"
frontend_processes=$(ps aux | grep -E "(vite|npm.*dev)" | grep -v grep | wc -l)
if [ $frontend_processes -gt 0 ]; then
    echo "    ✅ 发现 $frontend_processes 个前端进程"
    ps aux | grep -E "(vite|npm.*dev)" | grep -v grep | head -3
else
    echo "    ❌ 未发现前端进程"
fi

echo "  后端进程:"
backend_processes=$(ps aux | grep -E "(spring-boot:run|tgw-pp.jar|AdminApplication)" | grep -v grep | wc -l)
if [ $backend_processes -gt 0 ]; then
    echo "    ✅ 发现 $backend_processes 个后端进程"
    ps aux | grep -E "(spring-boot:run|tgw-pp.jar|AdminApplication)" | grep -v grep | head -3
else
    echo "    ❌ 未发现后端进程"
fi

# 测试API连接
echo ""
echo "🧪 API连接测试:"
echo "=================="

# 测试后端健康检查
echo "  测试后端健康检查 (localhost:8686):"
if curl -s --connect-timeout 5 http://localhost:8686/actuator/health >/dev/null 2>&1; then
    health_response=$(curl -s http://localhost:8686/actuator/health)
    echo "    ✅ 后端健康检查正常"
    echo "    响应: $health_response"
else
    echo "    ❌ 后端健康检查失败"
fi

# 测试前端连接
echo "  测试前端开发服务器 (localhost:3000):"
if curl -s --connect-timeout 5 http://localhost:3000/ >/dev/null 2>&1; then
    echo "    ✅ 前端开发服务器正常"
else
    echo "    ❌ 前端开发服务器无法访问"
fi

# 测试问题API (如果Nginx运行)
if [ "$nginx_running" = true ]; then
    echo "  测试问题API (localhost/app/v1/myTeam):"
    api_response=$(curl -s --connect-timeout 5 -H "Accept: application/json" "http://localhost/app/v1/myTeam?pageNum=0&pageSize=20" 2>/dev/null)
    if echo "$api_response" | grep -q "<!DOCTYPE html>"; then
        echo "    ❌ API返回HTML页面 (代理配置问题)"
        echo "    响应长度: $(echo "$api_response" | wc -c) 字符"
    elif echo "$api_response" | grep -q "{"; then
        echo "    ✅ API返回JSON数据"
        echo "    响应: $(echo "$api_response" | head -c 200)..."
    else
        echo "    ⚠️  API响应异常"
        echo "    响应: $(echo "$api_response" | head -c 100)..."
    fi
else
    echo "  ⚠️  Nginx未运行，跳过API测试"
fi

# 检查MySQL和Redis
echo ""
echo "💾 依赖服务:"
echo "============="
echo "  MySQL状态:"
if systemctl is-active --quiet mysql 2>/dev/null; then
    echo "    ✅ MySQL正在运行"
else
    echo "    ❌ MySQL未运行"
fi

echo "  Redis状态:"
if systemctl is-active --quiet redis-server 2>/dev/null; then
    echo "    ✅ Redis正在运行"
else
    echo "    ❌ Redis未运行"
fi

# 给出建议
echo ""
echo "💡 问题诊断和建议:"
echo "=================="

if [ "$nginx_running" = false ]; then
    echo "🔧 主要问题: Nginx未安装或未运行"
    echo ""
    echo "解决方案:"
    echo "1. 安装并配置Nginx代理:"
    echo "   sudo ./setup-nginx-proxy.sh"
    echo ""
    echo "2. 启动必要服务:"
    echo "   ./start-all-dev.sh"
    echo ""
    echo "3. 验证配置:"
    echo "   ./nginx-manager.sh"
fi

if [ $frontend_processes -eq 0 ]; then
    echo "⚠️  前端开发服务器未运行"
    echo "   启动命令: ./start-frontend-dev.sh"
fi

if [ $backend_processes -eq 0 ]; then
    echo "⚠️  后端API服务未运行"
    echo "   启动命令: ./start-backend-dev.sh"
fi

echo ""
echo "🚀 快速修复流程:"
echo "1. sudo ./setup-nginx-proxy.sh     # 安装配置Nginx"
echo "2. ./start-all-dev.sh              # 启动所有开发服务"
echo "3. ./check-status.sh               # 检查服务状态"
echo "4. 测试: http://localhost/app/v1/myTeam?pageNum=0&pageSize=20"

echo ""
echo "📞 如需进一步帮助:"
echo "   ./nginx-manager.sh              # Nginx管理工具"
echo "   ./logs.sh                       # 查看日志"