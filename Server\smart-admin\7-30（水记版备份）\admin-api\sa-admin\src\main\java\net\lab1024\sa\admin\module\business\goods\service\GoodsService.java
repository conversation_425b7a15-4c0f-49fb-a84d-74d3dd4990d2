package net.lab1024.sa.admin.module.business.goods.service;

import cn.idev.excel.FastExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesEntity;
import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.admin.module.business.category.constant.CategoryTypeEnum;
import net.lab1024.sa.admin.module.business.category.domain.entity.CategoryEntity;
import net.lab1024.sa.admin.module.business.category.service.CategoryQueryService;
import net.lab1024.sa.admin.module.business.goods.constant.GoodsStatusEnum;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsDao;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsSkusDao;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsEntity;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.admin.module.business.goods.domain.form.*;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsExcelVO;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsSkusVO;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsVO;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartEnumUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import net.lab1024.sa.base.module.support.dict.service.DictService;
import net.lab1024.sa.base.module.support.file.service.IFileStorageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsService {

    @Resource
    private GoodsDao goodsDao;

    @Resource
    private GoodsSkusDao goodsSkusDao;

    @Resource
    private CategoryQueryService categoryQueryService;

    @Resource
    private DataTracerService dataTracerService;

    @Resource
    ActivitiesService activitiesService;

    @Resource
    private DictService dictService;

    @Resource
    private IFileStorageService fileStorageService;
    /**
     * 添加商品
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(GoodsAddForm addForm) {
        // 商品校验
        ResponseDTO<String> res = this.checkGoods(addForm.getCategoryId());
        if (!res.getOk()) {
            return res;
        }
        GoodsEntity goodsEntity = SmartBeanUtil.copy(addForm, GoodsEntity.class);
        goodsEntity.setDeletedFlag(Boolean.FALSE);
        goodsDao.insert(goodsEntity);
        dataTracerService.insert(goodsEntity.getGoodsId(), DataTracerTypeEnum.GOODS);
        return ResponseDTO.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addDetail(GoodsAddDetailForm addForm) {
        // 商品校验
        ResponseDTO<String> res = this.checkGoods(addForm.getCategoryId());
        if (!res.getOk()) {
            return res;
        }
        GoodsEntity goodsEntity = SmartBeanUtil.copy(addForm, GoodsEntity.class);
        goodsEntity.setDeletedFlag(Boolean.FALSE);
        goodsDao.insert(goodsEntity);
        dataTracerService.insert(goodsEntity.getGoodsId(), DataTracerTypeEnum.GOODS);

        if(!addForm.getSkus().isEmpty()){
            for(GoodsSkusEntity gs : addForm.getSkus()){
                gs.setGoodsId(goodsEntity.getGoodsId());
                gs.setAloneFlag(addForm.getAloneFlag());
                goodsSkusDao.insert(gs);
            }

            //设置商品价格
            goodsEntity.setPrice(getPrice(addForm.getSkus(), 0));
            if(addForm.getAloneFlag()) {
                //设置单买价格
                goodsEntity.setAlonePrice(getPrice(addForm.getSkus(), 1));
            }

            goodsDao.updateById(goodsEntity);
        }

        return ResponseDTO.ok();
    }

    /**
     * 更新商品
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(GoodsUpdateForm updateForm) {
        // 商品校验
        ResponseDTO<String> res = this.checkGoods(updateForm.getCategoryId());
        if (!res.getOk()) {
            return res;
        }
        GoodsEntity originEntity = goodsDao.selectById(updateForm.getGoodsId());
        GoodsEntity goodsEntity = SmartBeanUtil.copy(updateForm, GoodsEntity.class);
        goodsDao.updateById(goodsEntity);
        dataTracerService.update(updateForm.getGoodsId(), DataTracerTypeEnum.GOODS, originEntity, goodsEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新商品
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateDetail(GoodsUpdateDetailForm updateForm) {
        // 商品校验
        ResponseDTO<String> res = this.checkGoods(updateForm.getCategoryId());
        if (!res.getOk()) {
            return res;
        }
        GoodsEntity originEntity = goodsDao.selectById(updateForm.getGoodsId());
        GoodsEntity goodsEntity = SmartBeanUtil.copy(updateForm, GoodsEntity.class);
        dataTracerService.update(updateForm.getGoodsId(), DataTracerTypeEnum.GOODS, originEntity, goodsEntity);

        if(!updateForm.getSkus().isEmpty()){
            goodsSkusDao.setNullByGoodsId(goodsEntity.getGoodsId());
            for(GoodsSkusEntity gs : updateForm.getSkus()){
                gs.setGoodsId(goodsEntity.getGoodsId());
                gs.setId(null);
                gs.setAloneFlag(updateForm.getAloneFlag());
                goodsSkusDao.insert(gs);
            }

            //设置商品价格
            goodsEntity.setPrice(getPrice(updateForm.getSkus(), 0));
            if(updateForm.getAloneFlag()) {
                //设置单买价格
                goodsEntity.setAlonePrice(getPrice(updateForm.getSkus(), 1));
            }
        }

        goodsDao.updateById(goodsEntity);

        return ResponseDTO.ok();
    }

    /**
     * 计算价格范围
     */
    public String getPrice(List<GoodsSkusEntity> resources, int typ) {
        String price;
        BigDecimal min = (typ == 1)?resources.get(0).getAlonePrice(): resources.get(0).getPrice();
        BigDecimal max = min;
        for (GoodsSkusEntity row: resources) {
            if(typ == 1) {
                if (min.compareTo(row.getAlonePrice()) > 0) {
                    min = row.getAlonePrice();
                }
                if (row.getAlonePrice().compareTo(max) > 0) {
                    max = row.getAlonePrice();
                }
            } else {
                if (min.compareTo(row.getPrice()) > 0) {
                    min = row.getPrice();
                }
                if (row.getPrice().compareTo(max) > 0) {
                    max = row.getPrice();
                }
            }
        }
        if(max.equals(min)){
            price = String.valueOf(min);
        } else {
            price = min + "~" + max;
        }
        return price;
    }

    /**
     * 添加/更新 商品校验
     */
    private ResponseDTO<String> checkGoods(Long categoryId) {
        // 校验类目id
        //Long categoryId = addForm.getCategoryId();
        Optional<CategoryEntity> optional = categoryQueryService.queryCategory(categoryId);
        if (!optional.isPresent() || !CategoryTypeEnum.GOODS.equalsValue(optional.get().getCategoryType())) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "商品类目不存在~");
        }

        return ResponseDTO.ok();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long goodsId) {
        GoodsEntity goodsEntity = goodsDao.selectById(goodsId);
        if (goodsEntity == null) {
            return ResponseDTO.userErrorParam("商品不存在");
        }

        if (!goodsEntity.getGoodsStatus().equals(GoodsStatusEnum.SELL_OUT.getValue())) {
            return ResponseDTO.userErrorParam("只有售罄的商品才可以删除");
        }

        batchDelete(Collections.singletonList(goodsId));
        dataTracerService.batchDelete(Collections.singletonList(goodsId), DataTracerTypeEnum.GOODS);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     */
    public ResponseDTO<String> batchDelete(List<Long> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return ResponseDTO.ok();
        }

        goodsDao.batchUpdateDeleted(goodsIdList, Boolean.TRUE);
        return ResponseDTO.ok();
    }


    /**
     * 分页查询
     */
    public ResponseDTO<PageResult<GoodsVO>> query(GoodsQueryForm queryForm) {
        queryForm.setDeletedFlag(false);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<GoodsVO> list = goodsDao.query(page, queryForm);
        PageResult<GoodsVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        if (pageResult.getEmptyFlag()) {
            return ResponseDTO.ok(pageResult);
        }
        // 查询分类名称
        List<Long> categoryIdList = list.stream().map(GoodsVO::getCategoryId).distinct().collect(Collectors.toList());
        Map<Long, CategoryEntity> categoryMap = categoryQueryService.queryCategoryList(categoryIdList);
        list.forEach(e -> {
            CategoryEntity categoryEntity = categoryMap.get(e.getCategoryId());
            if (categoryEntity != null) {
                e.setCategoryName(categoryEntity.getCategoryName());
            }
        });
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 分页查询
     */
    public ResponseDTO<PageResult<GoodsSkusVO>> skusQuery(GoodsSkusQueryForm queryForm) {
        queryForm.setDeletedFlag(false);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<GoodsSkusVO> list = goodsSkusDao.query(page, queryForm);
        for (GoodsSkusVO gs : list) {
            ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(gs.getImage());
            if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                gs.setImage(getFileUrl.getData());
            }
        }
        PageResult<GoodsSkusVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    public GoodsSkusVO selectOneSkusById(long skuId) {
        GoodsSkusVO gs = goodsSkusDao.queryById(skuId);
        if(gs==null){ return null; }
        ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(gs.getImage());
        if (BooleanUtils.isTrue(getFileUrl.getOk())) {
            gs.setImage(getFileUrl.getData());
        }
        return gs;
    }

    /**
     * 商品导入
     *
     * @param file 上传文件
     * @return 结果
     */
    public ResponseDTO<String> importGoods(MultipartFile file) {
        List<GoodsImportForm> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream()).head(GoodsImportForm.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            return ResponseDTO.userErrorParam("数据为空");
        }

        return ResponseDTO.okMsg("成功导入" + dataList.size() + "条，具体数据为：" + JSON.toJSONString(dataList));
    }

    /**
     * 商品导出
     */
    public List<GoodsExcelVO> getAllGoods() {
        List<GoodsEntity> goodsEntityList = goodsDao.selectList(null);
        String dictCode = "GOODS_PLACE";
        return goodsEntityList.stream()
                .map(e ->
                        GoodsExcelVO.builder()
                                .goodsStatus(SmartEnumUtil.getEnumDescByValue(e.getGoodsStatus(), GoodsStatusEnum.class))
                                .categoryName(categoryQueryService.queryCategoryName(e.getCategoryId()))
                                .place(Arrays.stream(e.getPlace().split(",")).map(code -> dictService.getDictDataLabel(dictCode, code)).collect(Collectors.joining(",")))
                                .price(e.getPrice())
                                .goodsName(e.getGoodsName())
                                .remark(e.getRemark())
                                .build()
                )
                .collect(Collectors.toList());

    }

    //hasActivity=true带活动数据
    public ResponseDTO<GoodsVO> getId(Long goodsId, Boolean hasActivity) {
        GoodsEntity ge = goodsDao.selectById(goodsId);
        if (ge == null) { return ResponseDTO.ok(); }
        if (ge.getImages() != null && !ge.getImages().isEmpty()){
            for(Map<String, Object> m : ge.getImages() ){
                String url = m.get("url").toString();
                ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                    m.put("url", getFileUrl.getData());
                }
            }
        }
        if (ge.getDetailImages() != null && !ge.getDetailImages().isEmpty()){
            for(Map<String, Object> m : ge.getDetailImages() ){
                String url = m.get("url").toString();
                ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                    m.put("url", getFileUrl.getData());
                }
            }
        }

        GoodsVO gv = SmartBeanUtil.copy(ge, GoodsVO.class);
        QueryWrapper<GoodsSkusEntity> Q1 = new QueryWrapper<>();
        Q1.eq("goods_id", goodsId).select("id,goods_id,sku_code,attributes,price,original_price,alone_price,stock,sales_count,status");
        gv.setSkus(goodsSkusDao.selectList(Q1));

        if(hasActivity && ge.getActivityId() != null){
            ActivitiesEntity activitiesEntity = activitiesService.selectById(ge.getActivityId());
            gv.setActivity(SmartBeanUtil.copy(activitiesEntity, ActivitiesVO.class));
        }

        return ResponseDTO.ok(gv);
    }
}
