  SELECT  inviter_id
  FROM t_employee
  WHERE employee_id = 91;


+-------------+-------------+---------------------------------------------------------------------------------------------------+-------------+--------+--------+-------------+---------------+-------------+-------+------------+-------------+--------------+------------+----------+---------------+--------------+--------------------+--------+---------------------+---------------------+
| employee_id | login_name  | login_pwd                                                                                         | actual_name | avatar | gender | phone       | department_id | position_id | email | inviter_id | child_count | novice_count | risk_level | has_real | disabled_flag | deleted_flag | administrator_flag | remark | update_time         | create_time         |
+-------------+-------------+---------------------------------------------------------------------------------------------------+-------------+--------+--------+-------------+---------------+-------------+-------+------------+-------------+--------------+------------+----------+---------------+--------------+--------------------+--------+---------------------+---------------------+
|          91 | 15816894388 | $argon2id$v=19$m=16384,t=2,p=1$ece40O3ab01HfAEKLQgu0w$1iD+YxIwP1lYfZLlMKOWHJlZOfP8FsxNAriy3kzxiAk | 15816894388 | NULL   |      0 | 15816894388 |             9 |           5 | NULL  |       NULL |           0 |            0 |          0 |        0 |             0 |            0 |                  0 | NULL   | 2025-07-27 10:37:40 | 2025-07-27 10:37:40 |
+-------------+-------------+---------------------------------------------------------------------------------------------------+-------------+--------+--------+-------------+---------------+-------------+-------+------------+-------------+--------------+------------+----------+---------------+--------------+--------------------+--------+---------------------+---------------------+
1 <USER> <GROUP> set (0.00 sec)


SELECT employee_id, login_name, novice_Count，novice_GroupLimit
  FROM t_employee
  WHERE employee_id = 102;


SELECT TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME
FROM information_schema.COLUMNS
WHERE COLUMN_NAME = 'novice';

  SELECT  *
  FROM t_activities_count


UPDATE t_employee
SET novice_Count = 0
WHERE employee_id = 102;


把免费拼团次数的来源由现在白noviceCount改为noviceGroupLimit

  SELECT  *
  FROM t_activities_count

SELECT TABLE_NAME, COLUMN_NAME
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = 'tgw_pp';