# 首页弹窗广告显示条件优化说明

## 修改概述

根据用户需求，在首页弹窗广告的显示条件中增加了对用户信息中 `noviceGroupLimit` 字段的检查。

## 修改内容

### 1. 导入 AuthStore

在 `HomePage.vue` 中新增了 `useAuthStore` 的导入和使用：

```javascript
import { useAuthStore } from '@/store/auth'

// 在 setup() 函数中
const authStore = useAuthStore()
```

### 2. 优化弹窗显示逻辑

修改了 `shouldShowSubsidyModal()` 函数，现在的显示条件为：

#### 显示弹窗的条件（所有条件都需要满足）：
1. **未登录用户**：直接显示弹窗
2. **已登录用户需满足以下所有条件**：
   - 用户订单数量 < 3 单
   - 用户信息中的 `noviceGroupLimit` < 4

#### 不显示弹窗的条件（满足任一条件）：
1. 用户订单数量 ≥ 3 单
2. 用户的 `noviceGroupLimit` > 3

## 具体实现

```javascript
const shouldShowSubsidyModal = async () => {
  try {
    // 检查用户是否登录
    const token = localStorage.getItem('access_token') || 
                 sessionStorage.getItem('access_token') ||
                 localStorage.getItem('token')
    
    // 未登录用户：显示弹窗
    if (!token) {
      console.log('🔍 用户未登录，显示弹窗')
      return true
    }
    
    // 已登录用户：检查多个条件
    console.log('🔍 用户已登录，检查弹窗显示条件...')
    
    // 1. 检查订单数量
    const orderCount = await getUserOrderCount()
    console.log('🔍 用户订单数量:', orderCount)
    
    if (orderCount >= 3) {
      console.log('🔍 订单数量>=3单，不显示弹窗')
      return false
    }
    
    // 2. 检查用户信息中的noviceGroupLimit
    const userInfo = authStore.user
    if (userInfo && userInfo.noviceGroupLimit !== undefined) {
      const noviceGroupLimit = userInfo.noviceGroupLimit
      console.log('🔍 用户noviceGroupLimit值:', noviceGroupLimit)
      
      if (noviceGroupLimit > 3) {
        console.log('🔍 noviceGroupLimit>3，不显示弹窗')
        return false
      }
    } else {
      console.log('🔍 用户信息中没有noviceGroupLimit字段或用户信息为空')
    }
    
    // 所有条件检查通过：显示弹窗
    console.log('🔍 所有检查条件通过，显示弹窗')
    return true
    
  } catch (error) {
    console.error('🚨 检查弹窗显示条件失败:', error)
    // 出错时，为了保险起见，不显示弹窗
    return false
  }
}
```

## 调试日志

函数中包含了详细的控制台日志，便于调试和追踪弹窗显示逻辑：

- `🔍 用户未登录，显示弹窗`
- `🔍 用户已登录，检查弹窗显示条件...`
- `🔍 用户订单数量: {orderCount}`
- `🔍 订单数量>=3单，不显示弹窗`
- `🔍 用户noviceGroupLimit值: {noviceGroupLimit}`
- `🔍 noviceGroupLimit>3，不显示弹窗`
- `🔍 用户信息中没有noviceGroupLimit字段或用户信息为空`
- `🔍 所有检查条件通过，显示弹窗`

## 兼容性处理

1. **安全检查**：在访问 `authStore.user.noviceGroupLimit` 前进行了空值检查
2. **容错处理**：如果用户信息中没有 `noviceGroupLimit` 字段，会记录日志但不会阻止其他条件的检查
3. **错误处理**：在发生异常时，默认不显示弹窗，确保用户体验

## 测试建议

1. **未登录用户**：确认弹窗正常显示
2. **已登录新用户**（订单数 < 3，noviceGroupLimit < 4）：确认弹窗显示
3. **订单数 ≥ 3 的用户**：确认弹窗不显示
4. **noviceGroupLimit > 3 的用户**：确认弹窗不显示
5. **用户信息缺失的情况**：确认不会产生错误

## 修改日期

2024年12月底 