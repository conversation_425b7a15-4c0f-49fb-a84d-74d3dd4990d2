#!/bin/bash

echo "🎯 启动完整开发环境..."

# 创建日志目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs"
mkdir -p "$LOG_DIR"

# 检查并启动MySQL和Redis
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "🔄 启动MySQL服务..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "🔄 启动Redis服务..."
    sudo systemctl start redis-server
fi

# 启动后端服务（后台运行）
echo "🚀 启动后端服务..."
cd "$PROJECT_ROOT/Server/smart-admin/admin-api/sa-admin"
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > "$LOG_DIR/backend.log" 2>&1 &
BACKEND_PID=$!
echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动完成
echo "⏳ 等待后端服务启动完成..."
sleep 30

# 启动前端应用（后台运行）
echo "🌐 启动前端应用..."
cd "$PROJECT_ROOT/APP"
nohup npm run dev > "$LOG_DIR/frontend.log" 2>&1 &
FRONTEND_PID=$!
echo "✅ 前端应用已启动 (PID: $FRONTEND_PID)"

# 启动管理端（后台运行）
echo "🔧 启动管理端..."
cd "$PROJECT_ROOT/Server/smart-admin/admin-web"
nohup npm run dev -- --port 3001 > "$LOG_DIR/admin-web.log" 2>&1 &
ADMIN_PID=$!
echo "✅ 管理端已启动 (PID: $ADMIN_PID)"

# 保存PID到文件
echo $BACKEND_PID > "$LOG_DIR/backend.pid"
echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
echo $ADMIN_PID > "$LOG_DIR/admin-web.pid"

echo ""
echo "🎉 所有服务启动完成！"
echo ""
echo "📍 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8686"
echo "   管理后台: http://localhost:3001"
echo "   API文档:  http://localhost:8686/doc.html"
echo ""
echo "📝 日志文件："
echo "   后端日志: $LOG_DIR/backend.log"
echo "   前端日志: $LOG_DIR/frontend.log"
echo "   管理端日志: $LOG_DIR/admin-web.log"
echo ""
echo "🛑 停止所有服务请运行: $SCRIPT_DIR/stop-all.sh"