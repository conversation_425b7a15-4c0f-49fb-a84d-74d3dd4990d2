package net.lab1024.sa.admin.module.business.orders.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.constant.OrdersConst;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsSkusQueryForm;
import net.lab1024.sa.admin.module.business.goods.service.GoodsService;
import net.lab1024.sa.admin.module.business.orders.dao.OrdersDao;
import net.lab1024.sa.admin.module.business.orders.dao.OrdersLogisticsDao;
import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersEntity;
import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersLogisticsEntity;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersAddForm;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersQueryForm;
import net.lab1024.sa.admin.module.business.orders.domain.form.OrdersUpdateForm;
import net.lab1024.sa.admin.module.business.orders.domain.vo.OrdersVO;
import java.util.Random;

import net.lab1024.sa.admin.module.business.userAddress.domain.entity.UserAddressEntity;
import net.lab1024.sa.admin.module.business.userAddress.domain.vo.UserAddressVO;
import net.lab1024.sa.admin.module.business.userAddress.service.UserAddressService;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.base.module.support.config.ConfigKeyEnum;
import net.lab1024.sa.base.module.support.config.ConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 订单表 Service
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */
@Slf4j
@Service
public class OrdersService {
    private static final Random random = new Random();

    @Resource
    private OrdersDao ordersDao;

    @Resource
    private OrdersLogisticsDao ordersLogisticsDao;

    @Resource
    private ConfigService configService;

    @Resource
    private UserAddressService userAddressService;

    @Resource
    private GoodsService goodsService;
    /**
     * 分页查询
     */
    public PageResult<OrdersVO> queryPage(OrdersQueryForm queryForm) {
        queryForm.setDeletedFlag(0);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<OrdersVO> list = ordersDao.queryPage(page, queryForm);
//        for (OrdersVO ordersVO : list) {
//            RequestEmployee re = loginManager.getRequestEmployee(ordersVO.getUserId());
//            if (re != null) {
//                ordersVO.setUserName(re.getActualName());
//            }
//        }
        for (OrdersVO ordersVO : list) {
            if(ordersVO.getSkuId()!=null){
//                GoodsSkusQueryForm goodsSkusQueryForm = new GoodsSkusQueryForm();
//                goodsSkusQueryForm.setId(ordersVO.getSkuId());
//                goodsSkusQueryForm.setPageNum(0L);
//                goodsSkusQueryForm.setPageSize(1L);
//                ordersVO.setSku(goodsService.skusQuery(goodsSkusQueryForm).getData().getList().get(0));
                ordersVO.setSku(goodsService.selectOneSkusById(ordersVO.getSkuId()));
            }
        }

        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(OrdersAddForm addForm) {
        OrdersEntity ordersEntity = SmartBeanUtil.copy(addForm, OrdersEntity.class);
        ordersDao.insert(ordersEntity);
        return ResponseDTO.ok();
    }

    /**
     * 添加
     */
    public void insert(OrdersEntity ordersEntity) {
        ordersDao.insert(ordersEntity);
    }

    /**
     * 保存
     */
    public void save(OrdersEntity ordersEntity) {
        ordersDao.updateById(ordersEntity);
    }

    /**
     * 更新
     *
     */
    public ResponseDTO<String> update(OrdersUpdateForm updateForm) {
        OrdersEntity oe = ordersDao.selectById(updateForm.getId());
        //OrdersEntity ordersEntity = SmartBeanUtil.copy(updateForm, OrdersEntity.class);
        oe.setStatus(updateForm.getStatus());
        ordersDao.updateById(oe);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        ordersDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        ordersDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    public ResponseDTO<OrdersVO> detail(Long id) {
        OrdersVO ov = SmartBeanUtil.copy(ordersDao.selectById(id), OrdersVO.class);
        return ResponseDTO.ok(ov);
    }

    public ResponseDTO<OrdersLogisticsEntity> detailLogisticsByOrderId(Long orderId) {
        return ResponseDTO.ok(ordersLogisticsDao.selectByOrderId(orderId));
    }

    /**
     * 添加物流信息
     */
    public ResponseDTO<String> addLogistics(OrdersLogisticsEntity addForm) {
        if (Objects.equals(addForm.getStatus(), "shipped")) {
            addForm.setShippedTime(LocalDateTime.now());
        } else if (Objects.equals(addForm.getStatus(), "delivered")) {
            addForm.setDeliverTime(LocalDateTime.now());
        }
        ordersLogisticsDao.insert(addForm);
        return ResponseDTO.ok();
    }

    /**
     * 更新物流信息
     */
    public ResponseDTO<String> updateLogistics(OrdersLogisticsEntity updateForm) {
        OrdersLogisticsEntity oe = ordersLogisticsDao.selectById(updateForm.getId());
        if (!Objects.equals(oe.getStatus(), updateForm.getStatus())) {
            if (Objects.equals(updateForm.getStatus(), "shipped")) {
                oe.setShippedTime(LocalDateTime.now());
            } else if (Objects.equals(updateForm.getStatus(), "delivered")) {
                oe.setDeliverTime(LocalDateTime.now());
            }
        }
        oe.setTrackingNumber(updateForm.getTrackingNumber());
        oe.setCourierCompany(updateForm.getCourierCompany());
        oe.setTrackingInfo(updateForm.getTrackingInfo());
        oe.setStatus(updateForm.getStatus());
        ordersLogisticsDao.updateById(oe);
        return ResponseDTO.ok();
    }

    public OrdersVO selectOne(QueryWrapper<OrdersEntity> q1) {
        OrdersVO ov = SmartBeanUtil.copy(ordersDao.selectOne(q1), OrdersVO.class);
        if(ov == null){ return null; }
        if(ov.getShippingAddressId() != null){
            UserAddressEntity uae = userAddressService.getId(ov.getShippingAddressId());
            if(uae != null) {
                UserAddressVO shippingInfo = SmartBeanUtil.copy(uae, UserAddressVO.class);
                ov.setShippingInfo(shippingInfo);
            }
        }
        if(ov.getSkuId() != null){
            ov.setSku(goodsService.selectOneSkusById(ov.getSkuId()));
        }
        return ov;
    }

    public Integer getWinPercentage(String activitiesType) {
        if (Objects.equals(activitiesType, OrdersConst.ActivitiesType.NOVICE)){
            return 0;
        }
        int winRate = 0;
        if (Objects.equals(activitiesType, OrdersConst.ActivitiesType.LOW_PRICE)){
            winRate = configService.getConfigValueInteger(ConfigKeyEnum.LOW_PRICE_WIN_RATIO);
            log.info("低价区几率：{}", winRate);
        } else if (Objects.equals(activitiesType, OrdersConst.ActivitiesType.HIGH_PRICE)) {
            winRate = configService.getConfigValueInteger(ConfigKeyEnum.HIGH_PRICE_WIN_RATIO);
            log.info("高价区几率：{}", winRate);
        }

        boolean isWin = false;
        int rand = random.nextInt(100);
        log.info("rand：{}", rand);
        if (rand < winRate) {         // 0-19 (20%)
            isWin = true;
        }
        return isWin ? 1 : 0;
    }

    public OrdersEntity getId(Long orderId) {
        return ordersDao.selectById(orderId);
    }
}
