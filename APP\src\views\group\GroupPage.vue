<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="nav-header">
      <van-nav-bar
        title="拼团专区"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <!-- Tab切换 -->
    <div class="tab-container">
      <van-tabs v-model:active="activeTab" animated swipeable>
        <van-tab 
          v-for="tab in groupTabs" 
          :key="tab.type" 
          :title="tab.title"
          :name="tab.type"
        >
          <!-- 商品列表 -->
          <div class="products-section">
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
              <van-list
                v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <div class="products-grid" v-if="currentProductList.length > 0">
                  <div 
                    v-for="product in currentProductList" 
                    :key="product.id"
                    class="product-card"
                    @click="goToProductDetail(product)"
                  >
                    <div class="product-image">
                      <img :src="product.image" :alt="product.name" />
                      <div class="product-tag" :class="getProductTagClass(product)">
                        {{ getProductTagText(product) }}
                      </div>
                      <div class="group-progress">
                        <div class="progress-bar">
                          <div 
                            class="progress-fill" 
                            :style="{ width: getProgressPercentage(product) + '%' }"
                          ></div>
                        </div>
                        <span class="progress-text">{{ getProgressText(product) }}</span>
                      </div>
                    </div>
                    <div class="product-info">
                      <h3 class="product-title">{{ product.name }}</h3>
                      <div class="product-price">
                        <span class="current-price">¥{{ product.groupPrice }}</span>
                        <span class="original-price">¥{{ product.originalPrice }}</span>
                        <span class="discount">{{ getDiscountText(product) }}</span>
                      </div>
                      <div class="product-status">
                        <div class="participants">
                          <van-icon name="friends" />
                          <span>{{ product.currentParticipants }}/{{ getTotalPeople(product) }}人</span>
                        </div>
                        <div class="time-left" v-if="product.timeLeft">
                          <van-icon name="clock" />
                          <span>{{ product.timeLeft }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 空状态 -->
                <div v-else class="empty-state">
                  <van-empty description="暂无商品" />
                </div>
              </van-list>
            </van-pull-refresh>
          </div>
        </van-tab>
      </van-tabs>
    </div>


    <!-- 底部导航栏 -->
    <BottomNav current="group" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import BottomNav from '@/components/common/BottomNav.vue'
import { showToast } from 'vant'
import { 
  getGroupConfig, 
  getEnabledGroupTypes, 
  getProductCategory, 
  getCategoryTagStyle,
  ProductCategories 
} from '@/utils/groupConfig'

const router = useRouter()
const route = useRoute()

// 响应式数据
const activeTab = ref('group3')
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)

// Tab配置
const groupTabs = ref([
  { type: 'group3', title: '三人团', groupSize: 3 },
  { type: 'group5', title: '五人团', groupSize: 5 }
])

// 商品列表数据
const productLists = reactive({
  group3: [],
  group5: []
})

// 拼团配置
const groupConfigs = ref({
  group3: { totalPeople: 3, tagText: '三人团' },
  group5: { totalPeople: 5, tagText: '五人团' }
})

// 计算属性
const currentProductList = computed(() => {
  return productLists[activeTab.value] || []
})

// 🚫 Mock数据已移除 - 使用真实API获取拼团商品数据

// 页面加载时触发
onMounted(async () => {
  await initializeGroupConfigs()
  
  // 从路由参数获取默认Tab
  const groupSize = route.query.groupSize
  if (groupSize) {
    activeTab.value = `group${groupSize}`
  }
  
  loadProducts()
})

// 监听Tab切换
watch(activeTab, () => {
  resetPagination()
  loadProducts()
})

// 初始化拼团配置
const initializeGroupConfigs = async () => {
  try {
    const [group3, group5] = await Promise.all([
      getGroupConfig('group3'),
      getGroupConfig('group5')
    ])
    
    groupConfigs.value.group3 = group3
    groupConfigs.value.group5 = group5
    
    // 更新Tab标题
    groupTabs.value[0].title = group3.tagText || '三人团'
    groupTabs.value[1].title = group5.tagText || '五人团'
  } catch (error) {
    console.error('获取拼团配置失败:', error)
  }
}

// 加载商品列表
const loadProducts = async () => {
  try {
    loading.value = true
    
    // 🚫 使用真实API获取拼团商品数据
    const response = await homeApi.getProducts({
      category: 'group',
      groupType: activeTab.value,
      page: currentPage.value,
      per_page: 10
    })
    
    if (response.code === 0 && response.ok) {
      const newProducts = response.data.products || []
      
      if (currentPage.value === 1) {
        productLists[activeTab.value] = newProducts
      } else {
        productLists[activeTab.value].push(...newProducts)
      }
      
      // 检查是否还有更多数据
      finished.value = !response.data.has_more
    } else {
      throw new Error(response.msg || '获取商品列表失败')
    }
  } catch (error) {
    console.error('加载商品失败:', error)
    showToast({ message: '加载商品失败，请重试', type: 'fail' })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1
  finished.value = false
  loading.value = false
}

// 获取商品标签样式
const getProductTagClass = (product) => {
  if (product.isActivity) return 'tag-activity'
  if (product.category === ProductCategories.VIP) return 'tag-vip'
  if (product.groupType === 'group3') return 'tag-group3'
  if (product.groupType === 'group5') return 'tag-group5'
  return 'tag-normal'
}

// 获取商品标签文字
const getProductTagText = (product) => {
  if (product.isActivity) return '活动商品'
  if (product.category === ProductCategories.VIP) return 'VIP商品'
  if (product.groupType === 'group3') return '三人团'
  if (product.groupType === 'group5') return '五人团'
  return '普通商品'
}

// 获取总人数
const getTotalPeople = (product) => {
  return groupConfigs.value[product.groupType]?.totalPeople || 5
}

// 获取进度百分比
const getProgressPercentage = (product) => {
  const total = getTotalPeople(product)
  return (product.currentParticipants / total * 100).toFixed(1)
}

// 获取进度文字
const getProgressText = (product) => {
  const total = getTotalPeople(product)
  const remaining = total - product.currentParticipants
  return remaining > 0 ? `还差${remaining}人` : '已成团'
}

// 获取折扣文字
const getDiscountText = (product) => {
  const discount = ((product.originalPrice - product.groupPrice) / product.originalPrice * 100).toFixed(0)
  return `${discount}折`
}

// 跳转到商品详情页面
const goToProductDetail = (product) => {
  router.push({
    path: '/product/details',
    query: { 
      id: product.id,
      type: product.groupType,
      category: product.category
    }
  })
}

// 下拉刷新
const onRefresh = () => {
  resetPagination()
  loadProducts()
}

// 上拉加载更多
const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    loadProducts()
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60px;
}

// 导航头部
.nav-header {
  background: white;
  border-bottom: 1px solid #eee;
}

// Tab容器
.tab-container {
  background: white;
  
  :deep(.van-tabs__wrap) {
    border-bottom: 1px solid #eee;
  }
  
  :deep(.van-tab) {
    font-weight: 500;
  }
  
  :deep(.van-tab--active) {
    color: #ff6b35;
  }
  
  :deep(.van-tabs__line) {
    background: #ff6b35;
  }
}

// 商品区域
.products-section {
  padding: 16px;
  background: #f5f5f5;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
  
  &:active {
    transform: scale(0.98);
  }
  
  .product-image {
    position: relative;
    width: 100%;
    height: 160px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .product-tag {
      position: absolute;
      top: 8px;
      left: 8px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      color: white;
      
      &.tag-group3 {
        background: #3b82f6;
      }
      
      &.tag-group5 {
        background: #8b5cf6;
      }
      
      &.tag-vip {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      }
      
      &.tag-activity {
        background: #ef4444;
      }
      
      &.tag-normal {
        background: #10b981;
      }
    }
    
    .group-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .progress-bar {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        margin-right: 8px;
        
        .progress-fill {
          height: 100%;
          background: #ff6b35;
          border-radius: 2px;
          transition: width 0.3s ease;
        }
      }
      
      .progress-text {
        color: white;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .product-info {
    padding: 12px;
    
    .product-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .product-price {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .current-price {
        color: #ef4444;
        font-size: 16px;
        font-weight: bold;
        margin-right: 8px;
      }
      
      .original-price {
        color: #999;
        font-size: 12px;
        text-decoration: line-through;
        margin-right: 8px;
      }
      
      .discount {
        background: #ef4444;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 600;
      }
    }
    
    .product-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      color: #666;
      
      .participants {
        display: flex;
        align-items: center;
        
        .van-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
      
      .time-left {
        display: flex;
        align-items: center;
        color: #ef4444;
        
        .van-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  padding: 40px 20px;
  text-align: center;
}
</style> 