<template>
  <div class="bg-gray-100">
    <!-- 顶部导航 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <iconify-icon 
          icon="material-symbols:arrow-back" 
          class="text-2xl text-gray-700 cursor-pointer" 
          @click="goBack"
        ></iconify-icon>
        <iconify-icon 
          icon="material-symbols:share" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="shareProduct"
          title="分享商品"
        ></iconify-icon>
      </div>
      <h1 class="text-lg font-semibold text-gray-800">商品详情</h1>
      <div class="flex space-x-4">
        <iconify-icon 
          icon="material-symbols:favorite-border" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="toggleFavorite"
        ></iconify-icon>
        <iconify-icon 
          icon="material-symbols:home" 
          class="text-2xl text-gray-700 cursor-pointer"
          @click="goToHome"
        ></iconify-icon>
      </div>
    </div>

    <div class="pb-24">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">正在加载商品详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <iconify-icon icon="material-symbols:error-outline" class="text-red-500 text-5xl mb-4"></iconify-icon>
        <p class="text-gray-500 text-sm mb-4">{{ error }}</p>
        <button 
          @click="loadProductDetail()" 
          class="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
        >
          重新加载
        </button>
      </div>

      <!-- 商品详情内容 -->
      <div v-else-if="product">
        <!-- 商品轮播图 -->
        <div class="bg-white">
          <div class="w-full relative">
            <img 
              :src="getProductMainImage()" 
              :alt="getProductName()"
              class="w-full h-auto object-contain"
              style="min-height: 250px; max-height: 500px;"
              @error="handleImageError"
              @load="handleImageLoad"
            />
            <div class="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              1/{{ getProductImages().length || 1 }}
            </div>
          </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="bg-white px-4 py-4 mt-2">
          <div class="flex items-center mb-3">
            <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded mr-2">
              直接购买
            </span>
            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded">包邮</span>
            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded ml-2">现货</span>
          </div>
          <h1 class="text-lg font-bold text-gray-800 mb-3">
            {{ getProductName() }}
          </h1>
          <div class="flex items-center justify-between mb-3">
            <div class="flex flex-col">
              <div class="flex items-center">
                <span class="text-red-500 font-bold text-2xl">
                  {{ formatPrice(getDirectBuyPrice()) }}
                </span>
              </div>
              <div class="text-xs text-gray-500 mt-1">直接购买价格，立即发货</div>
            </div>
            <!-- div class="text-right">
              <div class="text-xs text-gray-500">拼团价对比</div>
              <div class="text-orange-600 font-medium text-sm">
                拼团{{ formatPrice(getGroupPrice()) }}
              </div>
              <div class="text-xs text-gray-400">需等待成团</div>
            </div -->
          </div>
          <div class="flex items-center justify-between text-xs text-gray-600">
            <span>已售{{ getSalesCount() }}+件</span>
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:star" class="text-yellow-400 mr-1"></iconify-icon>
              <span>{{ getRating() }}分 ({{ getReviewCount() }}评价)</span>
            </div>
          </div>
        </div>

        <!-- 直接购买优势 -->
        <!--div class="bg-white px-4 py-4 mt-2">
          <h3 class="text-sm font-medium text-gray-800 mb-3">直接购买优势</h3>
          <div class="space-y-2">
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:check-circle" class="text-green-500 mr-2"></iconify-icon>
              <span class="text-gray-700">立即发货，无需等待成团</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:check-circle" class="text-green-500 mr-2"></iconify-icon>
              <span class="text-gray-700">100%保证有货，不会失败</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:check-circle" class="text-green-500 mr-2"></iconify-icon>
              <span class="text-gray-700">支持余额和积分支付</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:check-circle" class="text-green-500 mr-2"></iconify-icon>
              <span class="text-gray-700">7天无理由退换货</span>
            </div>
          </div>
        </div -->

        <!-- 服务保障 -->
        <div class="bg-white px-4 py-4 mt-2">
          <h3 class="text-sm font-medium text-gray-800 mb-3">服务保障</h3>
          <div class="grid grid-cols-2 gap-3">
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:local-shipping" class="text-blue-500 mr-2"></iconify-icon>
              <span class="text-gray-700">包邮到家</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:verified-user" class="text-green-500 mr-2"></iconify-icon>
              <span class="text-gray-700">正品保证</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:autorenew" class="text-orange-500 mr-2"></iconify-icon>
              <span class="text-gray-700">7天退换</span>
            </div>
            <div class="flex items-center text-sm">
              <iconify-icon icon="material-symbols:support-agent" class="text-purple-500 mr-2"></iconify-icon>
              <span class="text-gray-700">24h客服</span>
            </div>
          </div>
        </div>

        <!-- 商品详情 -->
        <div class="bg-white px-4 py-4 mt-2">
          <h3 class="text-sm font-medium text-gray-800 mb-3">商品详情</h3>
          <div class="space-y-4">
            <!-- 商品描述文字 -->
            <div v-if="getProductDescription()" class="text-gray-700 text-sm leading-relaxed">
              <p class="whitespace-pre-wrap">{{ getProductDescription() }}</p>
            </div>
            
            <!-- 详情图片 -->
            <div 
              v-for="(image, index) in getProductDetailImages()" 
              :key="index"
              class="w-full bg-gray-100 rounded overflow-hidden"
            >
              <img 
                :src="image" 
                :alt="`商品详情图片${index + 1}`"
                class="w-full h-auto object-contain"
                style="min-height: 200px; max-height: 600px;"
                @error="handleImageError"
                @load="handleImageLoad"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t px-4 py-3">
      <div class="w-full">
        <button 
          @click="directBuyNow" 
          class="w-full bg-red-500 text-white py-3 rounded-full font-medium text-sm hover:bg-red-600 transition-colors"
        >
          立即购买 {{ formatPrice(getDirectBuyPrice()) }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError } from '@/utils/message'
import { getImageUrl } from '@/config/image'
import { formatPrice } from '@/utils/format'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const error = ref('')
const product = ref(null)

// API 服务
let apiService = null

// 页面初始化
onMounted(async () => {
  await initApiService()
  await loadProductDetail()
})

// 初始化API服务
const initApiService = async () => {
  try {
    apiService = new StandardApiAdapter('/api/v1')
  } catch (error) {
    console.error('❌ Failed to initialize API service:', error)
    throw error
  }
}

// 加载商品详情
const loadProductDetail = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const productId = route.query.productId || route.params.id
    if (!productId) {
      error.value = '缺少商品ID参数'
      return
    }
    
    console.log('🔄 加载商品详情，产品ID:', productId)
    
    const response = await apiService.getProductDetail(productId)
    
    console.log('🔍 API响应详情:', {
      status: response.status,
      code: response.code,
      message: response.message || response.msg,
      data: response.data,
      fullResponse: response
    })
    
    if (response.code === 0 || response.code === 200) {
      product.value = response.data
      console.log('✅ 商品详情加载成功:', product.value)
    } else {
      console.error('❌ API返回错误代码:', response.code, '错误信息:', response.message || response.msg)
      error.value = response.message || response.msg || '商品加载失败'
    }
  } catch (err) {
    console.error('❌ 加载商品详情失败:', err)
    error.value = '商品详情加载失败，请重试'
  } finally {
    loading.value = false
  }
}

// 获取商品主图
const getProductMainImage = () => {
  if (!product.value) return ''
  
  const imagePath = product.value.image || 
                   product.value.imageUrl || 
                   product.value.mainImage || 
                   product.value.thumbnail ||
                   (product.value.images && product.value.images[0] && product.value.images[0].url)
  
  return getImageUrl(imagePath)
}

// 获取商品图片列表
const getProductImages = () => {
  if (!product.value) return []
  
  if (product.value.images && product.value.images.length > 0) {
    return product.value.images.map(img => getImageUrl(img.url))
  }
  
  return []
}

// 获取商品详情图片
const getProductDetailImages = () => {
  if (!product.value || !product.value.detailImages || product.value.detailImages.length === 0) {
    return []
  }
  
  return product.value.detailImages.map(img => {
    const imageUrl = typeof img === 'string' ? img : img.url
    return getImageUrl(imageUrl)
  })
}

// 使用统一的图片配置，已通过import导入

// 获取商品名称
const getProductName = () => {
  if (!product.value) return ''
  return product.value.goodsName || product.value.title || product.value.name || ''
}

// 获取直接购买价格
const getDirectBuyPrice = () => {
  if (!product.value) return 0
  
  // 优先使用alonePrice字段
  return product.value.alonePrice || 
         product.value.originalPrice || 
         product.value.original_price || 
         product.value.marketPrice || 
         product.value.directPrice || 
         (product.value.price * 1.5) || 
         product.value.price || 
         0
}

// 获取拼团价格（用于价格对比）
const getGroupPrice = () => {
  if (!product.value) return 0
  return product.value.price || product.value.salePrice || product.value.currentPrice || 0
}

// 获取销量
const getSalesCount = () => {
  if (!product.value) return 0
  return product.value.salesCount || product.value.sales || product.value.participants || 0
}

// 获取评分
const getRating = () => {
  if (!product.value) return 5.0
  return product.value.rating || product.value.score || 5.0
}

// 获取评价数量
const getReviewCount = () => {
  if (!product.value) return 0
  return product.value.reviewCount || product.value.reviews || 0
}

// 获取分类名称
const getCategoryName = () => {
  if (!product.value) return '其他'
  return product.value.categoryName || product.value.goodsType || '其他'
}

// 获取品牌名称
const getBrandName = () => {
  if (!product.value) return '品牌商品'
  return product.value.brandName || product.value.brand || '品牌商品'
}

// 价格格式化现在使用统一的越南盾格式化函数

// 立即购买 - 跳转到直接购买下单页面
const directBuyNow = () => {
  // 与拼团下单页面一致的用户鉴权机制
  if (!authStore.isLoggedIn) {
    showError('请先登录')
    router.push('/login')
    return
  }
  
  // 跳转到直接购买下单页面
  router.push({
    path: '/order/direct-buy',
    query: {
      productId: product.value.goodsId || product.value.id,
      type: 'direct',
      productName: getProductName()
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

const goToHome = () => {
  router.push('/')
}

// 分享商品
const shareProduct = () => {
  console.log('🔗 分享直购商品:', product.value?.goodsName)
  if (navigator.share) {
    navigator.share({
      title: product.value?.goodsName || '直接购买商品',
      text: '快来看看这个商品！',
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    showSuccess('链接已复制到剪贴板')
  }
}

// 收藏商品
const toggleFavorite = () => {
  showSuccess('收藏功能开发中')
}


// 获取商品描述
const getProductDescription = () => {
  if (!product.value) return ''
  
  // 只使用API返回的真实描述数据
  return product.value.description || ''
}

// 图片加载处理
const handleImageLoad = (event) => {
  console.log('图片加载成功:', event.target.src)
}

const handleImageError = (event) => {
  console.log('图片加载失败:', event.target.src)
  event.target.src = getImageUrl('') // 使用统一配置的占位图
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>