package net.lab1024.sa.admin.module.business.goods.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

@Data
public class GoodsSkusQueryForm extends PageParam {

    @Schema(description = "SkuId")
    private Long id;

    @Schema(description = "商品分类")
    private Integer categoryId;

    @Schema(description = "搜索词")
    @Length(max = 30, message = "搜索词最多30字符")
    private String goodsName;

    @Schema(description = "商品类型")
    private String goodsType;

    @Schema(description = "商品货币")
    private String goodsCurrency;

    @Schema(description = "状态")
    private Integer goodsStatus;

    @Schema(description = "付款模式")
    private Integer payMode;

    @Schema(description = "活动ID")
    private Integer activityId;

    @Schema(description = "上架状态")
    private Boolean shelvesFlag;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}
