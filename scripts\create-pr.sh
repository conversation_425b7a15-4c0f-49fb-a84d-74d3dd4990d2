#!/bin/bash
# PR创建脚本 - 自动生成摘要并创建PR

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🚀 自动PR创建脚本启动..." $BLUE

# 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_message "❌ 错误: 不在Git仓库中" $RED
    exit 1
fi

# 获取当前分支
CURRENT_BRANCH=$(git branch --show-current)
BASE_BRANCH=${1:-"master"}

print_message "当前分支: $CURRENT_BRANCH" $YELLOW
print_message "目标分支: $BASE_BRANCH" $YELLOW

# 检查是否有未提交的更改
if ! git diff --quiet; then
    print_message "⚠️  发现未提交的更改，是否继续？[y/N]" $YELLOW
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_message "操作已取消" $RED
        exit 1
    fi
fi

# 检查是否有已暂存的更改
if ! git diff --cached --quiet; then
    print_message "发现已暂存的更改，正在提交..." $YELLOW
    git commit -m "临时提交：准备创建PR"
fi

# 推送当前分支到远程
print_message "正在推送分支到远程..." $BLUE
git push -u origin "$CURRENT_BRANCH"

# 生成PR摘要
print_message "正在生成PR摘要..." $BLUE
if [ -f "scripts/generate-pr-summary.js" ]; then
    node scripts/generate-pr-summary.js
    SUMMARY_FILE=".git/pr-summary-${CURRENT_BRANCH}.md"
else
    print_message "⚠️  未找到摘要生成脚本，使用默认摘要" $YELLOW
    SUMMARY_FILE=""
fi

# 创建PR标题
PR_TITLE="feat: $(echo $CURRENT_BRANCH | sed 's/^feature\///' | sed 's/^fix\///' | sed 's/-/ /g')"

# 读取摘要内容
if [ -f "$SUMMARY_FILE" ]; then
    PR_BODY=$(cat "$SUMMARY_FILE")
else
    # 默认PR内容
    PR_BODY=$(cat << EOF
## 📋 更新摘要

本次PR包含以下更改：

$(git log $BASE_BRANCH..HEAD --oneline | sed 's/^/- /')

## 🚀 主要变更

- 功能更新和优化
- 代码质量改进
- 文档更新

## 📊 文件变更统计

\`\`\`
$(git diff $BASE_BRANCH...HEAD --stat)
\`\`\`

## ✅ 检查清单

- [ ] 代码已经过review
- [ ] 功能测试已通过
- [ ] 无破坏性变更
- [ ] 文档已更新
- [ ] 已考虑向后兼容性

## 📝 注意事项

请检查以上变更，确保符合项目规范。
EOF
)
fi

# 检查GitHub CLI是否可用
if command -v gh &> /dev/null; then
    print_message "正在使用GitHub CLI创建PR..." $BLUE
    
    # 使用GitHub CLI创建PR
    echo "$PR_BODY" | gh pr create \
        --title "$PR_TITLE" \
        --body-file - \
        --base "$BASE_BRANCH" \
        --head "$CURRENT_BRANCH"
    
    print_message "✅ PR创建成功!" $GREEN
    
    # 显示PR URL
    PR_URL=$(gh pr view --json url -q .url)
    print_message "PR链接: $PR_URL" $BLUE
    
else
    print_message "⚠️  GitHub CLI未安装，请手动创建PR" $YELLOW
    print_message "PR标题: $PR_TITLE" $YELLOW
    print_message "PR内容已保存到: $SUMMARY_FILE" $YELLOW
    
    # 生成PR URL
    REPO_URL=$(git config --get remote.origin.url | sed 's/\.git$//')
    if [[ $REPO_URL == *"github.com"* ]]; then
        REPO_URL=$(echo $REPO_URL | sed 's/git@github\.com:/https:\/\/github\.com\//')
        PR_CREATE_URL="$REPO_URL/compare/$BASE_BRANCH...$CURRENT_BRANCH"
        print_message "创建PR链接: $PR_CREATE_URL" $BLUE
    fi
fi

# 清理临时文件
if [ -f "$SUMMARY_FILE" ]; then
    print_message "保留摘要文件: $SUMMARY_FILE" $YELLOW
fi

print_message "🎉 PR创建流程完成!" $GREEN