package net.lab1024.sa.admin.module.business.goods.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
@TableName(value = "t_goods_skus", autoResultMap = true)
public class GoodsSkusEntity {

    /**
     * SKU ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 规格属性
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> attributes;

    /**
     * 商品标价
     */
    private BigDecimal price;

    /**
     * 商品标价
     */
    private BigDecimal alonePrice;

    /**
     * 单独购买标记
     */
    private Boolean aloneFlag;


    private BigDecimal originalPrice;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 销售数量
     */
    private Integer salesCount;

    /**
     * SKU状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonIgnore
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @JsonIgnore
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}
