● 下单
/api/v1/placeOrder
POST提交:
{
  "skuId": 26, //SkuID
  "quantity": 1, //数量(现在无论填多少，后台强制1)
  "shippingAddressId": 8 //收货地址ID
  //新增
  "aloneFlag":1,  //购买方式：1=直接购买，0默认
  "useCurrency":1,  // 支付方式：1=余额支付，2=积分支付
}
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "code": 0,
        "msg": "success",
        "ok": true,
        "data": "5", //订单ID
        "dataType": 1
    },
    "dataType": 1
}

● 订单物流信息
/app/v1/order/Logistics/订单ID
GET提交:
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "id": 2,
        "orderId": 5,
        "trackingNumber": "123123123654",
        "courierCompany": "飞越物流",
        "status": "shipped",
        "trackingInfo": {
            "list": [
                "货物在已达南宁"
            ]
        },
        "shippedTime": "2025-07-06 17:43:04",
        "createTime": "2025-07-06 17:43:04"
    },
    "dataType": 1
}