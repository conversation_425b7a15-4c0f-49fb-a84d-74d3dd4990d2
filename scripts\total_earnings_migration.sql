-- 总收益功能数据库迁移脚本
-- 执行时间：2025-07-30T17:03:35.162Z

-- 1. 添加总收益字段
ALTER TABLE t_wallets 
ADD COLUMN total_earnings DECIMAL(10,2) DEFAULT 0.00 
COMMENT '总收益（未中奖励汇总）' 
AFTER balance;

-- 2. 添加字段索引（可选，用于查询优化）
ALTER TABLE t_wallets 
ADD INDEX idx_total_earnings (total_earnings);

-- 3. 验证字段添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 't_wallets' 
AND COLUMN_NAME = 'total_earnings';
