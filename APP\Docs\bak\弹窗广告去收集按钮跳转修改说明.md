# 弹窗广告"去收集"按钮跳转修改说明

## 修改内容

### 问题描述
用户要求在首页的弹窗广告中，点击"去收集"按钮时，跳转到商品详情页面，打开商品id=34的商品。

### 修改位置
文件：`APP/src/views/home/<USER>

### 修改详情

#### 弹窗模板代码（第298行）
```html
<button 
  @click="goToActivityPage()" 
  class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white font-bold py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 mb-3"
>
  <span>{{ t('modal_collect') }}</span>
</button>
```

#### 跳转方法修改（第1284-1288行）
```javascript
// 修改前：跳转到活动页面
const goToActivityPage = () => {
  hideSubsidyModal()
  router.push('/activity')
}

// 修改后：跳转到商品详情页面
const goToActivityPage = () => {
  hideSubsidyModal()
  // 跳转到商品详情页面，打开商品id=34的商品
  router.push('/product/34')
}
```

### 功能流程
1. 用户访问首页
2. 满足条件时自动显示平台补贴弹窗
3. 用户点击"去收集"按钮
4. 弹窗关闭
5. **跳转到商品详情页面，显示商品id=34的商品**

### 弹窗显示条件
弹窗会在以下情况下显示：
- 未登录用户
- 已登录但订单数量小于3单的用户

### 弹窗内容
- 标题：平台补贴
- 副标题：每日领取
- 奖励金额：+10.000₫
- 描述：立即分享给朋友
- 按钮：去收集（现在跳转到商品详情页）

### 路由配置
确保路由配置中包含商品详情页面的路由：
```javascript
{
  path: '/product/:id',
  name: 'ProductDetail',
  component: () => import('@/views/product/ProductDetail.vue')
}
```

### 测试验证
1. 打开首页，等待弹窗显示
2. 点击"去收集"按钮
3. 验证弹窗关闭
4. 验证跳转到商品详情页面
5. 验证显示的是商品id=34的商品信息

### 技术细节
- 跳转路径：从 `/activity` 改为 `/product/34`
- 保持了 `hideSubsidyModal()` 调用，确保弹窗正确关闭
- 使用 Vue Router 的 `router.push()` 方法进行页面跳转
- 商品ID固定为34，如需动态配置可进一步修改

### 相关文件
- `APP/src/views/home/<USER>
- `APP/src/views/product/ProductDetail.vue` - 商品详情页（跳转目标）
- `APP/src/router/index.js` - 路由配置

### 注意事项
- 确保商品id=34在系统中存在
- 如果商品不存在，商品详情页应该有相应的错误处理
- 如需支持动态商品ID，可以将商品ID作为配置参数或从后端API获取 