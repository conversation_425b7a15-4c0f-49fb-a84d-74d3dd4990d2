# 社交拼团APP分步实施方案

| 版本 | 日期       | 作者   | 审核人 | 变更描述                                                     |
| :--- | :--------- | :----- | :----- | :----------------------------------------------------------- |
| V1.0 | 2025-01-27 | Claude |        | 基于Smart-Admin的分步实施方案：Mock数据开发 + 后端对接 |

---

## 1. 方案概述

本方案采用**"Mock数据先行，后端对接跟进"**的分步实施策略，旨在实现前后端并行开发，最大化开发效率，降低项目风险。

### 1.1 核心策略

- **第一步：Mock数据开发** - 前端基于现有原型和设计文档，使用Mock数据完成所有页面和交互逻辑
- **第二步：后端真实对接** - 在Smart-Admin中扩展C端业务API，替换Mock数据，实现真实业务逻辑

### 1.2 方案优势

- ✅ **并行开发** - 前后端可同时进行，互不阻塞
- ✅ **快速验证** - 通过Mock数据快速验证产品逻辑和用户体验
- ✅ **风险可控** - 分步实施，每步都有明确的交付物和验收标准
- ✅ **成本最优** - 充分利用现有原型，避免重复开发

---

## 2. 第一步：Mock数据开发阶段

### 2.1 阶段目标

**时间周期：** 4-5周  
**核心目标：** 完成H5前端所有功能页面，使用Mock数据实现完整的用户交互流程

### 2.2 技术架构

```plaintext
H5前端 ←→ Mock数据层 ←→ 本地JSON文件
```

#### 2.2.1 Mock数据设计

**Mock数据结构设计：**

```javascript
// mock/user.js - 用户相关Mock数据
export const mockUserData = {
  // 用户信息
  userInfo: {
    id: 10001,
    nickname: "张三",
    avatar: "/images/avatar.jpg",
    phone: "138****8888",
    balance: 299.50,
    experienceAmount: 50.00,
    points: 1280,
    inviteCode: "ABC123",
    registerTime: "2024-01-15 10:30:00"
  },
  
  // 钱包流水
  transactions: [
    {
      id: 1,
      type: "充值",
      amount: 100.00,
      description: "微信充值",
      time: "2024-01-20 14:30:00"
    },
    // ... 更多流水记录
  ],
  
  // 团队信息
  teamInfo: {
    directInvites: 12,
    totalEarnings: 156.80,
    monthlyInvites: 3,
    monthlyEarnings: 25.60
  }
}

// mock/product.js - 商品相关Mock数据
export const mockProductData = {
  // 商品列表
  productList: [
    {
      id: 1001,
      name: "iPhone 15 Pro Max 256GB",
      image: "/images/products/iphone15.jpg",
      originalPrice: 9999.00,
      participationPrice: 299.00,
      activityType: "低价抽奖团",
      participants: 156,
      status: "进行中"
    },
    // ... 更多商品
  ],
  
  // 商品详情
  productDetail: {
    id: 1001,
    name: "iPhone 15 Pro Max 256GB",
    images: ["/images/products/iphone15-1.jpg", "/images/products/iphone15-2.jpg"],
    originalPrice: 9999.00,
    participationPrice: 299.00,
    description: "全新iPhone 15 Pro Max，钛金属设计...",
    lotteryRules: {
      type: "低价抽奖团",
      winRate: "5%",
      refundRate: "80%",
      subsidyRate: "5%"
    }
  }
}

// mock/order.js - 订单相关Mock数据
export const mockOrderData = {
  // 订单列表
  orderList: [
    {
      id: "20240120001",
      productName: "iPhone 15 Pro Max",
      productImage: "/images/products/iphone15.jpg",
      amount: 299.00,
      status: "已中签",
      createTime: "2024-01-20 15:30:00",
      lotteryTime: "2024-01-20 20:00:00"
    },
    // ... 更多订单
  ]
}
```

#### 2.2.2 Mock API服务

**使用MSW (Mock Service Worker) 创建Mock API：**

```javascript
// mock/handlers.js
import { rest } from 'msw'
import { mockUserData, mockProductData, mockOrderData } from './data'

export const handlers = [
  // 用户相关API
  rest.post('/api/app/user/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        code: 20000,
        msg: '登录成功',
        data: {
          token: 'mock-token-123456',
          userInfo: mockUserData.userInfo
        }
      })
    )
  }),

  rest.get('/api/app/user/profile', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        code: 20000,
        msg: '获取成功',
        data: mockUserData.userInfo
      })
    )
  }),

  // 商品相关API
  rest.get('/api/app/product/list', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        code: 20000,
        msg: '获取成功',
        data: {
          list: mockProductData.productList,
          total: 50,
          pageNum: 1,
          pageSize: 10
        }
      })
    )
  }),

  // 订单相关API
  rest.post('/api/app/order/create', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        code: 20000,
        msg: '订单创建成功',
        data: {
          orderId: 'ORDER' + Date.now(),
          paymentUrl: '/order/payment?orderId=ORDER' + Date.now()
        }
      })
    )
  })
]
```

### 2.3 开发任务分解

#### 2.3.1 Week 1: 项目基础搭建

**任务清单：**
1. **项目初始化**
   - 创建Vite + Vue 3项目
   - 集成Pinia、Vue Router、Vant UI
   - 配置ESLint + Prettier

2. **Mock环境搭建**
   - 安装并配置MSW
   - 创建Mock数据结构
   - 搭建Mock API服务

3. **样式资源迁移**
   - 从`/原型2`提取CSS文件
   - 整理图片资源
   - 配置全局样式

**交付物：**
- 可运行的项目骨架
- 完整的Mock数据和API
- 基础样式系统

#### 2.3.2 Week 2: 首页和商品浏览

**任务清单：**
1. **首页开发**
   - 迁移`home.html` → `HomePage.vue`
   - 集成商品列表Mock API
   - 实现商品卡片组件

2. **活动专区开发**
   - 迁移`activity_zone.html` → `ActivityZonePage.vue`
   - 实现Tab切换功能
   - 对接活动列表Mock API

3. **商品详情开发**
   - 迁移`product_detail.html` → `DetailsPage.vue`
   - 实现抽奖规则展示
   - 添加参与按钮交互

**交付物：**
- 完整的商品浏览流程
- 可点击的商品卡片和详情页
- 活动专区Tab切换功能

#### 2.3.3 Week 3: 交易流程

**任务清单：**
1. **订单确认页**
   - 迁移`order_confirmation.html` → `ConfirmPage.vue`
   - 实现地址选择功能
   - 费用计算逻辑（体验金抵扣）

2. **支付页面**
   - 迁移`payment.html` → `PaymentPage.vue`
   - 支付方式选择
   - 支付倒计时功能

3. **等待开奖页**
   - 迁移`group_waiting.html` → `WaitingPage.vue`
   - 开奖倒计时组件
   - 开奖状态轮询

4. **结果弹窗**
   - 开发中签/未中签弹窗组件
   - 实现弹窗联动逻辑

**交付物：**
- 完整的下单支付流程
- 开奖等待和结果展示功能
- 所有交易相关弹窗

#### 2.3.4 Week 4: 个人中心

**任务清单：**
1. **个人中心首页**
   - 迁移`profile.html` → `ProfilePage.vue`
   - 用户信息展示
   - 钱包余额展示

2. **订单管理**
   - 迁移`orders.html` → `OrdersPage.vue`
   - Tab切换功能
   - 订单列表展示

3. **钱包功能**
   - 钱包页面开发
   - 账单流水展示
   - 充值提现页面（UI层面）

**交付物：**
- 完整的个人中心功能
- 订单管理系统
- 钱包相关页面

#### 2.3.5 Week 5: 裂变功能和收尾

**任务清单：**
1. **裂变功能**
   - 我的团队页面
   - 邀请推广页面
   - 分享功能集成

2. **登录注册**
   - 迁移`login.html` → `LoginPage.vue`
   - 登录表单验证
   - Token管理

3. **测试优化**
   - 功能测试
   - 交互优化
   - 性能优化

**交付物：**
- 完整的H5应用
- 所有功能页面和交互
- 测试报告

### 2.4 第一步验收标准

**功能验收：**
- ✅ 所有页面可正常访问和交互
- ✅ 完整的用户操作流程（注册→浏览→下单→支付→开奖→管理）
- ✅ 所有弹窗和组件正常工作
- ✅ 响应式布局适配移动端

**技术验收：**
- ✅ 代码结构清晰，组件复用度高
- ✅ Mock API完整覆盖所有业务场景
- ✅ 路由配置完善，支持懒加载
- ✅ 状态管理合理，数据流清晰

---

## 3. 第二步：后端真实对接阶段

### 3.1 阶段目标

**时间周期：** 3-4周  
**核心目标：** 在Smart-Admin中扩展C端业务API，替换Mock数据，实现真实业务逻辑

### 3.2 技术架构

```plaintext
H5前端 ←→ Smart-Admin扩展API ←→ 数据库
```

### 3.3 Smart-Admin扩展方案

#### 3.3.1 数据库设计

**核心业务表结构：**

```sql
-- 用户扩展表
CREATE TABLE app_user (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(20) UNIQUE NOT NULL,
  nickname VARCHAR(50),
  avatar VARCHAR(200),
  balance DECIMAL(10,2) DEFAULT 0.00,
  experience_amount DECIMAL(10,2) DEFAULT 0.00,
  points INT DEFAULT 0,
  invite_code VARCHAR(20) UNIQUE,
  inviter_id BIGINT,
  register_time DATETIME,
  last_login_time DATETIME,
  status TINYINT DEFAULT 1
);

-- 商品表
CREATE TABLE app_product (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(200) NOT NULL,
  images TEXT,
  original_price DECIMAL(10,2),
  participation_price DECIMAL(10,2),
  activity_type VARCHAR(20),
  description TEXT,
  status TINYINT DEFAULT 1,
  create_time DATETIME
);

-- 抽奖活动表
CREATE TABLE app_lottery_activity (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(200) NOT NULL,
  product_id BIGINT,
  activity_type VARCHAR(20),
  start_time DATETIME,
  end_time DATETIME,
  win_rate DECIMAL(5,2),
  refund_rate DECIMAL(5,2),
  subsidy_rate DECIMAL(5,2),
  status TINYINT DEFAULT 1
);

-- 订单表
CREATE TABLE app_order (
  id VARCHAR(50) PRIMARY KEY,
  user_id BIGINT NOT NULL,
  product_id BIGINT NOT NULL,
  activity_id BIGINT,
  amount DECIMAL(10,2),
  payment_method VARCHAR(20),
  order_status VARCHAR(20),
  lottery_result VARCHAR(20),
  create_time DATETIME,
  payment_time DATETIME,
  lottery_time DATETIME
);

-- 资金流水表
CREATE TABLE app_wallet_transaction (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  type VARCHAR(20),
  amount DECIMAL(10,2),
  balance_after DECIMAL(10,2),
  description VARCHAR(200),
  create_time DATETIME
);

-- 邀请关系表
CREATE TABLE app_invite_relation (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  inviter_id BIGINT NOT NULL,
  invitee_id BIGINT NOT NULL,
  invite_time DATETIME,
  first_order_time DATETIME,
  total_commission DECIMAL(10,2) DEFAULT 0.00
);
```

#### 3.3.2 API接口设计

**Controller层设计：**

```java
@RestController
@RequestMapping("/api/app")
@Slf4j
public class AppUserController {
    
    @Autowired
    private AppUserService appUserService;
    
    /**
     * 用户注册
     */
    @PostMapping("/user/register")
    public ResponseDTO<RegisterVO> register(@RequestBody @Valid RegisterDTO registerDTO) {
        return appUserService.register(registerDTO);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/user/login")
    public ResponseDTO<LoginVO> login(@RequestBody @Valid LoginDTO loginDTO) {
        return appUserService.login(loginDTO);
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/user/profile")
    public ResponseDTO<UserProfileVO> getProfile() {
        Long userId = getCurrentUserId();
        return appUserService.getProfile(userId);
    }
    
    /**
     * 获取钱包信息
     */
    @GetMapping("/user/wallet")
    public ResponseDTO<WalletVO> getWallet() {
        Long userId = getCurrentUserId();
        return appUserService.getWallet(userId);
    }
}

@RestController
@RequestMapping("/api/app")
public class AppProductController {
    
    @Autowired
    private AppProductService appProductService;
    
    /**
     * 获取商品列表
     */
    @GetMapping("/product/list")
    public ResponseDTO<PageResult<ProductListVO>> getProductList(
        @RequestParam(defaultValue = "1") Integer pageNum,
        @RequestParam(defaultValue = "10") Integer pageSize,
        @RequestParam(required = false) String category) {
        return appProductService.getProductList(pageNum, pageSize, category);
    }
    
    /**
     * 获取商品详情
     */
    @GetMapping("/product/detail/{id}")
    public ResponseDTO<ProductDetailVO> getProductDetail(@PathVariable Long id) {
        return appProductService.getProductDetail(id);
    }
}

@RestController
@RequestMapping("/api/app")
public class AppOrderController {
    
    @Autowired
    private AppOrderService appOrderService;
    
    /**
     * 创建订单
     */
    @PostMapping("/order/create")
    public ResponseDTO<CreateOrderVO> createOrder(@RequestBody @Valid CreateOrderDTO createOrderDTO) {
        Long userId = getCurrentUserId();
        return appOrderService.createOrder(userId, createOrderDTO);
    }
    
    /**
     * 订单支付
     */
    @PostMapping("/order/pay")
    public ResponseDTO<PayOrderVO> payOrder(@RequestBody @Valid PayOrderDTO payOrderDTO) {
        Long userId = getCurrentUserId();
        return appOrderService.payOrder(userId, payOrderDTO);
    }
    
    /**
     * 获取订单列表
     */
    @GetMapping("/order/list")
    public ResponseDTO<PageResult<OrderListVO>> getOrderList(
        @RequestParam(defaultValue = "1") Integer pageNum,
        @RequestParam(defaultValue = "10") Integer pageSize,
        @RequestParam(required = false) String status) {
        Long userId = getCurrentUserId();
        return appOrderService.getOrderList(userId, pageNum, pageSize, status);
    }
}
```

#### 3.3.3 核心业务逻辑

**抽奖引擎Service：**

```java
@Service
@Slf4j
public class LotteryService {
    
    @Autowired
    private AppOrderMapper appOrderMapper;
    
    @Autowired
    private LotteryStrategyService lotteryStrategyService;
    
    /**
     * 执行抽奖
     */
    @Transactional
    public LotteryResult drawLottery(String orderId) {
        // 1. 获取订单信息
        AppOrder order = appOrderMapper.selectByOrderId(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 2. 获取抽奖策略
        LotteryStrategy strategy = lotteryStrategyService.getStrategy(
            order.getUserId(), order.getActivityId());
        
        // 3. 执行抽奖算法
        boolean isWin = executeDrawAlgorithm(strategy);
        
        // 4. 更新订单状态
        order.setLotteryResult(isWin ? "WIN" : "LOSE");
        order.setLotteryTime(new Date());
        appOrderMapper.updateByOrderId(order);
        
        // 5. 处理后续逻辑（退款、补贴等）
        if (isWin) {
            handleWinResult(order);
        } else {
            handleLoseResult(order);
        }
        
        return LotteryResult.builder()
            .orderId(orderId)
            .isWin(isWin)
            .build();
    }
    
    /**
     * 抽奖算法核心逻辑
     */
    private boolean executeDrawAlgorithm(LotteryStrategy strategy) {
        // 检查必中/必不中策略
        if (strategy.isMustWin()) {
            return true;
        }
        if (strategy.isMustLose()) {
            return false;
        }
        
        // 基于概率的随机抽奖
        double random = Math.random();
        return random < strategy.getWinRate();
    }
}
```

### 3.4 对接任务分解

#### 3.4.1 Week 1: 后端基础搭建

**任务清单：**
1. **Smart-Admin环境搭建**
   - 部署Smart-Admin基础框架
   - 配置数据库连接
   - 创建业务数据表

2. **基础API开发**
   - 用户注册登录API
   - 基础用户信息API
   - JWT Token验证

**交付物：**
- 可运行的Smart-Admin环境
- 用户认证相关API

#### 3.4.2 Week 2: 商品和活动API

**任务清单：**
1. **商品管理API**
   - 商品列表查询
   - 商品详情查询
   - 商品分类管理

2. **活动管理API**
   - 抽奖活动创建
   - 活动配置管理
   - 活动状态控制

**交付物：**
- 完整的商品相关API
- 抽奖活动管理功能

#### 3.4.3 Week 3: 订单和支付API

**任务清单：**
1. **订单管理API**
   - 订单创建逻辑
   - 订单状态管理
   - 订单查询功能

2. **支付集成**
   - 余额支付逻辑
   - 第三方支付接口
   - 支付回调处理

3. **抽奖引擎**
   - 抽奖算法实现
   - 概率控制逻辑
   - 结果处理机制

**交付物：**
- 完整的订单交易API
- 抽奖引擎核心功能

#### 3.4.4 Week 4: 裂变和财务API

**任务清单：**
1. **裂变体系API**
   - 邀请关系管理
   - 佣金计算逻辑
   - 团队数据统计

2. **财务管理API**
   - 钱包余额管理
   - 资金流水记录
   - 提现申请处理

3. **前端对接调试**
   - 替换Mock数据
   - API联调测试
   - 异常处理优化

**交付物：**
- 完整的业务API系统
- 前后端联调完成

### 3.5 第二步验收标准

**功能验收：**
- ✅ 所有API接口正常工作
- ✅ 抽奖逻辑准确执行
- ✅ 支付流程完整可用
- ✅ 数据持久化正确

**性能验收：**
- ✅ API响应时间 < 500ms
- ✅ 支持并发用户 > 1000
- ✅ 数据库查询优化
- ✅ 缓存机制有效

---

## 4. 项目管理和风险控制

### 4.1 里程碑计划

| 里程碑 | 时间节点 | 交付物 | 验收标准 |
|--------|----------|--------|----------|
| M1 | Week 2 | Mock数据前端基础 | 首页和商品浏览功能完成 |
| M2 | Week 4 | Mock数据完整前端 | 所有页面和交互完成 |
| M3 | Week 6 | 后端API基础版 | 核心业务API开发完成 |
| M4 | Week 8 | 完整系统上线 | 前后端完全对接，系统可用 |

### 4.2 风险控制

**技术风险：**
- **风险：** Mock数据与真实API结构不匹配
- **应对：** 严格按照API设计文档创建Mock数据，定期review

**进度风险：**
- **风险：** 前后端开发进度不同步
- **应对：** 采用Mock数据解耦，前端可独立验收

**质量风险：**
- **风险：** 抽奖逻辑复杂，容易出错
- **应对：** 单独开发抽奖引擎，充分测试

### 4.3 团队协作

**前端团队：**
- 专注于页面开发和用户体验
- 基于Mock数据进行功能验证
- 准备API对接的适配层

**后端团队：**
- 专注于业务逻辑实现
- 严格按照API设计文档开发
- 提供完整的测试数据

---

## 5. 总结

本分步实施方案通过Mock数据先行的策略，实现了前后端的有效解耦，既保证了开发效率，又降低了项目风险。第一步完成后，我们将拥有一个功能完整的前端应用；第二步完成后，我们将拥有一个可商用的完整系统。

**关键成功因素：**
1. 严格按照设计文档执行
2. Mock数据与真实API保持一致
3. 持续的测试和验证
4. 团队间的有效沟通

这个方案确保了项目能够按时、高质量地交付，为后续的运营和迭代打下坚实的基础。 