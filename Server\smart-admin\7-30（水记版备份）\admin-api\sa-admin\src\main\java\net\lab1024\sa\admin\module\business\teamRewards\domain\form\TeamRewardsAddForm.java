package net.lab1024.sa.admin.module.business.teamRewards.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 团队奖励表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Data
public class TeamRewardsAddForm {

    @Schema(description = "奖励ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "奖励ID 不能为空")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID 不能为空")
    private Long userId;

    @Schema(description = "奖励日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "奖励日期 不能为空")
    private LocalDate rewardDate;

    @Schema(description = "达标人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "达标人数 不能为空")
    private Integer qualifiedCount;

    @Schema(description = "奖励金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "奖励金额 不能为空")
    private BigDecimal rewardAmount;

    @Schema(description = "发放状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "发放状态 不能为空")
    private String status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建时间 不能为空")
    private LocalDateTime createTime;

}