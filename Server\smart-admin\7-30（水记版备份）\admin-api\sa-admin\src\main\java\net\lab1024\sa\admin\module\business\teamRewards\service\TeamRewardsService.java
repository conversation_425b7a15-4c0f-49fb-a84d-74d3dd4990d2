package net.lab1024.sa.admin.module.business.teamRewards.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.teamRewards.dao.TeamRewardsDao;
import net.lab1024.sa.admin.module.business.teamRewards.domain.entity.TeamRewardsEntity;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsAddForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsQueryForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsUpdateForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.vo.TeamRewardsVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 团队奖励表 Service
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Service
public class TeamRewardsService {

    @Resource
    private TeamRewardsDao teamRewardsDao;

    /**
     * 分页查询
     */
    public PageResult<TeamRewardsVO> queryPage(TeamRewardsQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<TeamRewardsVO> list = teamRewardsDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(TeamRewardsAddForm addForm) {
        TeamRewardsEntity teamRewardsEntity = SmartBeanUtil.copy(addForm, TeamRewardsEntity.class);
        teamRewardsDao.insert(teamRewardsEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     */
    public ResponseDTO<String> update(TeamRewardsUpdateForm updateForm) {
        TeamRewardsEntity teamRewardsEntity = SmartBeanUtil.copy(updateForm, TeamRewardsEntity.class);
        teamRewardsDao.updateById(teamRewardsEntity);
        return ResponseDTO.ok();
    }

}
