package net.lab1024.sa.admin.module.business.userAddress.controller;

import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressAddForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressQueryForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressUpdateForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.vo.UserAddressVO;
import net.lab1024.sa.admin.module.business.userAddress.service.UserAddressService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 用户收货地址表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-28 15:09:41
 * @Copyright -
 */

@RestController
@Tag(name = "用户收货地址表")
public class UserAddressController {

    @Resource
    private UserAddressService userAddressService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/userAddress/queryPage")
    @SaCheckPermission("userAddress:query")
    public ResponseDTO<PageResult<UserAddressVO>> queryPage(@RequestBody @Valid UserAddressQueryForm queryForm) {
        return ResponseDTO.ok(userAddressService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/userAddress/add")
    @SaCheckPermission("userAddress:add")
    public ResponseDTO<String> add(@RequestBody @Valid UserAddressAddForm addForm) {
        return userAddressService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/userAddress/update")
    @SaCheckPermission("userAddress:update")
    public ResponseDTO<String> update(@RequestBody @Valid UserAddressUpdateForm updateForm) {
        return userAddressService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/userAddress/batchDelete")
    @SaCheckPermission("userAddress:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return userAddressService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/userAddress/delete/{id}")
    @SaCheckPermission("userAddress:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
        return userAddressService.delete(id);
    }
}
