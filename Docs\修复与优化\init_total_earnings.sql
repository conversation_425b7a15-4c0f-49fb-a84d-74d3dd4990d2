-- ========================================
-- 初始化所有用户的总收益数据
-- 基于现有的REFUND_WIN交易记录计算
-- ========================================

-- 更新所有用户的total_earnings字段
UPDATE t_wallets w 
SET total_earnings = (
    SELECT IFNULL(SUM(amount), 0) 
    FROM t_wallet_transactions wt 
    WHERE wt.user_id = w.user_id 
    AND wt.type = 'REFUND_WIN'
),
update_time = CURRENT_TIMESTAMP;

-- 显示更新结果
SELECT 
    w.user_id,
    w.total_earnings,
    (SELECT COUNT(*) FROM t_wallet_transactions wt 
     WHERE wt.user_id = w.user_id AND wt.type = 'REFUND_WIN') as refund_win_count,
    w.update_time
FROM t_wallets w 
WHERE w.total_earnings > 0
ORDER BY w.total_earnings DESC;

-- 统计报告
SELECT 
    '总用户数' as metric,
    COUNT(*) as value
FROM t_wallets
UNION ALL
SELECT 
    '有收益用户数' as metric,
    COUNT(*) as value
FROM t_wallets 
WHERE total_earnings > 0
UNION ALL
SELECT 
    '总收益金额' as metric,
    SUM(total_earnings) as value
FROM t_wallets;