package net.lab1024.sa.admin.module.business.invitationRecords.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邀请记录表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class InvitationRecordsQueryForm extends PageParam {

    @Schema(description = "邀请人ID")
    private Long inviterId;

    @Schema(description = "邀请人")
    private String inviterName;

    @Schema(description = "被邀请人ID")
    private Long inviteeId;

    @Schema(description = "被邀请人")
    private String inviteeName;

    @Schema(description = "创建时间")
    private LocalDate createTimeBegin;

    @Schema(description = "创建时间")
    private LocalDate createTimeEnd;

}
