// 30秒刷新问题深度调试工具
console.log('🔍 开始30秒刷新问题深度调试...');

// 记录开始时间
const startTime = Date.now();

// 监听所有可能导致页面刷新的行为
class RefreshDebugger {
  constructor() {
    this.timers = new Map();
    this.intervals = new Map();
    this.navigationHistory = [];
    this.timerIdCounter = 0;
    
    this.init();
  }
  
  init() {
    this.overrideTimers();
    this.overrideLocation();
    this.overrideHistory();
    this.monitorNavigation();
    this.monitorErrors();
    this.logSystemInfo();
  }
  
  overrideTimers() {
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalClearTimeout = window.clearTimeout;
    const originalClearInterval = window.clearInterval;
    
    window.setTimeout = (callback, delay, ...args) => {
      const timerId = this.timerIdCounter++;
      const stack = new Error().stack;
      
      // 记录所有大于20秒的定时器
      if (delay >= 20000) {
        console.warn(`⚠️ 长延迟setTimeout: ${delay}ms, ID: ${timerId}`);
        console.trace('调用栈:');
      }
      
      // 记录25-35秒范围的定时器
      if (delay >= 25000 && delay <= 35000) {
        console.error(`🚨 可疑的30秒定时器! 延迟: ${delay}ms, ID: ${timerId}`);
        console.log('回调函数:', callback.toString().substring(0, 200));
        console.trace('完整调用栈:');
      }
      
      this.timers.set(timerId, {
        type: 'setTimeout',
        delay,
        callback: callback.toString().substring(0, 100),
        stack: stack.split('\n').slice(1, 6).join('\n'),
        created: Date.now()
      });
      
      const originalId = originalSetTimeout.call(window, (...callbackArgs) => {
        console.log(`⏰ setTimeout触发: ID ${timerId}, 延迟 ${delay}ms`);
        this.timers.delete(timerId);
        return callback(...callbackArgs);
      }, delay, ...args);
      
      return originalId;
    };
    
    window.setInterval = (callback, delay, ...args) => {
      const timerId = this.timerIdCounter++;
      const stack = new Error().stack;
      
      // 记录所有大于20秒的间隔
      if (delay >= 20000) {
        console.warn(`⚠️ 长间隔setInterval: ${delay}ms, ID: ${timerId}`);
        console.trace('调用栈:');
      }
      
      // 记录25-35秒范围的间隔
      if (delay >= 25000 && delay <= 35000) {
        console.error(`🚨 可疑的30秒间隔定时器! 间隔: ${delay}ms, ID: ${timerId}`);
        console.log('回调函数:', callback.toString().substring(0, 200));
        console.trace('完整调用栈:');
      }
      
      this.intervals.set(timerId, {
        type: 'setInterval',
        delay,
        callback: callback.toString().substring(0, 100),
        stack: stack.split('\n').slice(1, 6).join('\n'),
        created: Date.now(),
        executions: 0
      });
      
      const originalId = originalSetInterval.call(window, (...callbackArgs) => {
        const intervalInfo = this.intervals.get(timerId);
        if (intervalInfo) {
          intervalInfo.executions++;
          console.log(`🔄 setInterval执行: ID ${timerId}, 第${intervalInfo.executions}次, 间隔 ${delay}ms`);
        }
        return callback(...callbackArgs);
      }, delay, ...args);
      
      return originalId;
    };
  }
  
  overrideLocation() {
    const originalReload = window.location.reload;
    
    Object.defineProperty(window.location, 'reload', {
      value: function() {
        console.error('🚨 window.location.reload() 被调用!');
        console.trace('调用栈:');
        console.log('时间:', new Date().toISOString());
        console.log('距离页面加载时间:', (Date.now() - startTime) / 1000, '秒');
        return originalReload.call(this);
      },
      writable: true
    });
    
    // 监听href变化
    let currentHref = window.location.href;
    Object.defineProperty(window.location, 'href', {
      get: () => currentHref,
      set: (value) => {
        console.error('🚨 window.location.href 被设置!');
        console.log('新URL:', value);
        console.log('旧URL:', currentHref);
        console.trace('调用栈:');
        currentHref = value;
      }
    });
  }
  
  overrideHistory() {
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    const originalGo = history.go;
    
    history.pushState = function(...args) {
      console.log('📱 history.pushState:', args);
      return originalPushState.apply(this, args);
    };
    
    history.replaceState = function(...args) {
      console.log('📱 history.replaceState:', args);
      return originalReplaceState.apply(this, args);
    };
    
    history.go = function(delta) {
      console.error('🚨 history.go() 被调用! delta:', delta);
      console.trace('调用栈:');
      return originalGo.call(this, delta);
    };
  }
  
  monitorNavigation() {
    // 监听beforeunload
    window.addEventListener('beforeunload', (e) => {
      console.error('🚨 页面即将卸载/刷新!');
      console.log('事件:', e);
      console.log('时间:', new Date().toISOString());
      console.log('距离页面加载时间:', (Date.now() - startTime) / 1000, '秒');
      console.trace('触发路径:');
    });
    
    // 监听unload
    window.addEventListener('unload', (e) => {
      console.error('🚨 页面正在卸载!');
      console.log('时间:', new Date().toISOString());
    });
    
    // 监听pagehide
    window.addEventListener('pagehide', (e) => {
      console.error('🚨 页面隐藏事件!');
      console.log('事件:', e);
      console.log('时间:', new Date().toISOString());
    });
    
    // 监听hashchange
    window.addEventListener('hashchange', (e) => {
      console.log('📱 hash变化:', e.oldURL, '->', e.newURL);
    });
    
    // 监听popstate
    window.addEventListener('popstate', (e) => {
      console.log('📱 popstate事件:', e.state);
    });
  }
  
  monitorErrors() {
    // 监听全局错误
    window.addEventListener('error', (e) => {
      console.error('🚨 全局错误:', e.error);
      console.log('文件:', e.filename, '行:', e.lineno);
    });
    
    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (e) => {
      console.error('🚨 未处理的Promise拒绝:', e.reason);
    });
  }
  
  logSystemInfo() {
    console.log('🔧 调试器初始化完成');
    console.log('📊 浏览器信息:', navigator.userAgent);
    console.log('📊 页面URL:', window.location.href);
    console.log('📊 开始时间:', new Date(startTime).toISOString());
  }
  
  // 定期报告状态
  startPeriodicReport() {
    setInterval(() => {
      const elapsed = (Date.now() - startTime) / 1000;
      console.log(`📊 状态报告 (${elapsed.toFixed(1)}秒):`);
      console.log(`   活跃定时器: ${this.timers.size}`);
      console.log(`   活跃间隔器: ${this.intervals.size}`);
      
      // 每30秒报告一次详细信息
      if (elapsed % 30 < 1) {
        console.log('📋 详细定时器信息:');
        this.timers.forEach((info, id) => {
          console.log(`   Timer ${id}: ${info.delay}ms, ${info.callback}`);
        });
        this.intervals.forEach((info, id) => {
          console.log(`   Interval ${id}: ${info.delay}ms, 执行${info.executions}次, ${info.callback}`);
        });
      }
    }, 1000);
  }
  
  // 手动触发检查
  checkNow() {
    console.log('🔍 手动检查状态:');
    console.log('当前活跃定时器:', this.timers);
    console.log('当前活跃间隔器:', this.intervals);
    console.log('导航历史:', this.navigationHistory);
  }
}

// 创建调试器实例
window.refreshDebugger = new RefreshDebugger();
window.refreshDebugger.startPeriodicReport();

// 添加到全局，方便手动调用
window.checkRefreshDebug = () => window.refreshDebugger.checkNow();

console.log('✅ 30秒刷新调试器已启动!');
console.log('💡 使用 window.checkRefreshDebug() 手动检查状态');

export default window.refreshDebugger;