package net.lab1024.sa.admin.module.business.identity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAddForm;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAuditForm;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationQueryForm;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationListVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationStatusVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import java.util.List;

/**
 * 实名认证服务接口
 *
 * <AUTHOR>
 * @Date 2025-01-27
 */
public interface IdentityVerificationService {

    /**
     * 提交实名认证申请
     *
     * @param addForm 提交表单
     * @param userId  用户ID
     * @return 操作结果
     */
    ResponseDTO<String> submitVerification(IdentityVerificationAddForm addForm, Long userId);

    /**
     * 获取用户认证状态
     *
     * @param userId 用户ID
     * @return 认证状态
     */
    ResponseDTO<IdentityVerificationStatusVO> getUserVerificationStatus(Long userId);

    /**
     * 分页查询实名认证记录（管理员）
     *
     * @param queryForm 查询条件
     * @return 分页结果
     */
    ResponseDTO<IPage<IdentityVerificationListVO>> queryByPage(IdentityVerificationQueryForm queryForm);

    /**
     * 查询认证详情
     *
     * @param id     记录ID
     * @param userId 当前用户ID（用于权限验证）
     * @return 详情信息
     */
    ResponseDTO<IdentityVerificationVO> getVerificationDetail(Long id, Long userId);

    /**
     * 获取用户认证详情（用户端）
     *
     * @param userId 用户ID
     * @return 详情信息
     */
    ResponseDTO<IdentityVerificationVO> getUserVerificationDetail(Long userId);

    /**
     * 审核实名认证申请
     *
     * @param auditForm   审核表单
     * @param auditUserId 审核人ID
     * @return 操作结果
     */
    ResponseDTO<String> auditVerification(IdentityVerificationAuditForm auditForm, Long auditUserId);

    /**
     * 重新提交实名认证申请
     *
     * @param addForm 提交表单
     * @param userId  用户ID
     * @return 操作结果
     */
    ResponseDTO<String> resubmitVerification(IdentityVerificationAddForm addForm, Long userId);

    /**
     * 获取待审核记录列表
     *
     * @return 待审核记录
     */
    ResponseDTO<List<IdentityVerificationListVO>> getPendingList();

    /**
     * 检查用户是否已通过实名认证
     *
     * @param userId 用户ID
     * @return 是否已通过认证
     */
    boolean isUserVerified(Long userId);

    /**
     * 批量查询用户认证状态
     *
     * @param userIds 用户ID列表
     * @return 用户认证状态映射
     */
    ResponseDTO<List<IdentityVerificationStatusVO>> batchGetUserVerificationStatus(List<Long> userIds);

    /**
     * 撤销实名认证申请（仅限待审核状态）
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    ResponseDTO<String> cancelVerification(Long userId);

    /**
     * 软删除认证记录（管理员）
     *
     * @param id       记录ID
     * @param deletedBy 删除人ID
     * @return 操作结果
     */
    ResponseDTO<String> deleteVerification(Long id, Long deletedBy);

    /**
     * 恢复已删除的认证记录（管理员）
     *
     * @param id 记录ID
     * @return 操作结果
     */
    ResponseDTO<String> restoreVerification(Long id);

    /**
     * 获取认证统计信息
     *
     * @return 统计信息
     */
    ResponseDTO<Object> getVerificationStatistics();

    /**
     * 检查身份证号是否已被使用
     *
     * @param idCard 身份证号
     * @param userId 排除的用户ID
     * @return 是否已被使用
     */
    boolean isIdCardExists(String idCard, Long userId);

    /**
     * 检查银行卡号是否已被使用
     *
     * @param bankCard 银行卡号
     * @param userId   排除的用户ID
     * @return 是否已被使用
     */
    boolean isBankCardExists(String bankCard, Long userId);

    /**
     * 验证用户是否可以进行认证操作
     *
     * @param userId 用户ID
     * @return 验证结果
     */
    ResponseDTO<String> validateUserCanVerify(Long userId);

    /**
     * 清理过期的认证记录
     *
     * @return 清理的记录数
     */
    ResponseDTO<Integer> cleanExpiredRecords();
}