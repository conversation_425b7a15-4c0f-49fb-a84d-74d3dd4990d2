package net.lab1024.sa.admin.module.business.oa.banners.controller;

import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersAddForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersQueryForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersUpdateForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.vo.BannersVO;
import net.lab1024.sa.admin.module.business.oa.banners.service.BannersService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 横幅管理 Controller
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@RestController
@Tag(name = "横幅管理")
public class BannersController {

    @Resource
    private BannersService bannersService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/banners/queryPage")
    @SaCheckPermission("banners:query")
    public ResponseDTO<PageResult<BannersVO>> queryPage(@RequestBody @Valid BannersQueryForm queryForm) {
        return ResponseDTO.ok(bannersService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/banners/add")
    @SaCheckPermission("banners:add")
    public ResponseDTO<String> add(@RequestBody @Valid BannersAddForm addForm) {
        return bannersService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/banners/update")
    @SaCheckPermission("banners:update")
    public ResponseDTO<String> update(@RequestBody @Valid BannersUpdateForm updateForm) {
        return bannersService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/banners/batchDelete")
    @SaCheckPermission("banners:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
        return bannersService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/banners/delete/{id}")
    @SaCheckPermission("banners:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
        return bannersService.delete(id);
    }
}
