<!--
  实名认证详情弹窗组件
  Created: 2025-08-11
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="实名认证详情"
    :width="800"
    :footer="null"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <div v-if="record" class="identity-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">
          <UserOutlined />
          基本信息
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">用户头像</span>
            <div class="value">
              <a-avatar :src="record.userAvatar" :size="48">
                {{ record.realName ? record.realName.charAt(0) : 'U' }}
              </a-avatar>
            </div>
          </div>
          <div class="info-item">
            <span class="label">真实姓名</span>
            <span class="value">{{ record.realName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份证号</span>
            <span class="value">{{ record.idCardNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码</span>
            <span class="value">{{ record.phoneNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户ID</span>
            <span class="value">{{ record.userId || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">审核状态</span>
            <div class="value">
              <identity-status-tag :status="record.status" />
            </div>
          </div>
        </div>
      </div>

      <!-- 身份证图片 -->
      <div class="detail-section">
        <div class="section-title">
          <IdcardOutlined />
          身份证图片
        </div>
        <div class="id-card-images">
          <div class="image-item">
            <div class="image-label">身份证正面</div>
            <div class="image-container">
              <a-image
                v-if="record.idCardFrontUrl"
                :src="record.idCardFrontUrl"
                :width="200"
                :height="126"
                :preview="{ mask: '点击预览' }"
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
              />
              <div v-else class="no-image">
                <FileImageOutlined />
                <span>暂无图片</span>
              </div>
            </div>
          </div>
          <div class="image-item">
            <div class="image-label">身份证反面</div>
            <div class="image-container">
              <a-image
                v-if="record.idCardBackUrl"
                :src="record.idCardBackUrl"
                :width="200"
                :height="126"
                :preview="{ mask: '点击预览' }"
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
              />
              <div v-else class="no-image">
                <FileImageOutlined />
                <span>暂无图片</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 提交信息 -->
      <div class="detail-section">
        <div class="section-title">
          <ClockCircleOutlined />
          提交信息
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">提交时间</span>
            <span class="value">{{ formatDateTime(record.createTime) || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">提交IP</span>
            <span class="value">{{ record.submitIp || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">设备信息</span>
            <span class="value">{{ record.userAgent || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div v-if="record.status !== 0" class="detail-section">
        <div class="section-title">
          <AuditOutlined />
          审核信息
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">审核人</span>
            <span class="value">{{ record.reviewerName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">审核时间</span>
            <span class="value">{{ formatDateTime(record.reviewTime) || '-' }}</span>
          </div>
          <div v-if="record.rejectReason" class="info-item full-width">
            <span class="label">拒绝原因</span>
            <div class="value reject-reason">
              <a-alert
                :message="record.rejectReason"
                type="error"
                show-icon
                banner
              />
            </div>
          </div>
          <div v-if="record.reviewNote" class="info-item full-width">
            <span class="label">审核备注</span>
            <div class="value">
              <a-textarea
                :value="record.reviewNote"
                :rows="3"
                readonly
                style="background-color: #f5f5f5;"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div v-if="record.status === 0" class="action-section">
        <a-space size="middle">
          <a-button
            type="primary"
            @click="handleReview('approve')"
          >
            <template #icon><CheckOutlined /></template>
            审核通过
          </a-button>
          <a-button
            danger
            @click="handleReview('reject')"
          >
            <template #icon><CloseOutlined /></template>
            审核拒绝
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-container">
      <a-spin size="large" tip="加载中..." />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  UserOutlined,
  IdcardOutlined,
  ClockCircleOutlined,
  AuditOutlined,
  CheckOutlined,
  CloseOutlined,
  FileImageOutlined
} from '@ant-design/icons-vue'
import IdentityStatusTag from './identity-status-tag.vue'
import { identityApi } from '../identity-api'
import { formatDateTime } from '/@/utils/format'
import { message } from 'ant-design-vue'
import { smartSentry } from '/@/lib/smart-sentry'

// 属性定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  }
})

// 事件定义
const emit = defineEmits(['update:visible', 'review'])

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const detailRecord = ref(null)

// 监听记录变化，获取详细信息
watch(
  () => [props.visible, props.record],
  async ([visible, record]) => {
    if (visible && record) {
      await loadDetailData(record.identityId)
    }
  },
  { immediate: true }
)

// 加载详细数据
const loadDetailData = async (identityId) => {
  if (!identityId) return
  
  try {
    const res = await identityApi.getDetail(identityId)
    if (res.data) {
      detailRecord.value = res.data
    }
  } catch (error) {
    message.error('获取详情失败：' + error.message)
    smartSentry.captureError(error)
  }
}

// 当前记录（优先使用详细记录）
const record = computed(() => detailRecord.value || props.record)

// 处理取消
const handleCancel = () => {
  modalVisible.value = false
  detailRecord.value = null
}

// 处理审核操作
const handleReview = (type) => {
  emit('review', type)
}
</script>

<style scoped>
.identity-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.section-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.info-item .value {
  font-size: 14px;
  color: #262626;
  font-weight: 400;
}

.id-card-images {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.image-item {
  text-align: center;
}

.image-label {
  font-size: 14px;
  color: #595959;
  margin-bottom: 8px;
  font-weight: 500;
}

.image-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: inline-block;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 126px;
  background-color: #fafafa;
  color: #8c8c8c;
}

.no-image .anticon {
  font-size: 24px;
  margin-bottom: 8px;
}

.reject-reason {
  margin-top: 4px;
}

.action-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

:deep(.ant-image) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.ant-alert) {
  border-radius: 4px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .id-card-images {
    flex-direction: column;
    align-items: center;
  }
}
</style>