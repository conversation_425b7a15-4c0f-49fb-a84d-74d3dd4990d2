package net.lab1024.sa.admin.module.business.invitationRecords.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 邀请记录表 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@Data
@TableName("t_invitation_records")
public class InvitationRecordsEntity {

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请人ID
     */
    private Long inviterId;

    /**
     * 被邀请人ID
     */
    private Long inviteeId;

    /**
     * 已获得佣金
     */
    private BigDecimal commissionEarned;

    /**
     * 邀请状态
     */
    private String status;

    /**
     * 首次充值时间
     */
    private LocalDateTime firstRechargeTime;

    /**
     * 首次下单时间
     */
    private LocalDateTime firstOrderTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
