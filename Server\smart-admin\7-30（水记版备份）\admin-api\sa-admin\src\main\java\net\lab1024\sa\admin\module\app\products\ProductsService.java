package net.lab1024.sa.admin.module.app.products;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsSkusDao;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsQueryForm;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsVO;
import net.lab1024.sa.admin.module.business.goods.service.GoodsService;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.module.support.file.service.IFileStorageService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class ProductsService {
    @Resource
    GoodsService goodsService;

    @Resource
    IFileStorageService fileStorageService;

    @Resource
    GoodsSkusDao goodsSkusDao;

    /**
     * 商品列表
     */
    public Object products(String category, String goodsType, String groupType, String priceRange, String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);

        //商品
        GoodsQueryForm goodsQueryForm = SmartBeanUtil.copy(pageParam, GoodsQueryForm.class);
        goodsQueryForm.setSortItemList(new ArrayList<>());

        if (category != null) {
            goodsQueryForm.setCategoryName(category);
        }
        if (goodsType != null) {
            goodsQueryForm.setGoodsType(goodsType);
        }

        if (Objects.equals(sort, "price_desc")) {
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("price");
            si.setIsAsc(false);
            goodsQueryForm.getSortItemList().add(si);
        }
        if (Objects.equals(sort, "price_asc")) {
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("price");
            si.setIsAsc(true);
            goodsQueryForm.getSortItemList().add(si);
        }
        goodsQueryForm.setDeletedFlag(false);

        PageResult<GoodsVO> products = goodsService.query(goodsQueryForm).getData();
        for(GoodsVO goodsVO: products.getList()){
            if (goodsVO.getImages() != null && !goodsVO.getImages().isEmpty()) {
                for (Map<String, Object> m : goodsVO.getImages()) {
                    String url = m.get("url").toString();
                    ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                    if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                        m.put("url", getFileUrl.getData());
                    }
                }
            }
            if (goodsVO.getDetailImages() != null && !goodsVO.getDetailImages().isEmpty()) {
                for (Map<String, Object> m : goodsVO.getDetailImages()) {
                    String url = m.get("url").toString();
                    ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                    if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                        m.put("url", getFileUrl.getData());
                    }
                }
            }

            QueryWrapper<GoodsSkusEntity> Q1 = new QueryWrapper<>();
            Q1.eq("goods_id", goodsVO.getGoodsId()).select("id,goods_id,sku_code,attributes,price,original_price,stock,sales_count,status");
            goodsVO.setSkus(goodsSkusDao.selectList(Q1));
        }



        return products;
    }


    /**
     * 商品详细
     */
    public GoodsVO productsDetail(Long goodId) {
        return goodsService.getId(goodId,true).getData();
    }
}
