package net.lab1024.sa.admin.module.business.orders.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Data
public class OrdersAddForm {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID 不能为空")
    private Long userId;

    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动ID 不能为空")
    private Long activityId;

    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单状态 不能为空")
    private String status;

    @Schema(description = "用户实付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户实付金额 不能为空")
    private BigDecimal amountPaid;

    @Schema(description = "使用体验金", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "使用体验金 不能为空")
    private BigDecimal experiencePaid;

    @Schema(description = "补贴金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "补贴金额 不能为空")
    private BigDecimal subsidyPaid;

}