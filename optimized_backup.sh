#!/bin/bash

# 优化的备份脚本 - 减少密码输入次数
# 使用方法: ./optimized_backup.sh [数据库名]

SERVER_IP="*************"
DATABASE_NAME=${1:-"tgw_pp"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_${TIMESTAMP}.sql"
LOCAL_BACKUP_DIR="DataBackup"

echo "=== MySQL 数据库备份脚本 ==="
echo "数据库: $DATABASE_NAME"
echo "服务器: $SERVER_IP"
echo "备份文件: $BACKUP_FILENAME"
echo ""

# 创建本地备份目录
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "创建备份目录: $LOCAL_BACKUP_DIR"
    mkdir -p "$LOCAL_BACKUP_DIR"
fi

echo "📋 密码说明:"
echo "   SSH密码: 服务器登录密码"
echo "   MySQL密码: 数据库密码"
echo ""

# 一次性执行所有服务器操作，减少SSH连接次数
echo "🔄 开始备份流程..."
echo ">>> 请输入SSH密码 (服务器登录密码):"

ssh root@$SERVER_IP "
echo '✅ 已连接到服务器'
echo ''

# 检查MySQL服务
echo '🔍 检查MySQL服务状态...'
if ! systemctl is-active --quiet mysql; then
    echo '❌ MySQL服务未运行，尝试启动...'
    systemctl start mysql
    if [ \$? -ne 0 ]; then
        echo '❌ 无法启动MySQL服务'
        exit 1
    fi
fi
echo '✅ MySQL服务正常'
echo ''

# 检查数据库并备份
echo '🔍 检查数据库并执行备份...'
echo '>>> 请输入MySQL密码 (数据库密码):'

# 检查数据库是否存在
DB_EXISTS=\$(mysql -u root -p -e \"SHOW DATABASES LIKE '$DATABASE_NAME';\" 2>/dev/null | grep '$DATABASE_NAME')
if [ -z \"\$DB_EXISTS\" ]; then
    echo '❌ 数据库 \"$DATABASE_NAME\" 不存在'
    echo '📋 可用的数据库列表:'
    mysql -u root -p -e 'SHOW DATABASES;' 2>/dev/null
    exit 1
fi
echo '✅ 数据库 \"$DATABASE_NAME\" 存在'

# 执行备份
echo '💾 正在备份数据库...'
mysqldump -u root -p --single-transaction --routines --triggers --set-gtid-purged=OFF $DATABASE_NAME > /root/$BACKUP_FILENAME 2>/tmp/backup_error.log

if [ \$? -eq 0 ]; then
    FILE_SIZE=\$(stat -c%s /root/$BACKUP_FILENAME 2>/dev/null)
    if [ \$FILE_SIZE -gt 0 ]; then
        echo \"✅ 备份成功! 文件大小: \$(ls -lh /root/$BACKUP_FILENAME | awk '{print \$5}')\"
        echo \"📁 备份文件: /root/$BACKUP_FILENAME\"
        
        # 显示备份文件的前几行验证内容
        echo '📄 备份文件内容预览:'
        head -3 /root/$BACKUP_FILENAME | grep -E '^(--|/\*|CREATE|INSERT)'
        echo '...'
    else
        echo '❌ 备份文件为空'
        echo '📋 错误详情:'
        cat /tmp/backup_error.log
        exit 1
    fi
else
    echo '❌ 备份失败'
    echo '📋 错误详情:'
    cat /tmp/backup_error.log
    exit 1
fi

# 清理错误日志
rm -f /tmp/backup_error.log
echo '✅ 服务器端备份完成'
"

# 检查SSH命令执行结果
if [ $? -ne 0 ]; then
    echo "❌ 服务器端操作失败"
    exit 1
fi

echo ""
echo "📥 下载备份文件到本地..."
echo ">>> 请输入SSH密码 (下载文件):"

scp root@$SERVER_IP:/root/$BACKUP_FILENAME "$LOCAL_BACKUP_DIR/"

if [ $? -ne 0 ]; then
    echo "❌ 文件下载失败"
    exit 1
fi

# 验证本地文件
LOCAL_FILE_PATH="$LOCAL_BACKUP_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    LOCAL_FILE_SIZE=$(stat -c%s "$LOCAL_FILE_PATH" 2>/dev/null || stat -f%z "$LOCAL_FILE_PATH" 2>/dev/null)
    if [ "$LOCAL_FILE_SIZE" -gt 0 ]; then
        echo "✅ 文件下载成功!"
        echo "📁 本地文件: $LOCAL_FILE_PATH"
        echo "📊 文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"
        
        # 显示文件内容预览
        echo ""
        echo "📄 本地文件内容预览:"
        head -5 "$LOCAL_FILE_PATH" | grep -E '^(--|/\*|CREATE|INSERT)' | head -3
        echo "..."
    else
        echo "❌ 本地文件为空"
        exit 1
    fi
else
    echo "❌ 本地文件不存在"
    exit 1
fi

# 询问是否清理远程文件
echo ""
echo "🗑️  清理选项:"
read -p "是否删除服务器上的备份文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ">>> 请输入SSH密码 (删除远程文件):"
    ssh root@$SERVER_IP "rm -f /root/$BACKUP_FILENAME && echo '✅ 远程文件已删除'"
else
    echo "📁 远程文件保留在: /root/$BACKUP_FILENAME"
fi

echo ""
echo "🎉 备份完成!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📁 本地文件: $LOCAL_FILE_PATH"
echo "📊 文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"
echo "🕐 备份时间: $(date)"

# 显示最近的备份文件
echo ""
echo "📋 最近的备份文件:"
ls -lt "$LOCAL_BACKUP_DIR"/*.sql 2>/dev/null | head -5 | while read line; do
    echo "   $line"
done

echo ""
echo "💡 提示:"
echo "   - 总共需要输入 2-3 次SSH密码 + 1次MySQL密码"
echo "   - 如需减少密码输入，可配置SSH密钥认证"
echo "   - 备份文件已保存到 $LOCAL_BACKUP_DIR 目录"
