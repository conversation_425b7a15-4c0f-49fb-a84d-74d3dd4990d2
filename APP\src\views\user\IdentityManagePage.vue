<template>
  <!-- 认证检查中的加载状态 -->
  <div v-if="!authCheckCompleted" class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
      <p class="text-gray-500 text-sm">正在验证登录状态...</p>
    </div>
  </div>
  
  <!-- 页面内容 - 只有认证通过才显示 -->
  <div v-else class="bg-gray-50 min-h-screen">
    <!-- Header导航模块 -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between p-4 h-14">
        <!-- 返回按钮 -->
        <button @click="goBack" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:arrow-back-ios" class="text-gray-800 text-xl"></iconify-icon>
        </button>
        
        <!-- 页面标题 -->
        <h1 class="text-sm font-semibold text-gray-800">
          实名认证管理
        </h1>
        
        <!-- 右侧占位 -->
        <div class="flex items-center justify-center w-6 h-6"></div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="px-4 py-6">
      <!-- 认证状态卡片 -->
      <div class="bg-white rounded-lg p-4 shadow-sm mb-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-base font-semibold text-gray-800">认证状态</h2>
          <div 
            :class="[
              'px-3 py-1 rounded-full text-sm font-medium flex items-center',
              getStatusStyle(identityInfo.status)
            ]"
          >
            <iconify-icon :icon="getStatusIcon(identityInfo.status)" class="mr-1"></iconify-icon>
            {{ getStatusText(identityInfo.status) }}
          </div>
        </div>

        <!-- 认证信息展示 -->
        <div v-if="identityInfo.status !== 'not_submitted'" class="space-y-3">
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">真实姓名</span>
            <span class="text-sm font-medium">{{ identityInfo.realName || '未填写' }}</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">身份证号</span>
            <span class="text-sm font-medium">{{ getMaskedIdNumber(identityInfo.idCard) }}</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">银行卡号</span>
            <span class="text-sm font-medium">{{ getMaskedBankAccount(identityInfo.bankCard) }}</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-sm text-gray-600">开户行</span>
            <span class="text-sm font-medium">{{ identityInfo.bankName || '未填写' }}</span>
          </div>
          <div class="flex items-center justify-between py-2">
            <span class="text-sm text-gray-600">提交时间</span>
            <span class="text-sm font-medium">{{ formatDate(identityInfo.submitTime) }}</span>
          </div>
        </div>

        <!-- 未认证状态 -->
        <div v-else class="text-center py-8">
          <iconify-icon icon="material-symbols:verified-user-outline" class="text-6xl text-gray-300 mb-4"></iconify-icon>
          <p class="text-gray-500 text-sm mb-4">您还未提交实名认证资料</p>
          <button 
            @click="goToVerification"
            class="bg-blue-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            立即认证
          </button>
        </div>
      </div>

      <!-- 认证被拒绝时显示原因 -->
      <div v-if="identityInfo.status === 'rejected'" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
          <iconify-icon icon="material-symbols:error" class="text-red-500 text-lg mr-3 mt-0.5"></iconify-icon>
          <div>
            <h3 class="text-red-800 font-medium mb-2">认证被拒绝</h3>
            <p class="text-red-700 text-sm">{{ identityInfo.rejectReason || '未通过审核，请重新提交' }}</p>
          </div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="bg-white rounded-lg shadow-sm mb-6">
        <!-- 重新认证 -->
        <div 
          v-if="identityInfo.status === 'rejected' || identityInfo.status === 'not_submitted'"
          class="p-4 border-b border-gray-100"
        >
          <button 
            @click="goToVerification"
            class="w-full flex items-center justify-between py-3 text-left"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <iconify-icon icon="material-symbols:upload" class="text-blue-500"></iconify-icon>
              </div>
              <div>
                <div class="font-medium text-gray-800 text-sm">
                  {{ identityInfo.status === 'rejected' ? '重新认证' : '开始认证' }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ identityInfo.status === 'rejected' ? '重新上传认证资料' : '上传身份证和银行卡信息' }}
                </div>
              </div>
            </div>
            <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
          </button>
        </div>

        <!-- 查看认证资料 -->
        <div 
          v-if="identityInfo.status === 'approved' || identityInfo.status === 'pending'"
          class="p-4 border-b border-gray-100"
        >
          <button 
            @click="viewCertificates"
            class="w-full flex items-center justify-between py-3 text-left"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <iconify-icon icon="material-symbols:visibility" class="text-green-500"></iconify-icon>
              </div>
              <div>
                <div class="font-medium text-gray-800 text-sm">查看认证资料</div>
                <div class="text-xs text-gray-500">查看已提交的身份证和银行卡信息</div>
              </div>
            </div>
            <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
          </button>
        </div>

        <!-- 认证记录 -->
        <div class="p-4 border-b border-gray-100">
          <button 
            @click="viewHistory"
            class="w-full flex items-center justify-between py-3 text-left"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                <iconify-icon icon="material-symbols:history" class="text-purple-500"></iconify-icon>
              </div>
              <div>
                <div class="font-medium text-gray-800 text-sm">认证记录</div>
                <div class="text-xs text-gray-500">查看历史认证记录</div>
              </div>
            </div>
            <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
          </button>
        </div>

        <!-- 帮助中心 -->
        <div class="p-4">
          <button 
            @click="goToHelp"
            class="w-full flex items-center justify-between py-3 text-left"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                <iconify-icon icon="material-symbols:help" class="text-orange-500"></iconify-icon>
              </div>
              <div>
                <div class="font-medium text-gray-800 text-sm">帮助中心</div>
                <div class="text-xs text-gray-500">常见问题和认证指南</div>
              </div>
            </div>
            <iconify-icon icon="material-symbols:chevron-right" class="text-gray-400"></iconify-icon>
          </button>
        </div>
      </div>

      <!-- 认证说明 -->
      <div class="bg-white rounded-lg p-4 shadow-sm">
        <h3 class="text-base font-semibold text-gray-800 mb-3">认证说明</h3>
        <div class="space-y-2">
          <div class="flex items-start">
            <iconify-icon icon="material-symbols:check-circle" class="text-green-500 text-sm mt-0.5 mr-2"></iconify-icon>
            <span class="text-sm text-gray-600">实名认证是提现的必要条件</span>
          </div>
          <div class="flex items-start">
            <iconify-icon icon="material-symbols:check-circle" class="text-green-500 text-sm mt-0.5 mr-2"></iconify-icon>
            <span class="text-sm text-gray-600">认证通过后可以正常提现</span>
          </div>
          <div class="flex items-start">
            <iconify-icon icon="material-symbols:check-circle" class="text-green-500 text-sm mt-0.5 mr-2"></iconify-icon>
            <span class="text-sm text-gray-600">我们会严格保护您的个人信息</span>
          </div>
          <div class="flex items-start">
            <iconify-icon icon="material-symbols:check-circle" class="text-green-500 text-sm mt-0.5 mr-2"></iconify-icon>
            <span class="text-sm text-gray-600">审核时间通常为1-3个工作日</span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError } from '@/utils/message'
import { requireAuth } from '@/composables/usePageAuth'
import { StandardApiAdapter } from '@/api/standardAdapter'

export default {
  name: 'IdentityManagePage',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 启用页面级登录检查
    requireAuth({
      errorMessage: '访问实名认证管理需要先登录'
    })

    // 认证检查状态
    const authCheckCompleted = ref(false)
    
    // 响应式数据
    const identityInfo = ref({
      status: 'not_submitted',
      realName: '',
      idCard: '',
      bankAccountName: '',
      bankName: '',
      bankCard: '',
      submitTime: null,
      auditTime: null,
      rejectReason: ''
    })

    // API服务
    let verificationApi = null

    // 初始化API服务
    const initApiService = async () => {
      try {
        verificationApi = new StandardApiAdapter()
        console.log('✅ 实名认证管理API服务初始化成功')
      } catch (err) {
        console.error('❌ 实名认证管理API服务初始化失败:', err)
      }
    }

    // 返回上一页
    const goBack = () => {
      router.back()
    }

    // 获取状态样式
    const getStatusStyle = (status) => {
      const styles = {
        'not_submitted': 'bg-gray-100 text-gray-600',
        'pending': 'bg-yellow-100 text-yellow-600',
        'approved': 'bg-green-100 text-green-600',
        'rejected': 'bg-red-100 text-red-600'
      }
      return styles[status] || styles['not_submitted']
    }

    // 获取状态图标
    const getStatusIcon = (status) => {
      const icons = {
        'not_submitted': 'material-symbols:info',
        'pending': 'material-symbols:schedule',
        'approved': 'material-symbols:check-circle',
        'rejected': 'material-symbols:cancel'
      }
      return icons[status] || icons['not_submitted']
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        'not_submitted': '未认证',
        'pending': '审核中',
        'approved': '已认证',
        'rejected': '已拒绝'
      }
      return texts[status] || texts['not_submitted']
    }

    // 获取脱敏的身份证号
    const getMaskedIdNumber = (idNumber) => {
      if (!idNumber) return '未填写'
      if (idNumber.length < 6) return idNumber
      return idNumber.substring(0, 3) + '****' + idNumber.substring(idNumber.length - 4)
    }

    // 获取脱敏的银行卡号
    const getMaskedBankAccount = (bankAccount) => {
      if (!bankAccount) return '未填写'
      if (bankAccount.length < 8) return bankAccount
      return '**** **** **** ' + bankAccount.substring(bankAccount.length - 4)
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 跳转到认证页面
    const goToVerification = () => {
      router.push('/user/identity-verification')
    }

    // 查看认证资料
    const viewCertificates = () => {
      showSuccess('查看认证资料功能开发中')
    }

    // 查看认证记录
    const viewHistory = () => {
      showSuccess('认证记录功能开发中')
    }

    // 跳转到帮助中心
    const goToHelp = () => {
      router.push('/user/customer-service')
    }

    // 加载认证信息
    const loadIdentityInfo = async () => {
      try {
        if (!verificationApi) {
          await initApiService()
        }

        console.log('🔍 获取实名认证信息...')
        const response = await verificationApi.getIdentityVerificationInfo()

        console.log('📝 实名认证信息响应:', response)

        if (response.code === 200 || response.code === 0) {
          const data = response.data || {}
          identityInfo.value = {
            status: data.status || 'not_submitted',
            realName: data.realName || '',
            idCard: data.idCard || '',
            bankAccountName: data.bankAccountName || '',
            bankName: data.bankName || '',
            bankCard: data.bankCard || '',
            submitTime: data.submitTime || null,
            auditTime: data.auditTime || null,
            rejectReason: data.rejectReason || ''
          }
          
          console.log('✅ 实名认证信息加载成功:', identityInfo.value)
        } else {
          console.warn('⚠️ 实名认证信息获取失败:', response.message)
        }
      } catch (error) {
        console.error('❌ 实名认证信息获取失败:', error)
        showError('获取认证信息失败')
      }
    }

    // 生命周期
    onMounted(async () => {
      console.log('🎯 [IdentityManagePage] 页面挂载开始')
      
      // 首先进行认证检查
      try {
        // 检查登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ [IdentityManagePage] 认证检查失败，用户未登录')
          
          // 显示错误并跳转
          showError('访问实名认证管理需要先登录')
          
          setTimeout(() => {
            router.replace({
              path: '/login',
              query: { redirect: '/user/identity-manage' }
            })
          }, 1500)
          
          return // 停止后续初始化
        }
        
        console.log('✅ [IdentityManagePage] 认证检查通过')
        authCheckCompleted.value = true
        
        // 认证通过后初始化页面数据
        await initApiService()
        await loadIdentityInfo()
        
      } catch (error) {
        console.error('❌ [IdentityManagePage] 页面初始化失败:', error)
        showError('页面初始化失败')
        
        // 初始化失败也要跳转到登录页
        setTimeout(() => {
          router.replace('/login')
        }, 1500)
      }
    })

    return {
      authCheckCompleted,
      identityInfo,
      goBack,
      getStatusStyle,
      getStatusIcon,
      getStatusText,
      getMaskedIdNumber,
      getMaskedBankAccount,
      formatDate,
      goToVerification,
      viewCertificates,
      viewHistory,
      goToHelp
    }
  }
}
</script>

<style scoped>
/* 添加一些基本的过渡效果 */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}
</style>