#!/bin/bash
# Git hooks 设置脚本

set -e

print_message() {
    echo -e "\033[0;32m$1\033[0m"
}

print_message "🚀 正在设置Git hooks..."

# 创建必要的目录
mkdir -p .git/hooks
mkdir -p .git/change-logs
mkdir -p .git/commit-logs

# 复制hooks到.git/hooks目录
if [ -f ".githooks/pre-commit" ]; then
    cp .githooks/pre-commit .git/hooks/pre-commit
    chmod +x .git/hooks/pre-commit
    print_message "✅ pre-commit hook已设置"
fi

if [ -f ".githooks/post-commit" ]; then
    cp .githooks/post-commit .git/hooks/post-commit
    chmod +x .git/hooks/post-commit
    print_message "✅ post-commit hook已设置"
fi

# 设置脚本执行权限
chmod +x scripts/generate-pr-summary.js
chmod +x scripts/create-pr.sh

print_message "✅ Git hooks设置完成!"
print_message "现在每次提交都会自动记录修改信息"
print_message "使用 './scripts/create-pr.sh' 创建PR时会自动生成摘要"