package net.lab1024.sa.admin.module.business.teamRewards.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 团队奖励表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Data
public class TeamRewardsVO {


    @Schema(description = "奖励ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "奖励日期")
    private LocalDate rewardDate;

    @Schema(description = "达标人数")
    private Integer qualifiedCount;

    @Schema(description = "奖励金额")
    private BigDecimal rewardAmount;

    @Schema(description = "发放状态")
    private String status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
