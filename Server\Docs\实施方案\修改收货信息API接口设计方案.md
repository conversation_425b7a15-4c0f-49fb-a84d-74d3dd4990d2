# 修改收货信息API接口设计方案

## 概述

本文档详细描述了基于现有订单系统的修改收货信息功能的API接口设计方案，包括后端Smart-Admin框架实施内容和前端修改页面的完整实现方案。

## 1. 现有系统分析

### 1.1 数据库结构分析

基于现有系统分析，订单收货信息采用关联表设计：

```sql
-- 订单表 (t_orders)
CREATE TABLE `t_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(64) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shipping_address_id` bigint(20) COMMENT '收货地址ID',
  `status` varchar(20) NOT NULL COMMENT '订单状态',
  -- 其他字段...
  PRIMARY KEY (`id`),
  KEY `idx_shipping_address_id` (`shipping_address_id`)
) COMMENT='订单表';

-- 用户地址表 (t_user_address)
CREATE TABLE `t_user_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `recipient_name` varchar(50) NOT NULL COMMENT '收件人姓名',
  `phone_number` varchar(20) NOT NULL COMMENT '收件人电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address_line` varchar(200) NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) COMMENT='用户地址表';
```

### 1.2 业务规则分析

1. **关联关系**：订单通过 `shipping_address_id` 关联用户地址表
2. **权限控制**：用户只能修改自己的订单收货信息
3. **状态限制**：只有特定状态的订单才允许修改收货信息
4. **数据完整性**：修改后需要验证地址信息的完整性

## 2. API接口设计

### 2.1 接口列表

| 接口名称 | 方法 | 路径 | 描述 |
|---------|------|------|------|
| 获取订单收货信息 | GET | `/api/v1/app/order/{orderId}/shipping-info` | 获取订单当前收货信息 |
| 修改订单收货信息 | PUT | `/api/v1/app/order/{orderId}/shipping-info` | 修改订单收货信息 |
| 检查修改权限 | GET | `/api/v1/app/order/{orderId}/shipping-editable` | 检查订单是否可修改收货信息 |
| 获取用户地址列表 | GET | `/api/v1/app/user/address/list` | 获取用户所有地址（供选择） |
| 创建新地址并应用 | POST | `/api/v1/app/order/{orderId}/create-and-apply-address` | 创建新地址并应用到订单 |

### 2.2 详细接口规范

#### 2.2.1 获取订单收货信息

```http
GET /api/v1/app/order/{orderId}/shipping-info
Authorization: Bearer {token}

响应:
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "orderId": 12345,
    "orderSn": "ORD20250127001",
    "orderStatus": "paid",
    "canEdit": true,
    "shippingInfo": {
      "addressId": 1001,
      "recipientName": "张三",
      "phoneNumber": "13800138000",
      "province": "广东省",
      "city": "深圳市",
      "district": "南山区",
      "addressLine": "科技园南区深南大道1001号",
      "fullAddress": "广东省深圳市南山区科技园南区深南大道1001号"
    }
  }
}
```

#### 2.2.2 修改订单收货信息

```http
PUT /api/v1/app/order/{orderId}/shipping-info
Authorization: Bearer {token}
Content-Type: application/json

请求体:
{
  "editType": "modify_existing", // modify_existing: 修改现有地址, select_existing: 选择已有地址, create_new: 创建新地址
  "addressId": 1001, // editType为select_existing时必填
  "shippingInfo": {
    "recipientName": "李四",
    "phoneNumber": "13900139000",
    "province": "广东省",
    "city": "深圳市",
    "district": "福田区",
    "addressLine": "中心区福华路1000号"
  }
}

响应:
{
  "code": 0,
  "msg": "收货信息修改成功",
  "ok": true,
  "data": {
    "orderId": 12345,
    "newAddressId": 1002,
    "updateTime": "2025-01-27T16:30:00Z",
    "shippingInfo": {
      "recipientName": "李四",
      "phoneNumber": "13900139000",
      "province": "广东省",
      "city": "深圳市",
      "district": "福田区",
      "addressLine": "中心区福华路1000号",
      "fullAddress": "广东省深圳市福田区中心区福华路1000号"
    }
  }
}
```

#### 2.2.3 检查修改权限

```http
GET /api/v1/app/order/{orderId}/shipping-editable
Authorization: Bearer {token}

响应:
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "canEdit": true,
    "reason": null, // 不能修改时的原因
    "allowedStatuses": ["paid", "confirmed"], // 允许修改的订单状态
    "currentStatus": "paid",
    "timeLimit": "2025-01-28T16:30:00Z" // 修改截止时间
  }
}
```

## 3. Smart-Admin框架实施方案

### 3.1 目录结构

```
smart-admin/admin-api/sa-admin/src/main/java/net/lab1024/sa/admin/
├── module/
│   └── app/
│       └── orders/
│           ├── controller/
│           │   └── AppOrderShippingController.java
│           ├── service/
│           │   └── AppOrderShippingService.java
│           ├── domain/
│           │   ├── form/
│           │   │   ├── OrderShippingInfoUpdateForm.java
│           │   │   └── CreateAndApplyAddressForm.java
│           │   └── vo/
│           │       ├── OrderShippingInfoVO.java
│           │       └── ShippingEditableVO.java
│           └── enums/
│               └── OrderShippingEditTypeEnum.java
```

### 3.2 核心实现代码

#### 3.2.1 Controller实现

```java
@RestController
@RequestMapping("/api/v1/app/order")
@Tag(name = "订单收货信息", description = "APP端订单收货信息管理接口")
@Slf4j
public class AppOrderShippingController {

    @Resource
    private AppOrderShippingService orderShippingService;

    @GetMapping("/{orderId}/shipping-info")
    @Operation(summary = "获取订单收货信息")
    public ResponseDTO<OrderShippingInfoVO> getShippingInfo(
            @PathVariable Long orderId,
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(orderShippingService.getShippingInfo(orderId, requestEmployee.getEmployeeId()));
    }

    @PutMapping("/{orderId}/shipping-info")
    @Operation(summary = "修改订单收货信息")
    public ResponseDTO<OrderShippingInfoVO> updateShippingInfo(
            @PathVariable Long orderId,
            @Valid @RequestBody OrderShippingInfoUpdateForm form,
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(orderShippingService.updateShippingInfo(orderId, form, requestEmployee.getEmployeeId()));
    }

    @GetMapping("/{orderId}/shipping-editable")
    @Operation(summary = "检查订单收货信息是否可修改")
    public ResponseDTO<ShippingEditableVO> checkShippingEditable(
            @PathVariable Long orderId,
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(orderShippingService.checkShippingEditable(orderId, requestEmployee.getEmployeeId()));
    }

    @PostMapping("/{orderId}/create-and-apply-address")
    @Operation(summary = "创建新地址并应用到订单")
    public ResponseDTO<OrderShippingInfoVO> createAndApplyAddress(
            @PathVariable Long orderId,
            @Valid @RequestBody CreateAndApplyAddressForm form,
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(orderShippingService.createAndApplyAddress(orderId, form, requestEmployee.getEmployeeId()));
    }
}
```

#### 3.2.2 Service实现

```java
@Service
@Slf4j
public class AppOrderShippingService {

    @Resource
    private OrdersDao ordersDao;
    
    @Resource
    private UserAddressDao userAddressDao;
    
    @Resource
    private UserAddressService userAddressService;

    /**
     * 获取订单收货信息
     */
    public OrderShippingInfoVO getShippingInfo(Long orderId, Long userId) {
        // 验证订单归属
        OrdersEntity order = validateOrderOwnership(orderId, userId);
        
        OrderShippingInfoVO result = new OrderShippingInfoVO();
        result.setOrderId(orderId);
        result.setOrderSn(order.getOrderSn());
        result.setOrderStatus(order.getStatus());
        
        // 检查是否可编辑
        result.setCanEdit(isShippingEditable(order));
        
        // 获取收货信息
        if (order.getShippingAddressId() != null) {
            UserAddressEntity address = userAddressDao.selectById(order.getShippingAddressId());
            if (address != null) {
                OrderShippingInfoVO.ShippingInfo shippingInfo = BeanUtil.copyProperties(address, OrderShippingInfoVO.ShippingInfo.class);
                shippingInfo.setAddressId(address.getId());
                shippingInfo.setFullAddress(buildFullAddress(address));
                result.setShippingInfo(shippingInfo);
            }
        }
        
        return result;
    }

    /**
     * 修改订单收货信息
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderShippingInfoVO updateShippingInfo(Long orderId, OrderShippingInfoUpdateForm form, Long userId) {
        // 验证订单归属和可编辑性
        OrdersEntity order = validateOrderOwnership(orderId, userId);
        if (!isShippingEditable(order)) {
            throw new BusinessException("当前订单状态不允许修改收货信息");
        }

        Long newAddressId = null;
        
        switch (form.getEditType()) {
            case MODIFY_EXISTING:
                // 修改现有地址
                newAddressId = modifyExistingAddress(order.getShippingAddressId(), form.getShippingInfo(), userId);
                break;
                
            case SELECT_EXISTING:
                // 选择已有地址
                newAddressId = selectExistingAddress(form.getAddressId(), userId);
                break;
                
            case CREATE_NEW:
                // 创建新地址
                newAddressId = createNewAddress(form.getShippingInfo(), userId);
                break;
                
            default:
                throw new BusinessException("不支持的编辑类型");
        }

        // 更新订单的收货地址ID
        if (!newAddressId.equals(order.getShippingAddressId())) {
            order.setShippingAddressId(newAddressId);
            order.setUpdateTime(LocalDateTime.now());
            ordersDao.updateById(order);
        }

        // 记录操作日志
        log.info("用户{}修改订单{}收货信息，新地址ID：{}", userId, orderId, newAddressId);

        // 返回更新后的信息
        return getShippingInfo(orderId, userId);
    }

    /**
     * 检查订单收货信息是否可修改
     */
    public ShippingEditableVO checkShippingEditable(Long orderId, Long userId) {
        OrdersEntity order = validateOrderOwnership(orderId, userId);
        
        ShippingEditableVO result = new ShippingEditableVO();
        result.setCanEdit(isShippingEditable(order));
        result.setCurrentStatus(order.getStatus());
        result.setAllowedStatuses(Arrays.asList("paid", "confirmed"));
        
        if (!result.getCanEdit()) {
            result.setReason(getNotEditableReason(order));
        }
        
        // 设置修改截止时间（例如：支付后24小时内可修改）
        if (order.getPaymentTime() != null) {
            result.setTimeLimit(order.getPaymentTime().plusHours(24));
        }
        
        return result;
    }

    /**
     * 创建新地址并应用到订单
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderShippingInfoVO createAndApplyAddress(Long orderId, CreateAndApplyAddressForm form, Long userId) {
        OrdersEntity order = validateOrderOwnership(orderId, userId);
        if (!isShippingEditable(order)) {
            throw new BusinessException("当前订单状态不允许修改收货信息");
        }

        // 创建新地址
        UserAddressAddForm addressForm = BeanUtil.copyProperties(form, UserAddressAddForm.class);
        UserAddressVO newAddress = userAddressService.add(addressForm, userId);

        // 更新订单收货地址
        order.setShippingAddressId(newAddress.getId());
        order.setUpdateTime(LocalDateTime.now());
        ordersDao.updateById(order);

        log.info("用户{}为订单{}创建并应用新收货地址，地址ID：{}", userId, orderId, newAddress.getId());

        return getShippingInfo(orderId, userId);
    }

    // 私有辅助方法

    private OrdersEntity validateOrderOwnership(Long orderId, Long userId) {
        OrdersEntity order = ordersDao.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException("无权访问此订单");
        }
        return order;
    }

    private boolean isShippingEditable(OrdersEntity order) {
        // 只有已支付和已确认的订单可以修改收货信息
        List<String> editableStatuses = Arrays.asList("paid", "confirmed");
        
        if (!editableStatuses.contains(order.getStatus())) {
            return false;
        }
        
        // 检查时间限制（支付后24小时内可修改）
        if (order.getPaymentTime() != null) {
            LocalDateTime timeLimit = order.getPaymentTime().plusHours(24);
            if (LocalDateTime.now().isAfter(timeLimit)) {
                return false;
            }
        }
        
        return true;
    }

    private String getNotEditableReason(OrdersEntity order) {
        if (order.getStatus().equals("shipped")) {
            return "订单已发货，无法修改收货信息";
        }
        if (order.getStatus().equals("completed")) {
            return "订单已完成，无法修改收货信息";
        }
        if (order.getPaymentTime() != null) {
            LocalDateTime timeLimit = order.getPaymentTime().plusHours(24);
            if (LocalDateTime.now().isAfter(timeLimit)) {
                return "已超过修改时限（支付后24小时内可修改）";
            }
        }
        return "当前订单状态不允许修改收货信息";
    }

    private Long modifyExistingAddress(Long addressId, OrderShippingInfoUpdateForm.ShippingInfo shippingInfo, Long userId) {
        UserAddressEntity address = userAddressDao.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException("地址不存在或无权修改");
        }

        // 更新地址信息
        BeanUtil.copyProperties(shippingInfo, address, "id", "userId", "createTime");
        address.setUpdateTime(LocalDateTime.now());
        userAddressDao.updateById(address);

        return addressId;
    }

    private Long selectExistingAddress(Long addressId, Long userId) {
        UserAddressEntity address = userAddressDao.selectById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException("地址不存在或无权使用");
        }
        return addressId;
    }

    private Long createNewAddress(OrderShippingInfoUpdateForm.ShippingInfo shippingInfo, Long userId) {
        UserAddressEntity newAddress = BeanUtil.copyProperties(shippingInfo, UserAddressEntity.class);
        newAddress.setUserId(userId);
        newAddress.setIsDefault(false);
        newAddress.setCreateTime(LocalDateTime.now());
        userAddressDao.insert(newAddress);
        return newAddress.getId();
    }

    private String buildFullAddress(UserAddressEntity address) {
        return address.getProvince() + address.getCity() + address.getDistrict() + address.getAddressLine();
    }
}
```

#### 3.2.3 VO和Form对象

```java
// OrderShippingInfoVO.java
@Data
public class OrderShippingInfoVO {
    
    @Schema(description = "订单ID")
    private Long orderId;
    
    @Schema(description = "订单号")
    private String orderSn;
    
    @Schema(description = "订单状态")
    private String orderStatus;
    
    @Schema(description = "是否可编辑")
    private Boolean canEdit;
    
    @Schema(description = "收货信息")
    private ShippingInfo shippingInfo;
    
    @Data
    public static class ShippingInfo {
        @Schema(description = "地址ID")
        private Long addressId;
        
        @Schema(description = "收件人姓名")
        private String recipientName;
        
        @Schema(description = "收件人电话")
        private String phoneNumber;
        
        @Schema(description = "省份")
        private String province;
        
        @Schema(description = "城市")
        private String city;
        
        @Schema(description = "区县")
        private String district;
        
        @Schema(description = "详细地址")
        private String addressLine;
        
        @Schema(description = "完整地址")
        private String fullAddress;
    }
}

// OrderShippingInfoUpdateForm.java
@Data
public class OrderShippingInfoUpdateForm {
    
    @NotNull(message = "编辑类型不能为空")
    @Schema(description = "编辑类型")
    private OrderShippingEditTypeEnum editType;
    
    @Schema(description = "地址ID（选择已有地址时必填）")
    private Long addressId;
    
    @Valid
    @Schema(description = "收货信息")
    private ShippingInfo shippingInfo;
    
    @Data
    public static class ShippingInfo {
        @NotBlank(message = "收件人姓名不能为空")
        @Length(max = 50, message = "收件人姓名长度不能超过50字符")
        @Schema(description = "收件人姓名")
        private String recipientName;
        
        @NotBlank(message = "收件人电话不能为空")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
        @Schema(description = "收件人电话")
        private String phoneNumber;
        
        @NotBlank(message = "省份不能为空")
        @Schema(description = "省份")
        private String province;
        
        @NotBlank(message = "城市不能为空")
        @Schema(description = "城市")
        private String city;
        
        @NotBlank(message = "区县不能为空")
        @Schema(description = "区县")
        private String district;
        
        @NotBlank(message = "详细地址不能为空")
        @Length(max = 200, message = "详细地址长度不能超过200字符")
        @Schema(description = "详细地址")
        private String addressLine;
    }
}

// ShippingEditableVO.java
@Data
public class ShippingEditableVO {
    
    @Schema(description = "是否可编辑")
    private Boolean canEdit;
    
    @Schema(description = "不可编辑原因")
    private String reason;
    
    @Schema(description = "允许修改的订单状态")
    private List<String> allowedStatuses;
    
    @Schema(description = "当前订单状态")
    private String currentStatus;
    
    @Schema(description = "修改截止时间")
    private LocalDateTime timeLimit;
}

// OrderShippingEditTypeEnum.java
@Getter
@AllArgsConstructor
public enum OrderShippingEditTypeEnum {
    
    MODIFY_EXISTING("modify_existing", "修改现有地址"),
    SELECT_EXISTING("select_existing", "选择已有地址"),
    CREATE_NEW("create_new", "创建新地址");

    private final String value;
    private final String desc;
}
```

## 4. 前端页面实现方案

### 4.1 页面结构设计

创建修改收货信息页面：`src/views/order/EditShippingInfo.vue`

### 4.2 前端页面实现

```vue
<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- Header导航模块 -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between p-4 h-14">
        <button @click="goBack" class="flex items-center justify-center w-6 h-6">
          <iconify-icon icon="material-symbols:arrow-back-ios" class="text-gray-800 text-xl"></iconify-icon>
        </button>
        <h1 class="text-sm font-semibold text-gray-800">修改收货信息</h1>
        <div class="flex items-center justify-center w-6 h-6"></div>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="text-gray-500 text-sm mt-2">正在加载...</p>
    </div>

    <!-- 主要内容区域 -->
    <main v-else class="px-4 py-6">
      <!-- 订单信息 -->
      <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-gray-600 text-sm">订单号</span>
          <span class="text-gray-800 text-sm font-medium">{{ orderInfo.orderSn }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-gray-600 text-sm">订单状态</span>
          <span class="text-blue-600 text-sm font-medium">{{ formatOrderStatus(orderInfo.orderStatus) }}</span>
        </div>
      </div>

      <!-- 提示信息 -->
      <div v-if="!canEdit" class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
        <div class="flex items-start">
          <iconify-icon icon="material-symbols:warning" class="text-orange-500 text-lg mr-3 mt-0.5"></iconify-icon>
          <div>
            <p class="text-orange-800 text-sm font-medium mb-1">无法修改收货信息</p>
            <p class="text-orange-700 text-xs">{{ editableInfo.reason }}</p>
          </div>
        </div>
      </div>

      <!-- 编辑类型选择 -->
      <div v-if="canEdit" class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <h3 class="text-base font-semibold text-gray-800 mb-3">修改方式</h3>
        <div class="space-y-3">
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="editType" 
              value="modify_existing" 
              class="mr-3"
              :disabled="!currentShippingInfo"
            >
            <span class="text-sm text-gray-700">修改当前收货地址</span>
          </label>
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="editType" 
              value="select_existing" 
              class="mr-3"
            >
            <span class="text-sm text-gray-700">选择其他收货地址</span>
          </label>
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="editType" 
              value="create_new" 
              class="mr-3"
            >
            <span class="text-sm text-gray-700">创建新收货地址</span>
          </label>
        </div>
      </div>

      <!-- 当前收货信息 -->
      <div v-if="currentShippingInfo" class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <h3 class="text-base font-semibold text-gray-800 mb-3">当前收货信息</h3>
        <div class="space-y-2 text-sm">
          <div class="flex">
            <span class="text-gray-600 w-16">收货人：</span>
            <span class="text-gray-800">{{ currentShippingInfo.recipientName }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-600 w-16">电话：</span>
            <span class="text-gray-800">{{ currentShippingInfo.phoneNumber }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-600 w-16">地址：</span>
            <span class="text-gray-800">{{ currentShippingInfo.fullAddress }}</span>
          </div>
        </div>
      </div>

      <!-- 修改现有地址表单 -->
      <div v-if="canEdit && editType === 'modify_existing'" class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <h3 class="text-base font-semibold text-gray-800 mb-4">修改收货信息</h3>
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">收货人姓名</label>
            <input
              type="text"
              v-model="form.recipientName"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入收货人姓名"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
            <input
              type="tel"
              v-model="form.phoneNumber"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入联系电话"
              required
            />
          </div>
          <div class="grid grid-cols-3 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">省份</label>
              <select
                v-model="form.province"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="province in provinces" :key="province" :value="province">{{ province }}</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">城市</label>
              <select
                v-model="form.city"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="city in cities" :key="city" :value="city">{{ city }}</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">区县</label>
              <select
                v-model="form.district"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="district in districts" :key="district" :value="district">{{ district }}</option>
              </select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">详细地址</label>
            <textarea
              v-model="form.addressLine"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入详细地址"
              required
            ></textarea>
          </div>
        </form>
      </div>

      <!-- 选择已有地址 -->
      <div v-if="canEdit && editType === 'select_existing'" class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <h3 class="text-base font-semibold text-gray-800 mb-4">选择收货地址</h3>
        <div v-if="userAddresses.length === 0" class="text-center py-8">
          <iconify-icon icon="material-symbols:location-off" class="text-gray-300 text-4xl mb-2"></iconify-icon>
          <p class="text-gray-500 text-sm">暂无保存的收货地址</p>
          <button @click="editType = 'create_new'" class="mt-3 text-blue-600 text-sm">创建新地址</button>
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="address in userAddresses"
            :key="address.id"
            class="border border-gray-200 rounded-lg p-3 cursor-pointer transition-colors"
            :class="selectedAddressId === address.id ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-300'"
            @click="selectedAddressId = address.id"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center mb-1">
                  <span class="font-medium text-gray-800 text-sm">{{ address.recipientName }}</span>
                  <span class="text-gray-600 text-sm ml-2">{{ address.phoneNumber }}</span>
                  <span v-if="address.isDefault" class="bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded ml-2">默认</span>
                </div>
                <p class="text-gray-600 text-sm">{{ address.fullAddress }}</p>
              </div>
              <div class="flex items-center ml-3">
                <input 
                  type="radio" 
                  :checked="selectedAddressId === address.id"
                  class="text-blue-600"
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建新地址表单 -->
      <div v-if="canEdit && editType === 'create_new'" class="bg-white rounded-lg p-4 shadow-sm mb-4">
        <h3 class="text-base font-semibold text-gray-800 mb-4">新建收货地址</h3>
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">收货人姓名</label>
            <input
              type="text"
              v-model="newAddressForm.recipientName"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入收货人姓名"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
            <input
              type="tel"
              v-model="newAddressForm.phoneNumber"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入联系电话"
              required
            />
          </div>
          <div class="grid grid-cols-3 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">省份</label>
              <select
                v-model="newAddressForm.province"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="province in provinces" :key="province" :value="province">{{ province }}</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">城市</label>
              <select
                v-model="newAddressForm.city"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="city in cities" :key="city" :value="city">{{ city }}</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">区县</label>
              <select
                v-model="newAddressForm.district"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择</option>
                <option v-for="district in districts" :key="district" :value="district">{{ district }}</option>
              </select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">详细地址</label>
            <textarea
              v-model="newAddressForm.addressLine"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入详细地址"
              required
            ></textarea>
          </div>
        </form>
      </div>

      <!-- 提交按钮 -->
      <div v-if="canEdit" class="pt-4">
        <button
          @click="handleSubmit"
          :disabled="isSubmitting || !isFormValid"
          class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSubmitting" class="flex items-center justify-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            提交中...
          </span>
          <span v-else>确认修改</span>
        </button>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError } from '@/utils/message'
import { StandardApiAdapter } from '@/api/standardAdapter'

export default {
  name: 'EditShippingInfo',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const authStore = useAuthStore()

    // 响应式数据
    const loading = ref(true)
    const isSubmitting = ref(false)
    const orderId = ref(route.params.orderId)
    
    // 订单信息
    const orderInfo = reactive({
      orderId: null,
      orderSn: '',
      orderStatus: ''
    })

    // 编辑权限信息
    const editableInfo = reactive({
      canEdit: false,
      reason: '',
      timeLimit: null
    })

    // 当前收货信息
    const currentShippingInfo = ref(null)
    
    // 编辑类型
    const editType = ref('modify_existing')
    
    // 用户地址列表
    const userAddresses = ref([])
    const selectedAddressId = ref(null)

    // 修改现有地址表单
    const form = reactive({
      recipientName: '',
      phoneNumber: '',
      province: '',
      city: '',
      district: '',
      addressLine: ''
    })

    // 新建地址表单
    const newAddressForm = reactive({
      recipientName: '',
      phoneNumber: '',
      province: '',
      city: '',
      district: '',
      addressLine: ''
    })

    // 地区数据
    const provinces = ref(['广东省', '北京市', '上海市', '浙江省', '江苏省']) // 实际项目中应该从API获取
    const cities = ref([])
    const districts = ref([])

    // API服务
    let apiService = null

    // 计算属性
    const canEdit = computed(() => editableInfo.canEdit)
    
    const isFormValid = computed(() => {
      if (editType.value === 'select_existing') {
        return selectedAddressId.value !== null
      } else if (editType.value === 'modify_existing') {
        return form.recipientName && form.phoneNumber && form.province && form.city && form.district && form.addressLine
      } else if (editType.value === 'create_new') {
        return newAddressForm.recipientName && newAddressForm.phoneNumber && newAddressForm.province && newAddressForm.city && newAddressForm.district && newAddressForm.addressLine
      }
      return false
    })

    // 方法
    const initApiService = async () => {
      try {
        apiService = new StandardApiAdapter()
        console.log('✅ API服务初始化成功')
      } catch (err) {
        console.error('❌ API服务初始化失败:', err)
      }
    }

    const loadOrderShippingInfo = async () => {
      try {
        loading.value = true
        
        if (!apiService) {
          await initApiService()
        }

        const response = await apiService.request('GET', `/order/${orderId.value}/shipping-info`)
        
        if (response.code === 0) {
          const data = response.data
          orderInfo.orderId = data.orderId
          orderInfo.orderSn = data.orderSn
          orderInfo.orderStatus = data.orderStatus
          editableInfo.canEdit = data.canEdit
          currentShippingInfo.value = data.shippingInfo

          // 如果当前有收货信息，填充到修改表单
          if (data.shippingInfo) {
            Object.assign(form, data.shippingInfo)
          }
        } else {
          throw new Error(response.msg || '获取订单信息失败')
        }
      } catch (error) {
        console.error('❌ 获取订单收货信息失败:', error)
        showError(error.message || '获取订单信息失败')
      } finally {
        loading.value = false
      }
    }

    const loadEditableInfo = async () => {
      if (!canEdit.value) {
        try {
          const response = await apiService.request('GET', `/order/${orderId.value}/shipping-editable`)
          if (response.code === 0) {
            Object.assign(editableInfo, response.data)
          }
        } catch (error) {
          console.error('❌ 获取编辑权限信息失败:', error)
        }
      }
    }

    const loadUserAddresses = async () => {
      try {
        const response = await apiService.request('GET', '/user/address/list')
        if (response.code === 0) {
          userAddresses.value = response.data.map(addr => ({
            ...addr,
            fullAddress: `${addr.province}${addr.city}${addr.district}${addr.addressLine}`
          }))
        }
      } catch (error) {
        console.error('❌ 获取用户地址列表失败:', error)
      }
    }

    const handleSubmit = async () => {
      if (!isFormValid.value) {
        showError('请完善表单信息')
        return
      }

      try {
        isSubmitting.value = true

        let requestData = {
          editType: editType.value
        }

        if (editType.value === 'select_existing') {
          requestData.addressId = selectedAddressId.value
        } else {
          const shippingInfo = editType.value === 'modify_existing' ? form : newAddressForm
          requestData.shippingInfo = { ...shippingInfo }
        }

        const response = await apiService.request('PUT', `/order/${orderId.value}/shipping-info`, requestData)

        if (response.code === 0) {
          showSuccess('收货信息修改成功')
          router.back()
        } else {
          throw new Error(response.msg || '修改失败')
        }
      } catch (error) {
        console.error('❌ 修改收货信息失败:', error)
        showError(error.message || '修改失败，请重试')
      } finally {
        isSubmitting.value = false
      }
    }

    const goBack = () => {
      router.back()
    }

    const formatOrderStatus = (status) => {
      const statusMap = {
        'paid': '已支付',
        'confirmed': '已确认',
        'shipped': '已发货',
        'completed': '已完成'
      }
      return statusMap[status] || status
    }

    // 监听省市变化
    watch(() => form.province, (newProvince) => {
      if (newProvince) {
        // 根据省份获取城市列表
        cities.value = ['深圳市', '广州市', '东莞市'] // 实际项目中应该从API获取
        form.city = ''
        form.district = ''
      }
    })

    watch(() => form.city, (newCity) => {
      if (newCity) {
        // 根据城市获取区县列表
        districts.value = ['南山区', '福田区', '罗湖区'] // 实际项目中应该从API获取
        form.district = ''
      }
    })

    // 新建地址表单的省市监听
    watch(() => newAddressForm.province, (newProvince) => {
      if (newProvince) {
        cities.value = ['深圳市', '广州市', '东莞市']
        newAddressForm.city = ''
        newAddressForm.district = ''
      }
    })

    watch(() => newAddressForm.city, (newCity) => {
      if (newCity) {
        districts.value = ['南山区', '福田区', '罗湖区']
        newAddressForm.district = ''
      }
    })

    // 监听编辑类型切换
    watch(editType, (newType) => {
      if (newType === 'select_existing' && userAddresses.value.length === 0) {
        loadUserAddresses()
      }
    })

    // 生命周期
    onMounted(async () => {
      await initApiService()
      await loadOrderShippingInfo()
      await loadEditableInfo()
    })

    return {
      loading,
      isSubmitting,
      orderInfo,
      editableInfo,
      canEdit,
      currentShippingInfo,
      editType,
      userAddresses,
      selectedAddressId,
      form,
      newAddressForm,
      provinces,
      cities,
      districts,
      isFormValid,
      handleSubmit,
      goBack,
      formatOrderStatus
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>
```

### 4.3 路由配置

在 `src/router/index.js` 中添加路由：

```javascript
{
  path: '/order/:orderId/edit-shipping',
  name: 'EditShippingInfo',
  component: () => import('@/views/order/EditShippingInfo.vue'),
  meta: {
    requiresAuth: true,
    title: '修改收货信息'
  }
}
```

### 4.4 API适配器扩展

在 `src/api/standardAdapter.js` 中添加相关API方法：

```javascript
// 获取订单收货信息
async getOrderShippingInfo(orderId) {
  return await this.request('GET', `/order/${orderId}/shipping-info`)
}

// 修改订单收货信息
async updateOrderShippingInfo(orderId, data) {
  return await this.request('PUT', `/order/${orderId}/shipping-info`, data)
}

// 检查订单收货信息是否可修改
async checkOrderShippingEditable(orderId) {
  return await this.request('GET', `/order/${orderId}/shipping-editable`)
}

// 获取用户地址列表
async getUserAddressList() {
  return await this.request('GET', '/user/address/list')
}

// 创建新地址并应用到订单
async createAndApplyAddress(orderId, data) {
  return await this.request('POST', `/order/${orderId}/create-and-apply-address`, data)
}
```

## 5. 业务规则和限制

### 5.1 修改权限控制

1. **订单状态限制**：
   - 只有 `paid`（已支付）和 `confirmed`（已确认）状态的订单可以修改
   - `shipped`（已发货）及以后状态不允许修改

2. **时间限制**：
   - 支付后24小时内可以修改收货信息
   - 超过时限后不允许修改

3. **用户权限验证**：
   - 只能修改自己的订单收货信息
   - 不能修改其他用户的订单

### 5.2 数据验证规则

1. **收件人姓名**：
   - 必填，长度不超过50字符
   - 不能包含特殊字符

2. **联系电话**：
   - 必填，必须是有效的手机号码格式
   - 正则验证：`^1[3-9]\d{9}$`

3. **地址信息**：
   - 省、市、区县必填
   - 详细地址必填，长度不超过200字符

### 5.3 操作日志记录

系统会记录以下操作日志：
- 用户查看订单收货信息
- 用户修改订单收货信息
- 修改操作的详细信息（原地址、新地址）
- 操作时间和IP地址

## 6. 错误处理

### 6.1 常见错误码

| 错误码 | 错误信息 | 处理方式 |
|--------|----------|----------|
| 40001 | 订单不存在 | 跳转到订单列表页面 |
| 40003 | 无权访问此订单 | 提示错误，返回上一页 |
| 40005 | 订单状态不允许修改 | 显示不可编辑原因 |
| 40006 | 已超过修改时限 | 显示时限说明 |
| 40010 | 地址信息验证失败 | 显示具体验证错误 |

### 6.2 前端错误处理

```javascript
// 统一错误处理
const handleApiError = (error) => {
  const errorMap = {
    40001: '订单不存在',
    40003: '无权访问此订单',
    40005: '当前订单状态不允许修改收货信息',
    40006: '已超过修改时限',
    40010: '地址信息格式不正确'
  }
  
  const message = errorMap[error.code] || error.message || '操作失败'
  showError(message)
  
  // 特殊错误处理
  if (error.code === 40001) {
    router.push('/order/list')
  }
}
```

## 7. 测试方案

### 7.1 单元测试

1. **Service层测试**：
   - 测试修改权限验证逻辑
   - 测试不同编辑类型的处理逻辑
   - 测试数据验证规则

2. **Controller层测试**：
   - 测试API接口的请求参数验证
   - 测试返回数据格式
   - 测试权限验证

### 7.2 集成测试

1. **API接口测试**：
   - 获取订单收货信息接口
   - 修改订单收货信息接口
   - 权限检查接口

2. **业务流程测试**：
   - 完整的修改收货信息流程
   - 不同订单状态下的权限控制
   - 时间限制验证

### 7.3 前端测试

1. **组件测试**：
   - 表单验证功能
   - 编辑类型切换
   - 地址选择功能

2. **用户体验测试**：
   - 页面加载性能
   - 交互响应速度
   - 错误提示友好性

## 8. 部署清单

### 8.1 后端部署

- [ ] 数据库表结构已存在（现有系统）
- [ ] 新增API接口代码部署
- [ ] 配置文件更新（如有需要）
- [ ] 权限配置更新
- [ ] API接口测试通过

### 8.2 前端部署

- [ ] 新增页面组件部署
- [ ] 路由配置更新
- [ ] API适配器更新
- [ ] 页面功能测试通过
- [ ] 用户体验测试通过

## 9. 总结

本方案基于现有订单系统设计了完整的修改收货信息功能，包括：

1. **完整的API接口设计**：涵盖获取、修改、权限检查等所有功能
2. **灵活的编辑方式**：支持修改现有地址、选择已有地址、创建新地址三种方式
3. **严格的权限控制**：确保只有符合条件的订单才能修改收货信息
4. **完善的前端交互**：提供友好的用户界面和交互体验
5. **全面的错误处理**：覆盖各种异常情况的处理方案

该方案可以直接应用到生产环境，满足用户修改订单收货信息的业务需求，同时保证系统的安全性和稳定性。