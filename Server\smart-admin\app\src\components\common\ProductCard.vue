<template>
  <div class="product-card" @click="$emit('click')">
    <!-- 商品图片 -->
    <div class="product-image-wrapper">
      <img
        :src="getImageUrl(product.image || product.imageUrl || product.mainImage || product.thumbnail)"
        :alt="product.goodsName || product.name || product.title"
        class="product-image"
        @load="onImageLoad"
        @error="onImageError"
      />
      <!-- 拼团人数徽章 -->
      <div class="group-size-badge">
        {{ getGroupSize(product) }}人团
      </div>
    </div>

    <!-- 商品信息区域 -->
    <div class="product-info">
      <!-- 商品标题 -->
      <h3 class="product-title">
        {{ product.goodsName || product.name || product.title || '商品名称' }}
      </h3>
      
      <!-- 促销信息标签 -->
      <div class="promotion-tags">
        <!-- 中奖概率 -->
        <div class="probability-tag">
          抢中概率{{ getProbabilityText(product) }}，送{{ getRewardPoints(product) }}积分
        </div>
        
        <!-- 保底机制 -->
        <div class="guarantee-tag">
          不中定金退回，送{{ getCashbackAmount(product) }}元红包
        </div>
      </div>
      
      <!-- 双按钮操作区域 -->
      <div class="action-buttons-row">
        <button class="direct-buy-button" @click.stop="handleDirectBuy">
          直接购买
          <span class="button-price">¥{{ formatPrice(getOriginalPrice(product)) }}</span>
        </button>
        
        <button class="group-buy-button" @click.stop="handleGroupBuy">
          拼团
          <span class="button-price">¥{{ formatPrice(getDisplayPrice(product)) }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click', 'directBuy', 'groupBuy'])

// 图片加载成功
const onImageLoad = (event) => {
  console.log('图片加载成功:', event.target.src)
}

// 图片加载失败
const onImageError = (event) => {
  console.log('图片加载失败:', event.target.src)
  // 设置默认图片
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTMwQzEwNS41MjMgMTMwIDExMCAxMjUuNTIzIDExMCAxMjBDMTEwIDExNC40NzcgMTA1LjUyMyAxMTAgMTAwIDExMEM5NC40NzcgMTEwIDkwIDExNC40NzcgOTAgMTIwQzkwIDEyNS41MjMgOTQuNDc3IDEzMCAxMDAgMTMwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDcwSDMwVjE3MEgxNzBWNzBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTUwIDEwMEwxMzAgMTIwTDEwMCA5MEw3MCA5MEw1MCA5MEw3MCA3MEwxMDAgNzBMMTMwIDcwTDE1MCA3MEwxNzAgNzBWMTcwSDMwVjcwSDUwTDcwIDkwTDEwMCA5MEwxMzAgMTIwTDE1MCA5MEwxNzAgNzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0'
  const num = Number(price)
  if (num >= 10000) {
    return Math.floor(num).toString()
  }
  return num.toFixed(0)
}

// 获取图片URL
const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTMwQzEwNS41MjMgMTMwIDExMCAxMjUuNTIzIDExMCAxMjBDMTEwIDExNC40NzcgMTA1LjUyMyAxMTAgMTAwIDExMEM5NC40NzcgMTEwIDkwIDExNC40NzcgOTAgMTIwQzkwIDEyNS41MjMgOTQuNDc3IDEzMCAxMDAgMTMwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDcwSDMwVjE3MEgxNzBWNzBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTUwIDEwMEwxMzAgMTIwTDEwMCA5MEw3MCA5MEw1MCA5MEw3MCA3MEwxMDAgNzBMMTMwIDcwTDE1MCA3MEwxNzAgNzBWMTcwSDMwVjcwSDUwTDcwIDkwTDEwMCA5MEwxMzAgMTIwTDE1MCA5MEwxNzAgNzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
  }
  
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  let cleanPath = imagePath.trim()
  if (!cleanPath.startsWith('/')) {
    cleanPath = '/' + cleanPath
  }
  
  return `http://localhost${cleanPath}`
}

// 获取显示价格（拼团价格）
const getDisplayPrice = (product) => {
  return product.price || product.salePrice || product.currentPrice || product.participationPrice || 0
}

// 获取原价（直接购买价格）
const getOriginalPrice = (product) => {
  return product.originalPrice || product.original_price || product.marketPrice || product.directPrice || (getDisplayPrice(product) * 1.5) || 0
}

// 获取中奖概率文本
const getProbabilityText = (product) => {
  // 根据商品类型或活动类型返回概率
  const activityType = product.activityType || product.activity_name || product.groupType
  
  if (activityType && activityType.includes('3')) {
    return '1/3'
  } else if (activityType && activityType.includes('10')) {
    return '1/10'
  } else if (activityType && activityType.includes('2')) {
    return '1/2'
  }
  
  // 默认概率
  return '1/3'
}

// 获取奖励积分
const getRewardPoints = (product) => {
  const price = getDisplayPrice(product)
  // 根据价格计算积分奖励，通常是价格的20%左右
  return Math.floor(price * 0.2) || 55
}

// 获取返现金额
const getCashbackAmount = (product) => {
  const price = getDisplayPrice(product)
  // 根据价格计算返现金额，通常是价格的10-20%
  return (price * 0.15).toFixed(2) || '5.57'
}

// 获取拼团人数
const getGroupSize = (product) => {
  // 优先从产品数据中获取
  if (product.groupSize) return product.groupSize
  if (product.group_size) return product.group_size
  if (product.participantLimit) return product.participantLimit
  
  // 从活动ID推断
  if (product.activityId === 3) return 3
  if (product.activityId === 5) return 10
  
  // 从活动类型推断
  const activityType = product.activityType || product.activity_name || product.groupType
  if (activityType && activityType.includes('3')) return 3
  if (activityType && activityType.includes('10')) return 10
  if (activityType && activityType.includes('2')) return 2
  
  // 默认3人团
  return 3
}

// 移除了不再需要的 getGroupSizeClass 函数，因为所有徽章都使用相同颜色

// 处理直接购买点击
const handleDirectBuy = (event) => {
  event.stopPropagation()
  console.log('直接购买:', props.product)
  // 触发直接购买事件
  emit('directBuy', props.product)
}

// 处理拼团购买点击 - 修复缓存问题
const handleGroupBuy = (event) => {
  event.stopPropagation()
  console.log('拼团购买:', props.product)
  // 触发拼团购买事件
  emit('groupBuy', props.product)
}
</script>

<style lang="scss" scoped>
.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .product-image-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
    flex-shrink: 0;
    overflow: hidden;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 12px;

    .product-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      border: none;
      outline: none;
      border-radius: 8px;
    }

    .group-size-badge {
      position: absolute;
      bottom: 5px;
      left: 6px;
      padding: 3px 7px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: bold;
      color: white;
      z-index: 10;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      background: linear-gradient(135deg, #FF4757 0%, #E91E63 100%);
    }
  }

  .product-info {
    padding: 16px 16px 16px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .product-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .promotion-tags {
      margin-bottom: 12px;

      .probability-tag {
        display: inline-block;
        padding: 3px 6px;
        border: 1px solid #ff4444;
        border-radius: 10px;
        font-size: 11px;
        color: #ff4444;
        background: #fff8f8;
        margin-bottom: 5px;
        margin-right: 6px;
      }

      .guarantee-tag {
        display: inline-block;
        padding: 3px 6px;
        border: 1px solid #ff6b35;
        border-radius: 10px;
        font-size: 11px;
        color: #ff6b35;
        background: #fff9f7;
      }
    }

    .action-buttons-row {
      display: flex;
      gap: 8px;
      margin-top: 8px;

      .direct-buy-button {
        flex: 1;
        padding: 6px 8px;
        background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 36px;
        line-height: 1.1;

        .button-price {
          font-size: 14px;
          font-weight: bold;
          margin-top: 1px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .group-buy-button {
        flex: 1;
        padding: 6px 8px;
        background: linear-gradient(135deg, #ff4444 0%, #e73c3c 100%);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 36px;
        line-height: 1.1;

        .button-price {
          font-size: 14px;
          font-weight: bold;
          margin-top: 1px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .product-card {
    .product-image-wrapper {
      width: 130px;
      height: 130px;
      margin: 10px;
    }

    .product-info {
      padding: 10px 10px 10px 0;

      .product-title {
        font-size: 15px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .promotion-tags {
        margin-bottom: 8px;

        .probability-tag,
        .guarantee-tag {
          font-size: 10px;
          padding: 2px 5px;
          margin-bottom: 3px;
        }
      }

      .action-buttons-row {
        gap: 6px;
        margin-top: 6px;

        .direct-buy-button,
        .group-buy-button {
          padding: 4px 6px;
          font-size: 12px;
          min-height: 32px;
          border-radius: 12px;
          line-height: 1.0;

          .button-price {
            font-size: 12px;
            margin-top: 0.5px;
          }
        }
      }
    }
  }
}
</style> 