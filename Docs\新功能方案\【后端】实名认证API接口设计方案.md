# 实名认证API接口设计方案

## 概述

本文档详细描述了实名认证系统的API接口设计方案，包括前端页面功能分析、API接口规范、数据库设计以及基于Smart-Admin框架的具体实施方案。

## 1. 前端功能分析

基于 `https://pp.oripicks.com/user/identity-verification` 页面分析，实名认证系统包含以下功能：

### 1.1 核心功能
- 身份证信息录入（真实姓名、身份证号码）
- 身份证照片上传（正面、反面）
- 银行卡信息录入（开户姓名、开户行、银行卡号）
- 表单验证和提交
- 认证状态查询

### 1.2 前端数据结构
```javascript
form: {
  realName: '',              // 真实姓名
  idNumber: '',              // 身份证号码
  idFrontImage: null,        // 身份证正面照片
  idBackImage: null,         // 身份证反面照片
  bankAccountName: '',       // 开户姓名
  bankName: '',              // 开户行
  bankAccountNumber: ''      // 银行卡号
}
```

## 2. API接口设计

### 2.1 接口列表

| 接口名称 | 方法 | 路径 | 描述 |
|---------|------|------|------|
| 提交实名认证 | POST | `/api/v1/user/identity-verification` | 提交实名认证资料 |
| 获取认证状态 | GET | `/api/v1/user/identity-verification/status` | 获取用户认证状态 |
| 获取认证信息 | GET | `/api/v1/user/identity-verification` | 获取用户认证详情 |
| 检查认证状态 | GET | `/api/v1/user/identity-verification/check` | 检查是否已认证 |
| 管理员审核 | PUT | `/api/v1/admin/identity-verification/{id}/audit` | 管理员审核认证 |
| 管理员列表 | GET | `/api/v1/admin/identity-verification` | 管理员获取认证列表 |
| 上传图片 | POST | `/api/v1/upload/identity` | 上传身份证图片 |

### 2.2 详细接口规范

#### 2.2.1 提交实名认证
```http
POST /api/v1/user/identity-verification
Content-Type: multipart/form-data
Authorization: Bearer {token}

参数:
- realName: string (必填) - 真实姓名
- idNumber: string (必填) - 身份证号码
- idFrontImage: file (必填) - 身份证正面照片
- idBackImage: file (必填) - 身份证反面照片
- bankAccountName: string (必填) - 开户姓名
- bankName: string (必填) - 开户行
- bankAccountNumber: string (必填) - 银行卡号

响应:
{
  "code": 0,
  "msg": "提交成功",
  "ok": true,
  "data": {
    "id": 12345,
    "status": "pending",
    "submitTime": "2025-01-27T10:30:00Z"
  }
}
```

#### 2.2.2 获取认证状态
```http
GET /api/v1/user/identity-verification/status
Authorization: Bearer {token}

响应:
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "isVerified": false,
    "status": "pending", // pending/approved/rejected/not_submitted
    "submitTime": "2025-01-27T10:30:00Z",
    "auditTime": null,
    "rejectReason": null
  }
}
```

#### 2.2.3 获取认证信息
```http
GET /api/v1/user/identity-verification
Authorization: Bearer {token}

响应:
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 12345,
    "realName": "张**",
    "idNumber": "110101199001011234",
    "bankAccountName": "张**",
    "bankName": "中国工商银行",
    "bankAccountNumber": "6222080200001234567",
    "status": "approved",
    "submitTime": "2025-01-27T10:30:00Z",
    "auditTime": "2025-01-27T15:30:00Z",
    "auditBy": "admin001",
    "rejectReason": null
  }
}
```

#### 2.2.4 管理员审核接口
```http
PUT /api/v1/admin/identity-verification/{id}/audit
Authorization: Bearer {admin_token}

请求体:
{
  "action": "approve", // approve/reject
  "rejectReason": "身份证照片不清晰" // action为reject时必填
}

响应:
{
  "code": 0,
  "msg": "审核完成",
  "ok": true,
  "data": {
    "id": 12345,
    "status": "approved",
    "auditTime": "2025-01-27T15:30:00Z"
  }
}
```

## 3. 数据库设计

### 3.1 实名认证表 (t_identity_verification)

```sql
CREATE TABLE `t_identity_verification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_number` varchar(18) NOT NULL COMMENT '身份证号码',
  `id_front_image` varchar(255) NOT NULL COMMENT '身份证正面照片URL',
  `id_back_image` varchar(255) NOT NULL COMMENT '身份证反面照片URL',
  `bank_account_name` varchar(50) NOT NULL COMMENT '银行卡开户姓名',
  `bank_name` varchar(100) NOT NULL COMMENT '开户行名称',
  `bank_account_number` varchar(30) NOT NULL COMMENT '银行卡号',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态:pending待审核,approved已通过,rejected已拒绝',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `audit_time` datetime NULL COMMENT '审核时间',
  `audit_by` varchar(50) NULL COMMENT '审核人员',
  `reject_reason` varchar(500) NULL COMMENT '拒绝原因',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标识:0未删除,1已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`) COMMENT '用户唯一认证',
  UNIQUE KEY `uk_id_number` (`id_number`) COMMENT '身份证号唯一',
  UNIQUE KEY `uk_bank_account` (`bank_account_number`) COMMENT '银行卡号唯一',
  KEY `idx_status` (`status`),
  KEY `idx_submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实名认证表';
```

### 3.2 审核日志表 (t_identity_verification_log)

```sql
CREATE TABLE `t_identity_verification_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `verification_id` bigint(20) NOT NULL COMMENT '认证记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `action` varchar(20) NOT NULL COMMENT '操作类型:submit提交,approve通过,reject拒绝',
  `operator` varchar(50) NULL COMMENT '操作人员',
  `operator_type` varchar(20) NOT NULL DEFAULT 'user' COMMENT '操作人类型:user用户,admin管理员',
  `remark` varchar(500) NULL COMMENT '操作备注',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_verification_id` (`verification_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实名认证审核日志表';
```

## 4. Smart-Admin框架实施方案

### 4.1 目录结构

```
smart-admin/admin-api/sa-admin/src/main/java/net/lab1024/sa/admin/
├── module/
│   └── business/
│       └── identity/
│           ├── controller/
│           │   ├── IdentityVerificationController.java
│           │   └── AdminIdentityVerificationController.java
│           ├── service/
│           │   ├── IdentityVerificationService.java
│           │   └── IdentityVerificationLogService.java
│           ├── dao/
│           │   ├── IdentityVerificationDao.java
│           │   └── IdentityVerificationLogDao.java
│           ├── domain/
│           │   ├── entity/
│           │   │   ├── IdentityVerificationEntity.java
│           │   │   └── IdentityVerificationLogEntity.java
│           │   ├── form/
│           │   │   ├── IdentityVerificationAddForm.java
│           │   │   ├── IdentityVerificationQueryForm.java
│           │   │   └── IdentityVerificationAuditForm.java
│           │   └── vo/
│           │       ├── IdentityVerificationVO.java
│           │       ├── IdentityVerificationStatusVO.java
│           │       └── IdentityVerificationListVO.java
│           └── enums/
│               └── IdentityVerificationStatusEnum.java
```

### 4.2 核心实体类

#### 4.2.1 IdentityVerificationEntity.java
```java
@Data
@TableName("t_identity_verification")
public class IdentityVerificationEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号码")
    private String idNumber;

    @Schema(description = "身份证正面照片URL")
    private String idFrontImage;

    @Schema(description = "身份证反面照片URL")
    private String idBackImage;

    @Schema(description = "银行卡开户姓名")
    private String bankAccountName;

    @Schema(description = "开户行名称")
    private String bankName;

    @Schema(description = "银行卡号")
    private String bankAccountNumber;

    @Schema(description = "审核状态")
    private String status;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人员")
    private String auditBy;

    @Schema(description = "拒绝原因")
    private String rejectReason;
}
```

#### 4.2.2 IdentityVerificationStatusEnum.java
```java
@Getter
@AllArgsConstructor
public enum IdentityVerificationStatusEnum {
    
    NOT_SUBMITTED("not_submitted", "未提交"),
    PENDING("pending", "待审核"),
    APPROVED("approved", "已通过"),
    REJECTED("rejected", "已拒绝");

    private final String value;
    private final String desc;
    
    public static IdentityVerificationStatusEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
```

### 4.3 核心Controller

#### 4.3.1 用户端Controller
```java
@RestController
@RequestMapping("/api/v1/user/identity-verification")
@Tag(name = "实名认证", description = "用户实名认证相关接口")
@Slf4j
public class IdentityVerificationController {

    @Resource
    private IdentityVerificationService identityVerificationService;

    @PostMapping
    @Operation(summary = "提交实名认证")
    public ResponseDTO<IdentityVerificationVO> submitVerification(
            @ModelAttribute IdentityVerificationAddForm form,
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(identityVerificationService.submit(form, requestEmployee.getEmployeeId()));
    }

    @GetMapping("/status")
    @Operation(summary = "获取认证状态")
    public ResponseDTO<IdentityVerificationStatusVO> getStatus(
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(identityVerificationService.getStatus(requestEmployee.getEmployeeId()));
    }

    @GetMapping
    @Operation(summary = "获取认证信息")
    public ResponseDTO<IdentityVerificationVO> getVerification(
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(identityVerificationService.getByUserId(requestEmployee.getEmployeeId()));
    }

    @GetMapping("/check")
    @Operation(summary = "检查是否已认证")
    public ResponseDTO<Boolean> checkVerification(
            @RequestAttribute RequestEmployee requestEmployee) {
        return ResponseDTO.ok(identityVerificationService.isVerified(requestEmployee.getEmployeeId()));
    }
}
```

#### 4.3.2 管理端Controller
```java
@RestController
@RequestMapping("/api/v1/admin/identity-verification")
@Tag(name = "实名认证管理", description = "管理员实名认证审核接口")
@Slf4j
public class AdminIdentityVerificationController {

    @Resource
    private IdentityVerificationService identityVerificationService;

    @GetMapping
    @Operation(summary = "获取认证列表")
    @SaCheckPermission("identity:verification:query")
    public ResponseDTO<PageResult<IdentityVerificationListVO>> queryPage(
            @Valid IdentityVerificationQueryForm queryForm) {
        return ResponseDTO.ok(identityVerificationService.queryPage(queryForm));
    }

    @PutMapping("/{id}/audit")
    @Operation(summary = "审核实名认证")
    @SaCheckPermission("identity:verification:audit")
    public ResponseDTO<Void> audit(
            @PathVariable Long id,
            @Valid @RequestBody IdentityVerificationAuditForm auditForm,
            @RequestAttribute RequestEmployee requestEmployee) {
        identityVerificationService.audit(id, auditForm, requestEmployee.getActualName());
        return ResponseDTO.ok();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取认证详情")
    @SaCheckPermission("identity:verification:detail")
    public ResponseDTO<IdentityVerificationVO> getDetail(@PathVariable Long id) {
        return ResponseDTO.ok(identityVerificationService.getById(id));
    }
}
```

### 4.4 核心Service

#### 4.4.1 IdentityVerificationService.java
```java
@Service
@Slf4j
public class IdentityVerificationService {

    @Resource
    private IdentityVerificationDao identityVerificationDao;
    
    @Resource
    private IdentityVerificationLogService logService;
    
    @Resource
    private FileService fileService;

    @Transactional(rollbackFor = Exception.class)
    public IdentityVerificationVO submit(IdentityVerificationAddForm form, Long userId) {
        // 检查是否已提交过
        IdentityVerificationEntity existing = identityVerificationDao.getByUserId(userId);
        if (existing != null && !IdentityVerificationStatusEnum.REJECTED.getValue().equals(existing.getStatus())) {
            throw new BusinessException("您已提交过实名认证，请勿重复提交");
        }

        // 验证身份证号格式
        if (!IdCardUtil.isValidCard(form.getIdNumber())) {
            throw new BusinessException("身份证号格式不正确");
        }

        // 验证银行卡号
        if (!BankCardUtil.isValid(form.getBankAccountNumber())) {
            throw new BusinessException("银行卡号格式不正确");
        }

        // 上传图片文件
        String idFrontImageUrl = fileService.uploadFile(form.getIdFrontImage(), "identity");
        String idBackImageUrl = fileService.uploadFile(form.getIdBackImage(), "identity");

        // 创建认证记录
        IdentityVerificationEntity entity = BeanUtil.copyProperties(form, IdentityVerificationEntity.class);
        entity.setUserId(userId);
        entity.setIdFrontImage(idFrontImageUrl);
        entity.setIdBackImage(idBackImageUrl);
        entity.setStatus(IdentityVerificationStatusEnum.PENDING.getValue());
        entity.setSubmitTime(LocalDateTime.now());

        if (existing != null) {
            // 更新已有记录
            entity.setId(existing.getId());
            identityVerificationDao.updateById(entity);
        } else {
            // 新增记录
            identityVerificationDao.insert(entity);
        }

        // 记录日志
        logService.recordLog(entity.getId(), userId, "submit", null, "user", "用户提交实名认证");

        return BeanUtil.copyProperties(entity, IdentityVerificationVO.class);
    }

    public IdentityVerificationStatusVO getStatus(Long userId) {
        IdentityVerificationEntity entity = identityVerificationDao.getByUserId(userId);
        
        IdentityVerificationStatusVO statusVO = new IdentityVerificationStatusVO();
        if (entity == null) {
            statusVO.setIsVerified(false);
            statusVO.setStatus(IdentityVerificationStatusEnum.NOT_SUBMITTED.getValue());
        } else {
            statusVO.setIsVerified(IdentityVerificationStatusEnum.APPROVED.getValue().equals(entity.getStatus()));
            statusVO.setStatus(entity.getStatus());
            statusVO.setSubmitTime(entity.getSubmitTime());
            statusVO.setAuditTime(entity.getAuditTime());
            statusVO.setRejectReason(entity.getRejectReason());
        }
        
        return statusVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void audit(Long id, IdentityVerificationAuditForm auditForm, String auditBy) {
        IdentityVerificationEntity entity = identityVerificationDao.selectById(id);
        if (entity == null) {
            throw new BusinessException("认证记录不存在");
        }

        if (!IdentityVerificationStatusEnum.PENDING.getValue().equals(entity.getStatus())) {
            throw new BusinessException("该记录已审核过，无法重复审核");
        }

        // 更新审核状态
        entity.setStatus(auditForm.getAction().equals("approve") ? 
            IdentityVerificationStatusEnum.APPROVED.getValue() : 
            IdentityVerificationStatusEnum.REJECTED.getValue());
        entity.setAuditTime(LocalDateTime.now());
        entity.setAuditBy(auditBy);
        entity.setRejectReason(auditForm.getRejectReason());

        identityVerificationDao.updateById(entity);

        // 记录审核日志
        logService.recordLog(id, entity.getUserId(), auditForm.getAction(), auditBy, "admin", 
            auditForm.getAction().equals("approve") ? "管理员审核通过" : "管理员审核拒绝: " + auditForm.getRejectReason());
    }

    public PageResult<IdentityVerificationListVO> queryPage(IdentityVerificationQueryForm queryForm) {
        Page<IdentityVerificationEntity> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<IdentityVerificationEntity> list = identityVerificationDao.queryPage(page, queryForm);
        PageResult<IdentityVerificationListVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<IdentityVerificationListVO> voList = pageResult.getList().stream()
                .map(entity -> {
                    IdentityVerificationListVO vo = BeanUtil.copyProperties(entity, IdentityVerificationListVO.class);
                    // 脱敏处理
                    vo.setRealName(desensitizeName(entity.getRealName()));
                    vo.setIdNumber(desensitizeIdNumber(entity.getIdNumber()));
                    vo.setBankAccountNumber(desensitizeBankCard(entity.getBankAccountNumber()));
                    return vo;
                })
                .collect(Collectors.toList());
            pageResult.setList(voList);
        }
        
        return pageResult;
    }

    public Boolean isVerified(Long userId) {
        IdentityVerificationEntity entity = identityVerificationDao.getByUserId(userId);
        return entity != null && IdentityVerificationStatusEnum.APPROVED.getValue().equals(entity.getStatus());
    }

    // 脱敏方法
    private String desensitizeName(String name) {
        if (StringUtil.isBlank(name) || name.length() <= 1) {
            return name;
        }
        return name.charAt(0) + "*".repeat(name.length() - 1);
    }

    private String desensitizeIdNumber(String idNumber) {
        if (StringUtil.isBlank(idNumber) || idNumber.length() < 8) {
            return idNumber;
        }
        return idNumber.substring(0, 4) + "**********" + idNumber.substring(idNumber.length() - 4);
    }

    private String desensitizeBankCard(String bankCard) {
        if (StringUtil.isBlank(bankCard) || bankCard.length() < 8) {
            return bankCard;
        }
        return bankCard.substring(0, 4) + "****" + bankCard.substring(bankCard.length() - 4);
    }
}
```

### 4.5 核心DAO

#### 4.5.1 IdentityVerificationDao.java
```java
@Mapper
public interface IdentityVerificationDao extends BaseMapper<IdentityVerificationEntity> {

    /**
     * 根据用户ID获取认证信息
     */
    @Select("SELECT * FROM t_identity_verification WHERE user_id = #{userId} AND deleted_flag = 0")
    IdentityVerificationEntity getByUserId(@Param("userId") Long userId);

    /**
     * 分页查询认证列表
     */
    List<IdentityVerificationEntity> queryPage(Page<IdentityVerificationEntity> page, 
                                              @Param("queryForm") IdentityVerificationQueryForm queryForm);

    /**
     * 根据身份证号查询
     */
    @Select("SELECT * FROM t_identity_verification WHERE id_number = #{idNumber} AND deleted_flag = 0")
    IdentityVerificationEntity getByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据银行卡号查询
     */
    @Select("SELECT * FROM t_identity_verification WHERE bank_account_number = #{bankAccountNumber} AND deleted_flag = 0")
    IdentityVerificationEntity getByBankAccountNumber(@Param("bankAccountNumber") String bankAccountNumber);
}
```

### 4.6 配置文件

#### 4.6.1 application.yml 新增配置
```yaml
# 实名认证配置
identity:
  verification:
    # 上传文件配置
    upload:
      max-size: 5MB
      allowed-types: jpg,jpeg,png
      save-path: /uploads/identity/
    # 审核配置
    audit:
      auto-approve: false
      require-admin-role: identity:admin
```

### 4.7 权限配置

#### 4.7.1 菜单权限SQL
```sql
-- 实名认证管理菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, path, component, perms, menu_status, remark, create_time, update_time) 
VALUES ('实名认证管理', 1, (SELECT id FROM t_menu WHERE menu_name = '业务管理'), 7, 'identity-verification', 'business/identity-verification/index', 'identity:verification:list', 1, '实名认证审核管理', NOW(), NOW());

-- 实名认证权限点
INSERT INTO t_menu (menu_name, menu_type, parent_id, sort, perms, menu_status, create_time, update_time) 
VALUES 
('查看', 2, (SELECT id FROM t_menu WHERE menu_name = '实名认证管理'), 1, 'identity:verification:query', 1, NOW(), NOW()),
('审核', 2, (SELECT id FROM t_menu WHERE menu_name = '实名认证管理'), 2, 'identity:verification:audit', 1, NOW(), NOW()),
('详情', 2, (SELECT id FROM t_menu WHERE menu_name = '实名认证管理'), 3, 'identity:verification:detail', 1, NOW(), NOW());
```

## 5. 前端适配建议

### 5.1 API调用示例
```javascript
// 提交实名认证
async submitIdentityVerification(formData) {
  return await this.request('POST', '/user/identity-verification', formData, {
    'Content-Type': 'multipart/form-data'
  })
}

// 获取认证状态
async getIdentityVerificationStatus() {
  return await this.request('GET', '/user/identity-verification/status')
}
```

### 5.2 错误处理
```javascript
try {
  const response = await verificationApi.submitIdentityVerification(formData)
  if (response.code === 0) {
    showSuccess('提交成功，请等待审核')
  }
} catch (error) {
  if (error.message.includes('重复提交')) {
    showError('您已提交过实名认证')
  } else if (error.message.includes('身份证号格式')) {
    showError('身份证号格式不正确')
  } else {
    showError('提交失败，请重试')
  }
}
```

## 6. 安全考虑

### 6.1 数据脱敏
- 姓名：显示首字符+星号
- 身份证号：显示前4位和后4位，中间用星号替换
- 银行卡号：显示前4位和后4位，中间用星号替换

### 6.2 权限控制
- 用户只能查看和提交自己的认证信息
- 管理员才能进行审核操作
- 图片文件上传需要验证文件类型和大小

### 6.3 审计日志
- 记录所有关键操作的日志
- 包括提交、审核通过、审核拒绝等操作
- 记录操作人员和操作时间

## 7. 部署和测试

### 7.1 部署清单
- [ ] 执行数据库建表SQL
- [ ] 配置文件上传路径
- [ ] 配置权限菜单
- [ ] 部署后端代码
- [ ] 测试API接口

### 7.2 测试用例
- [ ] 正常提交实名认证流程
- [ ] 重复提交验证
- [ ] 图片上传功能
- [ ] 管理员审核功能
- [ ] 数据脱敏验证
- [ ] 权限控制验证

## 8. 总结

本方案基于Smart-Admin框架设计了完整的实名认证系统，包括：

1. **完整的API接口设计**：覆盖用户端和管理端所有功能
2. **标准的数据库设计**：支持认证信息存储和审核日志
3. **完善的权限控制**：确保数据安全和操作规范
4. **详细的实施方案**：基于Smart-Admin框架的具体代码实现
5. **安全性考虑**：数据脱敏、权限控制、审计日志等

该方案可以直接用于生产环境，满足实名认证业务的所有需求。