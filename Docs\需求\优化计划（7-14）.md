# 优化计划（7-14）

根据需求文档 `优化需求（7-14）.md`，我们将项目优化分为以下几个主要模块。每个模块详细说明了前端、后端以及API接口需要进行的改动。

## 一、新用户激励机制

### 需求描述

新注册用户将获得一次免费的、必不中但有现金奖励的拼团机会。

### 改造点

- **后端**:
  - 用户表增加 `freeGroupBuyChances` 字段，默认为1。
  - 增加后台配置项，用于设置“未中奖”时的固定现金奖励金额。
  - 修改下单逻辑：允许在 `freeGroupBuyChances > 0` 的情况下零元下单。
  - 修改拼团开奖逻辑：对于使用免费机会的用户，强制设置为“未中奖”，并发放配置好的现金奖励。
- **前端**:
  - 在相关页面（如个人中心或活动弹窗）提示用户拥有免费拼团机会。
  - 下单页面进行适配，允许在有免费机会时直接下单。
- **API**:
  - `POST /api/orders` (拼团下单): 需要处理免费下单的逻辑。
  - `GET /api/user/profile`: 用户信息接口需返回 `freeGroupBuyChances` 字段。

---

## 二、首页界面与布局优化

### 需求描述

简化首页布局，调整商品展示方式，更新导航和分类。

### 改造点

- **前端**:
  1. **弹窗**: 暂时隐藏首页弹窗，但保留代码。 (`HomePage.vue`)
  2. **商品分类**: 将分类按钮硬编码修改为“全部”、“手机数码”、“奢饰品”。 (`HomePage.vue`)
  3. **拼团类型**: 移除拼团类型选择功能。 (`HomePage.vue`)
  4. **导航栏**: 移除“活动”和“购物车”按钮，只保留“首页”、“订单”、“我的”。 (需要找到对应的 `TabBar` 组件进行修改)
  5. **商品列表**: 布局从双列改为单列，调整商品卡片组件样式使其更宽。 (需要修改 `ProductCard.vue` 和 `HomePage.vue`)
- **后端**:
  1. **Banner**: 需要提供接口让后台可以配置每个Banner的跳转链接。
  2. **商品分类**: 后台商品管理需要能够对应新的分类。
- **API**:
  1. `GET /api/banners`: Banner接口返回的数据需要包含 `linkUrl` 字段。
  2. `GET /api/products`: 商品列表接口需要支持新的分类体系进行筛选。

---

## 三、商品详情与购买流程优化

### 需求描述

增加"直接购买"功能，与拼团购买区分开，并支持积分支付。

### 改造点

- **后端**:
  - 商品数据表增加 `allowPointsPurchase` (布尔型) 字段。
  - 创建新的"直接购买"订单逻辑，区别于拼团。
  - 实现积分和余额二选一支付的逻辑。
- **前端**:
  - **商品详情页 (`ProductDetailPage.vue`)**:
    - 修改目前的发起拼团按钮式样，分为两个按钮，一个是原有的拼团按钮（功能逻辑不变，放在右边），一个是增加"直接购买"按钮（放在左边）。
    - 根据商品 `allowPointsPurchase` 属性，在下单时显示不同的支付选项（余额/积分）。
  - **创建新的下单页面 (`DirectBuyOrderPage.vue`)**: 用于处理直接购买的订单确认和支付，链接到商品详情页的"直接购买"按钮
- **API**:
  - 复用现有的 `POST /api/v1/placeOrder` 接口，通过订单类型区分直接购买和拼团下单。
  - `GET /api/products/{id}`: 商品详情接口返回的数据需要包含 `allowPointsPurchase` 字段。

### 详细实现说明

#### 3.1 数据库设计
**商品表增加字段**:
```sql
ALTER TABLE products ADD COLUMN allowPointsPurchase BOOLEAN DEFAULT FALSE COMMENT '是否允许积分购买';
```

**订单表区分订单类型**:
```sql
ALTER TABLE orders ADD COLUMN orderType ENUM('GROUP_BUY', 'DIRECT_BUY') DEFAULT 'GROUP_BUY' COMMENT '订单类型';
ALTER TABLE orders ADD COLUMN paymentMethod ENUM('balance', 'points') DEFAULT 'balance' COMMENT '支付方式';
```

#### 3.2 直接购买订单逻辑
**与拼团订单的区别**:
1. **订单生成**: 直接购买订单无需等待拼团，立即生成有效订单
2. **价格计算**: 使用商品原价或直接购买价格，不享受拼团优惠
3. **支付方式**: 支持余额、积分二选一支付（根据商品 `allowPointsPurchase` 属性）
4. **订单状态**: 支付成功后直接进入"待发货"状态，无需开奖流程
5. **库存扣减**: 支付成功后立即扣减商品库存

#### 3.3 前端页面布局调整
**商品详情页底部操作栏**:
```html
<div class="fixed bottom-0 left-0 right-0 bg-white border-t px-4 py-3">
  <div class="flex items-center space-x-3">
    <!-- 客服按钮 -->
    <div class="flex space-x-4">
      <div class="flex flex-col items-center" @click="contactService">
        <iconify-icon icon="material-symbols:chat-outline" class="text-gray-600 text-xl"></iconify-icon>
        <span class="text-xs text-gray-600">客服</span>
      </div>
    </div>
    
    <!-- 主要操作按钮 -->
    <div class="flex-1 flex space-x-2">
      <button 
        @click="directBuy" 
        class="flex-1 bg-orange-500 text-white py-3 rounded-full font-medium"
      >
        直接购买 ¥{{ formatPrice(product.directPrice) }}
      </button>
      <button 
        @click="startGroup" 
        class="flex-1 bg-red-500 text-white py-3 rounded-full font-medium"
      >
        发起拼团 ¥{{ formatPrice(product.groupPrice) }}
      </button>
    </div>
  </div>
</div>
```

#### 3.4 直接购买下单页面设计
**页面结构** (`DirectBuyOrderPage.vue`):
1. **商品信息确认**: 显示商品名称、规格、单价、数量
2. **收货地址选择**: 复用现有地址选择组件
3. **支付方式选择**: 根据 `allowPointsPurchase` 显示余额/积分选项
4. **订单金额计算**: 商品总价 + 运费 - 优惠券
5. **提交订单**: 调用统一的下单接口

#### 3.5 支付流程优化
**支付方式判断逻辑**:
```javascript
// 根据商品属性显示支付选项
const paymentOptions = computed(() => {
  const options = [
    { value: 'balance', label: '余额支付', available: userBalance >= totalAmount }
  ]
  
  if (product.value.allowPointsPurchase) {
    options.push({
      value: 'points', 
      label: '积分支付', 
      available: userPoints >= totalAmount
    })
  }
  
  return options
})
```

#### 3.6 API接口设计
**统一下单接口** (基于现有接口优化):
```javascript
// POST /api/v1/placeOrder
{
  "skuId": 26,                    // SKU ID (必需)
  "quantity": 1,                  // 数量 (后台强制为1)
  "shippingAddressId": 8,         // 收货地址ID (必需)
  "orderType": "DIRECT_BUY",      // 订单类型 (新增字段: GROUP_BUY|DIRECT_BUY)
  "paymentMethod": "balance"      // 支付方式 (新增字段: balance|points)
}

// 返回数据 (保持现有格式)
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": "5",                  // 订单ID
    "dataType": 1
  },
  "dataType": 1
}
```

**接口优化说明**:
1. **复用现有接口**: 不新增接口，在现有 `/api/v1/placeOrder` 基础上扩展
2. **新增参数**: 
   - `orderType`: 区分直接购买和拼团订单
   - `paymentMethod`: 指定支付方式（余额/积分）
3. **保持兼容**: 现有拼团下单逻辑不变，默认 `orderType="GROUP_BUY"`
4. **返回格式**: 保持现有返回格式不变，确保前端兼容性

#### 3.7 业务流程图
**直接购买完整流程**:
1. 用户在商品详情页点击"直接购买"
2. 跳转到直接购买订单确认页
3. 选择收货地址、支付方式、使用优惠券
4. 提交订单，调用 `/api/v1/placeOrder` 接口 (orderType="DIRECT_BUY")
5. 后端创建直接购买订单，返回订单ID
6. 前端根据支付方式进行支付流程处理
7. 支付成功后，订单状态变为"待发货"
8. 跳转到订单详情页或我的订单页面

**后端接口处理逻辑**:
```javascript
// 后端 placeOrder 接口处理逻辑
if (orderType === 'DIRECT_BUY') {
  // 直接购买逻辑
  - 使用商品原价计算订单金额
  - 立即生成有效订单，无需拼团等待
  - 根据 paymentMethod 处理支付方式
  - 支付成功后直接进入"待发货"状态
} else {
  // 拼团购买逻辑 (保持现有逻辑不变)
  - 使用拼团价格计算订单金额  
  - 创建拼团订单，等待拼团完成
  - 默认使用余额支付
  - 拼团成功后进入开奖流程
}
```

---

## 四、拼团流程简化

### 需求描述

简化拼团下单支付方式，优化余额不足时的体验。

### 改造点

- **前端**:
  - **拼团下单页 (`OrderPage.vue` 或类似页面)**:
    - 移除积分支付选项，只保留余额支付。
    - 当检测到用户余额不足时，显示“充值”按钮，点击跳转至充值页面 (`RechargePage.vue`)。
  - **拼团结束页 (`GroupBuyResultPage.vue` 或类似页面)**:
    - 增加“返回首页”按钮。
    - 实现10秒后自动跳转回首页的定时器。

---

## 五、用户中心（我的）功能增强

### 需求描述

增加实名认证和团队信息展示。

### 改造点

- **后端**:
  - 用户表增加 `isRealNameVerified` (布尔型) 字段。
  - 新建数据表存储用户实名信息（姓名、身份证号、身份证照片、银行卡信息）。
  - 开发团队关系链，计算并存储每个用户的下级贡献值和团队总收益。
- **前端**:
  - **提现页面 (`WithdrawPage.vue`)**:
    - 在申请提现前，检查用户的 `isRealNameVerified` 状态。
    - 如果未认证，引导用户进入实名认证流程。
  - **新建实名认证页面 (`RealNameVerificationPage.vue`)**:
    - 创建表单用于提交姓名、身份证照片、银行卡信息。
  - **新建团队信息页面 (`TeamInfoPage.vue`)**:
    - 调用API获取并展示下级列表、贡献值、团队收益。
    - 对有消费记录的下级用户进行高亮显示。
- **API**:
  - `POST /api/user/verifyRealName`: 新增实名资料提交接口。
  - `GET /api/team/info`: 新增获取团队信息的接口（总览）。
  - `GET /api/team/members`: 新增获取下级成员列表的接口。
  - `GET /api/user/profile`: 用户信息接口需返回 `isRealNameVerified` 字段。

---

## 六、积分与风控系统

### 需求描述

完善积分体系和风控规则。

### 改造点

- **后端**:
  - **积分上限**: 实现每日用户获得积分的上限逻辑。
  - **积分退款**: 在退单逻辑中，判断支付方式，如果是积分支付，则不退还积分部分。
  - **提现手续费**: 在提现逻辑中，扣除10%的手续费。
  - **积分商品标识**:
    - 商品表增加 `isPointsOnly` (布尔型) 字段，标识仅限积分购买的商品。
    - 积分商品的 `allowPointsPurchase` 字段必须为 `true`，且不支持余额支付。
- **前端**:
  - **首页商品筛选**: 在商品列表中增加"积分商城"分类，展示仅限积分购买的商品。
  - **商品详情页**: 对于积分商品，只显示"积分购买"按钮，隐藏"发起拼团"按钮。
  - **直接购买页面**: 对于积分商品，支付方式只显示积分支付选项。
- **API**:
  - `GET /api/products`: 商品列表接口支持 `isPointsOnly` 参数筛选积分商品。
  - 直接购买接口 `POST /api/directBuyOrder` 已支持积分支付，无需新增接口。

### 积分商品实现说明

#### 6.1 数据库设计

**商品表增加积分商品标识**:

```sql
ALTER TABLE products ADD COLUMN isPointsOnly BOOLEAN DEFAULT FALSE COMMENT '是否仅限积分购买';
```

#### 6.2 积分商品逻辑

**积分商品特点**:

1. **购买限制**: 仅支持积分支付，不支持余额支付
2. **展示区别**: 商品详情页只显示"积分购买"按钮，不显示"发起拼团"按钮
3. **价格显示**: 商品价格以积分形式显示（如：1000积分）
4. **分类筛选**: 在首页商品分类中单独展示"积分商城"

#### 6.3 前端实现调整

**首页商品分类**:

```javascript
const productCategories = [
  { key: 'all', label: '全部' },
  { key: 'digital', label: '手机数码' },
  { key: 'luxury', label: '奢饰品' },
  { key: 'points', label: '积分商城' }  // 新增积分商城分类
]
```

**商品详情页按钮逻辑**:

```javascript
// 根据商品类型显示不同按钮
const showDirectBuyOnly = computed(() => {
  return product.value.isPointsOnly
})

const showGroupBuyButton = computed(() => {
  return !product.value.isPointsOnly
})
```

#### 6.4 支付流程整合

积分商品通过现有的直接购买流程实现，无需单独的兑换接口：

1. 用户在积分商品详情页点击"积分购买"
2. 跳转到直接购买订单确认页 (`DirectBuyOrderPage.vue`)
3. 系统检测到 `isPointsOnly=true`，支付方式只显示积分支付
4. 调用现有的 `POST /api/directBuyOrder` 接口，`paymentMethod` 设为 `points`
5. 后端验证用户积分余额，创建积分支付订单
6. 订单完成后，扣减用户积分，商品直接发货

---

## 七、二期规划

### 需求

- 订金模式

### 说明

此为二期需求，本次优化暂不包含。将在后续版本中进行详细设计和规划。
