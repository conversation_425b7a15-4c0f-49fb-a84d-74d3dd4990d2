package net.lab1024.sa.admin.module.business.identity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAuditForm;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationQueryForm;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationListVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationStatusVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationVO;
import net.lab1024.sa.admin.module.business.identity.service.IdentityVerificationService;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 实名认证管理端控制器
 * 
 * 管理员API，用于管理员审核和管理实名认证申请
 *
 * <AUTHOR>
 * @Date 2025-01-27
 */
@Slf4j
@Tag(name = "实名认证管理端")
@RestController
@RequestMapping("/admin/identity-verification")
@Validated
public class IdentityVerificationAdminController {

    @Resource
    private IdentityVerificationService identityVerificationService;

    /**
     * 分页查询实名认证记录
     */
    @Operation(summary = "分页查询实名认证记录")
    @PostMapping("/page")
    public ResponseDTO<IPage<IdentityVerificationListVO>> queryByPage(
            @RequestBody @Valid IdentityVerificationQueryForm queryForm) {
        
        log.info("🎯 [控制器] 收到分页查询请求");
        log.info("🎯 [控制器] 查询参数: {}", queryForm);
        log.info("🎯 [控制器] 当前用户ID: {}", SmartRequestUtil.getRequestUserId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            ResponseDTO<IPage<IdentityVerificationListVO>> result = identityVerificationService.queryByPage(queryForm);
            
            long endTime = System.currentTimeMillis();
            log.info("✅ [控制器] 查询执行成功，耗时: {}ms", endTime - startTime);
            
            if (result != null && result.getData() != null) {
                IPage<IdentityVerificationListVO> pageData = result.getData();
                log.info("📊 [控制器] 查询结果统计:");
                log.info("  - 当前页码: {}", pageData.getCurrent());
                log.info("  - 每页大小: {}", pageData.getSize());
                log.info("  - 总记录数: {}", pageData.getTotal());
                log.info("  - 总页数: {}", pageData.getPages());
                log.info("  - 当前页记录数: {}", pageData.getRecords().size());
                
                if (!pageData.getRecords().isEmpty()) {
                    log.info("📝 [控制器] 第一条记录示例: {}", pageData.getRecords().get(0));
                }
            } else {
                log.warn("⚠️ [控制器] 查询结果为空或格式异常");
            }
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("❌ [控制器] 查询执行失败，耗时: {}ms，错误信息: {}", endTime - startTime, e.getMessage());
            log.error("❌ [控制器] 错误堆栈:", e);
            throw e;
        }
    }

    /**
     * 查询认证详情
     */
    @Operation(summary = "查询认证详情")
    @GetMapping("/detail/{id}")
    public ResponseDTO<IdentityVerificationVO> getVerificationDetail(
            @PathVariable("id") @NotNull Long id) {
        Long currentUserId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.getVerificationDetail(id, currentUserId);
    }

    /**
     * 审核实名认证申请
     */
    @Operation(summary = "审核实名认证申请")
    @PostMapping("/audit")
    public ResponseDTO<String> auditVerification(
            @RequestBody @Valid IdentityVerificationAuditForm auditForm) {
        Long auditUserId = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.auditVerification(auditForm, auditUserId);
    }

    /**
     * 获取待审核记录列表
     */
    @Operation(summary = "获取待审核记录列表")
    @GetMapping("/pending")
    public ResponseDTO<List<IdentityVerificationListVO>> getPendingList() {
        return identityVerificationService.getPendingList();
    }

    /**
     * 批量查询用户认证状态
     */
    @Operation(summary = "批量查询用户认证状态")
    @PostMapping("/batch-status")
    public ResponseDTO<List<IdentityVerificationStatusVO>> batchGetUserVerificationStatus(
            @RequestBody @Valid @NotNull List<Long> userIds) {
        return identityVerificationService.batchGetUserVerificationStatus(userIds);
    }

    /**
     * 软删除认证记录
     */
    @Operation(summary = "删除认证记录")
    @DeleteMapping("/{id}")
    public ResponseDTO<String> deleteVerification(@PathVariable("id") @NotNull Long id) {
        Long deletedBy = SmartRequestUtil.getRequestUserId();
        return identityVerificationService.deleteVerification(id, deletedBy);
    }

    /**
     * 恢复已删除的认证记录
     */
    @Operation(summary = "恢复认证记录")
    @PostMapping("/restore/{id}")
    public ResponseDTO<String> restoreVerification(@PathVariable("id") @NotNull Long id) {
        return identityVerificationService.restoreVerification(id);
    }

    /**
     * 获取认证统计信息
     */
    @Operation(summary = "获取认证统计信息")
    @GetMapping("/statistics")
    public ResponseDTO<Object> getVerificationStatistics() {
        return identityVerificationService.getVerificationStatistics();
    }

    /**
     * 检查指定用户是否已通过实名认证
     */
    @Operation(summary = "检查指定用户是否已通过实名认证")
    @GetMapping("/is-verified/{userId}")
    public ResponseDTO<Boolean> isUserVerified(@PathVariable("userId") @NotNull Long userId) {
        boolean verified = identityVerificationService.isUserVerified(userId);
        return ResponseDTO.ok(verified);
    }

    /**
     * 查询指定用户的认证状态
     */
    @Operation(summary = "查询指定用户的认证状态")
    @GetMapping("/status/{userId}")
    public ResponseDTO<IdentityVerificationStatusVO> getUserVerificationStatus(
            @PathVariable("userId") @NotNull Long userId) {
        return identityVerificationService.getUserVerificationStatus(userId);
    }

    /**
     * 检查身份证号是否已被使用
     */
    @Operation(summary = "检查身份证号是否已被使用")
    @GetMapping("/check-idcard")
    public ResponseDTO<Boolean> checkIdCardExists(
            @RequestParam("idCard") String idCard,
            @RequestParam(value = "excludeUserId", required = false) Long excludeUserId) {
        boolean exists = identityVerificationService.isIdCardExists(idCard, excludeUserId);
        return ResponseDTO.ok(exists);
    }

    /**
     * 检查银行卡号是否已被使用
     */
    @Operation(summary = "检查银行卡号是否已被使用")
    @GetMapping("/check-bankcard")
    public ResponseDTO<Boolean> checkBankCardExists(
            @RequestParam("bankCard") String bankCard,
            @RequestParam(value = "excludeUserId", required = false) Long excludeUserId) {
        boolean exists = identityVerificationService.isBankCardExists(bankCard, excludeUserId);
        return ResponseDTO.ok(exists);
    }

    /**
     * 清理过期的认证记录
     */
    @Operation(summary = "清理过期的认证记录")
    @PostMapping("/clean-expired")
    public ResponseDTO<Integer> cleanExpiredRecords() {
        return identityVerificationService.cleanExpiredRecords();
    }

    /**
     * 批量审核认证申请
     */
    @Operation(summary = "批量审核认证申请")
    @PostMapping("/batch-audit")
    public ResponseDTO<String> batchAuditVerification(
            @RequestBody @Valid List<IdentityVerificationAuditForm> auditForms) {
        Long auditUserId = SmartRequestUtil.getRequestUserId();
        
        int successCount = 0;
        int failCount = 0;
        
        for (IdentityVerificationAuditForm auditForm : auditForms) {
            try {
                ResponseDTO<String> result = identityVerificationService.auditVerification(auditForm, auditUserId);
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    log.warn("Batch audit failed for verificationId: {}, error: {}", 
                            auditForm.getVerificationId(), result.getMsg());
                }
            } catch (Exception e) {
                failCount++;
                log.error("Batch audit error for verificationId: {}", auditForm.getVerificationId(), e);
            }
        }

        String message = String.format("批量审核完成，成功：%d，失败：%d", successCount, failCount);
        return ResponseDTO.ok(message);
    }

    /**
     * 导出认证记录
     */
    @Operation(summary = "导出认证记录")
    @PostMapping("/export")
    public ResponseDTO<String> exportVerificationRecords(
            @RequestBody @Valid IdentityVerificationQueryForm queryForm) {
        // TODO: 实现导出功能
        return ResponseDTO.error(UserErrorCode.DEVELOPING, "导出功能开发中");
    }

    /**
     * 获取审核日志
     */
    @Operation(summary = "获取认证记录的审核日志")
    @GetMapping("/audit-log/{verificationId}")
    public ResponseDTO<Object> getAuditLog(@PathVariable("verificationId") @NotNull Long verificationId) {
        // TODO: 实现审核日志查询功能
        return ResponseDTO.error(UserErrorCode.DEVELOPING, "审核日志功能开发中");
    }

    /**
     * 认证数据统计报表
     */
    @Operation(summary = "认证数据统计报表")
    @PostMapping("/report")
    public ResponseDTO<Object> getVerificationReport(
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "reportType", defaultValue = "daily") String reportType) {
        // TODO: 实现统计报表功能
        return ResponseDTO.error(UserErrorCode.DEVELOPING, "统计报表功能开发中");
    }
}