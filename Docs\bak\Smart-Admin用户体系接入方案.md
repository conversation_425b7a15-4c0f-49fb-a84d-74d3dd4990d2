# Smart-Admin用户体系接入方案

## 概述

基于对Smart-Admin框架的深度分析，为拼团项目提供完整的用户登录认证体系接入方案。

## 1. Smart-Admin认证体系核心特征

### 1.1 技术栈
- **Sa-Token**: 轻量级Java权限认证框架
- **SpringBoot**: 核心应用框架  
- **Redis**: Session存储和缓存
- **MySQL**: 用户数据持久化
- **RBAC**: 基于角色的访问控制模型

### 1.2 认证流程
```
用户请求 → Sa-Token拦截 → Token验证 → Redis查询Session → 权限校验 → 业务处理
```

## 2. 数据库设计方案

### 2.1 保持Smart-Admin原有用户表结构
```sql
-- Smart-Admin员工表（保持不变）
CREATE TABLE `t_employee` (
  `employee_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `login_name` varchar(30) NOT NULL COMMENT '登录账号',
  `login_pwd` varchar(60) NOT NULL COMMENT '登录密码',
  `actual_name` varchar(30) NOT NULL COMMENT '真实姓名',
  `phone` varchar(15) COMMENT '手机号',
  `department_id` bigint COMMENT '部门id',
  `is_disabled` tinyint DEFAULT 0 COMMENT '是否被禁用',
  `is_delete` tinyint DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`employee_id`),
  UNIQUE KEY `uk_login_name` (`login_name`),
  UNIQUE KEY `uk_phone` (`phone`)
) COMMENT='Smart-Admin员工表';
```

### 2.2 扩展拼团用户表
```sql
-- 拼团用户表（扩展Smart-Admin用户体系）
ALTER TABLE `users` ADD COLUMN `employee_id` BIGINT NULL COMMENT '关联Smart-Admin员工ID' AFTER `id`;
ALTER TABLE `users` ADD COLUMN `user_type` ENUM('customer','admin','super_admin') DEFAULT 'customer' COMMENT '用户类型';
ALTER TABLE `users` ADD COLUMN `smart_admin_sync` TINYINT DEFAULT 0 COMMENT '是否已同步到Smart-Admin';

-- 添加外键关联
ALTER TABLE `users` ADD CONSTRAINT `fk_users_employee` 
FOREIGN KEY (`employee_id`) REFERENCES `t_employee`(`employee_id`) ON DELETE SET NULL;
```

## 3. 后端集成方案

### 3.1 Sa-Token配置
```java
@Configuration
public class SaTokenConfig {
    
    @Bean
    @Primary
    public cn.dev33.satoken.config.SaTokenConfig getSaTokenConfig() {
        cn.dev33.satoken.config.SaTokenConfig config = new cn.dev33.satoken.config.SaTokenConfig();
        config.setTokenName("satoken");
        config.setTimeout(30 * 24 * 60 * 60); // 30天
        config.setActivityTimeout(30 * 60); // 30分钟无操作过期
        config.setIsConcurrent(true);
        config.setIsShare(false);
        config.setTokenStyle("uuid");
        config.setIsLog(true);
        return config;
    }
}
```

### 3.2 权限认证接口实现
```java
@Component
public class StpInterfaceImpl implements StpInterface {
    
    @Autowired
    private UserService userService;
    
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        Long userId = Long.valueOf(loginId.toString());
        User user = userService.getUserById(userId);
        
        List<String> permissions = new ArrayList<>();
        
        // 根据用户类型添加基础权限
        switch (user.getUserType()) {
            case "super_admin":
                permissions.add("*"); // 超级管理员拥有所有权限
                break;
            case "admin":
                permissions.addAll(getAdminPermissions());
                break;
            case "customer":
            default:
                permissions.addAll(getCustomerPermissions());
                break;
        }
        
        // 获取用户角色权限
        List<String> rolePermissions = userService.getUserPermissionsByRoles(userId);
        permissions.addAll(rolePermissions);
        
        return permissions.stream().distinct().collect(Collectors.toList());
    }
    
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        Long userId = Long.valueOf(loginId.toString());
        List<String> roles = userService.getUserRoles(userId);
        
        User user = userService.getUserById(userId);
        if (user != null) {
            roles.add(user.getUserType());
        }
        
        return roles.stream().distinct().collect(Collectors.toList());
    }
}
```

### 3.3 统一认证服务
```java
@Service
@Slf4j
public class SmartAdminAuthService {
    
    @Autowired
    private UserService userService;
    
    public ResponseResult<LoginResponse> login(LoginRequest request) {
        try {
            // 1. 验证用户凭据
            User user = validateUser(request.getPhone(), request.getPassword());
            
            // 2. 使用Sa-Token进行登录
            StpUtil.login(user.getId(), SaLoginConfig
                .setTimeout(7200) // 2小时
                .setDevice(request.getDeviceType())
                .setExtra("userType", user.getUserType())
                .setExtra("phone", user.getPhone())
            );
            
            // 3. 构建响应
            LoginResponse response = LoginResponse.builder()
                .token(StpUtil.getTokenValue())
                .refreshToken(StpUtil.getTokenInfo().getTokenValue())
                .expiresIn(StpUtil.getTokenTimeout())
                .user(buildUserInfoResponse(user))
                .build();
                
            return ResponseResult.success(response);
            
        } catch (Exception e) {
            log.error("用户登录失败: phone={}, error={}", request.getPhone(), e.getMessage());
            return ResponseResult.error(40001, "登录失败：" + e.getMessage());
        }
    }
    
    public ResponseResult<UserInfoResponse> getCurrentUserInfo() {
        try {
            StpUtil.checkLogin();
            Long userId = StpUtil.getLoginIdAsLong();
            User user = userService.getUserById(userId);
            
            List<String> permissions = StpUtil.getPermissionList();
            List<String> roles = StpUtil.getRoleList();
            
            UserInfoResponse response = UserInfoResponse.builder()
                .user(buildUserInfoResponse(user))
                .permissions(permissions)
                .roles(roles)
                .build();
                
            return ResponseResult.success(response);
            
        } catch (NotLoginException e) {
            return ResponseResult.error(40001, "用户未登录");
        }
    }
}
```

## 4. 前端集成方案

### 4.1 统一认证状态管理
```javascript
// store/auth.js
import { defineStore } from 'pinia'
import { smartAdminApi } from '@/api/smartAdmin'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('satoken') || '')
  const user = ref(null)
  const permissions = ref([])
  const roles = ref([])
  
  // 登录
  const login = async (loginData) => {
    const response = await smartAdminApi.login(loginData)
    
    if (response.code === 200) {
      token.value = response.data.token
      user.value = response.data.user
      
      localStorage.setItem('satoken', response.data.token)
      localStorage.setItem('user_info', JSON.stringify(response.data.user))
      
      await fetchUserInfo()
      return response
    } else {
      throw new Error(response.message || '登录失败')
    }
  }
  
  // 获取用户信息
  const fetchUserInfo = async () => {
    const response = await smartAdminApi.getUserInfo()
    if (response.code === 200) {
      permissions.value = response.data.permissions || []
      roles.value = response.data.roles || []
    }
  }
  
  // 权限检查
  const hasPermission = (permission) => {
    if (!permission) return true
    if (permissions.value.includes('*')) return true
    return permissions.value.includes(permission)
  }
  
  return {
    token,
    user,
    permissions,
    roles,
    login,
    hasPermission,
    fetchUserInfo
  }
})
```

### 4.2 Smart-Admin API适配器
```javascript
// api/smartAdmin.js
import axios from 'axios'

const smartAdminRequest = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
smartAdminRequest.interceptors.request.use((config) => {
  const token = localStorage.getItem('satoken')
  if (token) {
    config.headers['satoken'] = token
  }
  return config
})

// 响应拦截器  
smartAdminRequest.interceptors.response.use(
  (response) => {
    const data = response.data
    return {
      code: data.code || (data.success ? 200 : 500),
      message: data.msg || data.message || '',
      data: data.data || data.result,
      timestamp: data.timestamp || new Date().toISOString()
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('satoken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export const smartAdminApi = {
  login: (data) => smartAdminRequest.post('/auth/login', data),
  logout: () => smartAdminRequest.post('/auth/logout'),
  getUserInfo: () => smartAdminRequest.get('/auth/user/info'),
  register: (data) => smartAdminRequest.post('/auth/register', data)
}
```

## 5. 配置文件

### 5.1 应用配置
```yaml
# application.yml
sa-token:
  token-name: satoken
  timeout: 7200
  activity-timeout: 1800
  is-concurrent: true
  is-share: false
  token-style: uuid
  is-log: true
  jwt-secret-key: ${SA_TOKEN_JWT_SECRET:smart-admin-jwt-secret-key-2024}

spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:1}
```

## 6. 部署方案

### 6.1 Docker部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  tuangou-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - SA_TOKEN_JWT_SECRET=${SA_TOKEN_JWT_SECRET}
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: tuangou
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 7. 总结

这个方案实现了拼团项目与Smart-Admin用户体系的完全集成，具有以下优势：

1. **无缝对接**：完全兼容Smart-Admin的认证体系和权限模型
2. **统一管理**：用户、角色、权限统一在Smart-Admin体系中管理  
3. **灵活扩展**：支持普通用户和管理员用户的差异化权限控制
4. **高性能**：基于Sa-Token和Redis的高性能认证方案
5. **易维护**：标准化的API设计和清晰的代码结构

通过这个方案，您的拼团项目将获得企业级的用户认证和权限管理能力。 
