# 环境切换工具使用指南

## 概述

这个工具可以帮助你在本地开发环境和远程生产环境之间快速切换，无需手动修改配置文件。

## 环境配置

- **本地开发环境**: 连接到 `localhost:8686`
- **远程生产环境**: 连接到 `pp.oripicks.com`

## 使用方法

### 1. 查看当前环境状态
```bash
node switch-env.js --status
```

### 2. 切换到本地开发环境
```bash
node switch-env.js --local
```

### 3. 切换到远程生产环境
```bash
node switch-env.js --remote
```

### 4. 交互式选择环境
```bash
node switch-env.js
```

### 5. 查看帮助信息
```bash
node switch-env.js --help
```

## 自动备份机制

每次切换环境时，脚本会自动备份当前配置到 `config-backups/` 目录：

- 备份目录格式: `{环境}-{时间戳}`
- 备份包含: `vite.config.js`, `standardAdapter.js`, `request.js`
- 生成环境信息文件: `env-info.json`

## 影响的配置文件

1. **vite.config.js** - Vite开发服务器代理配置
2. **src/api/standardAdapter.js** - API适配器配置和注释
3. **src/utils/request.js** - HTTP请求工具配置
4. **.env** - 环境变量文件（自动创建）

## 切换流程

1. 检测当前环境
2. 备份当前配置
3. 更新所有相关配置文件
4. 创建新的环境变量文件
5. 提供后续操作指导

## 注意事项

1. **重启开发服务器**: 切换环境后必须重启 `npm run dev`
2. **本地后端服务**: 切换到本地环境时，确保本地后端服务正在运行
3. **配置同步**: 所有相关配置文件会同步更新，保持一致性

## 错误排查

### 1. 脚本执行失败
- 确保项目根目录下有 `vite.config.js` 等配置文件
- 检查文件权限是否允许读写

### 2. 环境切换后API连接失败
- 本地环境: 确保后端服务在 `localhost:8686` 运行
- 远程环境: 检查网络连接和防火墙设置

### 3. 配置备份问题
- 检查 `config-backups/` 目录权限
- 磁盘空间是否充足

## 恢复配置

如需手动恢复配置，可以从 `config-backups/` 目录中找到对应的备份文件，手动复制回原位置。

## 示例输出

```
📊 当前环境状态:

✅ 环境: 远程生产环境
🎯 目标: https://pp.oripicks.com/
📡 API: /api/v1
📝 描述: 连接到远程生产服务器 (pp.oripicks.com)

📁 配置文件状态:
  ✅ vite.config.js
  ✅ standardAdapter.js
  ✅ request.js

📦 最近的配置备份:
  📂 remote-2025-07-26T07-02-50
  📂 local-2025-07-26T07-03-01
```