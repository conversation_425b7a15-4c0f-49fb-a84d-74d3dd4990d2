
● 登录
/app/v1/auth
POST提交:
{
  "type": "login",
  "phone": "***********",
  "password": "123456",
  "captchaCode": "9256",
  "captchaUuid": "4604cf4dc962467a806efc7357c519c0"
}

captchaCode: "8277"
captchaUuid: "1aaf09edc45c438484ef798c065e0a8d"
password: "IGG50em@"
phone: "18906662339"
type: "login"


返回:
{
    "code": 0, //0表示成功，其它有错误
    "msg": "操作成功",
    "ok": true,
    "data": {
        "employeeId": 81,
        "userType": "H5",
        "loginName": "***********",
        "actualName": "***********",
        "gender": 0,
        "phone": "***********",
        "departmentId": 9,
        "departmentName": "客户",
        "positionId": 5,
        "riskLevel": 0,
        "childCount": 0,  //下级数量
        "noviceCount": 3, //免费拼团次数
        "disabledFlag": false,
        "administratorFlag": false,
        "token": "09fa852cca4042f4842dc4a9cff9f9e3",
        "menuList": [],
        "needUpdatePwdFlag": false,
        "lastLoginIp": "127.0.0.1",
        "lastLoginIpRegion": "0|0|0|内网IP|内网IP",
        "lastLoginUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "lastLoginTime": "2025-07-02 23:58:33",
        "userName": "***********",
        "userId": 81
    },
    "dataType": 1
}

● 注册
/app/v1/auth
POST提交:
{
  "type": "register",
  "phone": "13833883389",
  "password": "123456",
  "confirmPassword": "123456",
  "captchaCode": "5831",
  "captchaUuid": "684e05cac0814f01a5e1d975fa330cd8",
  "agreed_to_terms": true,
  "invite_code": ""
}
返回:
{
    "code": 0,
    "msg": "操作成功",
    "ok": true,
    "data": {
        "experience": 500 //experience=体验金
    },
    "dataType": 1
}

● 获取图形验证码
/app/v1/captcha
GET请求:
返回:
{
    "code": 0,
    "msg": "操作成功",
    "ok": true,
    "data": {
        "captchaUuid": "4604cf4dc962467a806efc7357c519c0",
        "captchaBase64Image": "data:image/png;base64,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",
        "expireSeconds": 65
    },
    "dataType": 1
}

● 登出
/app/v1/logout
GET请求:
返回:
{
    "code": 0,
    "msg": "操作成功",
    "ok": true,
    "dataType": 1
}

=============
● 下单
/api/v1/placeOrder
POST提交:
{
  "skuId": 26, //SkuID
  "quantity": 1, //数量(现在无论填多少，后台强制1)
  "shippingAddressId": 8 //收货地址ID
  //新增
  "aloneFlag":1,  //购买方式：1=直接购买，0默认
  "useCurrency":1,  // 支付方式：1=余额支付，2=积分支付
}
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "code": 0,
        "msg": "success",
        "ok": true,
        "data": "5", //订单ID
        "dataType": 1
    },
    "dataType": 1
}

● 订单物流信息
/app/v1/order/Logistics/订单ID
GET提交:
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "id": 2,
        "orderId": 5,
        "trackingNumber": "123123123654",
        "courierCompany": "飞越物流",
        "status": "shipped",
        "trackingInfo": {
            "list": [
                "货物在已达南宁"
            ]
        },
        "shippedTime": "2025-07-06 17:43:04",
        "createTime": "2025-07-06 17:43:04"
    },
    "dataType": 1
}
=======================
● 我的团队
/app/v1/myTeam?pageNum=1&pageSize=20
有pageNum， pageSize这两个翻页的参数
GET:
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "list": [
            {
                "id": 82, //用户ID
                "name": "13833883389", //用户名
                "childCount": 0, //用户下级数
                "noviceCount": 0, //试新数
                "hasReal": false //是否实名
                //新增
                "contribution": 3000  //对上级累计贡献积分
                "createTime":  //创建日期时间
                "consume":  //消费总金额
            },
            {
                "id": 74,
                "name": "xzh",
                "childCount": 0,
                "noviceCount": 0,
                "hasReal": false
            },
            {
                "id": 73,
                "name": "limbo",
                "childCount": 0,
                "noviceCount": 0,
                "hasReal": false    //实名状态
            }
        ],
        "total": 3, //下级数
        "totalTeamReward": 0.00 //所有下级的累计奖励积分
        "weekCount": 4  //本周新增成员数
    },
    "dataType": 1
}


  ✅ 页面改造总结：

  🎯 新的页面布局：

  1. 三列统计卡片：

  团队成员          累计奖励积分        本周新增成员
     8                10570              2

  2. 成员消费排行榜：

  - ✅ 标题：成员消费排行榜
  - ✅ 右上角链接：所有成员 →
  - ✅ 排名显示：1、2、3名有特殊颜色的圆形排名
  - ✅ 成员信息：ID: U003129 格式
  - ✅ 消费信息：总消费：¥45,800
  - ✅ 奖励积分：右侧大数字显示，下方"奖励积分"标签

  📊 API数据对接状态：

  已对接的数据：

  - ✅ 团队成员数量：teamData.total
  - ✅ 累计奖励积分：teamData.totalTeamReward
  - ✅ 团队成员列表：teamData.list

  注意事项：

  - 🔍 消费排行榜数据：当前/api/v1/myTeam接口只返回基本团队信息，没有消费数据
  - 🔍 排行榜显示：目前使用模拟数据展示UI效果
  - 🔍 本周新增成员：使用固定值2，实际需要基于时间计算或API提供

  🛠️ API数据分析：

  根据文档，/api/v1/myTeam返回的数据结构：
  {
    "data": {
      "list": [
        {
          "id": 82,           // 用户ID
          "name": "用户名",    // 显示为ID
          "childCount": 0,    // 下级数（未在排行榜中使用）
          "noviceCount": 0,   // 试新数（未在排行榜中使用）
          "hasReal": false    // 实名状态（未在排行榜中使用）
        }
      ],
      "total": 3,                    // ✅ 已对接：团队成员数
      "totalTeamReward": 10570.00    // ✅ 已对接：累计奖励积分
    }
  }

  🔍 需要额外API的功能：

  1. 成员消费排行榜：需要包含消费金额和奖励积分的API
  2. 本周新增成员：需要包含时间筛选的成员统计API

  📱 当前显示效果：

  现在页面会正确显示：
  - 团队成员总数（从API获取）
  - 累计奖励积分（从API获取）
  - 消费排行榜（使用模拟数据展示UI）

  页面的基础框架和API对接已完成，只需要后续补充消费数据相关的API即可完整显示所有功能！
  ==============================

  ● 申请记录
/api/v1/withdrawals
GET提交:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "pageNum": 1,
        "pageSize": 30,
        "total": 2,
        "pages": 1,
        "list": [
            {
                "id": 4,
                "userId": 81,
                "userName": "***********",
                "amount": 2000.00,
                "fee": 0.00,
                "actualAmount": 2000.00,
                "status": "approved",
                "bankName": "越南银行",
                "bankAccount": "**********",
                "accountHolder": "布洛林",
                "processedBy": 1,
                "createTime": "2025-07-06 10:38:48",
                "processedTime": "2025-07-06 10:38:58"
            },
            {
                "id": 3,
                "userId": 81,
                "userName": "***********",
                "amount": 20000.00,
                "fee": 0.00,
                "actualAmount": 0.00,
                "status": "rejected",
                "bankName": "越南银行",
                "bankAccount": "**********",
                "accountHolder": "布洛林",
                "rejectionReason": "余额不足",
                "processedBy": 1,
                "createTime": "2025-07-06 10:15:37",
                "processedTime": "2025-07-06 10:38:21"
            }
        ],
        "emptyFlag": false
    },
    "dataType": 1
}


● 提现申请
/api/v1/withdrawalsApply
POST提交:
{
  "amount": "2000", 		//提现额
  "bankName": "越南银行", 		//提现银行名称
  "bankAccount": "**********",	//提现银行账号
  "accountHolder": "布洛林"		//提现银行户名
}
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "dataType": 1
}
==========================
● 钱包接口
/api/v1/wallet?pageNum=1&pageSize=3(分页参数用于流水查询)
GET提交:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "balance": { //余额字段
            "totalBalance": 21742.40, 	//总余额
            "balance": 21242.40,		//现金余额
            "experienceBalance": 500.00,	//体验金余额
            "points": 0,			//积分余额
            "totalRecharge": 39300.00,	//总充值
            "totalWithdraw": 18020.00,	//总提现
            "status": 1
        },
        "transactions": { //流水字段
            "pageNum": 1,
            "pageSize": 3,
            "total": 42,
            "pages": 14,
            "list": [
                {
                    "id": 74,
                    "userId": 81,
                    "amount": -10.00, 			//操作额度
                    "balanceAfter": 21242.40,		//操作后现金余额
                    "experienceBalanceAfter": 500.00, 		//操作后体验金余额
                    "pointsAfter": 0,			//操作后积分余额
                    "mode": 0,				//操作模式( 0现金,1体验金, 2积分)
                    "type": "PAYMENT",			//交易类型('RECHARGE','WITHDRAWAL','PAYMENT','REFUND_WIN','REFUND_LOSS','SUBSIDY','COMMISSION','FIRST_REWARD','TEAM_REWARD','MANUAL_ADD','MANUAL_REDUCE','EXPERIENCE_GIFT','FREE_NOVICE')
                    "relatedId": 18,				//关联ID
                    "description": "购物支付",			//操作说明
                    "createTime": "2025-07-27 16:01:31"	//操作时间
                },
                {
                    "id": 73,
                    "userId": 81,
                    "amount": 10.00,
                    "balanceAfter": 21252.40,
                    "experienceBalanceAfter": 500.00,
                    "pointsAfter": 0,
                    "mode": 0,
                    "type": "REFUND_LOSS",
                    "relatedId": 17,
                    "description": "未中返还",
                    "createTime": "2025-07-27 16:01:22"
                },
                {
                    "id": 72,
                    "userId": 81,
                    "amount": 0.30,
                    "balanceAfter": 21242.40,
                    "experienceBalanceAfter": 500.00,
                    "pointsAfter": 0,
                    "mode": 0,
                    "type": "REFUND_WIN",
                    "relatedId": 17,
                    "description": "未中奖励",
                    "createTime": "2025-07-27 16:01:22"
                }
            ],
            "emptyFlag": false
        }
    },
    "dataType": 1
}
