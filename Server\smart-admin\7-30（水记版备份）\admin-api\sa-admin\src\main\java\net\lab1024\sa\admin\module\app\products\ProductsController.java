package net.lab1024.sa.admin.module.app.products;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import net.lab1024.sa.base.common.annoation.NoNeedLogin;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "APP:商品搜索，商品列表")
public class ProductsController {
    @Resource
    ProductsService productsService;

    @Operation(summary = "商品列表")
    @NoNeedLogin
    @GetMapping("/app/v1/products")
    public ResponseDTO<Object> products(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String goodsType,
            @RequestParam(required = false) String groupType,
            @RequestParam(required = false) String priceRange,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize) {
        return ResponseDTO.ok(productsService.products(category, goodsType, groupType, priceRange, sort, pageNum, pageSize));
    }

    @Operation(summary = "商品详情")
    @NoNeedLogin
    @GetMapping("/app/v1/product/{goodId}")
    public ResponseDTO<Object> productDetail(@PathVariable Long goodId) {
        return ResponseDTO.ok(productsService.productsDetail(goodId));
    }
}
