package net.lab1024.sa.admin.module.app.profile;

import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.userAddress.domain.entity.UserAddressEntity;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressAddForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressQueryForm;
import net.lab1024.sa.admin.module.business.userAddress.domain.form.UserAddressUpdateForm;
import net.lab1024.sa.admin.module.business.userAddress.service.UserAddressService;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Objects;

@Service
public class AppAddressService {
    @Resource
    private UserAddressService userAddressService;

    public ResponseDTO<Object> query(String name, String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        UserAddressQueryForm userAddressQueryForm = new UserAddressQueryForm();
        userAddressQueryForm.setSortItemList(new ArrayList<>());
        userAddressQueryForm.setPageNum(pageNum);
        userAddressQueryForm.setPageSize(pageSize);
        userAddressQueryForm.setUserId(SmartRequestUtil.getRequestUserId());
        if (name != null){
            userAddressQueryForm.setBlurry(name);
        }
        if (Objects.equals(sort, "createTime_desc")) {
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("create_time");
            si.setIsAsc(false);
            userAddressQueryForm.getSortItemList().add(si);
        }
        if (Objects.equals(sort, "createTime_asc")) {
            PageParam.SortItem si = new PageParam.SortItem();
            si.setColumn("create_time");
            si.setIsAsc(true);
            userAddressQueryForm.getSortItemList().add(si);
        }

        return ResponseDTO.ok(userAddressService.queryPage(userAddressQueryForm));
    }

    public ResponseDTO<Object> add(UserAddressAddForm userAddressAddForm) {
        userAddressAddForm.setUserId(SmartRequestUtil.getRequestUserId());
        return ResponseDTO.ok(userAddressService.add(userAddressAddForm).getData());
    }


    public ResponseDTO<Object> update(UserAddressUpdateForm userAddressUpdateForm) {
        UserAddressEntity address = userAddressService.getId(userAddressUpdateForm.getId());
        if(address == null || !Objects.equals(address.getUserId(), SmartRequestUtil.getRequestUserId())){
            return ResponseDTO.userErrorParam("no right");
        }
        return ResponseDTO.ok(userAddressService.update(userAddressUpdateForm).getData());
    }

    public ResponseDTO<Object> delete(Long addressId) {
        UserAddressEntity address = userAddressService.getId(addressId);
        if(address == null || !Objects.equals(address.getUserId(), SmartRequestUtil.getRequestUserId())){
            return ResponseDTO.userErrorParam("no right");
        }
        return ResponseDTO.ok(userAddressService.delete(addressId).getData());
    }
}
