package net.lab1024.sa.admin.module.business.oa.popups.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;

/**
 * 弹窗管理 新建表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Data
public class PopupsAddForm {

    @Schema(description = "弹窗标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "弹窗标题 不能为空")
    private String title;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图片URL 不能为空")
    private String imageUrl;

    @Schema(description = "连接URL")
    private String linkUrl;

    @Schema(description = "触发规则")
    private Map<String, Object> triggerRules;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

}