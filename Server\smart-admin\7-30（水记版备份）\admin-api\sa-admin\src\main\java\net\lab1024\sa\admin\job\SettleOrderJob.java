package net.lab1024.sa.admin.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.constant.OrdersConst;
import net.lab1024.sa.admin.constant.WalletConst;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesEntity;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsDao;
import net.lab1024.sa.admin.module.business.goods.dao.GoodsSkusDao;
import net.lab1024.sa.admin.module.business.goods.domain.entity.GoodsSkusEntity;
import net.lab1024.sa.admin.module.business.orders.dao.OrdersDao;
import net.lab1024.sa.admin.module.business.orders.domain.entity.OrdersEntity;
import net.lab1024.sa.admin.module.business.wallets.service.WalletsService;
import net.lab1024.sa.base.module.support.job.core.SmartJob;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class SettleOrderJob implements SmartJob {
    @Resource
    private OrdersDao ordersDao;

    @Resource
    private GoodsDao goodsDao;

    @Resource
    private GoodsSkusDao goodsSkusDao;

    @Resource
    private ActivitiesService activitiesService;

    @Resource
    private WalletsService walletsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String run(String param) {
        settleOrders();
        return "";
    }

    /**
     * 结算订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void settleOrders() {
        MathContext mc = new MathContext(2, RoundingMode.HALF_UP);

        QueryWrapper<OrdersEntity> Q1 =  new QueryWrapper<>();
        Q1.eq("settle_flag", 0).orderByAsc("create_time");
        List<OrdersEntity> list = ordersDao.selectList(Q1);
        if (list.isEmpty()) { return; }

        LocalDateTime now = LocalDateTime.now();
        for (OrdersEntity row : list) {
            if (Objects.equals(row.getStatus(), OrdersConst.Status.LOST)) {
                ActivitiesEntity activities = activitiesService.selectById(row.getActivityId());
                BigDecimal returnRatio = BigDecimal.valueOf(activities.getReturnRatio());
                GoodsSkusEntity goodsSkus = goodsSkusDao.selectById(row.getSkuId());

                BigDecimal ratio = returnRatio.divide(BigDecimal.valueOf(100), mc);

                BigDecimal award = goodsSkus.getPrice().multiply(ratio);

                log.info("{}, 未中奖，奖励 {} x {} = {}", row.getId(), ratio, goodsSkus.getPrice(), award);
                walletsService.rechargeOrWithdraw(row.getUserId(),  award, 0, WalletConst.TransactionsStatus.REFUND_WIN, row.getId(), null,"未中奖励",true);
                if(row.getAmountPaid().compareTo(BigDecimal.valueOf(0)) > 0) {
                    walletsService.rechargeOrWithdraw(row.getUserId(), row.getAmountPaid(), WalletConst.PayMode.BALANCE, WalletConst.TransactionsStatus.REFUND_LOSS, row.getId(), null, "未中返还", true);
                }
                if(row.getExperiencePaid().compareTo(BigDecimal.valueOf(0)) > 0) {
                    walletsService.rechargeOrWithdraw(row.getUserId(), row.getExperiencePaid(), WalletConst.PayMode.EXPERIENCE, WalletConst.TransactionsStatus.REFUND_LOSS, row.getId(), null, "未中返还", true);
                }
                if(row.getPointsPaid() > 0) {
                    walletsService.rechargeOrWithdraw(row.getUserId(), row.getExperiencePaid(), WalletConst.PayMode.POINTS, WalletConst.TransactionsStatus.REFUND_LOSS, row.getId(), null, "未中返还", true);
                }
                row.setSettleFlag(1);
                row.setCompleteTime(now);
                row.setUpdateTime(now);
                ordersDao.updateById(row);
            }
        }

    }
}
