package net.lab1024.sa.admin.module.app.data;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.base.common.annoation.NoNeedLogin;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "APP:首页数据，商品搜索，商品列表")
public class DataController {
    @Resource
    EmployeeService employeeService;

    @Resource
    DataService dataService;

    @Operation(summary = "首页数据")
    @NoNeedLogin
    @GetMapping("/app/v1/home")
    public ResponseDTO<Object> home(){
        return ResponseDTO.ok(dataService.home());
    }

    @Operation(summary = "用户状态")
    @GetMapping("/app/v1/userStatus")
    public ResponseDTO<Object> userStatus(){
        return ResponseDTO.ok(employeeService.getHomeUserStatus());
    }

    @Operation(summary = "商品搜索")
    @NoNeedLogin
    @GetMapping("/app/v1/search")
    public ResponseDTO<Object> search(
            @RequestParam(required = false) String q,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Integer priceMin,
            @RequestParam(required = false) Integer priceMax,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize) {
        return ResponseDTO.ok(dataService.search(q, type, category, priceMin, priceMax, sort, pageNum, pageSize));
    }

    @Operation(summary = "分类列表")
    @NoNeedLogin
    @GetMapping("/app/v1/categories")
    public ResponseDTO<Object> categories(
            @RequestParam(required = false) Integer categoryType,
            @RequestParam(required = false) Long parentId) {
        return ResponseDTO.ok(dataService.categories(categoryType, parentId));
    }

    @Operation(summary = "活动列表")
    @NoNeedLogin
    @GetMapping("/app/v1/activities")
    public ResponseDTO<Object> activities(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize) {
        return ResponseDTO.ok(dataService.activities(name, type, pageNum, pageSize));
    }

    @Operation(summary = "我的团队")
    @GetMapping("/app/v1/myTeam")
    public ResponseDTO<Object> myTeam(
            @RequestParam(required = false) Long pageNum,
            @RequestParam(required = false) Long pageSize){
        return ResponseDTO.ok(dataService.myTeam(pageNum, pageSize));
    }
}
