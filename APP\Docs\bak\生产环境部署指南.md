# 生产环境部署指南

## 📋 概述

本项目已实现 Mock API 与真实 API 的完全解耦，支持无感知切换。通过环境配置可以轻松在开发、测试、生产环境间切换。

## 🔧 环境配置

### 开发环境（使用Mock）

```bash
# .env.development
VITE_USE_MOCK=true
VITE_API_BASE_URL=http://localhost:3000
VITE_BUILD_TIME=development
```

### 生产环境（使用真实API）

```bash
# .env.production
VITE_USE_MOCK=false
VITE_API_BASE_URL=https://api.production.com
VITE_BUILD_TIME=production
```

## 🚀 部署步骤

### 1. 环境变量配置

在生产环境中设置以下环境变量：

```bash
export VITE_USE_MOCK=false
export VITE_API_BASE_URL=https://your-production-api.com
export NODE_ENV=production
```

### 2. 构建生产版本

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 预览构建结果（可选）
npm run preview
```

### 3. 验证切换效果

- ✅ Mock调试面板自动隐藏
- ✅ API请求自动切换到真实API
- ✅ 控制台输出："Home API service initialized: Real"
- ✅ 无需修改任何业务代码

## 📁 真实API接口实现

需要在 `src/api/real/` 目录下创建真实API接口文件：

### 必需的API文件

```
src/api/real/
├── authApi.js          # 认证API (登录注册)
├── homeApi.js          # 首页API
├── orderApi.js         # 订单API  
├── walletApi.js        # 钱包API
├── userApi.js          # 用户API
├── paymentApi.js       # 支付API
├── supportApi.js       # 客服API
└── uploadApi.js        # 文件上传API
```

### authApi.js 示例

```javascript
import axios from 'axios'
import { getApiConfig } from '@/config/env.js'

const apiConfig = getApiConfig()

const authRequest = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout
})

export const authApi = {
  async login(phone, password) {
    const response = await authRequest.post('/auth/login', {
      phone,
      password
    })
    return response.data
  },

  async register(registerData) {
    const response = await authRequest.post('/auth/register', registerData)
    return response.data
  },

  async sendVerificationCode(phone, type) {
    const response = await authRequest.post('/auth/verification-code', {
      phone,
      type
    })
    return response.data
  },

  async oauthLogin(provider, token, userInfo) {
    const response = await authRequest.post('/auth/oauth', {
      provider,
      oauth_token: token,
      user_info: userInfo
    })
    return response.data
  }
}
```

## 🔄 API响应格式要求

真实API需要返回与Mock API相同的数据格式：

```javascript
{
  code: 200,           // 状态码
  data: {...},         // 响应数据
  message: 'success',  // 响应消息
  timestamp: '2024-12-15T10:00:00Z'
}
```

## 🛠️ 架构特性

### 1. 自动服务发现

- API工厂自动根据环境选择正确的服务实现
- 支持服务降级（真实API失败时自动回退到Mock）
- 服务健康检查和状态监控

### 2. 零侵入切换

- 业务组件无需修改
- 自动环境检测
- 配置驱动的API切换

### 3. 开发体验优化

- Mock调试面板自动显示/隐藏
- 详细的服务状态日志
- 错误处理和回退机制

## 📊 服务状态监控

### 检查服务健康状态

```javascript
import { checkServicesHealth, createApiServices } from '@/api/apiFactory.js'

// 创建所有服务
const services = await createApiServices(['home', 'order', 'wallet'])

// 检查服务状态
const healthReport = checkServicesHealth(services)
console.log('服务状态报告:', healthReport)
```

### 状态报告示例

```javascript
{
  total: 7,         // 总服务数
  available: 7,     // 可用服务数
  mock: 0,          // Mock服务数
  real: 7,          // 真实服务数
  fallback: 0,      // 降级服务数
  services: {
    home: { available: true, isMock: false, serviceName: 'home' },
    order: { available: true, isMock: false, serviceName: 'order' }
    // ...
  }
}
```

## ⚠️ 注意事项

### 1. API兼容性

- 确保真实API返回格式与Mock API一致
- 检查所有字段类型和结构
- 验证分页、排序、筛选逻辑

### 2. 错误处理

- 实现统一的错误码映射
- 添加网络超时处理
- 提供用户友好的错误提示

### 3. 性能优化

- 配置合适的请求超时时间
- 实现API响应缓存
- 添加请求重试机制

### 4. 安全考虑

- 验证API接口安全性
- 添加请求签名验证
- 处理敏感数据加密

## 🔍 故障排查

### 常见问题

1. **API服务初始化失败**

   ```
   错误：Failed to initialize home API service
   解决：检查网络连接和API地址配置
   ```
2. **服务降级到Mock**

   ```
   警告：Falling back to mock service for home
   解决：检查真实API服务可用性
   ```
3. **环境变量未生效**

   ```
   现象：仍然显示Mock调试面板
   解决：确认VITE_USE_MOCK=false已正确设置
   ```

## 📝 检查清单

部署前检查：

- [ ] 设置正确的环境变量
- [ ] 实现所有必需的真实API接口
- [ ] 验证API响应格式兼容性
- [ ] 测试错误处理逻辑
- [ ] 确认Mock调试面板已隐藏
- [ ] 验证服务状态监控正常

## 🎯 最佳实践

1. **渐进式切换**：先在测试环境验证真实API，再部署到生产环境
2. **监控告警**：设置API服务健康监控和异常告警
3. **回滚准备**：保留快速回滚到Mock模式的能力
4. **文档维护**：及时更新API接口文档和部署说明

通过以上配置，您可以实现Mock API到真实API的无感知、无障碍切换，确保生产环境的稳定运行。
