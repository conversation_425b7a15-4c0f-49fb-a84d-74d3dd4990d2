package net.lab1024.sa.admin.module.business.userAddress.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserAddressUpdateForm extends UserAddressAddForm {

    @Schema(description = "地址ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "地址ID 不能为空")
    private Long id;

}