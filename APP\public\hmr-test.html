<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HMR功能测试页面</title>
</head>
<body>
    <div id="app"></div>
    
    <script type="module">
        import { createApp, ref, onMounted } from '/node_modules/vue/dist/vue.esm-browser.js'
        
        const App = {
            setup() {
                const count = ref(0)
                const currentTime = ref('')
                const bgColor = ref('#4ecdc4')
                
                onMounted(() => {
                    const updateTime = () => {
                        currentTime.value = new Date().toLocaleTimeString()
                    }
                    updateTime()
                    setInterval(updateTime, 1000)
                    
                    console.log('✅ HMR测试应用已启动')
                    
                    // 检查HMR状态
                    if (import.meta.hot) {
                        console.log('✅ HMR功能已启用')
                        
                        // 保持状态
                        if (import.meta.hot.data.count) {
                            count.value = import.meta.hot.data.count
                        }
                        if (import.meta.hot.data.bgColor) {
                            bgColor.value = import.meta.hot.data.bgColor
                        }
                        
                        // 在重新加载前保存状态
                        import.meta.hot.dispose((data) => {
                            data.count = count.value
                            data.bgColor = bgColor.value
                        })
                    } else {
                        console.log('❌ HMR功能未启用')
                    }
                })
                
                const changeBgColor = () => {
                    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd']
                    const currentIndex = colors.indexOf(bgColor.value)
                    bgColor.value = colors[(currentIndex + 1) % colors.length]
                }
                
                return {
                    count,
                    currentTime,
                    bgColor,
                    changeBgColor
                }
            },
            template: `
                <div style="font-family: Arial, sans-serif; margin: 20px;">
                    <h1>🔥 纯净HMR功能测试</h1>
                    
                    <div :style="{
                        background: bgColor,
                        padding: '20px',
                        margin: '10px 0',
                        borderRadius: '12px',
                        color: 'white',
                        boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                    }">
                        <h2>HMR状态测试区域</h2>
                        <p><strong>当前时间:</strong> {{ currentTime }}</p>
                        <p><strong>点击计数:</strong> {{ count }}</p>
                        <p><strong>背景颜色:</strong> {{ bgColor }}</p>
                        
                        <button @click="count++" style="
                            background: white;
                            color: #333;
                            border: none;
                            padding: 10px 20px;
                            margin: 5px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 16px;
                        ">点击计数 (+1)</button>
                        
                        <button @click="changeBgColor" style="
                            background: rgba(255,255,255,0.2);
                            color: white;
                            border: 1px solid white;
                            padding: 10px 20px;
                            margin: 5px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 16px;
                        ">切换背景色</button>
                    </div>
                    
                    <div style="
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        border-left: 4px solid #007bff;
                        margin: 10px 0;
                    ">
                        <h3>🧪 测试说明</h3>
                        <ol>
                            <li>点击"点击计数"让数值变为5</li>
                            <li>修改此文件中的背景颜色代码</li>
                            <li>保存文件</li>
                            <li>观察是否热更新（数值保持5，颜色立即变化）</li>
                        </ol>
                        
                        <div style="margin-top: 10px; font-size: 14px; color: #666;">
                            💡 这个测试不依赖任何API，纯粹测试HMR功能
                        </div>
                    </div>
                </div>
            `
        }
        
        createApp(App).mount('#app')
    </script>
</body>
</html>
