package net.lab1024.sa.admin.module.business.oa.banners.service;

import java.util.ArrayList;
import java.util.List;
import net.lab1024.sa.admin.module.business.oa.banners.dao.BannersDao;
import net.lab1024.sa.admin.module.business.oa.banners.domain.entity.BannersEntity;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersAddForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersQueryForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersUpdateForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.vo.BannersVO;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.base.module.support.file.service.IFileStorageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 横幅管理 Service
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Service
public class BannersService {

    @Resource
    private BannersDao bannersDao;

    @Resource
    private IFileStorageService fileStorageService;
    /**
     * 分页查询
     */
    public PageResult<BannersVO> queryPage(BannersQueryForm queryForm) {
        List<PageParam.SortItem> ls = new ArrayList<>();
        PageParam.SortItem sortItem  = new PageParam.SortItem();
        sortItem.setIsAsc(false);
        sortItem.setColumn("sort_order");
        ls.add(sortItem);
        if(queryForm.getSortItemList() != null) {
            ls.addAll(queryForm.getSortItemList());
        }

        queryForm.setSortItemList(ls);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<BannersVO> list = bannersDao.queryPage(page, queryForm);
        for (BannersVO vo : list) {
            ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(vo.getImageUrl());
            if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                vo.setImageUrl(getFileUrl.getData());
            }
        }
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(BannersAddForm addForm) {
        BannersEntity bannersEntity = SmartBeanUtil.copy(addForm, BannersEntity.class);
        bannersDao.insert(bannersEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     */
    public ResponseDTO<String> update(BannersUpdateForm updateForm) {
        BannersEntity bannersEntity = SmartBeanUtil.copy(updateForm, BannersEntity.class);
        bannersDao.updateById(bannersEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        bannersDao.deleteBatchIds(idList);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        bannersDao.deleteById(id);
        return ResponseDTO.ok();
    }
}
