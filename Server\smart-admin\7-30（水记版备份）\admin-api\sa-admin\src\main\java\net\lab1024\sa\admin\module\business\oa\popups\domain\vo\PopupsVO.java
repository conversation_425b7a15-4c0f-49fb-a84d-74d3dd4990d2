package net.lab1024.sa.admin.module.business.oa.popups.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;

/**
 * 弹窗管理 列表VO
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Data
public class PopupsVO {


    @Schema(description = "弹窗ID")
    private Integer id;

    @Schema(description = "弹窗标题")
    private String title;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "跳转链接")
    private String linkUrl;

    @Schema(description = "触发规则")
    private Map<String, Object> triggerRules;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
