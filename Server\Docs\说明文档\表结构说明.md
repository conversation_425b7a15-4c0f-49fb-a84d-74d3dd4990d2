# 团购系统数据库表结构详细说明

## 概述

本文档详细说明了团购系统数据库中所有表的结构和字段含义。该系统基于Smart-Admin框架开发，采用MySQL数据库，支持团购、拼团、钱包、用户管理等核心业务功能。

## 目录

- [核心业务表](#核心业务表)
  - [商品相关表](#商品相关表)
  - [活动相关表](#活动相关表)
  - [订单相关表](#订单相关表)
  - [用户相关表](#用户相关表)
  - [钱包相关表](#钱包相关表)
- [系统管理表](#系统管理表)
- [辅助功能表](#辅助功能表)

---

## 核心业务表

### 商品相关表

#### 1. t_goods - 商品主表

商品信息的核心存储表，包含商品的基本信息、价格、状态等。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| goods_id | BIGINT(主键) | 商品ID | 商品的唯一标识，自增主键 |
| goods_status | INT | 商品状态 | 1:预约中, 2:售卖中, 3:售罄 |
| category_id | BIGINT | 商品分类ID | 关联t_category表，商品所属分类 |
| activity_id | BIGINT | 活动ID | 关联t_activities表，商品参与的活动 |
| goods_name | VARCHAR | 商品名称 | 商品的显示名称 |
| goods_type | VARCHAR | 商品类型 | 商品分类标识 |
| goods_currency | VARCHAR | 货币类型 | 商品价格的货币单位 |
| place | VARCHAR | 产地 | 商品生产地或来源地 |
| price | VARCHAR | 商品价格 | 商品的基础价格 |
| alone_flag | BOOLEAN | 单独购买标记 | 是否支持单独购买（非拼团） |
| alone_price | VARCHAR | 单独购买价格 | 非拼团时的商品价格 |
| pay_mode | INT | 付款模式 | 支付方式限制 |
| description | TEXT | 商品描述 | 商品的详细描述信息 |
| images | JSON | 商品图片数组 | 商品展示图片的URL数组 |
| detail_images | JSON | 详情图片数组 | 商品详情页的图片数组 |
| shelves_flag | BOOLEAN | 上架状态 | 商品是否上架销售 |
| deleted_flag | BOOLEAN | 删除状态 | 软删除标记 |
| remark | VARCHAR | 备注 | 管理员备注信息 |
| create_time | DATETIME | 创建时间 | 记录创建时间 |
| update_time | DATETIME | 更新时间 | 记录最后更新时间 |

#### 2. t_goods_skus - 商品SKU表

商品的具体规格信息，支持商品的多规格销售。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | SKU ID | SKU的唯一标识 |
| goods_id | BIGINT | 商品ID | 关联t_goods表 |
| sku_code | VARCHAR | SKU编码 | SKU的唯一编码 |
| attributes | JSON | 规格属性 | 颜色、尺寸等属性信息 |
| price | DECIMAL | SKU价格 | 该规格的拼团价格 |
| original_price | DECIMAL | 原价 | 该规格的原始价格 |
| alone_flag | BOOLEAN | 单独购买标记 | 该SKU是否支持单独购买 |
| alone_price | DECIMAL | 单独购买价格 | 该SKU的单独购买价格 |
| stock | INT | 库存数量 | 该SKU的可售库存 |
| sales_count | INT | 销售数量 | 该SKU的累计销售量 |
| status | INT | SKU状态 | SKU的启用状态 |
| create_time | DATETIME | 创建时间 | 记录创建时间 |
| update_time | DATETIME | 更新时间 | 记录最后更新时间 |

#### 3. t_category - 商品分类表

商品分类的层级结构管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| category_id | BIGINT(主键) | 分类ID | 分类的唯一标识 |
| category_name | VARCHAR | 分类名称 | 分类的显示名称 |
| category_type | INT | 分类类型 | 分类的类型标识 |
| parent_id | BIGINT | 父分类ID | 上级分类ID，支持多级分类 |
| sort | INT | 排序权重 | 分类显示的排序值 |
| disabled_flag | BOOLEAN | 禁用标记 | 分类是否禁用 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| remark | VARCHAR | 备注 | 分类说明信息 |
| create_time | DATETIME | 创建时间 | 记录创建时间 |
| update_time | DATETIME | 更新时间 | 记录最后更新时间 |

### 活动相关表

#### 4. t_activities - 抽奖活动表

拼团活动的核心配置表，管理各种抽奖和拼团活动。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 活动ID | 活动的唯一标识 |
| name | VARCHAR | 活动名称 | 活动的显示名称 |
| description | TEXT | 活动描述 | 活动的详细说明 |
| type | VARCHAR | 活动类型 | 活动类型标识（如拼团、抽奖等） |
| status | INT | 活动状态 | 0:待开始, 1:进行中, 2:已结束, 3:已取消 |
| return_ratio | INT | 返还比例 | 活动失败时的返还比例(%) |
| config_info | JSON | 活动配置参数 | 活动的详细配置信息 |
| force_loss_flag | INT | 必不中开关 | 是否强制用户不中奖的控制开关 |
| participant_limit | INT | 参与人数限制 | 活动最大参与人数 |
| current_participants | INT | 当前参与人数 | 当前已参与的用户数量 |
| start_time | DATETIME | 活动开始时间 | 活动开始的时间 |
| end_time | DATETIME | 活动结束时间 | 活动结束的时间 |
| remaining_time | INT | 开奖计时 | 距离开奖的剩余时间(秒) |
| create_time | DATETIME | 创建时间 | 记录创建时间 |
| deleted_flag | INT | 删除标记 | 软删除标记 |

#### 5. t_activities_count - 活动参与统计表

用户参与各类活动的统计信息。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| user_id | BIGINT | 用户ID | 用户的唯一标识 |
| high_price | DECIMAL | 高价商品金额 | 用户参与高价商品的总金额 |
| high_price_count | INT | 高价商品次数 | 用户参与高价商品的次数 |
| low_price | DECIMAL | 低价商品金额 | 用户参与低价商品的总金额 |
| low_price_count | INT | 低价商品次数 | 用户参与低价商品的次数 |
| novice | DECIMAL | 新手活动金额 | 用户参与新手活动的总金额 |
| novice_count | INT | 新手活动次数 | 用户参与新手活动的次数 |
| update_time | DATETIME | 更新时间 | 统计数据最后更新时间 |

### 订单相关表

#### 6. t_orders - 订单主表

系统中所有订单的核心信息存储。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 订单ID | 订单的唯一标识 |
| order_sn | VARCHAR | 订单编号 | 订单的业务编号 |
| user_id | BIGINT | 用户ID | 下单用户的ID |
| goods_id | BIGINT | 商品ID | 订单商品的ID |
| sku_id | BIGINT | SKU ID | 订单商品的具体规格ID |
| activity_id | BIGINT | 活动ID | 订单参与的活动ID |
| alone_flag | BOOLEAN | 单独购买标记 | 是否为单独购买订单 |
| payable_amount | DECIMAL | 应付金额 | 订单应付的总金额 |
| amount_paid | DECIMAL | 现金支付金额 | 用现金支付的金额 |
| experience_paid | DECIMAL | 体验金支付金额 | 用体验金支付的金额 |
| points_paid | DECIMAL | 积分支付金额 | 用积分支付的金额 |
| subsidy_paid | DECIMAL | 补贴金额 | 平台补贴的金额 |
| payment_method | VARCHAR | 支付方式 | 支付方式标识 |
| payment_time | DATETIME | 支付时间 | 订单支付完成时间 |
| shipping_address_id | BIGINT | 收货地址ID | 关联t_user_address表 |
| status | VARCHAR | 订单状态 | 订单当前状态 |
| draw_result | VARCHAR | 开奖结果 | 拼团/抽奖的结果 |
| draw_time | DATETIME | 开奖时间 | 开奖或拼团结果确定时间 |
| win_option | VARCHAR | 中奖选项 | 中奖时的具体选项 |
| reward_amount | DECIMAL | 奖励金额 | 中奖时的奖励金额 |
| settle_flag | BOOLEAN | 结算标记 | 订单是否已结算 |
| refund_reason | VARCHAR | 退款原因 | 订单退款的原因说明 |
| refund_time | DATETIME | 退款时间 | 订单退款完成时间 |
| complete_time | DATETIME | 完成时间 | 订单完成时间 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| create_time | DATETIME | 创建时间 | 订单创建时间 |
| update_time | DATETIME | 更新时间 | 订单最后更新时间 |

#### 7. t_order_logistics - 订单物流表

订单的物流跟踪信息。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 物流ID | 物流记录的唯一标识 |
| order_id | BIGINT | 订单ID | 关联的订单ID |
| tracking_number | VARCHAR | 快递单号 | 快递公司的运单号 |
| courier_company | VARCHAR | 快递公司 | 承运的快递公司名称 |
| status | VARCHAR | 物流状态 | 物流当前状态 |
| tracking_info | JSON | 物流跟踪信息 | 物流轨迹的详细信息 |
| shipped_time | DATETIME | 发货时间 | 商品发货时间 |
| deliver_time | DATETIME | 送达时间 | 商品送达时间 |
| create_time | DATETIME | 创建时间 | 物流记录创建时间 |

### 用户相关表

#### 8. t_employee - 用户主表

系统用户的核心信息表，包含H5用户和管理员用户。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| employee_id | BIGINT(主键) | 用户ID | 用户的唯一标识 |
| login_name | VARCHAR | 登录名 | 用户登录时使用的账号 |
| login_pwd | VARCHAR | 登录密码 | 用户密码的加密存储 |
| actual_name | VARCHAR | 真实姓名 | 用户的真实姓名 |
| phone | VARCHAR | 手机号 | 用户的手机号码 |
| email | VARCHAR | 邮箱 | 用户的邮箱地址 |
| avatar | VARCHAR | 头像 | 用户头像图片URL |
| gender | INT | 性别 | 用户性别：0未知，1男，2女 |
| department_id | BIGINT | 部门ID | 用户所属部门（用于分级管理） |
| position_id | BIGINT | 职位ID | 用户职位 |
| administrator_flag | BOOLEAN | 管理员标记 | 是否为系统管理员 |
| disabled_flag | BOOLEAN | 禁用标记 | 账号是否被禁用 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| inviter_id | BIGINT | 邀请人ID | 邀请该用户注册的用户ID |
| child_count | INT | 下级数量 | 该用户直接邀请的用户数量 |
| novice_count | INT | 免费拼团次数 | 新用户的免费拼团机会 |
| has_real | BOOLEAN | 实名认证状态 | 是否已完成实名认证 |
| risk_level | INT | 风险等级 | 用户的风险控制等级 |
| remark | VARCHAR | 备注 | 管理员对用户的备注 |
| create_time | DATETIME | 创建时间 | 账号创建时间 |
| update_time | DATETIME | 更新时间 | 账号信息最后更新时间 |

#### 9. t_user_address - 用户地址表

用户的收货地址信息。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 地址ID | 地址记录的唯一标识 |
| user_id | BIGINT | 用户ID | 地址所属的用户ID |
| recipient_name | VARCHAR | 收货人姓名 | 收货人的真实姓名 |
| phone_number | VARCHAR | 收货人电话 | 收货人的联系电话 |
| province | VARCHAR | 省份 | 收货地址的省份 |
| city | VARCHAR | 城市 | 收货地址的城市 |
| district | VARCHAR | 区县 | 收货地址的区县 |
| address_line | VARCHAR | 详细地址 | 收货地址的具体位置 |
| is_default | BOOLEAN | 默认地址标记 | 是否为用户的默认收货地址 |
| create_time | DATETIME | 创建时间 | 地址创建时间 |

#### 10. t_invitation_records - 邀请记录表

用户邀请关系和奖励记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 记录ID | 邀请记录的唯一标识 |
| inviter_id | BIGINT | 邀请人ID | 发起邀请的用户ID |
| invitee_id | BIGINT | 被邀请人ID | 被邀请注册的用户ID |
| status | VARCHAR | 邀请状态 | 邀请关系的当前状态 |
| commission_earned | DECIMAL | 佣金收益 | 邀请人获得的佣金 |
| first_order_time | DATETIME | 首次下单时间 | 被邀请人首次下单时间 |
| first_recharge_time | DATETIME | 首次充值时间 | 被邀请人首次充值时间 |
| create_time | DATETIME | 创建时间 | 邀请关系建立时间 |

### 钱包相关表

#### 11. t_wallets - 用户钱包表

用户的虚拟钱包信息，包含余额、体验金、积分等。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| user_id | BIGINT(主键) | 用户ID | 钱包所属的用户ID |
| balance | DECIMAL | 现金余额 | 用户的可用现金余额 |
| experience_balance | DECIMAL | 体验金余额 | 平台赠送的体验金余额 |
| points | DECIMAL | 积分余额 | 用户的积分余额 |
| total_recharge | DECIMAL | 总充值金额 | 用户累计充值的总金额 |
| total_withdraw | DECIMAL | 总提现金额 | 用户累计提现的总金额 |
| status | INT | 钱包状态 | 钱包的启用状态 |
| create_time | DATETIME | 创建时间 | 钱包创建时间 |
| update_time | DATETIME | 更新时间 | 钱包最后更新时间 |

#### 12. t_wallet_transactions - 钱包交易流水表

用户钱包的所有交易记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 交易ID | 交易记录的唯一标识 |
| user_id | BIGINT | 用户ID | 交易所属的用户ID |
| type | VARCHAR | 交易类型 | 交易类型（充值、提现、支付、退款等） |
| amount | DECIMAL | 交易金额 | 交易的金额（正数为收入，负数为支出） |
| balance_after | DECIMAL | 交易后余额 | 交易后的现金余额 |
| experience_balance_after | DECIMAL | 交易后体验金余额 | 交易后的体验金余额 |
| points_after | DECIMAL | 交易后积分余额 | 交易后的积分余额 |
| mode | INT | 交易模式 | 0:现金, 1:体验金, 2:积分 |
| related_id | BIGINT | 关联ID | 关联的业务记录ID（如订单ID） |
| related_user_id | BIGINT | 关联用户ID | 相关的其他用户ID |
| description | VARCHAR | 交易说明 | 交易的详细说明 |
| create_time | DATETIME | 创建时间 | 交易发生时间 |

#### 13. t_withdrawals - 提现申请表

用户的提现申请记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 提现ID | 提现申请的唯一标识 |
| user_id | BIGINT | 用户ID | 申请提现的用户ID |
| amount | DECIMAL | 提现金额 | 用户申请提现的金额 |
| fee | DECIMAL | 手续费 | 提现收取的手续费 |
| actual_amount | DECIMAL | 实际到账金额 | 扣除手续费后的实际金额 |
| status | VARCHAR | 提现状态 | 提现申请的处理状态 |
| bank_name | VARCHAR | 银行名称 | 提现目标银行 |
| bank_account | VARCHAR | 银行账号 | 提现目标账号 |
| account_holder | VARCHAR | 账户持有人 | 银行账户的持有人姓名 |
| processed_by | BIGINT | 处理人ID | 处理该提现申请的管理员ID |
| processed_time | DATETIME | 处理时间 | 提现申请的处理时间 |
| rejection_reason | VARCHAR | 拒绝理由 | 提现被拒绝时的原因 |
| create_time | DATETIME | 创建时间 | 提现申请创建时间 |

#### 14. t_team_rewards - 团队奖励表

用户团队奖励的统计和发放记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 奖励ID | 团队奖励记录的唯一标识 |
| user_id | BIGINT | 用户ID | 获得奖励的用户ID |
| reward_date | DATE | 奖励日期 | 奖励统计的日期 |
| qualified_count | INT | 合格人数 | 团队中符合奖励条件的人数 |
| reward_amount | DECIMAL | 奖励金额 | 根据团队表现计算的奖励金额 |
| status | VARCHAR | 发放状态 | 奖励的发放状态 |
| create_time | DATETIME | 创建时间 | 奖励记录创建时间 |

---

## 系统管理表

### 15. t_department - 部门表

系统的组织架构管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| department_id | BIGINT(主键) | 部门ID | 部门的唯一标识 |
| department_name | VARCHAR | 部门名称 | 部门的显示名称 |
| parent_id | BIGINT | 父部门ID | 上级部门ID，支持部门层级 |
| manager_id | BIGINT | 部门负责人ID | 部门负责人的用户ID |
| sort | INT | 排序权重 | 部门显示的排序值 |
| create_time | DATETIME | 创建时间 | 部门创建时间 |
| update_time | DATETIME | 更新时间 | 部门信息最后更新时间 |

### 16. t_position - 职位表

系统的职位管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| position_id | BIGINT(主键) | 职位ID | 职位的唯一标识 |
| position_name | VARCHAR | 职位名称 | 职位的显示名称 |
| position_level | INT | 职位级别 | 职位的等级层次 |
| sort | INT | 排序权重 | 职位显示的排序值 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| remark | VARCHAR | 备注 | 职位说明信息 |
| create_time | DATETIME | 创建时间 | 职位创建时间 |
| update_time | DATETIME | 更新时间 | 职位信息最后更新时间 |

### 17. t_role - 角色表

系统的角色权限管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| role_id | BIGINT(主键) | 角色ID | 角色的唯一标识 |
| role_name | VARCHAR | 角色名称 | 角色的显示名称 |
| role_code | VARCHAR | 角色编码 | 角色的唯一编码 |
| remark | VARCHAR | 备注 | 角色说明信息 |
| create_time | DATETIME | 创建时间 | 角色创建时间 |
| update_time | DATETIME | 更新时间 | 角色信息最后更新时间 |

### 18. t_menu - 菜单表

系统的菜单和权限管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| menu_id | BIGINT(主键) | 菜单ID | 菜单的唯一标识 |
| menu_name | VARCHAR | 菜单名称 | 菜单的显示名称 |
| menu_type | INT | 菜单类型 | 菜单的类型分类 |
| parent_id | BIGINT | 父菜单ID | 上级菜单ID，支持菜单层级 |
| path | VARCHAR | 路由路径 | 前端路由路径 |
| component | VARCHAR | 组件路径 | 前端组件路径 |
| perms_type | INT | 权限类型 | 权限的分类类型 |
| api_perms | VARCHAR | API权限 | 后端API权限标识 |
| web_perms | VARCHAR | 前端权限 | 前端权限标识 |
| icon | VARCHAR | 图标 | 菜单显示的图标 |
| context_menu_id | BIGINT | 右键菜单ID | 关联的右键菜单 |
| frame_flag | BOOLEAN | 外链标记 | 是否为外链菜单 |
| frame_url | VARCHAR | 外链地址 | 外链菜单的URL |
| cache_flag | BOOLEAN | 缓存标记 | 页面是否需要缓存 |
| visible_flag | BOOLEAN | 显示标记 | 菜单是否可见 |
| disabled_flag | BOOLEAN | 禁用标记 | 菜单是否禁用 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| sort | INT | 排序权重 | 菜单显示的排序值 |
| create_user_id | BIGINT | 创建人ID | 创建该菜单的用户ID |
| update_user_id | BIGINT | 更新人ID | 最后更新该菜单的用户ID |
| create_time | DATETIME | 创建时间 | 菜单创建时间 |
| update_time | DATETIME | 更新时间 | 菜单最后更新时间 |

---

## 辅助功能表

### 19. t_banners - 轮播图表

首页和活动页面的轮播图管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 轮播图ID | 轮播图的唯一标识 |
| title | VARCHAR | 轮播图标题 | 轮播图的显示标题 |
| image_url | VARCHAR | 图片地址 | 轮播图图片的URL |
| link_type | VARCHAR | 链接类型 | 点击跳转的链接类型 |
| link_url | VARCHAR | 链接地址 | 点击跳转的具体地址 |
| sort_order | INT | 排序权重 | 轮播图显示的排序值 |
| status | INT | 显示状态 | 轮播图的启用状态 |
| start_time | DATETIME | 开始时间 | 轮播图开始显示的时间 |
| end_time | DATETIME | 结束时间 | 轮播图结束显示的时间 |
| create_time | DATETIME | 创建时间 | 轮播图创建时间 |

### 20. t_popups - 弹窗广告表

系统的弹窗广告管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 弹窗ID | 弹窗的唯一标识 |
| title | VARCHAR | 弹窗标题 | 弹窗的显示标题 |
| image_url | VARCHAR | 图片地址 | 弹窗图片的URL |
| link_url | VARCHAR | 链接地址 | 点击跳转的地址 |
| trigger_rules | JSON | 触发规则 | 弹窗显示的条件规则 |
| status | INT | 显示状态 | 弹窗的启用状态 |
| start_time | DATETIME | 开始时间 | 弹窗开始显示的时间 |
| end_time | DATETIME | 结束时间 | 弹窗结束显示的时间 |
| create_time | DATETIME | 创建时间 | 弹窗创建时间 |

### 21. t_message - 消息表

系统内的消息通知管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| message_id | BIGINT(主键) | 消息ID | 消息的唯一标识 |
| title | VARCHAR | 消息标题 | 消息的标题 |
| content | TEXT | 消息内容 | 消息的详细内容 |
| message_type | VARCHAR | 消息类型 | 消息的分类类型 |
| receiver_user_id | BIGINT | 接收用户ID | 消息接收者的用户ID |
| receiver_user_type | VARCHAR | 接收用户类型 | 接收者的用户类型 |
| data_id | BIGINT | 关联数据ID | 消息关联的业务数据ID |
| read_flag | BOOLEAN | 已读标记 | 消息是否已被阅读 |
| read_time | DATETIME | 阅读时间 | 消息被阅读的时间 |
| create_time | DATETIME | 创建时间 | 消息创建时间 |
| update_time | DATETIME | 更新时间 | 消息最后更新时间 |

### 22. t_feedback - 用户反馈表

用户意见反馈和建议收集。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| feedback_id | BIGINT(主键) | 反馈ID | 反馈记录的唯一标识 |
| user_id | BIGINT | 用户ID | 提交反馈的用户ID |
| user_name | VARCHAR | 用户名 | 提交反馈的用户名 |
| user_type | VARCHAR | 用户类型 | 提交反馈的用户类型 |
| feedback_content | TEXT | 反馈内容 | 用户反馈的详细内容 |
| feedback_attachment | VARCHAR | 反馈附件 | 反馈相关的附件文件 |
| create_time | DATETIME | 创建时间 | 反馈提交时间 |
| update_time | DATETIME | 更新时间 | 反馈最后更新时间 |

---

## 系统日志表

### 23. t_login_log - 登录日志表

用户登录行为的记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| login_log_id | BIGINT(主键) | 日志ID | 登录日志的唯一标识 |
| user_id | BIGINT | 用户ID | 登录用户的ID |
| user_name | VARCHAR | 用户名 | 登录用户的用户名 |
| user_type | VARCHAR | 用户类型 | 登录用户的类型 |
| login_ip | VARCHAR | 登录IP | 用户登录的IP地址 |
| login_ip_region | VARCHAR | IP归属地 | 登录IP的地理位置 |
| login_device | VARCHAR | 登录设备 | 用户登录的设备信息 |
| user_agent | VARCHAR | 用户代理 | 浏览器用户代理信息 |
| login_result | INT | 登录结果 | 登录是否成功 |
| remark | VARCHAR | 备注 | 登录相关的备注信息 |
| create_time | DATETIME | 创建时间 | 登录时间 |
| update_time | DATETIME | 更新时间 | 记录更新时间 |

### 24. t_operate_log - 操作日志表

系统操作行为的审计记录。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| operate_log_id | BIGINT(主键) | 日志ID | 操作日志的唯一标识 |
| operate_user_id | BIGINT | 操作用户ID | 执行操作的用户ID |
| operate_user_name | VARCHAR | 操作用户名 | 执行操作的用户名 |
| operate_user_type | VARCHAR | 操作用户类型 | 执行操作的用户类型 |
| module | VARCHAR | 操作模块 | 操作所属的功能模块 |
| content | VARCHAR | 操作内容 | 操作的详细描述 |
| url | VARCHAR | 请求URL | 操作对应的请求地址 |
| method | VARCHAR | 请求方法 | HTTP请求方法 |
| param | TEXT | 请求参数 | 操作的请求参数 |
| ip | VARCHAR | 操作IP | 执行操作的IP地址 |
| ip_region | VARCHAR | IP归属地 | 操作IP的地理位置 |
| user_agent | VARCHAR | 用户代理 | 浏览器用户代理信息 |
| success_flag | BOOLEAN | 成功标记 | 操作是否执行成功 |
| fail_reason | VARCHAR | 失败原因 | 操作失败时的原因说明 |
| create_time | DATETIME | 创建时间 | 操作执行时间 |
| update_time | DATETIME | 更新时间 | 记录更新时间 |

---

## 配置管理表

### 25. t_config - 系统配置表

系统全局配置参数的存储。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| config_id | BIGINT(主键) | 配置ID | 配置项的唯一标识 |
| config_key | VARCHAR | 配置键 | 配置项的唯一键名 |
| config_name | VARCHAR | 配置名称 | 配置项的显示名称 |
| config_value | TEXT | 配置值 | 配置项的具体值 |
| remark | VARCHAR | 备注 | 配置项的说明信息 |
| create_time | DATETIME | 创建时间 | 配置创建时间 |
| update_time | DATETIME | 更新时间 | 配置最后更新时间 |

### 26. t_dict - 数据字典表

系统数据字典的分类管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| dict_id | BIGINT(主键) | 字典ID | 数据字典的唯一标识 |
| dict_code | VARCHAR | 字典编码 | 数据字典的唯一编码 |
| dict_name | VARCHAR | 字典名称 | 数据字典的显示名称 |
| disabled_flag | BOOLEAN | 禁用标记 | 字典是否禁用 |
| remark | VARCHAR | 备注 | 字典说明信息 |
| create_time | DATETIME | 创建时间 | 字典创建时间 |
| update_time | DATETIME | 更新时间 | 字典最后更新时间 |

### 27. t_dict_data - 数据字典项表

数据字典的具体选项值。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| dict_data_id | BIGINT(主键) | 字典项ID | 字典项的唯一标识 |
| dict_id | BIGINT | 字典ID | 所属的数据字典ID |
| data_label | VARCHAR | 显示标签 | 字典项的显示文本 |
| data_value | VARCHAR | 数据值 | 字典项的实际值 |
| sort_order | INT | 排序权重 | 字典项的排序值 |
| disabled_flag | BOOLEAN | 禁用标记 | 字典项是否禁用 |
| remark | VARCHAR | 备注 | 字典项说明信息 |
| create_time | DATETIME | 创建时间 | 字典项创建时间 |
| update_time | DATETIME | 更新时间 | 字典项最后更新时间 |

---

## 关联关系表

### 28. t_role_employee - 用户角色关联表

用户与角色的多对多关联关系。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| id | BIGINT(主键) | 关联ID | 关联关系的唯一标识 |
| employee_id | BIGINT | 用户ID | 关联的用户ID |
| role_id | BIGINT | 角色ID | 关联的角色ID |
| create_time | DATETIME | 创建时间 | 关联关系建立时间 |
| update_time | DATETIME | 更新时间 | 关联关系最后更新时间 |

### 29. t_role_menu - 角色菜单关联表

角色与菜单权限的多对多关联关系。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| role_menu_id | BIGINT(主键) | 关联ID | 关联关系的唯一标识 |
| role_id | BIGINT | 角色ID | 关联的角色ID |
| menu_id | BIGINT | 菜单ID | 关联的菜单ID |
| create_time | DATETIME | 创建时间 | 关联关系建立时间 |
| update_time | DATETIME | 更新时间 | 关联关系最后更新时间 |

---

## 文件管理表

### 30. t_file - 文件信息表

系统上传文件的管理信息。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| file_id | BIGINT(主键) | 文件ID | 文件记录的唯一标识 |
| file_key | VARCHAR | 文件键 | 文件的唯一标识键 |
| file_name | VARCHAR | 文件名 | 文件的原始名称 |
| file_size | BIGINT | 文件大小 | 文件的字节大小 |
| file_type | VARCHAR | 文件类型 | 文件的MIME类型 |
| folder_type | VARCHAR | 文件夹类型 | 文件存储的分类文件夹 |
| creator_id | BIGINT | 上传者ID | 上传文件的用户ID |
| creator_name | VARCHAR | 上传者名称 | 上传文件的用户名 |
| creator_user_type | VARCHAR | 上传者类型 | 上传用户的类型 |
| create_time | DATETIME | 创建时间 | 文件上传时间 |
| update_time | DATETIME | 更新时间 | 文件信息最后更新时间 |

---

## 业务特色功能表

### 31. t_oa_enterprise - 企业信息表

系统支持的企业客户信息管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| enterprise_id | BIGINT(主键) | 企业ID | 企业的唯一标识 |
| enterprise_name | VARCHAR | 企业名称 | 企业的注册名称 |
| enterprise_logo | VARCHAR | 企业logo | 企业标识图片URL |
| unified_social_credit_code | VARCHAR | 统一社会信用代码 | 企业的法定识别码 |
| type | INT | 企业类型 | 企业的分类类型 |
| contact | VARCHAR | 联系人 | 企业联系人姓名 |
| contact_phone | VARCHAR | 联系电话 | 企业联系电话 |
| email | VARCHAR | 邮箱 | 企业联系邮箱 |
| province | VARCHAR | 省份 | 企业所在省份 |
| province_name | VARCHAR | 省份名称 | 省份的完整名称 |
| city | VARCHAR | 城市 | 企业所在城市 |
| city_name | VARCHAR | 城市名称 | 城市的完整名称 |
| district | VARCHAR | 区县 | 企业所在区县 |
| district_name | VARCHAR | 区县名称 | 区县的完整名称 |
| address | VARCHAR | 详细地址 | 企业的具体地址 |
| business_license | VARCHAR | 营业执照 | 营业执照图片URL |
| disabled_flag | BOOLEAN | 禁用标记 | 企业是否被禁用 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| create_user_id | BIGINT | 创建人ID | 创建该企业记录的用户ID |
| create_user_name | VARCHAR | 创建人名称 | 创建该企业记录的用户名 |
| create_time | DATETIME | 创建时间 | 企业记录创建时间 |
| update_time | DATETIME | 更新时间 | 企业信息最后更新时间 |

### 32. t_oa_bank - 银行信息表

企业和用户的银行账户信息管理。

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| bank_id | BIGINT(主键) | 银行ID | 银行记录的唯一标识 |
| bank_name | VARCHAR | 银行名称 | 银行的名称 |
| account_number | VARCHAR | 账户号码 | 银行账户号码 |
| account_name | VARCHAR | 账户名称 | 银行账户持有人姓名 |
| enterprise_id | BIGINT | 企业ID | 关联的企业ID |
| business_flag | BOOLEAN | 对公账户标记 | 是否为对公账户 |
| disabled_flag | BOOLEAN | 禁用标记 | 账户是否被禁用 |
| deleted_flag | BOOLEAN | 删除标记 | 软删除标记 |
| remark | VARCHAR | 备注 | 银行账户备注信息 |
| create_user_id | BIGINT | 创建人ID | 创建该银行记录的用户ID |
| create_user_name | VARCHAR | 创建人名称 | 创建该银行记录的用户名 |
| create_time | DATETIME | 创建时间 | 银行记录创建时间 |
| update_time | DATETIME | 更新时间 | 银行信息最后更新时间 |

---

## 业务关系总结

### 核心业务流程表关系

1. **用户注册流程**: `t_employee` → `t_wallets` → `t_invitation_records`
2. **商品浏览流程**: `t_goods` → `t_goods_skus` → `t_category` → `t_activities`
3. **订单创建流程**: `t_orders` → `t_goods` → `t_user_address` → `t_wallet_transactions`
4. **拼团参与流程**: `t_activities` → `t_orders` → `t_wallet_transactions`
5. **钱包管理流程**: `t_wallets` → `t_wallet_transactions` → `t_withdrawals`

### 权限管理表关系

1. **用户权限**: `t_employee` → `t_role_employee` → `t_role` → `t_role_menu` → `t_menu`
2. **组织架构**: `t_employee` → `t_department` → `t_position`

### 内容管理表关系

1. **首页展示**: `t_banners` → `t_popups` → `t_goods` → `t_activities`
2. **消息通知**: `t_message` → `t_employee`

---

## 数据库设计特点

### 1. 软删除设计
大部分业务表都采用 `deleted_flag` 字段实现软删除，保证数据的完整性和可追溯性。

### 2. 审计字段
所有表都包含 `create_time` 和 `update_time` 字段，便于数据追踪和审计。

### 3. JSON字段使用
在商品图片(`images`)、活动配置(`config_info`)、物流信息(`tracking_info`)等复杂数据存储中使用JSON类型，提高存储灵活性。

### 4. 多货币支持
钱包系统支持现金余额、体验金、积分等多种虚拟货币，满足不同的业务场景需求。

### 5. 分级管理
用户、部门、菜单等都支持层级结构，通过 `parent_id` 字段实现树形结构管理。

---

*本文档基于团购系统数据库表结构分析整理，涵盖了系统的所有核心业务表和辅助功能表。文档将根据系统迭代和业务需求的变化持续更新。*

*最后更新时间：2025年7月29日*