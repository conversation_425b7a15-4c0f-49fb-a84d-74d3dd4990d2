/**
 * 图片服务配置
 * 统一管理图片URL配置，避免硬编码
 */

/**
 * 动态获取图片服务器基础URL
 * 根据当前页面访问地址自动推断图片服务器地址
 */
const getImageBaseUrl = () => {
  // 优先使用环境变量配置
  if (import.meta.env.VITE_IMAGE_BASE_URL) {
    return import.meta.env.VITE_IMAGE_BASE_URL
  }
  
  // 开发环境使用本地图片服务
  if (import.meta.env.DEV) {
    return 'http://localhost'
  }
  
  // 生产环境使用生产服务器图片服务
  return 'https://pp.oripicks.com'
}

// 图片服务器配置
export const IMAGE_CONFIG = {
  // 动态获取图片服务器域名
  get baseUrl() {
    return getImageBaseUrl()
  },
  
  // 默认占位图片
  placeholderImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTMwQzEwNS41MjMgMTMwIDExMCAxMjUuNTIzIDExMCAxMjBDMTEwIDExNC40NzcgMTA1LjUyMyAxMTAgMTAwIDExMEM5NC40NzcgMTEwIDkwIDExNC40NzcgOTAgMTIwQzkwIDEyNS41MjMgOTQuNDc3IDEzMCAxMDAgMTMwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDcwSDMwVjE3MEgxNzBWNzBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTUwIDEwMEwxMzAgMTIwTDEwMCA5MEw3MCA5MEw1MCA5MEw3MCA3MEwxMDAgNzBMMTMwIDcwTDE1MCA3MEwxNzAgNzBWMTcwSDMwVjcwSDUwTDcwIDkwTDEwMCA5MEwxMzAgMTIwTDE1MCA5MEwxNzAgNzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
}

/**
 * 获取完整的图片URL
 * @param {string} imagePath - 图片路径
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (imagePath) => {
  // 如果没有图片路径，返回默认占位图
  if (!imagePath) {
    return IMAGE_CONFIG.placeholderImage
  }
  
  // 如果是完整URL，直接返回（nginx已经处理了URL替换）
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 处理相对路径 - 确保以/开头
  let cleanPath = imagePath.trim()
  
  // 确保路径以/开头
  if (!cleanPath.startsWith('/')) {
    cleanPath = '/' + cleanPath
  }
  
  // 对于相对路径，仍然需要拼接baseUrl
  const fullUrl = `${IMAGE_CONFIG.baseUrl}${cleanPath}`
  
  return fullUrl
}

/**
 * 更新图片服务器配置
 * @param {object} config - 新的配置
 */
export const updateImageConfig = (config) => {
  Object.assign(IMAGE_CONFIG, config)
}

/**
 * 获取当前图片配置
 * @returns {object} 当前配置
 */
export const getImageConfig = () => {
  return { ...IMAGE_CONFIG }
}