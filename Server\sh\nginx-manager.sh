#!/bin/bash

echo "🔧 Nginx 管理工具"
echo "=================="

# 显示菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1. 检查Nginx状态"
    echo "2. 启动Nginx"
    echo "3. 停止Nginx"
    echo "4. 重启Nginx"
    echo "5. 重新加载配置"
    echo "6. 测试配置"
    echo "7. 查看访问日志"
    echo "8. 查看错误日志"
    echo "9. 检查端口占用"
    echo "10. 显示配置文件"
    echo "0. 退出"
    echo ""
}

# 检查是否需要sudo权限
check_sudo() {
    if [ "$EUID" -ne 0 ]; then
        echo "❌ 此操作需要sudo权限"
        echo "请使用: sudo $0"
        return 1
    fi
    return 0
}

# 检查Nginx状态
check_status() {
    echo "📊 Nginx服务状态："
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx正在运行"
        systemctl status nginx --no-pager -l
    else
        echo "❌ Nginx未运行"
    fi
    
    echo ""
    echo "📡 端口占用情况："
    netstat -tlnp | grep :80 || echo "端口80未占用"
}

# 启动Nginx
start_nginx() {
    if ! check_sudo; then return 1; fi
    
    echo "🚀 启动Nginx..."
    systemctl start nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ Nginx启动成功"
    else
        echo "❌ Nginx启动失败"
        systemctl status nginx --no-pager -l
    fi
}

# 停止Nginx
stop_nginx() {
    if ! check_sudo; then return 1; fi
    
    echo "🛑 停止Nginx..."
    systemctl stop nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ Nginx停止成功"
    else
        echo "❌ Nginx停止失败"
    fi
}

# 重启Nginx
restart_nginx() {
    if ! check_sudo; then return 1; fi
    
    echo "🔄 重启Nginx..."
    systemctl restart nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ Nginx重启成功"
    else
        echo "❌ Nginx重启失败"
        systemctl status nginx --no-pager -l
    fi
}

# 重新加载配置
reload_nginx() {
    if ! check_sudo; then return 1; fi
    
    echo "🔄 重新加载Nginx配置..."
    nginx -t && systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ 配置重新加载成功"
    else
        echo "❌ 配置重新加载失败"
    fi
}

# 测试配置
test_config() {
    echo "🧪 测试Nginx配置..."
    nginx -t
    
    if [ $? -eq 0 ]; then
        echo "✅ 配置文件语法正确"
    else
        echo "❌ 配置文件存在语法错误"
    fi
}

# 查看访问日志
view_access_log() {
    echo "📖 查看访问日志 (最后20条):"
    echo "=========================="
    if [ -f /var/log/nginx/tgw-access.log ]; then
        tail -20 /var/log/nginx/tgw-access.log
    else
        echo "访问日志文件不存在"
    fi
}

# 查看错误日志  
view_error_log() {
    echo "📖 查看错误日志 (最后20条):"
    echo "=========================="
    if [ -f /var/log/nginx/tgw-error.log ]; then
        tail -20 /var/log/nginx/tgw-error.log
    else
        echo "错误日志文件不存在"
    fi
}

# 检查端口占用
check_ports() {
    echo "📡 端口占用情况："
    echo "=================="
    echo "端口80 (Nginx):"
    netstat -tlnp | grep :80 || echo "  未占用"
    echo ""
    echo "端口3000 (前端):"
    netstat -tlnp | grep :3000 || echo "  未占用"
    echo ""
    echo "端口8686 (后端):"
    netstat -tlnp | grep :8686 || echo "  未占用"
}

# 显示配置文件
show_config() {
    echo "📝 当前Nginx配置："
    echo "=================="
    if [ -f /etc/nginx/sites-available/tgw-proxy ]; then
        cat /etc/nginx/sites-available/tgw-proxy
    else
        echo "配置文件不存在: /etc/nginx/sites-available/tgw-proxy"
        echo ""
        echo "💡 请先运行安装脚本:"
        echo "sudo ./setup-nginx-proxy.sh"
    fi
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (0-10): " choice
    
    case $choice in
        1) check_status ;;
        2) start_nginx ;;
        3) stop_nginx ;;
        4) restart_nginx ;;
        5) reload_nginx ;;
        6) test_config ;;
        7) view_access_log ;;
        8) view_error_log ;;
        9) check_ports ;;
        10) show_config ;;
        0) echo "👋 再见！"; exit 0 ;;
        *) echo "❌ 无效选项，请重新选择" ;;
    esac
    
    read -p "按回车键继续..."
done