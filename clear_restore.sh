#!/bin/bash

# 清晰提示的数据库恢复脚本
# 使用方法: ./clear_restore.sh [备份文件] [数据库名]

BACKUP_DIR="DataBackup"

# 显示可用备份文件
show_backups() {
    echo "可用的备份文件:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -lt "$BACKUP_DIR"/*.sql 2>/dev/null | nl -w2 -s'. ' | head -10
    else
        echo "备份目录 $BACKUP_DIR 不存在"
        return 1
    fi
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    echo "=== 数据库恢复脚本 ==="
    echo ""
    echo "使用方法:"
    echo "  $0 [备份文件] [数据库名]"
    echo ""
    show_backups
    echo ""
    echo "示例:"
    echo "  $0 1                          # 恢复编号为1的备份文件"
    echo "  $0 backup.sql                 # 恢复指定备份文件"
    echo "  $0 backup.sql mydb            # 恢复到指定数据库"
    echo ""
    echo "密码说明:"
    echo "- 恢复过程中需要输入本地 MySQL root 用户的数据库密码"
    exit 0
fi

# 处理参数
BACKUP_INPUT="$1"
TARGET_DB="$2"

# 如果输入的是数字，按编号选择文件
if [[ "$BACKUP_INPUT" =~ ^[0-9]+$ ]]; then
    BACKUP_FILE=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | sed -n "${BACKUP_INPUT}p")
    if [ -z "$BACKUP_FILE" ]; then
        echo "错误: 编号 $BACKUP_INPUT 对应的备份文件不存在"
        show_backups
        exit 1
    fi
else
    # 处理文件名
    if [[ "$BACKUP_INPUT" == *.sql ]]; then
        if [ -f "$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_INPUT"
        elif [ -f "$BACKUP_DIR/$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        else
            echo "错误: 备份文件不存在: $BACKUP_INPUT"
            exit 1
        fi
    else
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        if [ ! -f "$BACKUP_FILE" ]; then
            echo "错误: 备份文件不存在: $BACKUP_FILE"
            exit 1
        fi
    fi
fi

# 如果没有指定数据库名，从文件名推断
if [ -z "$TARGET_DB" ]; then
    BASENAME=$(basename "$BACKUP_FILE" .sql)
    TARGET_DB=$(echo "$BASENAME" | sed 's/_[0-9]\{8\}_[0-9]\{6\}$//' | sed 's/_backup$//')
fi

echo "=== 数据库恢复配置 ==="
echo "备份文件: $BACKUP_FILE"
echo "目标数据库: $TARGET_DB"
echo "文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"
echo ""

# 确认操作
read -p "确认恢复数据库吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 检查 MySQL 连接
echo "步骤1: 检查 MySQL 连接..."
echo "提示: 请输入本地 MySQL root 用户的数据库密码"
mysql -u root -p -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到本地 MySQL"
    echo "请检查:"
    echo "1. MySQL 服务是否运行"
    echo "2. MySQL root 密码是否正确"
    echo "3. MySQL 是否已正确安装"
    exit 1
fi
echo "MySQL 连接成功"

# 检查数据库是否存在
echo "步骤2: 检查目标数据库..."
echo "提示: 请输入本地 MySQL root 用户的数据库密码"
DB_EXISTS=$(mysql -u root -p -e "SHOW DATABASES LIKE '$TARGET_DB';" 2>/dev/null | grep "$TARGET_DB")
if [ -n "$DB_EXISTS" ]; then
    echo "警告: 数据库 '$TARGET_DB' 已存在"
    echo "恢复操作将完全覆盖现有数据库的所有内容！"
    read -p "是否继续覆盖现有数据库？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
    echo "将覆盖现有数据库"
else
    echo "将创建新数据库: $TARGET_DB"
fi

# 创建/重建数据库
echo "步骤3: 准备目标数据库..."
echo "提示: 请输入本地 MySQL root 用户的数据库密码"
mysql -u root -p -e "DROP DATABASE IF EXISTS \`$TARGET_DB\`; CREATE DATABASE \`$TARGET_DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
    echo "错误: 创建数据库失败"
    echo "可能的原因:"
    echo "1. MySQL 密码错误"
    echo "2. 权限不足"
    echo "3. 数据库名称包含特殊字符"
    exit 1
fi
echo "数据库准备完成"

# 恢复数据
echo "步骤4: 恢复数据..."
echo "提示: 请输入本地 MySQL root 用户的数据库密码"
echo "正在恢复数据，请稍候..."

mysql -u root -p "$TARGET_DB" < "$BACKUP_FILE"

if [ $? -ne 0 ]; then
    echo "错误: 数据恢复失败"
    echo "可能的原因:"
    echo "1. MySQL 密码错误"
    echo "2. 备份文件损坏"
    echo "3. 备份文件格式不兼容"
    echo "4. 磁盘空间不足"
    exit 1
fi

# 验证结果
echo "步骤5: 验证恢复结果..."
echo "提示: 请输入本地 MySQL root 用户的数据库密码"
TABLE_COUNT=$(mysql -u root -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null | wc -l)
TABLE_COUNT=$((TABLE_COUNT - 1))

echo ""
echo "=== 恢复完成 ==="
echo "数据库: $TARGET_DB"
echo "表数量: $TABLE_COUNT"

if [ $TABLE_COUNT -gt 0 ]; then
    echo ""
    echo "数据库表列表:"
    echo "提示: 请输入本地 MySQL root 用户的数据库密码"
    mysql -u root -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null
    echo ""
    echo "恢复成功！数据库已准备就绪。"
else
    echo ""
    echo "警告: 未检测到表，可能的原因:"
    echo "1. 备份文件为空"
    echo "2. 备份文件只包含数据库结构，没有表"
    echo "3. 恢复过程中出现错误"
    echo ""
    echo "请手动检查数据库内容"
fi

echo ""
echo "密码说明:"
echo "- 本脚本使用的是本地 MySQL root 用户的数据库密码"
echo "- 如果忘记密码，可以重置 MySQL root 密码"
echo "- 如果需要使用其他用户，请修改脚本中的用户名"
