# 社交拼团APP页面详细设计文档

| 版本   | 日期       | 作者   | 审核人 | 变更描述                               |
| :----- | :--------- | :----- | :----- | :------------------------------------- |
| V1.0 | 2025-06-25 | Gemini |        | 基于V2.2.1版PRD创建页面详细设计文档 |
| V1.1 | 2025-06-25 | Gemini |        | 基于V2.2.2版PRD补充个人中心页面及下层页面设计 |
| V1.2 | 2025-06-25 | Gemini |        | 优化个人中心设计：充值提现按钮前置，删除订单待开奖状态 |

---

## 1. 引言

本文档旨在为"社交拼团APP"的核心页面提供详细的设计说明，内容涵盖了每个页面的**主要功能、包含元素、交互逻辑**等。本文档是 `社交拼团APP产品需求文档-简化版3.md (V2.2.2)` 的配套设计文档，需结合PRD一同阅读。

---

## 2. 页面设计详述

### 2.1. 首页 (Home Page)

*   **页面ID:** `HOME_001`
*   **入口:** 用户打开APP后进入的第一个页面。
*   **核心功能/页面元素:**
    1.  **顶部搜索栏:**
        *   **元素:** 搜索框，内含占位符文本（如"搜索你想要的宝贝"）。
        *   **功能:** 支持用户通过关键词搜索商品。
    2.  **Banner广告位:**
        *   **元素:** 可轮播的横幅广告图。
        *   **功能:** 展示平台主推的活动或高价值商品，是活动专区的重要入口。
    3.  **金刚区/活动入口:**
        *   **元素:** 2xN布局的圆形或方形图标按钮。
        *   **功能:** 提供核心活动专区（如"新手专区"、"低价好物"、"高价精品"）的快速入口。
    4.  **信息流商品推荐:**
        *   **元素:** "猜你喜欢"瀑布流，以商品卡片形式展示。
        *   **商品卡片包含:** 商品主图、商品标题、价格（或预付款金额）、参与人数。
        *   **功能:** 基于算法向用户推荐可能感兴趣的抽奖商品，是流量转化的主要区域。
*   **交互逻辑:**
    *   点击`搜索框`跳转至搜索页面。
    *   点击`Banner图`或`金刚区图标`跳转至对应的【活动专区页】。
    *   在信息流中，点击任意`商品卡片`跳转至对应的【商品详情页】。
    *   **联动:** 【拉新/活动弹窗】可能会根据特定规则在首页触发。

### 2.2. 拉新/活动弹窗 (Acquisition/Activity Pop-up)

*   **页面ID:** `POP_001`
*   **入口:** 在【首页】根据后台配置的规则自动触发（例如：新用户首次打开APP、特定营销活动开始时）。
*   **核心功能/页面元素:**
    1.  **背景/图片:** 强视觉冲击力的背景图或活动海报。
    2.  **标题/文案:** 简短、有吸引力的活动标题和描述。
    3.  **核心操作按钮 (CTA):** 一个非常醒目的按钮，如"立即参与"、"免费体验"。
    4.  **关闭按钮:** 一个不显眼的关闭图标（通常在右上角）。
*   **交互逻辑:**
    *   点击`核心操作按钮`，根据活动类型直接跳转至【商品详情页】或【订单确认页】（新手团可跳过详情页，加速转化）。
    *   点击`关闭按钮`，弹窗消失，用户可继续浏览当前页面。

### 2.3. 活动专区页 (Activity Zone Page)

*   **页面ID:** `ACTIVITY_001`
*   **入口:** 点击【首页】的Banner或金刚区入口。
*   **核心功能/页面元素:**
    1.  **顶部Tab切换栏:**
        *   **元素:** 可横向滑动的Tab，如"为你推荐"、"新手专区"、"低价好物"、"高价精品"。
        *   **功能:** 用户可通过点击切换，筛选不同类型的活动商品。
    2.  **活动商品列表:**
        *   **元素:** 与首页信息流类似的商品卡片列表。
        *   **功能:** 集中展示某一特定类型的所有抽奖活动商品。
*   **交互逻辑:**
    *   默认选中第一个Tab（如"为你推荐"）。
    *   点击不同`Tab`，下方列表内容异步刷新，展示对应分类的商品。
    *   点击任意`商品卡片`，跳转至【商品详情页】。

### 2.4. 商品详情页 (Product Details Page)

*   **页面ID:** `PDP_001`
*   **入口:** 从【首页】或【活动专区页】点击商品卡片。
*   **核心功能/页面元素:**
    1.  **媒体区:** 商品的轮播图或视频展示。
    2.  **价格与标题区:**
        *   **价格:** 突出显示参与价格（如"¥20参与"或"¥299"），并用小字注明商品原价。
        *   **标题:** 商品全名。
    3.  **抽奖规则说明区:**
        *   **元素:** 一个固定的、可展开/收起的区域，用简明语言和图标解释抽奖规则。
        *   **内容:** 清晰说明"中签"和"未中签"两种结果的处理方式。
    4.  **图文详情区:** 传统的商品长图文介绍。
    5.  **底部悬浮操作栏:**
        *   **元素:** 一个从底部弹出的固定操作栏，包含一个醒目的`"立即参与"`按钮。
*   **交互逻辑:**
    *   页面可上下滑动浏览。
    *   点击`"立即参与"`按钮，跳转至【订单确认页】。

### 2.5. 订单确认页 (Order Confirmation Page)

*   **页面ID:** `ORDER_CONFIRM_001`
*   **入口:** 点击【商品详情页】的"立即参与"按钮。
*   **核心功能/页面元素:**
    1.  **收货地址模块:**
        *   **元素:** 显示用户默认的收货人、电话、地址。
        *   **功能:** 点击可切换或新增收货地址。
    2.  **商品信息模块:**
        *   **元素:** 商品缩略图、标题、规格（如颜色、尺码，若有）、参与数量（默认为1，通常不可修改）。
    3.  **费用明细模块:**
        *   **元素:** 列表形式展示各项费用。
        *   **内容:** 商品金额、运费、**体验金抵扣**（若有，显示为负数）、**应付总额**。
    4.  **底部悬浮支付栏:**
        *   **元素:** 左侧显示"合计：¥XXX"，右侧是一个醒目的`"提交订单"`按钮。
*   **交互逻辑:**
    *   进入页面时，系统自动检查并应用可用体验金。
    *   用户确认信息无误后，点击`"提交订单"`按钮，跳转至【支付页】。

### 2.6. 支付页 (Payment Page)

*   **页面ID:** `PAY_001`
*   **入口:** 点击【订单确认页】的"提交订单"按钮。
*   **核心功能/页面元素:**
    1.  **支付倒计时:** 页面顶部显示"请在15分00秒内完成支付"，并开始倒数。
    2.  **支付金额:** 醒目地显示需要支付的总金额。
    3.  **支付方式选择:**
        *   **账户余额支付:** 显示当前可用余额，若余额充足则为首选。
        *   **第三方支付:** 列表展示所有支持的支付渠道（如ZaloPay, Momo, 银行卡）。
*   **交互逻辑:**
    *   用户选择`支付方式`后，点击确认支付。
    *   选择`账户余额支付`，可能需要输入支付密码进行验证。
    *   选择`第三方支付`，则调用相应SDK，拉起第三方APP进行支付。
    *   支付成功后，自动跳转至【开奖等待页】。
    *   支付失败或超时，弹出提示，订单自动取消，用户可选择返回首页或订单列表。

### 2.7. 开奖等待页 (Lottery Waiting Page)

*   **页面ID:** `WAITING_001`
*   **入口:** 【支付页】支付成功后。
*   **核心功能/页面元素:**
    1.  **状态提示:** 顶部大字标题"支付成功，等待开奖"。
    2.  **商品信息:** 展示用户此次参与的商品图片和标题。
    3.  **开奖倒计时:** 一个动态的倒计时组件，显示"距开奖 XX时XX分XX秒"。
    4.  **操作按钮:** 提供"查看订单详情"和"返回首页"两个按钮。
    5.  **(可选)邀请好友模块:** "邀请好友参与APP，同享好运"的分享模块，点击可分享APP而非特定团。
*   **交互逻辑:**
    *   倒计时结束后，后台执行开奖逻辑。页面可轮询或通过推送被动更新。
    *   用户下次进入APP或收到推送时，会触发【结果弹窗】。

### 2.8. 结果弹窗 (Result Pop-up)

*   **页面ID:** `POP_002`
*   **入口:** 开奖后，用户启动APP或收到推送点击后触发。
*   **核心功能/页面元素 (二选一):**
    1.  **中签样式:**
        *   **视觉:** 红色、金色等喜庆背景，标题"恭喜您已中签！"
        *   **内容:** 展示中签的商品信息。
        *   **按钮:** 根据商品类型提供不同按钮。
            *   **低价团:** "立即领取商品"、"选择折现"
            *   **高价团:** "支付尾款，领取商品"、"放弃购买"（并提示定金不退）
    2.  **未中签样式:**
        *   **视觉:** 蓝色、灰色等安慰性色调，标题"很遗憾，本次未中签"。
        *   **内容:** 清晰告知"您的支付款 ¥XXX 已退回账户余额，并获得平台补贴 ¥X.XX"。
        *   **按钮:** "再逛逛"、"查看我的钱包"。
*   **交互逻辑:**
    *   点击各按钮执行相应操作（跳转订单详情、支付尾款、跳转首页、跳转钱包等）。
    *   点击弹窗外的蒙层或关闭按钮，关闭此弹窗，并**联动触发【分享引导弹窗】**。

### 2.9. 分享引导弹窗 (Share Incentive Pop-up)

*   **页面ID:** `POP_003`
*   **入口:** 关闭【结果弹窗】后触发。
*   **核心功能/页面元素:**
    1.  **标题:** "分享给好友，一起来赢大奖！"
    2.  **内容:** "每成功邀请一位好友并完成首次消费，即可获得丰厚奖励！"
    3.  **按钮:** "立即分享"、"残忍拒绝"。
*   **交互逻辑:**
    *   点击`"立即分享"`，调用系统分享组件，分享带用自己邀请码的APP下载链接。
    *   点击`"残忍拒绝"`或关闭按钮，弹窗消失。

---

## 3. 个人中心模块页面设计

### 3.1. 个人中心首页 (User Center Home)

*   **页面ID:** `UC_HOME_001`
*   **入口:** 通过底部Tab导航或其他页面的用户头像入口进入。
*   **核心功能/页面元素:**
    1.  **用户信息头部区:**
        *   **元素:** 用户头像、昵称、个人等级标识（如"新手"、"达人"等）。
        *   **功能:** 点击头像可进入【个人资料编辑页】。
    2.  **钱包余额展示区:**
        *   **元素:** 大字显示总可用金额，下方分别显示账户余额和体验金。
        *   **视觉:** 使用渐变背景卡片，营造财富感。
        *   **功能:** 点击整个卡片可进入【我的钱包页】查看详细流水。
    3.  **充值提现操作区:**
        *   **元素:** 两个并排的大按钮，位于余额卡片正下方。
        *   **视觉:** **高度突出显示**，使用主色调渐变背景，按钮尺寸较大。
        *   **内容:** 
            *   **充值按钮**：左侧，醒目的主色调按钮，点击进入【充值页】
            *   **提现按钮**：右侧，同样醒目的按钮，点击进入【提现页】
    4.  **核心数据卡片区:**
        *   **元素:** 1x3网格布局，展示核心数据。
        *   **内容:** 
            *   **我的订单**：显示待处理订单数量，点击进入【我的订单页】
            *   **我的团队**：显示直接邀请人数，点击进入【我的团队页】
            *   **我的积分**：显示当前总积分，点击进入【我的积分页】
    5.  **功能菜单区:**
        *   **元素:** 列表形式的功能入口，每行包含图标、功能名称、右箭头。
        *   **内容:** 
            *   收货地址管理
            *   客服中心
            *   设置
            *   关于我们
    6.  **邀请推广区:**
        *   **元素:** 一个醒目的推广卡片。
        *   **内容:** "邀请好友赚钱"，展示邀请奖励规则简介。
        *   **功能:** 点击进入【邀请推广页】或直接触发分享。
*   **交互逻辑:**
    *   各卡片和菜单项点击后跳转至对应的详情页面。
    *   充值、提现按钮具有最高的视觉优先级，便于用户快速操作。
    *   页面支持下拉刷新，更新用户数据。

### 3.2. 我的钱包页 (My Wallet)

*   **页面ID:** `WALLET_001`
*   **入口:** 点击【个人中心首页】的钱包余额展示区。
*   **核心功能/页面元素:**
    1.  **钱包总览区:**
        *   **元素:** 大字显示总可用金额，下方分别显示账户余额和体验金。
        *   **视觉:** 使用渐变背景，营造财富感。
    2.  **体验金说明区:**
        *   **元素:** 可折叠的信息卡片。
        *   **内容:** 清晰说明体验金的使用规则（优先抵扣、不可提现等）。
    3.  **账单流水区:**
        *   **元素:** 时间轴形式的流水记录列表。
        *   **内容:** 每条记录包含：时间、类型图标、描述、金额变动（正负）。
        *   **分类:** 充值、订单支付、余额退款、中签后退款、未中签补贴、邀请返佣、团队奖励、提现等。
    4.  **筛选与搜索:**
        *   **元素:** 顶部的筛选标签和日期选择。
        *   **功能:** 用户可按类型或时间范围筛选账单。
*   **交互逻辑:**
    *   页面支持上拉加载更多账单记录。
    *   点击单条账单可查看详细信息。
    *   支持按类型筛选和按时间范围查询。
    *   **注意:** 充值、提现操作入口已移至【个人中心首页】，此页面专注于账单流水查看。

### 3.3. 充值页 (Recharge Page)

*   **页面ID:** `RECHARGE_001`
*   **入口:** 点击【我的钱包页】的"充值"按钮。
*   **核心功能/页面元素:**
    1.  **充值金额选择区:**
        *   **元素:** 预设金额按钮网格（如¥50、¥100、¥200、¥500等）。
        *   **功能:** 用户可快速选择常用金额。
    2.  **自定义金额输入区:**
        *   **元素:** 数字输入框。
        *   **功能:** 用户可输入任意金额（需符合最低充值限制）。
    3.  **支付方式选择区:**
        *   **元素:** 单选列表，展示所有支持的支付渠道。
        *   **内容:** ZaloPay、Momo、银行卡等，每项显示对应图标。
    4.  **充值优惠提示区:**
        *   **元素:** 活动横幅或提示文字。
        *   **内容:** 如"首次充值送10%奖励"等优惠信息。
    5.  **确认充值按钮:**
        *   **元素:** 底部固定的大按钮。
        *   **功能:** 显示"充值 ¥XXX"，点击后调用支付。
*   **交互逻辑:**
    *   选择预设金额时，自动填入输入框。
    *   输入自定义金额时，实时验证是否符合限制。
    *   选择支付方式后，确认按钮变为可点击状态。
    *   点击确认后，调用第三方支付SDK完成充值。

### 3.4. 提现页 (Withdrawal Page)

*   **页面ID:** `WITHDRAW_001`
*   **入口:** 点击【我的钱包页】的"提现"按钮。
*   **核心功能/页面元素:**
    1.  **可提现余额显示:**
        *   **元素:** 大字显示当前可提现的账户余额。
        *   **说明:** 明确标注体验金不可提现。
    2.  **提现金额输入区:**
        *   **元素:** 数字输入框和"全部提现"快捷按钮。
        *   **功能:** 用户输入提现金额，支持一键全部提现。
    3.  **提现账户选择区:**
        *   **元素:** 单选列表，展示已绑定的银行卡或电子钱包。
        *   **功能:** 用户可选择提现到账的账户，支持新增绑定。
    4.  **费用说明区:**
        *   **元素:** 信息卡片形式。
        *   **内容:** 清晰展示提现手续费率、最低提现金额、到账时间等。
    5.  **提现记录入口:**
        *   **元素:** 文字链接。
        *   **功能:** 点击可查看【提现记录页】。
    6.  **确认提现按钮:**
        *   **元素:** 底部固定按钮。
        *   **功能:** 显示"提现 ¥XXX（手续费¥X.XX）"。
*   **交互逻辑:**
    *   输入金额时，实时计算并显示手续费。
    *   验证提现金额是否符合最低限制和余额限制。
    *   提交提现申请后，跳转至提现成功页或返回钱包页。

### 3.5. 我的订单页 (My Orders)

*   **页面ID:** `ORDERS_001`
*   **入口:** 点击【个人中心首页】的"我的订单"卡片。
*   **核心功能/页面元素:**
    1.  **订单状态Tab栏:**
        *   **元素:** 可横向滑动的Tab切换栏。
        *   **内容:** "全部"、"已中签"、"未中签"、"已完成"、"已失效"。
    2.  **订单列表区:**
        *   **元素:** 卡片式订单列表，每个订单一张卡片。
        *   **订单卡片包含:**
            *   订单号、下单时间
            *   商品缩略图、标题、参与金额
            *   订单状态标签（用不同颜色区分）
            *   主要操作按钮（根据状态显示不同按钮）
    3.  **订单操作按钮:**
        *   **已中签订单:** "查看详情"、"选择处理方式"（要货/折现/补尾款）
        *   **未中签订单:** "查看详情"、"再逛逛"
        *   **已完成订单:** "查看详情"、"再次购买"
        *   **已失效订单:** "查看详情"
    4.  **空状态页:**
        *   **元素:** 当某个Tab下无订单时，显示空状态插图和引导文案。
        *   **功能:** 提供"去逛逛"按钮，引导用户回到首页。
*   **交互逻辑:**
    *   默认显示"全部"Tab。
    *   点击不同Tab，列表内容异步刷新。
    *   支持上拉加载更多订单。
    *   点击订单卡片或"查看详情"跳转至【订单详情页】。

### 3.6. 订单详情页 (Order Details)

*   **页面ID:** `ORDER_DETAIL_001`
*   **入口:** 点击【我的订单页】的订单卡片或"查看详情"按钮。
*   **核心功能/页面元素:**
    1.  **订单状态头部:**
        *   **元素:** 大字状态标题和状态图标。
        *   **内容:** 根据订单状态显示不同的标题和颜色（如"恭喜中签"、"很遗憾未中签"）。
    2.  **商品信息区:**
        *   **元素:** 商品图片、标题、规格、参与价格等基本信息。
    3.  **订单信息区:**
        *   **元素:** 表格形式展示订单详细信息。
        *   **内容:** 订单号、下单时间、支付时间、开奖时间、支付方式、收货地址等。
    4.  **费用明细区:**
        *   **元素:** 详细的费用拆解列表。
        *   **内容:** 商品金额、体验金抵扣、实付金额、补贴金额（如有）等。
    5.  **处理结果区:**
        *   **元素:** 根据订单状态显示不同内容。
        *   **中签订单:** 显示用户的选择（要货/折现）和相关金额。
        *   **未中签订单:** 显示退款和补贴的具体金额及到账时间。
    6.  **物流信息区（中签要货订单）:**
        *   **元素:** 物流状态时间轴。
        *   **内容:** 发货时间、物流公司、运单号、配送状态等。
    7.  **操作按钮区:**
        *   **元素:** 根据订单状态显示相应的操作按钮。
        *   **功能:** 如"补交尾款"、"申请退货"、"确认收货"、"联系客服"等。
*   **交互逻辑:**
    *   页面内容根据订单状态动态展示。
    *   物流信息支持点击查看详细轨迹。
    *   操作按钮点击后执行相应业务逻辑或跳转。

### 3.7. 我的团队页 (My Team)

*   **页面ID:** `TEAM_001`
*   **入口:** 点击【个人中心首页】的"我的团队"卡片。
*   **核心功能/页面元素:**
    1.  **团队数据总览区:**
        *   **元素:** 卡片式数据展示区域。
        *   **内容:** 
            *   直接邀请人数（大字显示）
            *   累计邀请收益（醒目显示）
            *   本月新增人数
            *   本月收益
    2.  **收益构成说明区:**
        *   **元素:** 可展开的信息卡片。
        *   **内容:** 详细说明各种收益的来源和计算规则（消费返佣、团队奖励等）。
    3.  **邀请推广区:**
        *   **元素:** 醒目的推广卡片。
        *   **内容:** "继续邀请好友，赚取更多收益"，包含邀请链接和二维码。
        *   **功能:** 一键分享邀请链接或保存二维码图片。
    4.  **团队成员列表区:**
        *   **元素:** 成员卡片列表。
        *   **成员卡片包含:**
            *   头像、昵称（脱敏处理）
            *   注册时间
            *   消费状态标签（"已消费"/"未消费"）
            *   为我带来的收益金额
    5.  **筛选与搜索区:**
        *   **元素:** 顶部筛选标签。
        *   **功能:** 可按"全部"、"已消费"、"未消费"筛选成员。
*   **交互逻辑:**
    *   页面支持下拉刷新团队数据。
    *   成员列表支持上拉加载更多。
    *   点击邀请推广区可直接分享或查看邀请规则详情。

### 3.8. 我的积分页 (My Points)

*   **页面ID:** `POINTS_001`
*   **入口:** 点击【个人中心首页】的"我的积分"卡片。
*   **核心功能/页面元素:**
    1.  **积分总览区:**
        *   **元素:** 大字显示当前总积分，配以积分图标。
        *   **视觉:** 使用金色主题，营造价值感。
    2.  **积分规则说明区:**
        *   **元素:** 信息卡片。
        *   **内容:** 清晰说明积分获取规则（如1元消费=10积分）。
    3.  **积分商城入口区:**
        *   **元素:** 推广横幅。
        *   **内容:** "积分商城即将上线，敬请期待"或直接跳转积分商城。
        *   **功能:** 未来可跳转至积分兑换页面。
    4.  **积分明细区:**
        *   **元素:** 时间轴形式的积分变动记录。
        *   **内容:** 每条记录包含时间、获取方式、积分变动数量。
        *   **分类:** 消费获得、活动奖励、兑换消耗等。
    5.  **筛选功能:**
        *   **元素:** 顶部筛选标签。
        *   **功能:** 可按"全部"、"获得"、"消耗"筛选记录。
*   **交互逻辑:**
    *   页面支持下拉刷新积分数据。
    *   积分明细支持上拉加载更多记录。
    *   点击单条记录可查看详细信息。

### 3.9. 邀请推广页 (Invitation Promotion)

*   **页面ID:** `INVITE_001`
*   **入口:** 点击【个人中心首页】的邀请推广区或【我的团队页】的推广卡片。
*   **核心功能/页面元素:**
    1.  **推广收益预览区:**
        *   **元素:** 醒目的收益展示卡片。
        *   **内容:** "每成功邀请1人，最高可获得XXX元奖励"。
    2.  **邀请规则详情区:**
        *   **元素:** 可展开的规则说明卡片。
        *   **内容:** 详细的邀请奖励规则：
            *   新用户注册奖励
            *   消费返佣比例（0.5%）
            *   团队人数奖励规则
            *   有效用户转盘抽奖机会
    3.  **邀请工具区:**
        *   **元素:** 多种分享方式的按钮组。
        *   **内容:** 
            *   **复制邀请链接**：一键复制专属邀请链接
            *   **分享到微信**：调用微信分享
            *   **分享到朋友圈**：生成分享海报
            *   **保存二维码**：保存邀请二维码图片
    4.  **邀请码展示区:**
        *   **元素:** 用户专属邀请码的显示和复制功能。
    5.  **邀请记录区:**
        *   **元素:** 最近邀请记录的简要列表。
        *   **功能:** 点击"查看全部"跳转至【我的团队页】。
*   **交互逻辑:**
    *   各分享按钮点击后调用相应的系统分享功能。
    *   复制功能执行后给出Toast提示。
    *   页面支持下拉刷新最新的邀请数据。

### 3.10. 客服中心页 (Customer Service)

*   **页面ID:** `SERVICE_001`
*   **入口:** 点击【个人中心首页】功能菜单中的"客服中心"。
*   **核心功能/页面元素:**
    1.  **常见问题区:**
        *   **元素:** 可折叠的FAQ列表。
        *   **内容:** 按分类整理的常见问题和答案（如抽奖规则、支付问题、提现问题等）。
    2.  **联系方式区:**
        *   **元素:** 联系方式卡片。
        *   **内容:** 
            *   在线客服按钮（集成第三方客服系统）
            *   客服电话（可一键拨打）
            *   客服邮箱
            *   工作时间说明
    3.  **意见反馈区:**
        *   **元素:** 反馈表单。
        *   **内容:** 问题类型选择、问题描述输入框、联系方式输入框。
        *   **功能:** 用户可直接提交问题反馈。
    4.  **帮助中心入口:**
        *   **元素:** 链接按钮。
        *   **功能:** 跳转至更详细的帮助文档页面。
*   **交互逻辑:**
    *   FAQ支持关键词搜索。
    *   点击在线客服按钮调用第三方客服SDK。
    *   意见反馈表单支持图片上传。
    *   提交反馈后显示成功提示。

---

**文档结束** 