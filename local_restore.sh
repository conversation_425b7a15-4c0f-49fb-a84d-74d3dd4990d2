#!/bin/bash

# 本地数据库恢复脚本
# 使用方法: ./local_restore.sh [备份文件] [数据库名] [MySQL用户]

BACKUP_DIR="DataBackup"
DEFAULT_USER="root"

# 显示可用备份文件
show_backups() {
    echo "📋 可用的备份文件:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -lt "$BACKUP_DIR"/*.sql 2>/dev/null | nl -w2 -s'. ' | head -10
    elif [ -d "../$BACKUP_DIR" ]; then
        ls -lt "../$BACKUP_DIR"/*.sql 2>/dev/null | nl -w2 -s'. ' | head -10
        BACKUP_DIR="../$BACKUP_DIR"
    else
        echo "❌ 备份目录不存在"
        echo "请确保备份文件在 DataBackup 目录中"
        return 1
    fi
}

# 检查MySQL安装和服务
check_mysql() {
    echo "🔍 检查MySQL环境..."
    
    # 检查MySQL是否安装
    if ! command -v mysql &> /dev/null; then
        echo "❌ MySQL未安装"
        echo ""
        echo "📋 安装MySQL的命令:"
        echo "   sudo apt update"
        echo "   sudo apt install mysql-server"
        echo "   sudo systemctl start mysql"
        echo "   sudo mysql_secure_installation"
        return 1
    fi
    
    # 检查MySQL服务状态
    if ! sudo systemctl is-active --quiet mysql 2>/dev/null; then
        echo "⚠️  MySQL服务未运行，尝试启动..."
        sudo systemctl start mysql
        if [ $? -eq 0 ]; then
            echo "✅ MySQL服务已启动"
        else
            echo "❌ 无法启动MySQL服务"
            echo ""
            echo "📋 手动启动命令:"
            echo "   sudo systemctl start mysql"
            echo "   sudo systemctl status mysql"
            return 1
        fi
    else
        echo "✅ MySQL服务正在运行"
    fi
    
    return 0
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    echo "=== 本地数据库恢复脚本 ==="
    echo ""
    echo "🔧 使用方法:"
    echo "  $0 [备份文件] [数据库名] [MySQL用户]"
    echo ""
    show_backups
    echo ""
    echo "📝 示例:"
    echo "  $0 1                          # 恢复编号为1的备份文件"
    echo "  $0 backup.sql                 # 恢复指定备份文件"
    echo "  $0 backup.sql mydb            # 恢复到指定数据库"
    echo "  $0 backup.sql mydb admin      # 使用指定MySQL用户"
    echo ""
    echo "💡 提示:"
    echo "  - 默认MySQL用户: root"
    echo "  - 需要输入MySQL密码进行恢复"
    exit 0
fi

# 检查MySQL环境
if ! check_mysql; then
    exit 1
fi

# 处理参数
BACKUP_INPUT="$1"
TARGET_DB="$2"
MYSQL_USER="${3:-$DEFAULT_USER}"

# 如果输入的是数字，按编号选择文件
if [[ "$BACKUP_INPUT" =~ ^[0-9]+$ ]]; then
    BACKUP_FILE=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | sed -n "${BACKUP_INPUT}p")
    if [ -z "$BACKUP_FILE" ]; then
        echo "❌ 编号 $BACKUP_INPUT 对应的备份文件不存在"
        show_backups
        exit 1
    fi
else
    # 处理文件名
    if [[ "$BACKUP_INPUT" == *.sql ]]; then
        if [ -f "$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_INPUT"
        elif [ -f "$BACKUP_DIR/$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        else
            echo "❌ 备份文件不存在: $BACKUP_INPUT"
            exit 1
        fi
    else
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        if [ ! -f "$BACKUP_FILE" ]; then
            echo "❌ 备份文件不存在: $BACKUP_FILE"
            exit 1
        fi
    fi
fi

# 如果没有指定数据库名，从文件名推断
if [ -z "$TARGET_DB" ]; then
    BASENAME=$(basename "$BACKUP_FILE" .sql)
    TARGET_DB=$(echo "$BASENAME" | sed 's/_[0-9]\{8\}_[0-9]\{6\}$//' | sed 's/_backup$//')
fi

echo "=== 本地数据库恢复配置 ==="
echo "📁 备份文件: $BACKUP_FILE"
echo "🗄️  目标数据库: $TARGET_DB"
echo "👤 MySQL用户: $MYSQL_USER"
echo "📊 文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"
echo ""

# 确认操作
read -p "确认要恢复数据库吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

# 检查 MySQL 连接
echo "🔍 步骤1: 检查 MySQL 连接..."
echo ">>> 请输入MySQL用户 '$MYSQL_USER' 的密码:"
mysql -u "$MYSQL_USER" -p -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到 MySQL"
    echo ""
    echo "📋 可能的解决方案:"
    echo "1. 检查MySQL密码是否正确"
    echo "2. 重置MySQL root密码:"
    echo "   sudo mysql"
    echo "   ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '新密码';"
    echo "   FLUSH PRIVILEGES;"
    echo "   EXIT;"
    echo "3. 检查MySQL服务状态:"
    echo "   sudo systemctl status mysql"
    exit 1
fi
echo "✅ MySQL连接成功"

# 检查数据库是否存在
echo "🔍 步骤2: 检查目标数据库..."
echo ">>> 请输入MySQL密码:"
DB_EXISTS=$(mysql -u "$MYSQL_USER" -p -e "SHOW DATABASES LIKE '$TARGET_DB';" 2>/dev/null | grep "$TARGET_DB")
if [ -n "$DB_EXISTS" ]; then
    echo "⚠️  数据库 '$TARGET_DB' 已存在"
    echo "🚨 恢复操作将完全覆盖现有数据库的所有内容！"
    read -p "是否继续覆盖现有数据库？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        exit 0
    fi
    echo "⚠️  将覆盖现有数据库"
else
    echo "✅ 将创建新数据库: $TARGET_DB"
fi

# 创建/重建数据库
echo "🔧 步骤3: 准备目标数据库..."
echo ">>> 请输入MySQL密码:"
mysql -u "$MYSQL_USER" -p -e "DROP DATABASE IF EXISTS \`$TARGET_DB\`; CREATE DATABASE \`$TARGET_DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
    echo "❌ 创建数据库失败"
    exit 1
fi
echo "✅ 数据库准备完成"

# 恢复数据
echo "📥 步骤4: 恢复数据..."
echo ">>> 请输入MySQL密码:"
echo "⏳ 正在恢复数据，请稍候..."

mysql -u "$MYSQL_USER" -p "$TARGET_DB" < "$BACKUP_FILE"

if [ $? -ne 0 ]; then
    echo "❌ 数据恢复失败"
    exit 1
fi

# 验证结果
echo "✅ 步骤5: 验证恢复结果..."
echo ">>> 请输入MySQL密码:"
TABLE_COUNT=$(mysql -u "$MYSQL_USER" -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null | wc -l)
TABLE_COUNT=$((TABLE_COUNT - 1))

echo ""
echo "🎉 恢复完成!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🗄️  数据库: $TARGET_DB"
echo "📊 表数量: $TABLE_COUNT"

if [ $TABLE_COUNT -gt 0 ]; then
    echo ""
    echo "📋 数据库表列表:"
    echo ">>> 请输入MySQL密码:"
    mysql -u "$MYSQL_USER" -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null
    echo ""
    echo "✅ 恢复成功！数据库已准备就绪。"
else
    echo ""
    echo "⚠️  警告: 未检测到表"
    echo "📋 可能的原因:"
    echo "1. 备份文件为空或损坏"
    echo "2. 备份文件只包含数据库结构"
    echo "3. 恢复过程中出现错误"
fi

echo ""
echo "💡 使用提示:"
echo "   - 连接数据库: mysql -u $MYSQL_USER -p"
echo "   - 使用数据库: USE $TARGET_DB;"
echo "   - 查看表: SHOW TABLES;"
