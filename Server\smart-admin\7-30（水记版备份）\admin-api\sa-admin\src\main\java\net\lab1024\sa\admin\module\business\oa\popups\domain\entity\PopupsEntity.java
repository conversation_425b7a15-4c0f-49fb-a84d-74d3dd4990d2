package net.lab1024.sa.admin.module.business.oa.popups.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Map;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

/**
 * 弹窗管理 实体类
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Data
@TableName(value = "t_popups", autoResultMap = true)
public class PopupsEntity {

    /**
     * 弹窗ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 弹窗标题
     */
    private String title;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 跳转链接
     */
    private String linkUrl;

    /**
     * 触发规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> triggerRules;

    /**
     * 状态 1 开, 0 关
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
