# 个人中心钱包和团队API数据获取问题修复方案

## 问题描述

用户反馈：在个人中心页面中，"我的钱包"和"我的团队"这两部分的API获取数据有问题。

## 问题分析

### 根本原因

1. **API调用不一致**：ProfilePage.vue混合使用了直接fetch和StandardApiAdapter
2. **缺失团队API方法**：StandardApiAdapter没有`getMyTeam`相关方法
3. **重复的钱包API逻辑**：ProfilePage中实现了与StandardApiAdapter重复的钱包API调用逻辑
4. **错误处理不统一**：不同API调用的错误处理逻辑不一致

### 具体表现

**钱包API问题**：
- ProfilePage直接使用fetch调用`/api/v1/wallet`
- StandardApiAdapter已有完整的`getWallet`方法
- 重复实现导致维护困难

**团队API问题**：
- ProfilePage直接使用fetch调用`/api/v1/myTeam`
- StandardApiAdapter缺少`getMyTeam`方法
- 没有统一的团队数据获取接口

## 修复方案

### 1. 新增团队相关API方法到StandardApiAdapter

**文件**：`APP/src/api/standardAdapter.js`

**新增内容**：
```javascript
// ==================== 团队相关接口 ====================

/**
 * 获取我的团队信息
 * @param {Object} params - 查询参数
 * @returns {Promise} 团队信息
 */
async getMyTeam(params = {}) {
  const token = this.getToken()
  
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  }

  // 添加认证头（如果有token）
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
    config.headers['token'] = token
  }

  // 处理查询参数...
  // 完整的错误处理和响应格式化...
}

/**
 * 获取团队成员列表
 */
async getTeamMembers(params = {}) {
  return await this.request('GET', '/team/members', params)
}

/**
 * 获取团队统计信息
 */
async getTeamStats() {
  return await this.request('GET', '/team/stats')
}
```

### 2. 重构ProfilePage钱包数据获取逻辑

**文件**：`APP/src/views/user/ProfilePage.vue`

**修改前**：
```javascript
// 直接使用fetch调用API
const response = await fetch('/api/v1/wallet', {
  method: 'GET',
  headers: headers,
  mode: 'cors',
  credentials: 'omit',
  cache: 'no-store'
})

const result = await response.json()
// 复杂的响应处理逻辑...
```

**修改后**：
```javascript
// 使用StandardApiAdapter获取钱包信息
const params = {}
if (forceRefresh) {
  params._t = new Date().getTime()
}

const result = await apiAdapter.getWallet(params)
// 统一的响应处理...
```

### 3. 重构ProfilePage团队数据获取逻辑

**修改前**：
```javascript
// 直接使用fetch调用API
const response = await fetch('/api/v1/myTeam', {
  method: 'GET',
  headers: headers,
  mode: 'cors',
  credentials: 'omit'
})

const result = await response.json()
// 手动处理响应格式...
```

**修改后**：
```javascript
// 使用StandardApiAdapter获取团队数据
const result = await apiAdapter.getMyTeam()
// 统一的数据结构处理...
```

### 4. 统一错误处理逻辑

**优化前**：
```javascript
} else if (result.code === 30007 || result.code === 401 || result.code === 403) {
  console.error('🔐 钱包API认证失败:', result)
  await recheckAuth()
  return
}
```

**优化后**：
```javascript
} catch (error) {
  // 处理认证相关错误
  if (error.message?.includes('登录已过期') || 
      error.message?.includes('认证失败') ||
      error.message?.includes('token')) {
    console.log('🔐 检测到认证错误，触发重新检查')
    await recheckAuth()
    return
  }
  // 其他错误处理...
}
```

### 5. 优化实名认证状态获取

也将实名认证状态获取改为使用StandardApiAdapter：

```javascript
// 使用StandardApiAdapter获取钱包信息（其中包含实名认证状态）
const result = await apiAdapter.getWallet()
const balance = result.data?.balance || {}
const hasReal = balance.hasReal === true || balance.hasReal === 1
```

## 修复效果

### 修复前的问题
1. ❌ 混合使用fetch和StandardApiAdapter，代码不一致
2. ❌ 缺少团队API的标准化方法
3. ❌ 重复的API调用逻辑，维护困难
4. ❌ 错误处理逻辑分散，不易维护

### 修复后的效果
1. ✅ 统一使用StandardApiAdapter，代码一致性好
2. ✅ 完整的团队API方法集合
3. ✅ 消除重复代码，统一维护
4. ✅ 统一的错误处理和认证检查
5. ✅ 更好的调试日志和错误信息
6. ✅ 保持向后兼容性

## 技术细节

### API调用流程优化

**钱包API流程**：
1. 检查登录状态（使用认证守卫）
2. 调用`apiAdapter.getWallet(params)`
3. 统一的响应格式处理
4. 标准化的错误处理

**团队API流程**：
1. 检查登录状态
2. 调用`apiAdapter.getMyTeam()`
3. 解析团队统计和成员排行榜数据
4. 统一的错误处理

### 数据结构统一

**钱包数据结构**：
```javascript
walletData.value = {
  balance: rawData.balance?.balance || 0,
  totalBalance: rawData.balance?.totalBalance || 0,
  experienceBalance: rawData.balance?.experienceBalance || 0,
  points: rawData.balance?.points || 0,
  totalEarnings: rawData.balance?.totalEarnings || 0,
  currency: 'VND',
  _lastUpdated: new Date().toISOString(),
  _raw: rawData
}
```

**团队数据结构**：
```javascript
teamStats.value = {
  totalMembers: data.total || 0,
  totalRewardPoints: data.totalTeamReward || 0,
  activeMembers: data.total || 0,
  thisWeekNewMembers: data.weekCount || 0,
  thisMonthRewards: data.totalTeamReward || 0
}
```

### 错误处理增强

1. **认证错误自动处理**：检测到登录过期自动触发重新认证
2. **网络错误友好提示**：区分网络错误和业务错误
3. **静默失败**：非关键功能（如团队数据）失败不影响主要功能
4. **详细日志**：提供完整的调试信息

## 测试验证

### 前端服务启动
```bash
npm run dev
# ✅ 服务成功启动在端口3002
```

### 功能测试清单

**基本功能测试**：
- [ ] 个人中心页面正常加载
- [ ] 钱包信息正确显示
- [ ] 团队数据正确显示
- [ ] 实名认证状态正确

**API调用测试**：
- [ ] `/api/v1/wallet` 调用正常
- [ ] `/api/v1/myTeam` 调用正常
- [ ] 错误响应处理正确
- [ ] 认证失败自动处理

**边界情况测试**：
- [ ] 网络断开时的处理
- [ ] Token过期时的处理
- [ ] 服务器错误时的处理
- [ ] 数据为空时的显示

### 性能优化

1. **减少重复请求**：钱包和实名认证状态使用同一个API
2. **统一缓存策略**：通过StandardApiAdapter统一管理
3. **错误恢复**：认证失败后自动重试
4. **代码复用**：消除重复的API调用逻辑

## 部署说明

### 生产环境部署

1. **代码审查**：确保所有API调用都使用StandardApiAdapter
2. **接口测试**：验证钱包和团队API的正确性
3. **错误监控**：关注认证相关的错误日志

### 监控建议

1. **API成功率监控**：监控钱包和团队API的成功率
2. **错误日志分析**：分析认证失败的原因
3. **用户体验监控**：监控页面加载时间和错误率

## 总结

本次修复通过以下几个方面解决了个人中心API数据获取问题：

1. **技术层面**：统一API调用方式，消除代码重复
2. **架构层面**：完善StandardApiAdapter的功能覆盖
3. **用户体验**：提供更好的错误处理和恢复机制
4. **代码质量**：提高代码的一致性和可维护性

修复后的个人中心页面具有更好的稳定性和用户体验，API调用更加规范和统一。