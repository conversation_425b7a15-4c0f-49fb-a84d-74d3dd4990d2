# 社交拼团APP产品需求文档 (PRD) - 简化版

| 版本 | 日期       | 作者   | 审核人 | 变更描述     |
| :--- | :--------- | :----- | :----- | :----------- |
| V1.0 | 2023-10-27 | Gemini |        | 初始版本创建 |

---

## 1. 引言 (Introduction)

### 1.1. 文档目的

本文档旨在明确"社交拼团APP"的产品需求，定义其核心功能、业务逻辑、用户角色及非功能性需求。旨在为产品、设计、开发、测试等团队提供统一的指导和依据，确保产品能够高效、高质量地完成。

### 1.2. 产品愿景

打造一个以社交拼团为核心模式的直营电商平台。我们的愿景是：

* **对用户：** 成为用户通过社交分享发现和购买高性价比商品的首选入口，提供"更低价格、更好服务"的购物体验。
* **对平台：** 构建一个高效、精选、可持续发展的B2C直营商业生态系统。

### 1.3. 目标用户

- **C端用户 (消费者):**
  - **价格敏感型用户：** 追求高性价比，乐于发现和分享优惠。
  - **社交活跃型用户：** 享受与朋友共同购物的乐趣，乐于在社交网络中传播。
  - **品质生活追求者：** 相信平台精选的商品，寻求高品质的直营产品。

### 1.4. 名词解释

| 术语                                     | 解释                                                                                                               |
| :--------------------------------------- | :----------------------------------------------------------------------------------------------------------------- |
| **SPU (Standard Product Unit)**    | 标准化产品单元，如 "某品牌T恤"。                                                                                   |
| **SKU (Stock Keeping Unit)**       | 库存量单位，如 "某品牌T恤 白色 L码"。                                                                              |
| **拼团**                           | 由一人开团，邀请好友在规定时间内成团，成功后享受优惠价。                                                           |
| **幸运拼团**                       | 一种7人参与的特殊拼团模式。由平台发起，集齐7人后倒计时开奖，随机1人成功购买商品，其余6人根据管理员设置的比例退款。 |
| **账户余额**                       | 用户在平台内的虚拟资金账户，主要用于接收退款和支付订单。                                                           |
| **GMV (Gross Merchandise Volume)** | 商品交易总额，是衡量电商平台规模的核心指标。                                                                       |

---

## 2. 产品功能 (Product Features)

### 2.1. 功能总览 (Feature Overview)

```mermaid
graph TD;
    A["社交拼团APP平台"] --> B["C端用户APP"];
    A --> E["平台管理后台 (Web)"];
    A --> F["通用底层服务"];

    subgraph C端用户APP
        B1["用户中心 (资料, 订单, 收藏)"];
        B2["首页浏览 (搜索, 推荐, 分类)"];
        B3["交易流程 (下单, 支付, 物流)"];
        B4["核心玩法 (社交拼团)"];
    end

    subgraph 平台管理后台
        E1["用户管理"];
        E2["商品与库存管理"];
        E3["订单与售后管理"];
        E4["营销与内容配置"];
        E5["数据分析报表"];
    end

    subgraph 通用底层服务
        F1["LBS定位服务"];
        F2["支付网关"];
        F3["消息推送"];
        F4["智能推荐引擎"];
        F5["仓储物流接口"];
    end

    B --> B1;
    B --> B2;
    B --> B3;
    B --> B4;
  
    E --> E1;
    E --> E2;
    E --> E3;
    E --> E4;
    E --> E5;

    F --> F1;
    F --> F2;
    F --> F3;
    F --> F4;
    F --> F5;
```

### 2.2. 用户角色与权限 (User Roles & Permissions)

| 角色                      | 核心权限                                                                                                |
| :------------------------ | :------------------------------------------------------------------------------------------------------ |
| **消费者 (未登录)** | 浏览首页、商品列表、详情页；查看评价；无法下单、评论、收藏。                                            |
| **消费者 (已登录)** | 拥有未登录用户所有权限；下单支付；发表评价；管理订单；收藏商品；发起或参与拼团。                        |
| **平台管理员**      | 访问平台管理后台；管理用户；管理商品(SPU/SKU)、库存、分类；管理订单；配置营销活动；系统设置；数据监控。 |

### 2.3. 功能详述 (Detailed Features)

#### 2.3.1. C端 - 消费者APP (iOS/Android)

**模块一：用户中心模块 (UC)**

| 功能ID | 功能名称     | 用户故事                                                         | 功能描述                                                                                                                                                                                                                                                    | 优先级 |
| :----- | :----------- | :--------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| UC-001 | 注册/登录    | 作为新用户，我希望能通过手机号快速注册，并通过密码或验证码登录。 | 1. 支持（国家区号）+手机号+验证码注册/登录。`<br>`2. 支持Zalo、Facebook等第三方授权登录。`<br>`3. 包含《用户协议》和《隐私政策》的同意流程。                                                                                                            | 高     |
| UC-002 | 个人资料管理 | 作为用户，我希望能编辑我的昵称、头像和收货地址。                 | 1. 用户可自定义头像、昵称等。`<br>`2. 收货地址管理：支持新增、删除、修改、设置默认地址。                                                                                                                                                                  | 高     |
| UC-003 | 我的订单     | 作为用户，我希望能方便地查看我所有的订单状态。                   | 1. 列表展示所有订单，按"待付款"、"待成团"、"待发货"、"待收货"、"待评价"、"退款/售后"等状态分类。`<br>`2. 订单详情页展示商品信息、金额、支付方式、物流信息。                                                                                               | 高     |
| UC-004 | 收藏/足迹    | 作为用户，我想收藏感兴趣的商品，并能看到我最近浏览过的记录。     | 1. "我的收藏"：可收藏商品。`<br>`2. "我的足迹"：按时间倒序展示最近浏览过的商品。                                                                                                                                                                          | 中     |
| UC-005 | 优惠券/红包  | 作为用户，我想查看我领取的平台优惠券和红包。                     | 列表展示可用、已使用、已过期的优惠券和红包，并显示使用规则。                                                                                                                                                                                                | 高     |
| UC-006 | 我的钱包     | 作为用户，我希望能管理我的账户余额和查看所有资金流水。           | 1.**余额展示**：清晰显示当前账户可用余额。`<br>`2. **支付密码**：支持设置和修改支付密码，用于余额支付时的身份验证。`<br>`3. **账单/消费记录**：提供详细的资金流水列表，记录每一笔收入（如退款）和支出（如订单支付），并可按类型筛选。 | 高     |

**模块二：首页及浏览模块 (Browse)**

| 功能ID | 功能名称         | 用户故事                                                 | 功能描述                                                                                                                                                                                                                                                                                                                                                                                                                                                              | 优先级 |
| :----- | :--------------- | :------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| BR-001 | 首页             | 作为用户，我希望首页内容丰富，能快速找到我感兴趣的东西。 | 1.**顶部搜索框**：支持关键词搜索。`<br>`2. **Banner广告位**：展示平台精选活动或商品。`<br>`3. **金刚区**：按商品品类（如服饰、家居、数码等）分类的快捷入口。`<br>`4. **信息流推荐**：基于算法的"猜你喜欢"瀑布流，展示精选拼团商品。                                                                                                                                                                                                     | 高     |
| BR-002 | 搜索功能         | 作为用户，我希望能通过关键词搜索到我想要的商品。         | 1. 提供搜索历史和热门搜索推荐。`<br>`2. 搜索结果页支持排序（综合、销量、价格）、筛选（品类、价格区间）。                                                                                                                                                                                                                                                                                                                                                            | 高     |
| BR-003 | 商品详情页 (PDP) | 作为用户，我想了解商品的全部信息，以决定是否购买。       | 1.**商品信息**：轮播图、视频、标题、价格（原价、拼团价）、已售数量。`<br>`2. **拼团规则**：根据商品参与的活动类型，展示不同规则。`<br>`- **社交拼团**：显示开团/参团按钮、人数要求、剩余时间、好友分享等。`<br>`- **幸运拼团**：显示参与按钮、7人团规则说明（1人中奖，6人退款）、当前参与人数、开奖倒计时机制等。`<br>`3. **用户评价**：展示其他用户的图文评价和评分。`<br>`4. **图文详情**：由平台上传的官方详细介绍。 | 高     |

**模块三：交易流程模块 (Transaction)**

| 功能ID | 功能名称     | 用户故事                                                                                 | 功能描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 优先级 |
| :----- | :----------- | :--------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| TR-001 | 下单流程     | 作为用户，我选好商品后，希望能快速完成下单。                                             | 1. 选择SKU（规格、数量）。`<br>`2. 确认订单信息：商品、数量、金额、收货地址、联系电话。`<br>`3. 选择优惠券/红包进行抵扣。`<br>`4. 提交订单，进入支付选择页面。                                                                                                                                                                                                                                                                                                                                                                                                 | 高     |
| TR-002 | 支付功能     | 作为用户，我希望能用我习惯的支付方式安全地付款。                                         | 1.**余额支付**：优先使用账户余额进行支付（需输入支付密码），若余额不足，则显示剩余应付金额。`<br>`2. **组合支付**：用户可选择第三方支付渠道（如ZaloPay, Momo, 银行卡）来支付全部金额或余额不足部分的剩余金额。`<br>`3. 支付有时效限制（如15分钟），超时自动取消订单。`<br>`4. 支付成功/失败有明确的页面提示。                                                                                                                                                                                                                                      | 高     |
| TR-003 | 社交拼团玩法 | 作为用户，我想发起或参与拼团，用更低的价格买到商品。                                     | 1. 用户可选择"单独购买"或"发起拼团"。`<br>`2. 开团后，生成专属分享链接/海报，邀请好友参团。`<br>`3. 在规定时间内凑齐人数则拼团成功，否则自动退款。`<br>`4. 用户也可直接加入他人已开的团。                                                                                                                                                                                                                                                                                                                                                                      | 高     |
| TR-004 | 物流跟踪     | 作为用户，我希望能追踪我的包裹。                                                         | 平台发货后，订单状态变为"待收货"，用户可在订单详情中查看物流轨迹。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 高     |
| TR-005 | 退款/售后    | 作为用户，如果我不满意，希望能方便地申请退款。                                           | 1. 用户在订单页发起退款/售后申请，填写原因和凭证。`<br>`2. 平台管理员审核后，原路退回款项至**用户账户余额**。                                                                                                                                                                                                                                                                                                                                                                                                                                                | 高     |
| TR-006 | 幸运拼团玩法 | 作为用户，我希望能参与官方发起的"幸运拼团"，有机会以标价"赢取"商品，如果没中奖也能退款。 | 1.**平台发起**：此类拼团仅由平台针对特定商品发起，用户不能自行开团。`<br>`2. **付费参与**：用户通过支付完整的商品价格来参与一个7人团。`<br>`3. **满员开奖**：当一个团成功招募7位（成团人数由后台设定）用户后，该团进入7分钟（开奖时间由后台设置）开奖倒计时。`<br>`4. **随机抽奖**：倒计时结束后，系统从7位参与者中随机抽取1位作为中奖者。`<br>`5. **结果处理**：`<br>` - 中奖者：订单状态变为"待发货"，正常履约。`<br>` - 未中奖者（6人）：订单自动关闭，参与费用根据管理员预设的比例原路**退回至用户的账户余额**。 | 高     |

#### 2.3.2. 平台管理后台 (Web)

此为内部系统，用于平台日常运营和管理。

| 功能模块                     | 功能点         | 详细描述                                                                                                                                                                                                                                                                                                         |
| :--------------------------- | :------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **主面板 (Dashboard)** | 核心数据概览   | 1.**实时数据**：今日GMV、支付订单数、新增用户数、在线用户数。`<br>`2. **核心指标统计**：展示昨日、近7日、近30日的核心数据曲线图（GMV、订单量、用户增长等）。`<br>`3. **待办事项**：快速入口，显示待处理的退款申请、待发货订单等。                                                          |
| **用户管理**           | 用户列表与查询 | 1. 展示所有用户列表（昵称、手机号、注册时间、用户状态）。`<br>`2. 支持按手机号、昵称进行搜索。`<br>`3. 支持按状态（正常、已禁用）进行筛选。                                                                                                                                                                  |
|                              | 用户详情与操作 | 1. 查看用户详细资料、收货地址、历史订单记录。`<br>`2. 可对用户账户进行"禁用"或"解禁"操作。禁用后用户无法登录。                                                                                                                                                                                                 |
| **商品管理**           | 商品分类管理   | 1. 创建、编辑、删除商品分类。`<br>`2. 支持二级分类结构。`<br>`3. 可调整分类的显示顺序。                                                                                                                                                                                                                      |
|                              | 商品列表 (SPU) | 1. 以SPU（标准产品单元）维度展示商品列表。`<br>`2. 支持按商品名称、分类、状态（上架/下架）进行搜索和筛选。`<br>`3. 可进行"上架"、"下架"、"删除"操作。                                                                                                                                                        |
|                              | 商品发布/编辑  | 1.**基本信息**：填写商品名称、分类、描述、角标（新品/爆款）。`<br>`2. **媒体管理**：上传商品主图、轮播图、详情页长图/视频。`<br>`3. **规格与库存 (SKU)**：设置不同规格（如颜色、尺码），并为每个SKU设定价格、库存、商品编码。                                                              |
| **订单管理**           | 订单列表       | 1. 集中展示所有用户订单，包含订单号、下单用户、商品信息、实付金额、订单状态。`<br>`2. **多维度查询**：支持按订单号、用户手机号、商品名称进行精确搜索。`<br>`3. **状态筛选**：支持按"待付款"、"待成团"、"待发货"、"已发货"、"已完成"、"已关闭"等状态筛选。                                        |
|                              | 订单详情与操作 | 1. 查看订单完整信息，包括商品、收货人、支付、物流信息。`<br>`2. **发货处理**：对"待发货"订单，可录入物流公司及单号，并确认发货。`<br>`3. **售后处理**：审核用户的退款/售后申请，进行"同意退款"或"驳回申请"操作。                                                                                 |
| **营销管理**           | 社交拼团活动   | 1. 创建社交拼团活动，关联指定商品。`<br>`2. 设置拼团参数：成团人数、有效时长、活动时间范围。`<br>`3. 可查看活动列表，对活动进行"暂停"或"结束"操作。                                                                                                                                                          |
|                              | 幸运拼团活动   | 1. 创建幸运拼团活动，关联指定商品（通常为高价值、引流款商品）。`<br>`2. **设置退款比例**：为未中奖的用户设置退款百分比（如98%，意味着平台收取2%服务费）。`<br>`3. 管理活动列表，监控各幸运团的参与进度，对活动进行"暂停"或"结束"操作。<br />`<br>`4.设置拼团参数：成团人数、有效时长、活动时间范围。 |
|                              | 优惠券管理     | 1. 创建优惠券：设置优惠券名称、类型（满减/折扣）、面额、使用门槛、有效期、发放总量。`<br>`2. 管理优惠券列表，可手动"作废"未到期的优惠券。                                                                                                                                                                      |
| **内容管理**           | Banner管理     | 1. 上传首页Banner图片。`<br>`2. 设置Banner的跳转链接（可跳转至商品详情页、专题活动页等）。`<br>`3. 可拖拽调整Banner的显示顺序。                                                                                                                                                                              |
|                              | 公告管理       | 1. 发布、编辑、删除平台公告。`<br>`2. 公告内容将在APP内指定位置滚动或静态显示。                                                                                                                                                                                                                                |
| **财务管理**           | 支付对账       | 1. 查看支付流水，与微信支付、支付宝等第三方支付平台的账单进行核对。`<br>`2. 筛选和导出特定时间范围内的交易记录。                                                                                                                                                                                               |
|                              | 数据统计       | 1. 提供每日、每周、每月的财务结算报表。`<br>`2. 统计平台总收入、总支出（退款）、实际盈利等关键财务数据。                                                                                                                                                                                                       |
| **系统设置**           | 管理员与角色   | 1. 创建、编辑、删除后台管理员账号。`<br>`2. **角色管理**：创建不同角色（如运营、客服、财务），并为角色分配不同的功能模块权限（RBAC）。                                                                                                                                                                   |
|                              | 操作日志       | 1. 记录所有管理员在后台的关键操作（如登录、商品编辑、订单修改等）。`<br>`2. 支持按操作人、时间范围查询日志，便于审计和问题追溯。                                                                                                                                                                               |

---

## 3. 非功能性需求 (Non-Functional Requirements)

| 类别                 | 需求描述                                                                                                                                                      |
| :------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **性能需求**   | - 核心页面加载时间在3G网络下应小于3秒。`<br>`- 服务器对95%的用户请求响应时间应在200ms以内。`<br>`- 系统能支持至少10,000人/秒的并发访问。                  |
| **安全性需求** | - 用户密码、支付信息等敏感数据必须加密存储。`<br>`- 所有API接口需进行身份验证和权限校验。`<br>`- 有效防止SQL注入、XSS、CSRF等常见Web攻击。                |
| **兼容性需求** | -**APP端：** 兼容近3年发布的iOS和Android主流版本及主流分辨率机型。`<br>`- **Web端：** 兼容Chrome、Firefox、Safari、Edge等主流浏览器的最新版本。 |
| **易用性需求** | - 界面设计简洁美观，交互流程符合用户习惯，无需过多学习成本。`<br>`- 关键操作有明确的引导和反馈提示。`<br>`- 提供便捷的客服入口。                          |
| **扩展性需求** | - 系统架构采用模块化设计，便于未来功能扩展。`<br>`- 数据库设计应考虑未来数据量增长，支持分库分表。                                                          |

---

## 4. 未来规划 (Future Roadmap)

### V2.0 - 深化运营与社交属性

- **直播带货：** 引入平台官方或达人直播，更直观地展示精选商品，提高转化率。
- **内容社区：** 增加"好物说"板块，鼓励用户发布开箱测评、好物推荐等UGC内容，增强用户粘性。
- **会员体系：** 建立付费会员体系，提供"大额优惠券"、"会员专属价"、"包邮"等权益。

### V3.0 - 智能化与效率提升

- **智能推荐升级：** 引入更复杂的机器学习算法，实现"千人千面"的精准推荐。
- **仓储物流体系：** 建立或合作建立高效的仓储和配送体系，提升履约效率和用户体验。
- **API生态建设：** 提供API接口，支持与第三方仓储、物流、营销工具集成，提升运营效率。

---

**文档结束**
