package net.lab1024.sa.admin.module.business.oa.popups.controller;

import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsAddForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsQueryForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsUpdateForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.vo.PopupsVO;
import net.lab1024.sa.admin.module.business.oa.popups.service.PopupsService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 弹窗管理 Controller
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@RestController
@Tag(name = "弹窗管理")
public class PopupsController {

    @Resource
    private PopupsService popupsService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/popups/queryPage")
    @SaCheckPermission("popups:query")
    public ResponseDTO<PageResult<PopupsVO>> queryPage(@RequestBody @Valid PopupsQueryForm queryForm) {
        return ResponseDTO.ok(popupsService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/popups/add")
    @SaCheckPermission("popups:add")
    public ResponseDTO<String> add(@RequestBody @Valid PopupsAddForm addForm) {
        return popupsService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/popups/update")
    @SaCheckPermission("popups:update")
    public ResponseDTO<String> update(@RequestBody @Valid PopupsUpdateForm updateForm) {
        return popupsService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/popups/batchDelete")
    @SaCheckPermission("popups:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
        return popupsService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/popups/delete/{id}")
    @SaCheckPermission("popups:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
        return popupsService.delete(id);
    }
}
