# 高保真原型

## 一、APP前端

### 网上介绍的提示词

#### A 版

#角色
你是一位资深前端开发工程师，精通UI/UX设计，擅长将产品需求转化为优雅高效的界面代码，能够结合设计风格和技术规格输出高质量的HTML文件。

#设计风格
优雅的极简主义美学与功能的完美平衡；
清新柔和的渐变配色与品牌色系浑然一体（如从#FFB74D橙黄渐变到#FFF8E1浅黄）；
恰到好处的留白设计（元素间距至少16px）；
轻盈通透的沉浸式体验（如背景透明度50%-70%，避免过多重叠元素）；
信息层级通过微妙的阴影过渡（阴影值如shadow-md）和模块化卡片布局清晰呈现；
用户视线能自然聚焦核心功能；
精心打磨的圆角（建议radius值8px-12px）；
细腻的微交互（如hover时轻微放大1.05倍）；
舒适的视觉比例（内容区域占屏幕60%-80%）；
强调色：便签APP选用温暖柔和的橙黄色系（如#FFB74D）作为主色调，搭配中性灰（如#E0E0E0）作为辅助色。

#技术规格
1、单个页面尺寸为375x812px，带有描边，模拟手机边框；
2、图标: 引用在线矢量图标库内的图标（如FontAwesome或Iconify），任何图标都不要带有背景色块、底板、外框；
3、图片: 使用开源图片网站链接的形式引入（如Unsplash）；
4、样式必须通过引入Tailwind CSS CDN（https://cdn.tailwindcss.com）实现，且优先使用Tailwind的utility classes完成布局和样式，不写原生CSS；
5、不要显示状态栏以及时间、信号等信息；
6、不要显示非移动端元素，如滚动条；
7、所有页面必须使用响应式设计，确保在375x812px的手机尺寸下完美展示，同时兼容其他移动端分辨率；
8、文件名规则：每个.html文件以页面功能命名。

---

#### B 版

我想开发一个类似外卖APP「饿了么」，APP叫「死了么」，用于养老的，每天问一句，以防独自一个人死在家里没人发现。APP也有骑手，哪里有人死了就去接单收尸。 注意这是专门为独居90后的年轻人设计的。风格要求清新好看、APP内的文案多用搞怪的网络用语。

现在需要输出高保真的原型图，请通过以下方式帮我完成所有界面的原型设计，并确保这些原型界面可以直接用于开发：

1、用户体验分析：先分析这个 App 的主要功能和用户需求，确定核心交互逻辑。

2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。

3、高保真 UI 设计：作为 UI 设计师，设计贴近真实 iOS/Android 设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。

4、HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成所有原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。拆分代码文件，保持结构清晰：

5、每个界面应作为独立的 HTML 文件存放，例如 home.html、profile.html、settings.html 等。

– index.html 作为主入口，不直接写入所有界面的 HTML 代码，而是使用 iframe 的方式嵌入这些 HTML 片段，并将所有页面直接平铺展示在 index 页面中，而不是跳转链接。

– 真实感增强：

– 界面尺寸应模拟 iPhone 15 Pro，并让界面圆角化，使其更像真实的手机界面。

– 使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）。

– 添加顶部状态栏（模拟 iOS 状态栏），并包含 App 导航栏（类似 iOS 底部 Tab Bar）。

请按照以上要求生成完整的 HTML 代码，并确保其可用于实际开发。

请你根据  这份PRD文档来帮我整合、优化以上这段提示词，用于指导AI为我生成原型

===============================================================

### gemini-2.5-pro【超级提示词】社交拼团APP高保真原型设计指令

#### 1. 角色 (Persona)

你是一位资深的UI/UX设计师和前端开发工程师，精通将复杂的产品需求转化为优雅、直观且高效的界面代码。你对移动端设计规范有深刻理解，擅长创造富有情感连接的用户体验，并能严格遵循技术规格，输出高质量、可直接用于开发的HTML文件。

#### 2. 项目核心 (Core Project)

你的任务是为一款名为 “乐拼GO” (或你认为更佳的名称) 的社交拼团APP，设计并实现一套完整的高保真HTML原型。此APP的核心业务是B2C直营社交拼团，并包含一个创新的 “幸运拼团” 玩法。所有功能、流程和业务逻辑，都必须严格遵循我们已确认的 《社交拼团APP产品需求文档-简化版.md》。

#### 3. 设计风格 (Design Style)

* 核心美学： 优雅的极简主义与强大功能的完美平衡。界面需保持整洁、直观，避免不必要的装饰。
* 品牌色系： 选用温暖柔和的橙黄色系作为品牌主色调，以激发购物热情和营造积极的社交氛围。
* 主色/强调色： #FFB74D (温暖橙)
* 渐变色： 从主色 #FFB74D 自然渐变到 #FFF8E1 (浅黄) 用于背景或关键卡片，营造清新柔和的视觉感。
* 辅助色： 中性灰 #E0E0E0 用于边框、分割线和次要文字。
* 布局与间距：
* 留白是关键： 所有可交互元素之间、卡片与卡片之间，应保持至少16px的间距，创造呼吸感。
* 模块化卡片布局： 使用卡片（Card）作为信息承载的主要单元，信息层级通过微妙的阴影（如Tailwind的 shadow-md）和边框来区分。
* 视觉比例： 核心内容区域应占据屏幕宽度的80%-90%，两侧留有舒适的边距。
* 元素细节：
* 圆角： 为所有卡片、按钮等元素应用 8px 到 12px 的圆角（rounded-lg 或 rounded-xl），提升亲和力。
* 图标： 简洁、清晰的线性图标（Line Icon），避免填充和色块背景，保持视觉轻盈。
* 微交互：
* Hover效果： 鼠标悬停在可点击元素上时，应有轻微放大 (scale-105) 或亮度变化的过渡效果，提供即时反馈。

#### 4. 技术规格 (Technical Specifications)

* 交付物： 一套完整的、由多个HTML文件构成的静态原型。
* 样式实现： 必须且仅通过引入 Tailwind CSS V3 CDN (`<script src="https://cdn.tailwindcss.com"></script>`) 实现所有样式。优先使用Tailwind的Utility Classes，禁止编写任何内联或外部的自定义CSS。
* 图标库： 必须引用在线矢量图标库，推荐使用 Iconify (https://icon-sets.iconify.design/) 或 FontAwesome，以确保图标的清晰度和一致性。
* 图片资源： 所有图片（如商品图、Banner）必须使用 Unsplash (https://source.unsplash.com/random/) 或类似开源图片网站的链接形式引入，确保原型生动真实。
* 文件结构与规范：

1. 独立界面文件： 每个核心界面都必须是一个独立的HTML文件，并根据其核心功能命名（例如：home.html, product_detail.html, profile.html等）。
2. 主入口 index.html： 此文件作为所有界面的展示容器。它本身不包含具体的界面代码，而是通过 `<iframe>` 的方式，将所有独立的HTML界面平铺垂直展示在一个页面中，以便于一次性预览所有设计。每个iframe外应有标题注明其代表的页面。
3. 响应式设计： 所有页面必须为移动端优先设计，确保在 375x812px 的视口下完美呈现。
4. 所有文件保存到“\APP\原型”目录下

#### 5. 真实感增强 (Realism Enhancement)

* 手机模拟框： 在index.html中，每个通过iframe嵌入的界面，其容器都应被设计成一个尺寸为375x812px的、带有圆角和灰色描边的手机模型，以模拟iPhone 15 Pro的真实外观。
* 顶部状态栏： 每个独立界面HTML文件的顶部，都应包含一个模拟iOS风格的顶部状态栏（左侧时间，右侧信号、Wi-Fi、电池图标），以增强沉浸感。
* 底部导航栏 (Tab Bar)： 所有核心页面（如首页、订单、个人中心等）的底部，都必须包含一个固定的、模拟iOS风格的底部导航栏。

#### 6. 核心界面设计清单 (Key Interface Checklist)

请根据以下清单，结合PRD文档《社交拼团APP产品需求文档-简化版.md》中的详细描述，完成每个界面的设计与实现：

home.html (首页):

* 顶部包含一个搜索框。
* 一个醒目的Banner广告位。

* 一个由多个圆形图标组成的“金刚区”（商品分类快捷入口）。
* 一个商品信息流，每个商品以卡片形式展示，包含：商品图、名称、拼团价和原价（划线价）、已拼人数。

product_detail.html (商品详情页):

* 顶部为商品轮播图。
* 清晰展示商品标题、价格信息。

* 核心交互区（必须清晰区分两种模式）：
* 社交拼团： 显示“发起拼团”和“单独购买”两个按钮。

* 幸运拼团： 显示“参与幸运拼团”按钮，并有文字清晰说明“7人参与，1人独享，其余6人按比例退款”的规则。
* 展示“图文详情”和“用户评价”两个板块。

orders.html (我的订单):

* 顶部有标签页（Tabs）用于切换不同状态的订单（如：待付款、待发货、待收货、已完成）。
* 订单列表，每个订单以卡片形式展示商品缩略图、名称、总价和订单状态。

profile.html (个人中心):

* 顶部显示用户头像和昵称。
* 核心功能入口（必须突出）：

* 我的钱包： 显示账户余额，并可点击进入钱包页面。
* 我的订单

* 我的收藏
* 收货地址管理

* 一个“设置”入口。

wallet.html (我的钱包):

* 顶部显眼位置展示用户的账户总余额。
* 下方是一个账单列表，清晰记录每一笔收入（如退款）和支出（如购物）的金额、类型和时间。

checkout.html (支付页):

* 清晰展示订单总金额。
* 支付方式选择：

    第一选项为“余额支付”，并显示当前可用余额。

    其他支付方式，如“ZaloPay”、“Momo”等。

=================================================================================

## 二、管理后台
