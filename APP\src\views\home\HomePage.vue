<template>
  <div class="bg-gray-50">
    <!-- 主内容区域 -->
    <div class="pb-20">
      
      <!-- 顶部搜索区域 -->
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4" style="padding-top: 12px; padding-bottom: 6px;">
        <div class="flex items-center bg-white rounded-full px-3 py-1">
          <iconify-icon icon="material-symbols:search" class="text-gray-400 text-lg mr-3"></iconify-icon>
          <input 
            type="text" 
            id="searchInput" 
            v-model="searchKeyword"
            :placeholder="t('search_placeholder')" 
            class="flex-1 outline-none text-xs border-none focus:outline-none focus:ring-0 focus:border-none"
            @keyup.enter="handleSearch"
          >
        </div>
      </div>

      <!-- 简单HMR测试组件 -->
      <test-hmr-simple v-if="isDev" />
      
      <!-- HMR测试区域 (临时添加用于测试) -->
      <div v-if="isDev" class="hmr-test-area">
        <h3>🚀 HMR功能测试区域 - 已更新!</h3>
        <p>开发环境: {{ isDev }}</p>
        <p>当前时间: {{ currentTime }}</p>
        <p>测试计数器: {{ testCounter }}</p>
        <button @click="testCounter++" class="hmr-test-btn">点击测试 (+1)</button>
        <div class="hmr-info">
          <p>✅ 修改这个区域的样式后保存，应该热更新不刷新</p>
          <p>✅ 计数器值应该保持不变</p>
        </div>
      </div>
      
      <!-- Banner广告位 - 修复版本 -->
      <div class="px-4" style="padding-top: 6px; padding-bottom: 6px;">
        <div id="bannerContainer" class="relative rounded-2xl overflow-hidden cursor-pointer aspect-[3/2]">
          <!-- 动态Banner -->
          <div 
            v-for="(banner, index) in displayBanners" 
            :key="banner.id"
            class="banner-slide absolute inset-0 transition-all duration-500"
            :class="[
              currentBannerIndex === index ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'
            ]"
            :style="{ backgroundImage: getBannerBackground(banner) }"
            @click="handleBannerClick(banner)"
          >
            <!-- 添加空div确保banner有内容 -->
            <div class="w-full h-full"></div>
          </div>
          
          <!-- 轮播指示器 -->
          <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2" v-if="displayBanners.length > 1">
            <div 
              v-for="(banner, index) in displayBanners" 
              :key="index"
              :class="[
                'w-2 h-2 rounded-full cursor-pointer',
                currentBannerIndex === index ? 'bg-white' : 'bg-white bg-opacity-50'
              ]"
              @click="switchBanner(index)"
            ></div>
          </div>
        </div>
      </div>

      <!-- 商品分类导航 -->
      <div class="bg-white mx-4 rounded-2xl p-4 shadow-sm" style="margin-bottom: 6px;">
        <div class="grid grid-cols-3 gap-6">
          <div class="flex flex-col items-center cursor-pointer" @click="goToCategory('all')">
            <div :class="[
              'w-12 h-12 rounded-2xl flex items-center justify-center mb-2 relative transition-all duration-200',
              currentCategory === 'all' 
                ? 'bg-gradient-to-br from-green-400 to-green-600 shadow-lg scale-105' 
                : 'bg-gradient-to-br from-green-300 to-green-500 opacity-70'
            ]">
              <iconify-icon icon="material-symbols:inventory" class="text-white text-xl"></iconify-icon>
              <div v-if="currentCategory === 'all'" class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
            <span :class="[
              'text-xs transition-colors duration-200',
              currentCategory === 'all' ? 'text-green-600 font-medium' : 'text-gray-700'
            ]">所有商品</span>
          </div>
          <div class="flex flex-col items-center cursor-pointer" @click="goToCategory('digital')">
            <div :class="[
              'w-12 h-12 rounded-2xl flex items-center justify-center mb-2 transition-all duration-200',
              currentCategory === 'digital' 
                ? 'bg-gradient-to-br from-blue-400 to-blue-600 shadow-lg scale-105' 
                : 'bg-gradient-to-br from-blue-300 to-blue-500 opacity-70'
            ]">
              <iconify-icon icon="material-symbols:smartphone" class="text-white text-xl"></iconify-icon>
              <div v-if="currentCategory === 'digital'" class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
            <span :class="[
              'text-xs transition-colors duration-200',
              currentCategory === 'digital' ? 'text-blue-600 font-medium' : 'text-gray-700'
            ]">特价活动专区</span>
          </div>
          <div class="flex flex-col items-center cursor-pointer" @click="goToCategory('luxury')">
            <div :class="[
              'w-12 h-12 rounded-2xl flex items-center justify-center mb-2 transition-all duration-200',
              currentCategory === 'luxury' 
                ? 'bg-gradient-to-br from-purple-400 to-purple-600 shadow-lg scale-105' 
                : 'bg-gradient-to-br from-purple-300 to-purple-500 opacity-70'
            ]">
              <iconify-icon icon="material-symbols:diamond" class="text-white text-xl"></iconify-icon>
              <div v-if="currentCategory === 'luxury'" class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
            </div>
            <span :class="[
              'text-xs transition-colors duration-200',
              currentCategory === 'luxury' ? 'text-purple-600 font-medium' : 'text-gray-700'
            ]">国际品牌专区</span>
          </div>
        </div>
      </div>

      <!-- 拼团标签页 -->
      <div class="px-4" style="padding-top: 6px; padding-bottom: 6px;">
        
        <!-- 拼团类型选择 - 已移除功能 -->
        <!-- <div class="bg-white rounded-2xl p-3 shadow-sm mb-2">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <iconify-icon icon="material-symbols:group" class="text-blue-500 mr-2"></iconify-icon>
              <span class="text-sm font-medium text-gray-700">拼团类型</span>
            </div>
            <div class="relative">
              <select 
                v-model="currentGroupType"
                @change="handleGroupTypeChange"
                class="custom-select appearance-none bg-gray-50 border border-gray-200 rounded-lg px-3 py-2 pr-8 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option 
                  v-for="option in activityOptions" 
                  :key="option.id"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
              <iconify-icon 
                icon="material-symbols:keyboard-arrow-down" 
                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
              ></iconify-icon>
            </div>
          </div>
        </div> -->
      </div>

      <!-- 商品瀑布流 -->
      <div class="px-4">
        <!-- 加载状态 -->
        <div v-if="loading && currentProducts.length === 0" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p class="text-gray-500 text-sm mt-2">{{ t('loading') }}</p>
        </div>
        
        <!-- 中奖公告滚动区域 -->
        <div class="bg-white rounded-2xl p-4 shadow-sm mb-4" v-if="!loading">
          <div class="flex items-center mb-3">
            <iconify-icon icon="material-symbols:campaign" class="text-orange-500 mr-2 text-lg"></iconify-icon>
            <span class="text-sm font-medium text-gray-700">拼团赚红包，中不中都有赚！</span>
          </div>
          
          <div class="relative h-12 overflow-hidden">
            <div 
              :class="showScrollTransition ? 'transition-transform duration-500 ease-in-out' : ''"
              :style="{ transform: `translateY(-${currentAnnouncementIndex * 48}px)` }"
            >
              <div 
                v-for="(announcementPair, index) in visibleAnnouncementPairs" 
                :key="`pair-${index}`"
                class="h-12 py-0.5"
              >
                <!-- 显示2个用户的信息 -->
                <div 
                  v-for="(announcement, userIndex) in announcementPair" 
                  :key="announcement.id"
                  class="flex items-center justify-between h-5 mb-0.5 last:mb-0"
                >
                  <div class="flex items-center flex-1">
                    <span class="text-xs font-medium text-blue-600">{{ announcement.nickname }}</span>
                    <span class="text-xs text-gray-500 mx-1">参与拼团</span>
                    <span class="text-xs px-1 py-0.5 rounded" 
                          :class="announcement.status === '拼中' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'">
                      {{ announcement.status }}
                    </span>
                    <!-- 未拼中时显示红包金额 -->
                    <span v-if="announcement.status === '未拼中'" class="text-xs text-gray-600 ml-1">
                      获得红包{{ formatVietnamDong(announcement.amount) }}
                    </span>
                    <!-- 拼中时显示省钱金额 -->
                    <span v-if="announcement.status === '拼中'" class="text-xs text-gray-600 ml-1">
                      省{{ formatVietnamDong(announcement.savings) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品网格 -->
        <div v-if="!loading || currentProducts.length > 0" style="display: flex; flex-direction: column; gap: 8px;">
          <template v-for="product in currentProducts" :key="product.id">
            <!-- 新用户专享商品卡片 -->
            <NewUserProductCard
              v-if="product.goodsType === 'newUser'"
              :product="product"
              @detail="(product) => goToProductDetail(currentCategory, product.goodsId || product.id)"
            />
            <!-- 普通商品卡片 -->
            <ProductCard
              v-else
              :product="product"
              @click="goToProductDetail(currentCategory, product.goodsId || product.id)"
              @directBuy="handleDirectBuy"
              @groupBuy="handleGroupBuy"
            />
          </template>
        </div>
        
        <!-- 暂无数据 -->
        <div v-if="!loading && currentProducts.length === 0" class="text-center py-12">
          <iconify-icon icon="material-symbols:inventory-2-outline" class="text-gray-300 text-5xl mb-4"></iconify-icon>
          <p class="text-gray-500 text-sm">暂无商品数据</p>
          <button 
            @click="refreshProducts()" 
            class="mt-4 px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>

      <!-- 底部状态 -->
      <div class="text-center py-8">
        <div v-if="loading" class="text-gray-500 text-xs" style="font-size: 10px;">
          <div class="inline-block animate-spin rounded-full h-3 w-3 border-b border-gray-400 mr-2"></div>
          <span>{{ t('loading') }}</span>
        </div>
        <div v-else-if="currentProducts.length > 0" class="text-gray-400 text-xs flex items-center justify-center" style="font-size: 11px;">
          <span class="mr-1">🎉</span>
          <span>{{ t('reached_bottom') }}</span>
          <span class="ml-1">🎉</span>
        </div>
        <div v-else class="text-gray-500 text-xs" style="font-size: 10px;">
          <span>{{ t('no_more_products') }}</span>
        </div>
      </div>

    </div>

    <!-- 底部导航栏 -->
    <BottomNav current="home" />

    <!-- 平台补贴弹窗 -->
    <div v-if="showModal" class="fixed inset-0 z-50">
      <!-- 背景遮罩 -->
      <div class="modal-backdrop absolute inset-0" @click="hideSubsidyModal()"></div>
      
      <!-- 弹窗内容 -->
      <div class="modal-content fixed top-1/2 left-1/2 w-11/12 max-w-sm" style="transform: translate(-50%, -50%);">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
          <!-- 弹窗头部 -->
          <div class="subsidy-modal p-6 text-white text-center relative">
            <!-- 装饰图标 -->
            <div class="absolute top-4 left-4">
              <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <iconify-icon icon="material-symbols:diamond" class="text-white text-lg"></iconify-icon>
              </div>
            </div>
            <div class="absolute top-4 right-4">
              <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <iconify-icon icon="material-symbols:star" class="text-white text-lg"></iconify-icon>
              </div>
            </div>
            
            <!-- 主标题 -->
            <h2 class="text-xl font-bold mb-2">{{ t('modal_title') }}</h2>
            <p class="text-sm opacity-90">{{ t('modal_subtitle') }}</p>
          </div>

          <!-- 弹窗主体 -->
          <div class="p-6 text-center">
            <!-- 金币图标和金额 -->
            <div class="flex items-center justify-center mb-4">
              <div class="subsidy-coins w-16 h-16 rounded-full flex items-center justify-center mr-3">
                <iconify-icon icon="material-symbols:monetization-on" class="text-white text-3xl"></iconify-icon>
              </div>
              <div class="subsidy-coins w-12 h-12 rounded-full flex items-center justify-center">
                <iconify-icon icon="material-symbols:monetization-on" class="text-white text-2xl"></iconify-icon>
              </div>
            </div>
            
            <div class="mb-2">
              <span class="text-lg text-gray-600">{{ t('modal_cash') }}</span>
            </div>
            <div class="text-3xl font-bold text-green-500 mb-4">+10.000₫</div>
            
            <p class="text-gray-600 mb-6 text-sm">{{ t('modal_share') }}</p>
            
            <!-- 操作按钮 -->
            <button 
              @click="goToActivityPage()" 
              class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white font-bold py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 mb-3"
            >
              <span>{{ t('modal_collect') }}</span>
            </button>
            
            <p class="text-xs text-gray-400">{{ t('modal_desc') }}</p>
          </div>
        </div>
        
        <!-- 关闭按钮 -->
        <button 
          @click="hideSubsidyModal()" 
          class="absolute -bottom-14 left-1/2 -translate-x-1/2 w-10 h-10 rounded-full bg-black bg-opacity-30 text-white flex items-center justify-center hover:bg-opacity-50 transition-colors"
        >
          <iconify-icon icon="material-symbols:close" class="text-xl"></iconify-icon>
        </button>
      </div>
    </div>

    <!-- 新用户引导弹窗 -->
    <NewUserGuideModal 
      :visible="showNewUserGuide"
      :noviceCount="newUserGuideCount"
      @close="closeNewUserGuide"
      @experience="handleNewUserExperience"
    />

  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { getUserStatus } from '@/api/user'
import { authApi } from '@/api/auth'
import TestHmr from '@/test-hmr.vue'
import TestHmrSimple from '@/test-hmr-simple.vue'
import { StandardApiAdapter } from '@/api/standardAdapter'
import { showSuccess, showError } from '@/utils/message'
import { getImageUrl } from '@/config/image'
import { getInviteCodeFromUrl, handleInviteCode } from '@/utils/share'
import BottomNav from '@/components/common/BottomNav.vue'
import ProductCard from '@/components/common/ProductCard.vue'
import NewUserProductCard from '@/components/product/NewUserProductCard.vue'
import NewUserGuideModal from '@/components/guide/NewUserGuideModal.vue'

export default {
  name: 'HomePage',
  components: {
    BottomNav,
    ProductCard,
    NewUserProductCard,
    NewUserGuideModal,
    TestHmr,
    TestHmrSimple
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 开发环境标识（用于HMR测试）
    const isDev = import.meta.env.DEV
    const testCounter = ref(0)
    const currentTime = ref('')
    
    // 更新时间
    const updateTime = () => {
      currentTime.value = new Date().toLocaleTimeString()
    }
    
    // 响应式数据
    const searchKeyword = ref('')
    const showModal = ref(false)
    const currentCategory = ref('all')
    const currentGroupType = ref('')
    const currentBannerIndex = ref(0)
    const loading = ref(false)
    const searchLoading = ref(false)
    
    // 新增：新用户引导相关状态
    const showNewUserGuide = ref(false)
    const newUserGuideCount = ref(0)
    
    // API 数据
    const homeData = ref(null)
    const banners = ref([])
    const categories = ref([])
    const products = ref([])
    const pagination = ref({})
    const activities = ref([])  // 新增活动数据
    
    // 用户状态数据 - 新增
    const userStatus = ref({
      isLoggedIn: false,
      nickname: '',
      unreadMessages: 0
    })
    
    // 中奖公告数据
    const announcements = ref([])
    const currentAnnouncementIndex = ref(0)
    let announcementScrollTimer = null
    let announcementGenerateTimer = null
    
    // API 服务
    let homeApi = null
    
    let bannerInterval = null

    // 多语言配置
    const languages = {
      'zh-CN': {
        search_placeholder: '搜索商品、品牌、活动...',
        banner1_activity: '限时活动',
        banner1_title: '平台补贴专区',
        banner1_desc: '每日签到领奖励',
        banner1_btn: '立即参与',
        banner2_activity: '新人专享',
        banner2_title: '首次拼团特惠',
        banner2_desc: '超低价格等你来',
        banner2_btn: '立即抢购',
        banner3_activity: '热门推荐',
        banner3_title: '爆款商品团购',
        banner3_desc: '限时拼团享优惠',
        banner3_btn: '查看热门',
        category_recommended: '推荐商品',
        category_special: '特惠商品',
        category_hot: '热门商品',
        category_all: '全部商品',
        refresh: '换一批',
        loading: '正在加载更多商品...',
        reached_bottom: '到底啦~~',
        no_more_products: '暂无更多商品',
        nav_home: '首页',
        nav_category: '分类',
        nav_orders: '订单',
        nav_profile: '我的',
        group_3: '3人团',
        group_10: '10人团',
        modal_title: '平台补贴',
        modal_subtitle: '每日领取',
        modal_cash: '现金',
        modal_share: '立即分享给朋友',
        modal_collect: '去收集',
        modal_desc: '通过每日签到奖励和分享返利来获得更多收益',
        special_restricted: '特惠商品仅限新用户购买',
        group_remaining: '已有{count}人参与拼团'
      },
      'vi': {
        search_placeholder: 'Tìm kiếm sản phẩm, thương hiệu, hoạt động...',
        banner1_activity: 'Hoạt động có thời hạn',
        banner1_title: 'Khu vực trợ cấp nền tảng',
        banner1_desc: 'Điểm danh hàng ngày nhận thưởng',
        banner1_btn: 'Tham gia ngay',
        banner2_activity: 'Độc quyền người mới',
        banner2_title: 'Ưu đãi mua nhóm lần đầu',
        banner2_desc: 'Giá siêu thấp đang chờ bạn',
        banner2_btn: 'Mua ngay',
        banner3_activity: 'Đề xuất hot',
        banner3_title: 'Mua nhóm sản phẩm bom tấn',
        banner3_desc: 'Mua nhóm có thời hạn hưởng ưu đãi',
        banner3_btn: 'Xem hot',
        category_recommended: 'Sản phẩm đề xuất',
        category_special: 'Sản phẩm ưu đãi',
        category_hot: 'Sản phẩm hot',
        category_all: 'Tất cả sản phẩm',
        refresh: 'Đổi batch',
        loading: 'Đang tải thêm sản phẩm...',
        reached_bottom: 'Đã đến cuối rồi~~',
        no_more_products: 'Không còn sản phẩm',
        nav_home: 'Trang chủ',
        nav_category: 'Danh mục',
        nav_orders: 'Đơn hàng',
        nav_profile: 'Của tôi',
        group_3: 'Nhóm 3 người',
        group_10: 'Nhóm 10 người',
        modal_title: 'Nền tảng trợ cấp',
        modal_subtitle: 'Mỗi ngày nhận',
        modal_cash: 'Tiền mặt',
        modal_share: 'Hãy chia sẻ ngay cho bạn bè',
        modal_collect: 'Đi để thu thập',
        modal_desc: 'Kiếm thêm thu nhập thông qua phần thưởng điểm danh hàng ngày và chia sẻ',
        special_restricted: 'Sản phẩm ưu đãi chỉ dành cho người dùng mới',
        group_remaining: 'Đã có {count} người tham gia mua chung'
      }
    }

    // 计算属性
    const categoryTitle = computed(() => {
      switch (currentCategory.value) {
        case 'digital':
          return '特价活动专区'
        case 'luxury':
          return '国际品牌专区'
        default:
          return '所有商品'
      }
    })

    const currentProducts = computed(() => {
      console.log('🛍️ 计算 currentProducts，当前 products.value:', products.value)
      console.log('🛍️ 当前选择的商品分类 currentCategory.value:', currentCategory.value)
      console.log('🛍️ 当前选择的拼团类型 currentGroupType.value:', currentGroupType.value)
      
      const allProducts = products.value || []
      
      // 详细输出每个商品的字段结构
      if (allProducts.length > 0) {
        console.log('📋 商品详细信息:')
        allProducts.forEach((product, index) => {
          console.log(`  商品 ${index + 1}:`, {
            id: product.id,
            goodsId: product.goodsId,
            goodsName: product.goodsName,
            goodsType: product.goodsType,  // 重点关注商品类型字段
            title: product.title,
            name: product.name,
            image: product.image,
            imageUrl: product.imageUrl,
            price: product.price,
            originalPrice: product.originalPrice,
            original_price: product.original_price,
            salesCount: product.salesCount,
            participants: product.participants,
            tag: product.tag,
            tags: product.tags,
            group_type: product.group_type,
            groupType: product.groupType,
            activityId: product.activityId,
            allFields: Object.keys(product)
          })
        })
      }
      
      // 第一步：新用户商品优先处理
      let sortedProducts = [...allProducts]
      
      // 多重检查新用户状态：authStore 或 userStatus
      const isNewUserFromAuth = authStore.isNewUser && authStore.noviceCount > 0
      const isNewUserFromStatus = userStatus.value.isLoggedIn && userStatus.value.noviceGroupLimit > 0
      const isNewUser = isNewUserFromAuth || isNewUserFromStatus
      
      console.log('🔍 新用户检测:', {
        isNewUserFromAuth,
        isNewUserFromStatus,
        isNewUser,
        authStore: {
          isNewUser: authStore.isNewUser,
          noviceCount: authStore.noviceCount
        },
        userStatus: {
          isLoggedIn: userStatus.value.isLoggedIn,
          noviceGroupLimit: userStatus.value.noviceGroupLimit
        }
      })
      
      if (isNewUser) {
        console.log('🎯 新用户检测成功，优先显示新用户专享商品')
        // 分离新用户商品和普通商品
        const newUserProducts = allProducts.filter(p => p.goodsType === 'newUser')
        const regularProducts = allProducts.filter(p => p.goodsType !== 'newUser')
        
        // 调试：输出新用户商品详情
        console.log('🔍 新用户商品详情:', newUserProducts.map(p => ({
          id: p.id,
          name: p.goods_name || p.name,
          goodsType: p.goodsType,
          allFields: Object.keys(p)
        })))
        
        // 调试：输出所有商品的goodsType分布
        const goodsTypeDistribution = {}
        allProducts.forEach(p => {
          const type = p.goodsType || 'undefined'
          goodsTypeDistribution[type] = (goodsTypeDistribution[type] || 0) + 1
        })
        console.log('📊 商品类型分布:', goodsTypeDistribution)
        
        // 新用户商品置顶显示
        sortedProducts = [...newUserProducts, ...regularProducts]
        console.log(`🎯 商品重新排序完成: ${newUserProducts.length} 个新用户商品置顶, ${regularProducts.length} 个普通商品`)
      } else {
        console.log('👤 普通用户或老用户，不显示新用户专享商品')
        // 老用户：过滤掉新用户专享商品
        sortedProducts = allProducts.filter(p => p.goodsType !== 'newUser')
      }
      
      // 第二步：根据商品分类进行筛选（使用goodsType字段）
      let categoryFilteredProducts = sortedProducts
      if (currentCategory.value && currentCategory.value !== 'all') {
        console.log(`🏷️ 根据商品分类筛选: ${currentCategory.value}`)
        console.log(`🏷️ 当前用户状态: isNewUser=${isNewUser}`)
        categoryFilteredProducts = sortedProducts.filter(product => {
          const productGoodsType = product.goodsType
          let matches = false
          
          // 新用户专享商品在所有分类中都显示（如果用户是新用户）
          if (productGoodsType === 'newUser' && isNewUser) {
            matches = true
            console.log(`🎯 新用户商品在分类 ${currentCategory.value} 中保留显示:`, product.goodsName)
          } else {
            // 根据分类按钮映射到goodsType值
            switch (currentCategory.value) {
              case 'digital':
                // 特价活动专区 - 过滤special类型
                matches = productGoodsType === 'special' || 
                         productGoodsType === 'digital' || 
                         productGoodsType === '手机数码' || 
                         productGoodsType === 'mobile' ||
                         productGoodsType === 'phone' ||
                         productGoodsType === 'electronics' ||
                         productGoodsType === 1 ||
                         productGoodsType === '1'
                break
              case 'luxury':
                // 国际品牌专区 - 过滤recommended类型
                matches = productGoodsType === 'recommended' || 
                         productGoodsType === 'luxury' || 
                         productGoodsType === '奢饰品' || 
                         productGoodsType === 'premium' ||
                         productGoodsType === 'brand' ||
                         productGoodsType === 2 ||
                         productGoodsType === '2'
                break
              default:
                matches = true
            }
          }
          
          console.log(`🏷️ 商品 ${product.goodsName}: goodsType=${productGoodsType}, 分类=${currentCategory.value}, 匹配=${matches}`)
          return matches
        })
        console.log(`🏷️ 分类筛选结果: ${categoryFilteredProducts.length} 个商品匹配分类 ${currentCategory.value}`)
      } else {
        console.log('🏷️ 显示全部分类商品')
      }
      
      // 第二步：根据选择的活动ID进行筛选
      let finalFilteredProducts = categoryFilteredProducts
      if (currentGroupType.value && currentGroupType.value !== '') {
        console.log(`🔍 根据活动ID筛选商品: ${currentGroupType.value}`)
        finalFilteredProducts = categoryFilteredProducts.filter(product => {
          const productActivityId = product.activityId
          const matches = productActivityId === currentGroupType.value
          console.log(`📦 商品 ${product.goodsName}: activityId=${productActivityId}, 选择的ID=${currentGroupType.value}, 匹配=${matches}`)
          return matches
        })
        console.log(`🔍 活动筛选结果: ${finalFilteredProducts.length} 个商品匹配活动ID ${currentGroupType.value}`)
      } else {
        console.log('🔍 显示全部活动商品（未选择特定活动）')
      }
      
      console.log('🛍️ currentProducts 最终结果:', {
        总商品数: allProducts.length,
        新用户排序后: sortedProducts.length,
        分类筛选后: categoryFilteredProducts.length,
        活动筛选后: finalFilteredProducts.length,
        最终商品: finalFilteredProducts.map(p => ({
          id: p.id,
          name: p.goodsName || p.goods_name,
          goodsType: p.goodsType
        }))
      })
      
      // 如果是新用户但没有商品，给出特别提示
      if (isNewUser && finalFilteredProducts.length === 0 && allProducts.length > 0) {
        console.warn('⚠️ 新用户但筛选后无商品，可能分类筛选过严格')
      }
      
      return finalFilteredProducts
    })

    // 确保Banner总是有数据显示
    const displayBanners = computed(() => {
      if (banners.value && banners.value.length > 0) {
        return banners.value
      }
      // 返回默认Banner
      return [
        {
          id: 'default_banner_1',
          title: '平台补贴专区',
          subtitle: '每日签到领奖励',
          activity_text: '🎉 限时活动',
          button_text: '立即参与',
          background_color: 'linear-gradient(135deg, #FF6B9D 0%, #C084F5 100%)',
          link_type: 'activity',
          link_value: 'subsidy'
        },
        {
          id: 'default_banner_2',
          title: '新人专享福利',
          subtitle: '注册即送50,000₫',
          activity_text: '🎁 新人福利',
          button_text: '立即注册',
          background_color: 'linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%)',
          link_type: 'activity',
          link_value: 'register'
        }
      ]
    })

    // 活动选项计算属性 - 新增
    const activityOptions = computed(() => {
      console.log('🎯 计算 activityOptions，当前 activities.value:', activities.value)
      console.log('🎯 activities.value 类型:', typeof activities.value, '是否为数组:', Array.isArray(activities.value))
      
      // 始终包含"全部"选项
      const options = [
        { value: '', label: '全部', id: 'all', activity_id: null }
      ]
      
      if (activities.value && activities.value.length > 0) {
        // 从活动列表中提取唯一的拼团类型
        const seenIds = new Set()
        
        console.log('🔍 开始处理活动数据...')
        activities.value.forEach((activity, index) => {
          console.log(`🔍 处理活动 ${index + 1}:`, activity)
          const name = activity.name
          const id = activity.id
          console.log(`🔍 活动名称: "${name}", ID: "${id}"`)
          
          if (name && id && !seenIds.has(id)) {
            seenIds.add(id)
            const option = {
              value: id,  // 使用活动ID作为value
              label: name,  // 显示活动名称
              id: activity.id,
              activity_id: activity.id,
              group_type: activity.group_type || null
            }
            options.push(option)
            console.log(`✅ 添加选项:`, option)
          } else {
            console.log(`❌ 跳过活动 (名称为空、ID为空或重复): name="${name}", id="${id}"`)
          }
        })
        
        console.log('✅ 最终活动选项:', options)
        return options
      }
      
      console.log('⚠️ 使用默认活动选项 (activities为空或未加载)')
      // 返回包含"全部"的默认选项
      return [
        { value: '', label: '全部', id: 'all', activity_id: null },
        { value: 'default_3', label: '3人团', group_type: 3, id: 'default_3', activity_id: 'default_3' },
        { value: 'default_10', label: '10人团', group_type: 10, id: 'default_10', activity_id: 'default_10' }
      ]
    })

    // 显示的公告列表（显示所有20条数据）
    const visibleAnnouncements = computed(() => {
      return announcements.value
    })

    // 将公告配对显示，每次显示2个用户，并在最后添加第一组用于无缝循环
    const visibleAnnouncementPairs = computed(() => {
      const announcements = visibleAnnouncements.value
      const pairs = []
      
      for (let i = 0; i < announcements.length; i += 2) {
        const pair = [announcements[i]]
        if (i + 1 < announcements.length) {
          pair.push(announcements[i + 1])
        }
        pairs.push(pair)
      }
      
      // 在最后添加第一组，用于无缝循环
      if (pairs.length > 0) {
        pairs.push(pairs[0])
      }
      
      return pairs
    })

    // 在显示区域只显示2条公告（2行）
    const displayAnnouncements = computed(() => {
      return visibleAnnouncements.value.slice(-2)
    })

    // 获取Banner背景样式
    const getBannerBackground = (banner) => {
      // 优先使用图片背景，不添加遮罩
      if (banner.imageUrl) {
        const imageUrl = getImageUrl(banner.imageUrl)
        return `url('${imageUrl}')`
      }
      
      if (banner.background_color) {
        // 如果background_color包含gradient，直接使用
        if (banner.background_color.includes('gradient')) {
          return banner.background_color
        }
        // 如果是CSS类名，转换为gradient
        if (banner.background_color.includes('from-')) {
          return 'linear-gradient(135deg, #FF6B9D 0%, #C084F5 100%)'
        }
        return banner.background_color
      }
      // 默认背景
      return 'linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%)'
    }

    // 获取当前语言
    const getCurrentLanguage = () => {
      const browserLang = navigator.language || navigator.userLanguage
      if (browserLang.startsWith('vi')) {
        return 'vi'
      }
      return 'zh-CN'
    }

    // 获取翻译文本
    const t = (key, params = {}) => {
      const lang = getCurrentLanguage()
      let text = languages[lang][key] || languages['zh-CN'][key] || key
      
      // 替换参数
      Object.keys(params).forEach(param => {
        text = text.replace(`{${param}}`, params[param])
      })
      
      return text
    }

    // 图片URL处理函数 - 新增
    // getImageUrl函数现在通过import从统一配置文件导入

    // 获取当前用户类型
    const getCurrentUserType = () => {
      // 使用兼容的token获取方式
      const isLoggedIn = localStorage.getItem('access_token') || 
                        sessionStorage.getItem('access_token') ||
                        localStorage.getItem('token')
      const hasRecharge = localStorage.getItem('hasRecharge') === 'true'
      
      if (!isLoggedIn) {
        return 'guest'
      }
      
      return hasRecharge ? 'old' : 'new'
    }

    // 检查是否应该显示平台补贴弹窗 - 修改逻辑
    const shouldShowSubsidyModal = async () => {
      try {
        // 检查用户是否登录
        const token = localStorage.getItem('access_token') || 
                     sessionStorage.getItem('access_token') ||
                     localStorage.getItem('token')
        
        // 未登录用户：显示弹窗
        if (!token) {
          console.log('🔍 用户未登录，显示弹窗')
          return true
        }
        
        // 已登录用户：检查多个条件
        console.log('🔍 用户已登录，检查弹窗显示条件...')
        
        // 1. 检查订单数量
        const orderCount = await getUserOrderCount()
        console.log('🔍 用户订单数量:', orderCount)
        
        if (orderCount >= 3) {
          console.log('🔍 订单数量>=3单，不显示弹窗')
          return false
        }
        
        // 2. 检查用户信息中的noviceGroupLimit
        const userInfo = authStore.user
        if (userInfo && userInfo.noviceGroupLimit !== undefined) {
          const noviceGroupLimit = userInfo.noviceGroupLimit
          console.log('🔍 用户noviceGroupLimit值:', noviceGroupLimit)
          
          if (noviceGroupLimit > 3) {
            console.log('🔍 noviceGroupLimit>3，不显示弹窗')
            return false
          }
        } else {
          console.log('🔍 用户信息中没有noviceGroupLimit字段或用户信息为空')
        }
        
        // 所有条件检查通过：显示弹窗
        console.log('🔍 所有检查条件通过，显示弹窗')
        return true
        
      } catch (error) {
        console.error('🚨 检查弹窗显示条件失败:', error)
        // 出错时，为了保险起见，不显示弹窗
        return false
      }
    }

    // 获取用户订单数量 - 新增方法
    const getUserOrderCount = async () => {
      try {
        if (!homeApi) {
          await initApiService()
        }
        
        // 调用订单列表API获取订单数量
        const response = await homeApi.getOrders({
          page: 1,
          per_page: 1, // 只需要获取数量，不需要具体数据
          status: 'all'
        })
        
        if (response.code === 200 || response.code === 0) {
          // 从响应中获取订单总数
          const totalOrders = response.data?.pagination?.total || 
                            response.data?.total || 
                            response.data?.count || 0
          
          console.log('📊 用户订单总数:', totalOrders)
          return totalOrders
        } else {
          console.warn('⚠️ 获取订单数量失败:', response.message)
          return 0
        }
      } catch (error) {
        console.error('❌ 获取用户订单数量失败:', error)
        
        // 如果是认证错误（401、403等），说明用户未登录或token过期
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log('🔐 用户未登录或token过期，按未登录处理')
          return 0
        }
        
        // 其他错误，返回0（保守处理）
        return 0
      }
    }

    // 初始化API服务
    const initApiService = async () => {
      try {
        console.log('🚀 正在初始化API服务...')
        
        // 创建API实例，使用正确的基础路径
        homeApi = new StandardApiAdapter('/api/v1')
        
        console.log('✅ Home API service initialized:', {
          baseURL: '/api/v1',
          proxyTarget: '[域名已隐藏]',
          realAPIOnly: true
        })
        
        // 测试API连接
        try {
          console.log('🔍 测试API连接...')
          const testResponse = await homeApi.request('GET', '/home', {})
          console.log('✅ API连接测试成功:', testResponse)
        } catch (testError) {
          console.warn('⚠️ API连接测试失败:', testError.message)
          // 不抛出错误，允许页面继续加载
        }
        
        return true
      } catch (error) {
        console.error('❌ Failed to initialize home API service:', error)
        throw error
      }
    }

    // 获取用户状态 - 新增方法
    const loadUserStatus = async () => {
      try {
        // 检查是否有token，如果没有则不需要获取用户状态
        // 使用兼容的token获取方式
        const token = localStorage.getItem('access_token') || 
                     sessionStorage.getItem('access_token') ||
                     localStorage.getItem('token')
        if (!token) {
          console.log('🔒 用户未登录，跳过用户状态获取')
          userStatus.value = {
            isLoggedIn: false,
            nickname: '',
            unreadMessages: 0
          }
          return
        }

        console.log('🔄 开始获取用户状态...')
        const response = await getUserStatus()
        
        // 检查响应
        if (response.code === 200 || response.code === 0) {
          userStatus.value = {
            isLoggedIn: response.data.isLoggedIn || true,
            nickname: response.data.nickname || '',
            unreadMessages: response.data.unreadMessages || 0,
            noviceGroupLimit: response.data.noviceGroupLimit || 0
          }
          
          console.log('✅ 用户状态获取成功:', userStatus.value)
          
          // 重要：检查并处理新用户引导
          const noviceGroupLimit = response.data.noviceGroupLimit || 0
          console.log('🎯 检查noviceGroupLimit:', noviceGroupLimit)
          
          if (noviceGroupLimit > 0) {
            // 设置新用户引导标记
            localStorage.setItem('show_newuser_guide', JSON.stringify({
              noviceCount: noviceGroupLimit,
              timestamp: Date.now()
            }))
            console.log('🎯 从userStatus接口检测到新用户，设置引导标记，剩余免费次数:', noviceGroupLimit)
            
            // 立即检查并显示引导弹窗
            setTimeout(() => {
              checkNewUserGuide()
            }, 500)
          }
        } else {
          console.warn('⚠️ 用户状态获取失败:', response.message || response._original?.msg)
          // 设置默认未登录状态
          userStatus.value = {
            isLoggedIn: false,
            nickname: '',
            unreadMessages: 0
          }
        }
      } catch (error) {
        console.error('❌ 获取用户状态失败:', error)
        // 开发环境：暂时禁用用户状态错误处理以调试30秒刷新问题
        if (import.meta.env.DEV) {
          console.warn('🐛 [DEBUG] 开发环境：忽略用户状态错误，避免可能的30秒刷新问题')
          console.warn('⚠️ 用户状态获取失败，保持当前状态:', error.message)
          return
        }
        
        // 如果是认证错误（30007），说明用户未登录或token过期
        if (error.message && error.message.includes('30007')) {
          console.log('🔒 用户未登录或token过期，清除本地token')
          // 清除所有可能的token存储
          localStorage.removeItem('token')
          localStorage.removeItem('access_token')
          sessionStorage.removeItem('access_token')
          userStatus.value = {
            isLoggedIn: false,
            nickname: '',
            unreadMessages: 0
          }
        } else {
          // 其他错误，保持当前状态
          console.warn('⚠️ 用户状态获取失败，保持当前状态')
        }
      }
    }

    // 加载活动数据 - 新增方法
    const loadActivities = async () => {
      try {
        if (!homeApi) {
          await initApiService()
        }
        
        console.log('🔄 开始加载活动数据...')
        const response = await homeApi.getActivities()
        
        console.log('🔍 活动接口原始响应:', response)
        console.log('🔍 response.data:', response.data)
        console.log('🔍 response.data.activities:', response.data?.activities)
        
        // 检查响应
        if (response.code === 200 || response.code === 0) {
          // 尝试多种可能的数据路径
          let activitiesData = response.data?.activities || response.data?.list || response.data || []
          
          console.log('🎯 提取的活动数据:', activitiesData)
          
          // 如果是数组，直接使用；如果不是，尝试其他路径
          if (Array.isArray(activitiesData)) {
            activities.value = activitiesData
          } else if (activitiesData && typeof activitiesData === 'object') {
            // 如果是对象，尝试找到数组字段
            const possibleArrays = Object.values(activitiesData).filter(val => Array.isArray(val))
            if (possibleArrays.length > 0) {
              activities.value = possibleArrays[0]
            } else {
              activities.value = []
            }
          } else {
            activities.value = []
          }
          
          console.log('✅ 最终活动数据:', activities.value)
          
          // 详细输出每个活动的字段
          if (activities.value.length > 0) {
            console.log('📋 活动详细信息:')
            activities.value.forEach((activity, index) => {
              console.log(`  活动 ${index + 1}:`, {
                id: activity.id,
                name: activity.name,
                title: activity.title,
                group_type: activity.group_type,
                type: activity.type,
                allFields: Object.keys(activity)
              })
            })
          } else {
            console.warn('⚠️ 活动数据为空，使用默认数据')
            activities.value = [
              { id: 'default_3', name: '3人团', group_type: 3, title: '3人成团，立享优惠' },
              { id: 'default_10', name: '10人团', group_type: 10, title: '10人成团，更多优惠' }
            ]
          }
        } else {
          console.warn('⚠️ 活动数据加载失败:', response.message || response._original?.msg)
          // 设置默认活动数据
          activities.value = [
            { id: 'default_3', name: '3人团', group_type: 3, title: '3人成团，立享优惠' },
            { id: 'default_10', name: '10人团', group_type: 10, title: '10人成团，更多优惠' }
          ]
        }
      } catch (error) {
        console.error('❌ 加载活动数据失败:', error)
        // 设置默认活动数据
        activities.value = [
          { id: 'default_3', name: '3人团', group_type: 3, title: '3人成团，立享优惠' },
          { id: 'default_10', name: '10人团', group_type: 10, title: '10人成团，更多优惠' }
        ]
      }
    }

    // API 方法
    const loadHomeData = async () => {
      try {
        if (!homeApi) {
          await initApiService()
        }
        
        loading.value = true
        const language = getCurrentLanguage()
        
        console.log('🔄 开始加载首页数据...')
        const response = await homeApi.getHomeData({ language })
        
        // 检查是否有错误（包括业务错误）
        if (response.error || (response.code !== 200 && response.code !== 0)) {
          // 显示后端错误信息
          showError(`后端系统错误 (${response._original?.code || response.code}): ${response.message || response._original?.msg || '未知错误'}`)
          
          // 设置空数据，确保页面正常显示
          homeData.value = response.data || {}
          banners.value = response.data?.banners || []
          categories.value = response.data?.categories || []
          products.value = response.data?.initialProducts?.list || []
          pagination.value = {
            page: response.data?.initialProducts?.pageNum || 1,
            per_page: response.data?.initialProducts?.pageSize || 20,
            total: response.data?.initialProducts?.total || 0,
            pages: response.data?.initialProducts?.pages || 1
          }
          
          console.warn('⚠️ 后端返回错误，已设置空数据以保证页面正常显示')
          return
        }
        
        if (response.code === 200 || response.code === 0) {
          homeData.value = response.data
          banners.value = response.data.banners || []
          categories.value = response.data.categories || []
          
          console.log('📊 处理首页数据:')
          console.log('  - banners:', response.data.banners)
          console.log('  - categories:', response.data.categories)
          console.log('  - initialProducts:', response.data.initialProducts)
          
          // 修正：使用后端实际返回的数据结构 initialProducts 和 list
          if (response.data.initialProducts) {
            products.value = response.data.initialProducts.list || []
            pagination.value = {
              page: response.data.initialProducts.pageNum || 1,
              per_page: response.data.initialProducts.pageSize || 20,
              total: response.data.initialProducts.total || 0,
              pages: response.data.initialProducts.pages || 1
            }
            
            console.log('📦 商品数据处理完成:')
            console.log('  - products.value:', products.value)
            console.log('  - pagination.value:', pagination.value)
          }
          
          // 保存数据到localStorage供其他页面使用
          try {
            const homeDataForShare = {
              categories: categories.value,
              products: products.value,
              banners: banners.value,
              timestamp: Date.now()
            }
            localStorage.setItem('homeData', JSON.stringify(homeDataForShare))
            console.log('📦 首页数据已保存到缓存')
          } catch (error) {
            console.log('⚠️ 保存数据到缓存失败:', error)
          }
          
          console.log('✅ 首页数据加载成功:', {
            banners: banners.value.length,
            categories: categories.value.length,
            products: products.value.length,
            productData: response.data.initialProducts
          })
          
          // 🚫 备用数据已移除 - 仅使用真实API数据
          if (products.value.length === 0) {
            console.log('⚠️ 主数据为空，请检查后端API接口')
          }
        } else {
          console.error('❌ 首页数据响应异常:', response)
          console.log('⚠️ 请检查后端API接口配置')
        }
      } catch (error) {
        console.error('❌ 加载首页数据失败:', error)
        console.log('⚠️ 请检查网络连接和后端API接口')
        
        // 向用户显示错误信息
        showError('后端服务暂时不可用，显示演示数据')
        
        // 设置默认演示数据，确保页面能正常显示
        homeData.value = {
          banners: [],
          categories: [],
          initial_products: {
            items: [],
            pagination: {}
          }
        }
        banners.value = []
        categories.value = []
        products.value = []
        pagination.value = {}
        
        console.log('🔄 已设置默认演示数据，页面可正常浏览')
      } finally {
        loading.value = false
      }
    }

    const loadProducts = async (category = null, groupType = null, reset = true) => {
      try {
        if (!homeApi) {
          await initApiService()
        }
        
        if (reset) {
          loading.value = true
        }
        
        const params = {
          category: category || currentCategory.value,
          page: reset ? 1 : (pagination.value.page || 1) + 1,
          per_page: 20
        }
        
        console.log('🔄 加载商品列表，参数:', params)
        
        try {
          // 使用独立的商品列表接口
          const response = await homeApi.getProducts(params)
          
          console.log('📦 商品接口响应:', response)
          
          if (response.code === 0 || response.code === 200) {
            // 根据测试页面的数据结构处理响应
            const productData = response.data || {}
            const productList = productData.list || []
            
            if (reset) {
              products.value = productList
            } else {
              products.value = [...products.value, ...productList]
            }
            
            // 更新分页信息
            pagination.value = {
              page: productData.pageNum || 1,
              per_page: productData.pageSize || 20,
              total: productData.total || 0,
              pages: productData.pages || 1
            }
            
            console.log(`✅ 商品列表加载成功，共 ${productList.length} 个商品`)
            
            if (productList.length > 0) {
              showSuccess(`加载了 ${productList.length} 个商品`)
            } else if (productData.emptyFlag) {
              console.log('📋 商品列表为空，可能是筛选条件没有匹配的商品')
              
              // 检查是否有新用户但当前分类没有新用户商品
              const isCurrentUserNew = (authStore.isNewUser && authStore.noviceCount > 0) || 
                                     (userStatus.value.isLoggedIn && userStatus.value.noviceGroupLimit > 0)
              
              if (isCurrentUserNew && currentCategory.value !== 'all') {
                showError('当前分类暂无商品，请切换到"所有商品"查看新手专享商品')
              } else {
                showError('暂无符合条件的商品')
              }
            }
          } else {
            throw new Error(`商品接口错误 (${response.code}): ${response.message || response.msg}`)
          }
        } catch (apiError) {
          console.warn('⚠️ 商品列表接口调用失败，尝试使用首页数据:', apiError.message)
          
          // 如果商品接口失败，回退到使用首页数据进行前端筛选
          if (homeData.value && homeData.value.initialProducts) {
            let filteredProducts = homeData.value.initialProducts.list || []
            
            // 按分类筛选
            const targetCategory = category || currentCategory.value
            if (targetCategory && targetCategory !== 'all') {
              filteredProducts = filteredProducts.filter(product => {
                switch (targetCategory) {
                  case 'recommended':
                    return product.isRecommended || product.tag === '推荐' || product.id % 2 === 0
                  case 'special':
                    return product.isSpecial || product.tag === '特惠' || product.price < 50
                  case 'hot':
                    return product.isHot || product.sales > 1000 || product.salesCount > 0
                  default:
                    return true
                }
              })
            }
            
            // 注意：不再在这里进行活动筛选，因为已经在currentProducts计算属性中处理
            
            products.value = filteredProducts
            console.log(`✅ 使用首页数据筛选，显示 ${filteredProducts.length} 个商品`)
            
            if (filteredProducts.length > 0) {
              showSuccess(`筛选出 ${filteredProducts.length} 个商品`)
            } else {
              // 检查是否有新用户商品但被筛选掉了
              const hasNewUserProducts = homeData.value?.goods?.some(p => p.goodsType === 'newUser') || false
              const isCurrentUserNew = (authStore.isNewUser && authStore.noviceCount > 0) || 
                                     (userStatus.value.isLoggedIn && userStatus.value.noviceGroupLimit > 0)
              
              if (hasNewUserProducts && isCurrentUserNew) {
                console.warn('⚠️ 有新用户商品但被分类筛选过滤，建议切换到"所有商品"查看')
                showError('当前分类暂无商品，请切换到"所有商品"查看新手专享商品')
              } else {
                showError('暂无符合条件的商品')
              }
            }
          } else {
            // 如果没有首页数据，尝试重新加载首页数据
            console.warn('⚠️ 没有首页数据，尝试重新加载...')
            await loadHomeData()
            
            if (homeData.value && homeData.value.initialProducts) {
              const filteredProducts = homeData.value.initialProducts.list || []
              products.value = filteredProducts
              console.log(`✅ 重新加载后显示 ${filteredProducts.length} 个商品`)
            } else {
              products.value = []
              console.warn('⚠️ 仍然没有可用的商品数据')
              showError('暂无商品数据，请刷新页面重试')
            }
          }
        }
      } catch (error) {
        console.error('❌ 商品加载失败:', error)
        showError('商品数据加载失败，请重试')
        
        // 设置空数据
        if (reset) {
          products.value = []
        }
        pagination.value = {}
      } finally {
        loading.value = false
      }
    }

    const searchProducts = async (keyword) => {
      if (!keyword || keyword.trim().length < 2) {
        showError('搜索关键词至少需要2个字符')
        return
      }
      
      try {
        if (!homeApi) {
          await initApiService()
        }
        
        searchLoading.value = true
        
        const params = {
          q: keyword.trim(),
          type: 'product',
          page: 1,
          per_page: 20
        }
        
        const response = await homeApi.search(params)
        
        if (response.code === 200) {
          products.value = response.data.results.products || []
          pagination.value = response.data.pagination || {}
          showSuccess(`找到 ${response.data.search_stats.total_products} 个相关商品`)
        } else {
          showError(response.message || '搜索失败')
        }
      } catch (error) {
        console.error('搜索失败:', error)
        showError('搜索功能暂时不可用，后端服务离线')
        
        // 设置空搜索结果
        products.value = []
        pagination.value = {}
      } finally {
        searchLoading.value = false
      }
    }

    // 方法
    const showSubsidyModal = () => {
      showModal.value = true
    }

    const hideSubsidyModal = () => {
      showModal.value = false
    }

    // 新增：新用户引导相关方法
    const checkNewUserGuide = () => {
      console.log('🔍 检查新用户引导标记...')
      
      // 方法1：从localStorage检查引导标记
      const guideData = localStorage.getItem('show_newuser_guide')
      console.log('📦 localStorage中的引导数据:', guideData)
      
      if (guideData) {
        try {
          const { noviceCount } = JSON.parse(guideData)
          showNewUserGuide.value = true
          newUserGuideCount.value = noviceCount
          
          // 清除标记
          localStorage.removeItem('show_newuser_guide')
          console.log('🎯 显示新用户引导弹窗，剩余次数:', noviceCount)
          return
        } catch (error) {
          console.error('解析新用户引导数据失败:', error)
        }
      }
      
      // 方法2：直接从authStore检查（作为备用方案）
      if (authStore.isLoggedIn && authStore.isNewUser && authStore.noviceCount > 0) {
        console.log('🎯 从authStore检测到新用户，显示引导弹窗')
        showNewUserGuide.value = true
        newUserGuideCount.value = authStore.noviceCount
      } else {
        console.log('❌ 不是新用户或没有剩余次数:', {
          isLoggedIn: authStore.isLoggedIn,
          isNewUser: authStore.isNewUser,
          noviceCount: authStore.noviceCount,
          userValue: authStore.user
        })
        
        // 方法3：检查userStatus中的noviceGroupLimit（最新添加）
        if (userStatus.value.isLoggedIn && userStatus.value.noviceGroupLimit > 0) {
          console.log('🎯 从userStatus检测到新用户，显示引导弹窗')
          showNewUserGuide.value = true
          newUserGuideCount.value = userStatus.value.noviceGroupLimit
        }
      }
    }

    const closeNewUserGuide = () => {
      showNewUserGuide.value = false
      console.log('🔒 关闭新用户引导弹窗')
    }

    const handleNewUserExperience = () => {
      showNewUserGuide.value = false
      console.log('🚀 用户点击立即体验免费拼团')
      // 弹窗关闭后，首页会自动显示新用户商品置顶
      showSuccess('新手专享商品已为您置顶显示！')
    }

    const goToActivityPage = () => {
      hideSubsidyModal()
      // 跳转到商品详情页面，打开商品id=34的商品
      router.push('/product/34')
    }

    const goToCategory = async (categoryName) => {
      console.log('🏷️ 切换商品分类:', categoryName)
      currentCategory.value = categoryName
      
      // 重置拼团类型为"全部"
      currentGroupType.value = ''
      console.log('🔄 已重置拼团类型为"全部"')
      
      // 由于筛选现在在currentProducts计算属性中进行，这里不需要重新加载商品
      // currentProducts会自动重新计算并应用筛选
      showSuccess(`已切换到${getCategoryTitle(categoryName)}`)
    }

    const goToProfile = () => {
      router.push('/user')
    }

    const filterProducts = async (category) => {
      currentCategory.value = category
      await loadProducts(category, currentGroupType.value, true)
    }

    const refreshProducts = async () => {
      console.log('🔄 执行换一批操作')
      
      try {
        // 尝试加载新的商品数据
        await loadProducts(currentCategory.value, currentGroupType.value, true)
        
        // 如果加载后商品数据为空或很少，使用随机打乱策略
        if (products.value.length === 0) {
          console.log('⚠️ 商品数据为空，尝试使用首页数据并打乱')
          
          // 从首页数据中获取商品
          if (homeData.value && homeData.value.initialProducts && homeData.value.initialProducts.list) {
            let allProducts = [...homeData.value.initialProducts.list]
            
            // 如果有商品，随机打乱并展示
            if (allProducts.length > 0) {
              // 随机打乱数组
              for (let i = allProducts.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [allProducts[i], allProducts[j]] = [allProducts[j], allProducts[i]]
              }
              
              // 只取前8个商品，模拟"换一批"的效果
              products.value = allProducts.slice(0, Math.min(8, allProducts.length))
              showSuccess(`已为您推荐${products.value.length}个商品`)
              console.log('✅ 使用打乱的商品数据:', products.value.length)
            } else {
              console.log('⚠️ 首页数据也为空，使用演示数据')
              await loadHomeData()
            }
          } else {
            console.log('⚠️ 首页数据不可用，使用演示数据')
            await loadHomeData()
          }
        } else {
          // 如果有数据，也可以进行随机打乱
          if (products.value.length > 1) {
            const shuffled = [...products.value]
            for (let i = shuffled.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1));
              [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
            }
            products.value = shuffled
            console.log('✅ 商品列表已随机打乱')
          }
          showSuccess('商品列表已刷新')
        }
      } catch (error) {
        console.error('❌ 换一批操作失败:', error)
        showError('刷新失败，请稍后重试')
      }
    }

    const switchGroupType = async (groupType) => {
      currentGroupType.value = groupType
      await loadProducts(currentCategory.value, groupType, true)
    }

    // 处理拼团类型下拉选择框变化 - 新增
    const handleGroupTypeChange = async () => {
      console.log('🔄 拼团类型变化:', currentGroupType.value)
      // 由于筛选现在在currentProducts计算属性中进行，这里不需要重新加载商品
      // currentProducts会自动重新计算并应用筛选
      showSuccess(currentGroupType.value ? '已筛选活动商品' : '显示全部商品')
    }

    const goToProductDetail = (type, productId) => {
      const userType = getCurrentUserType()
      
      // 检查特惠商品权限
      if (type === 'special' && userType === 'old') {
        showError(t('special_restricted'))
        return
      }
      
      // 确保使用正确的商品ID
      const finalProductId = productId || 'prod_123' // 如果没有ID，使用默认ID
      
      console.log('🔄 跳转到商品详情页:', {
        type,
        productId: finalProductId,
        groupType: currentGroupType.value
      })
      
      router.push(`/product/${finalProductId}?type=${type}&groupType=${currentGroupType.value}`)
    }

    // 处理直接购买
    const handleDirectBuy = (product) => {
      console.log('🛒 直接购买商品:', product)
      
      // 跳转到直接购买商品详情页面（不需要登录即可浏览）
      const productId = product.goodsId || product.id
      router.push({
        path: `/product/direct-buy/${productId}`,
        query: {
          productId: productId,
          type: 'direct'
        }
      })
    }

    // 处理拼团购买
    const handleGroupBuy = (product) => {
      console.log('👥 拼团购买商品:', product)
      
      // 跳转到商品详情页面（原有的拼团页面）
      const productId = product.goodsId || product.id
      
      console.log('🔄 跳转到商品详情页（拼团）:', {
        productId,
        currentCategory: currentCategory.value,
        groupType: currentGroupType.value
      })
      
      router.push(`/product/${productId}?type=${currentCategory.value}&groupType=${currentGroupType.value}`)
    }

    const getGroupRemainingText = (groupType) => {
      const participated = Math.floor(Math.random() * (groupType - 1)) + 1
      return t('group_remaining', { count: participated })
    }

    // Banner轮播功能
    const switchBanner = (index) => {
      currentBannerIndex.value = index
    }

    const handleBannerClick = (banner) => {
      // 优先使用API提供的URL
      if (banner.url || banner.link_url || banner.redirect_url) {
        const targetUrl = banner.url || banner.link_url || banner.redirect_url
        // 在新窗口打开外部链接
        window.open(targetUrl, '_blank')
        return
      }
      
      // 保持原有的逻辑作为备选
      if (banner.link_type === 'activity') {
        showSubsidyModal()
      } else if (banner.link_type === 'category') {
        filterProducts(banner.link_value)
      } else if (banner.link_type === 'product') {
        // 跳转到商品详情
        router.push(`/product/${banner.link_value}`)
      }
    }

    const startBannerAutoPlay = () => {
      if (displayBanners.value.length > 1) {
        bannerInterval = setInterval(() => {
          const nextIndex = (currentBannerIndex.value + 1) % displayBanners.value.length
          switchBanner(nextIndex)
        }, 4000)
        console.log('✅ Banner自动轮播已启动，间隔4秒，共', displayBanners.value.length, '个Banner')
      } else {
        console.log('⚠️ Banner数量不足，无需轮播:', displayBanners.value.length)
      }
    }

    const stopBannerAutoPlay = () => {
      if (bannerInterval) {
        clearInterval(bannerInterval)
        bannerInterval = null
      }
    }

    // 页面可见性变化处理函数
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopBannerAutoPlay()
      } else {
        startBannerAutoPlay()
      }
    }

    // 搜索功能
    const handleSearch = () => {
      if (searchKeyword.value.trim()) {
        searchProducts(searchKeyword.value)
      }
    }

    // 监听搜索关键词变化
    watch(searchKeyword, (newKeyword) => {
      if (newKeyword.trim().length === 0) {
        // 如果搜索关键词清空，重新加载当前分类的商品
        loadProducts(currentCategory.value, currentGroupType.value, true)
      }
    })
    
    // 监听登录状态变化，重新检查新用户引导
    watch(() => authStore.isLoggedIn, (isLoggedIn) => {
      console.log('👤 登录状态变化:', isLoggedIn)
      if (isLoggedIn) {
        // 延迟检查，确保用户信息已更新
        setTimeout(() => {
          checkNewUserGuide()
        }, 500)
        
        // 增加额外的延迟检查，确保数据完全同步
        setTimeout(() => {
          console.log('🔄 第二次检查新用户引导（确保数据同步）')
          checkNewUserGuide()
        }, 1500)
      }
    })
    
    // 监听用户数据变化，重新检查新用户引导
    watch(() => authStore.user, (user) => {
      console.log('👤 用户数据变化，noviceGroupLimit(映射为noviceCount):', user?.noviceCount)
      if (user && user.noviceCount > 0) {
        setTimeout(() => {
          checkNewUserGuide()
        }, 300)
      }
    })
    
    // 监听userStatus变化，重新处理商品列表
    watch(() => userStatus.value.noviceGroupLimit, (noviceGroupLimit) => {
      console.log('👤 userStatus.noviceGroupLimit 变化:', noviceGroupLimit)
      if (noviceGroupLimit > 0 && products.value.length > 0) {
        console.log('🔄 noviceGroupLimit 变化，重新处理商品列表')
        // 延迟重新处理商品列表，确保数据同步
        setTimeout(() => {
          if (homeApi) {
            loadProducts(currentCategory.value, currentGroupType.value, false)
          }
        }, 300)
      }
    })

    // 临时调试：手动触发新用户引导弹窗（用于测试）
    const manualTriggerGuide = () => {
      console.log('🧪 手动触发新用户引导弹窗（测试用）')
      showNewUserGuide.value = true
      newUserGuideCount.value = 3
    }
    
    // 开发环境下添加全局调试方法
    if (import.meta.env.DEV) {
      window.triggerNewUserGuide = manualTriggerGuide
      
      // 手动设置新用户标记并触发检查
      window.setNewUserGuide = (noviceCount = 3) => {
        localStorage.setItem('show_newuser_guide', JSON.stringify({
          noviceCount: noviceCount,
          timestamp: Date.now()
        }))
        console.log('🎯 已设置新用户引导标记，调用checkNewUserGuide()...')
        checkNewUserGuide()
      }
      
      console.log('🔧 开发模式调试方法:')
      console.log('  - triggerNewUserGuide(): 直接显示弹窗')
      console.log('  - setNewUserGuide(): 设置localStorage标记并触发检查')
      console.log('  - checkNewUserGuide(): 检查并显示引导')
      window.checkNewUserGuide = checkNewUserGuide
    }
    
    // 生命周期
    onMounted(async () => {
      console.log('🎯 HomePage mounted - 开始加载数据')
      
      // 开发环境：启动时间更新和HMR测试
      if (isDev) {
        updateTime()
        setInterval(updateTime, 1000)
        console.log('🔥 HMR测试已启用，isDev:', isDev)
      }
      
      // 检查是否需要显示新用户引导
      checkNewUserGuide()
      
      // 如果是登录/注册跳转过来的，等待更长时间确保用户数据已完全加载
      const fromSource = router.currentRoute.value.query.from
      if (fromSource === 'login' || fromSource === 'register') {
        console.log(`🔄 检测到从${fromSource}页跳转，延迟检查新用户引导`)
        setTimeout(() => {
          checkNewUserGuide()
        }, 2000)
        
        // 对于注册用户，增加额外的检查
        if (fromSource === 'register') {
          setTimeout(() => {
            console.log('🔄 注册用户额外检查新用户引导')
            checkNewUserGuide()
          }, 3000)
        }
      }
      
      // 处理邀请码参数
      const inviteCode = getInviteCodeFromUrl()
      if (inviteCode) {
        handleInviteCode(inviteCode)
      }
      
      // 初始化API服务
      await initApiService()
      
      // 并行加载首页数据和用户状态
      try {
        await Promise.all([
          loadHomeData(),
          loadUserStatus(),
          loadActivities()
        ])
      } catch (error) {
        console.warn('⚠️ API连接失败，使用Mock数据演示优化效果')
        // 使用Mock数据展示优化效果
        await loadHomeData()
      }
      
      // 🚫 备用数据已完全移除 - 只使用真实API数据
      if (banners.value.length === 0) {
        console.log('⚠️ Banner数据为空，请检查后端API接口')
      }
      
      if (products.value.length === 0) {
        console.log('⚠️ 商品数据为空，请检查后端API接口')
      }
      
      // 启动Banner自动轮播
      startBannerAutoPlay()
      
      // 检查是否需要显示平台补贴弹窗 - 已暂时隐藏
      // if (await shouldShowSubsidyModal()) {
      //   setTimeout(() => {
      //     showSubsidyModal()
      //   }, 2000)
      // }

      // 添加页面可见性变化监听器
      document.addEventListener('visibilitychange', handleVisibilityChange)
      
      // 初始化公告滚动
      initAnnouncements()
      
      console.log('✅ HomePage 初始化完成')
    })

    onUnmounted(() => {
      stopBannerAutoPlay()
      stopAnnouncementScroll()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    })

    // 工具函数
    const formatCurrency = (price) => {
      return `¥${parseFloat(price).toFixed(2)}`
    }

    // 获取商品对应的活动名称 - 新增
    const getProductActivityName = (product) => {
      // 优先使用已有的activity_name
      if (product.activity_name) {
        return product.activity_name
      }
      
      // 如果有activityId，尝试从activities列表中找到对应的活动名称
      if (product.activityId && activities.value && activities.value.length > 0) {
        const matchedActivity = activities.value.find(activity => activity.id === product.activityId)
        if (matchedActivity && matchedActivity.name) {
          return matchedActivity.name
        }
      }
      
      // 回退到使用group_type
      if (product.group_type) {
        return `${product.group_type}人团`
      }
      
      if (product.groupType) {
        return `${product.groupType}人团`
      }
      
      // 默认显示
      return '拼团'
    }

    // 获取分类标题 - 新增
    const getCategoryTitle = (categoryName) => {
      const categoryMap = {
        'digital': '特价活动专区',
        'luxury': '国际品牌专区',
        'all': '所有商品'
      }
      return categoryMap[categoryName] || '商品分类'
    }

    // 格式化越南盾
    const formatVietnamDong = (amount) => {
      if (!amount) return '0đ'
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        minimumFractionDigits: 0
      }).format(amount)
    }

    // 生成20条固定模拟公告数据
    const generateFixedAnnouncements = () => {
      const fixedData = [
        { nickname: '138****2586', productName: 'iPhone 15 Pro Max 256GB', status: '拼中', amount: null, savings: '1580000' },
        { nickname: '155****7649', productName: 'iPad Air 10.9英寸', status: '未拼中', amount: '25880' },
        { nickname: '187****3421', productName: '华为P60 Pro', status: '拼中', amount: null, savings: '890000' },
        { nickname: '139****8765', productName: '小米14 Ultra', status: '未拼中', amount: '18660' },
        { nickname: '158****9012', productName: 'MacBook Pro 14英寸', status: '拼中', amount: null, savings: '2200000' },
        { nickname: '176****5438', productName: 'AirPods Pro 2代', status: '未拼中', amount: '32500' },
        { nickname: '188****7290', productName: '三星Galaxy S24', status: '拼中', amount: null, savings: '720000' },
        { nickname: '135****4567', productName: '索尼 WH-1000XM5', status: '未拼中', amount: '41200' },
        { nickname: '159****8901', productName: 'Nintendo Switch OLED', status: '拼中', amount: null, savings: '450000' },
        { nickname: '186****2345', productName: '大疆Air 3', status: '未拼中', amount: '15990' },
        { nickname: '137****6789', productName: 'Dyson V15 Detect', status: '拼中', amount: null, savings: '980000' },
        { nickname: '152****0123', productName: '美的冰箱BCD-650', status: '未拼中', amount: '28750' },
        { nickname: '189****4567', productName: '格力空调一级能效', status: '拼中', amount: null, savings: '1350000' },
        { nickname: '133****8901', productName: '海尔洗衣机', status: '未拼中', amount: '22350' },
        { nickname: '177****2345', productName: '梅花运动鞋', status: '拼中', amount: null, savings: '280000' },
        { nickname: '156****6789', productName: 'Lego 建筑系列', status: '未拼中', amount: '35800' },
        { nickname: '182****0123', productName: '拉拉熟食礼盒', status: '拼中', amount: null, savings: '150000' },
        { nickname: '134****4567', productName: '全家便利店电子卡', status: '未拼中', amount: '12660' },
        { nickname: '173****8901', productName: '星巴克咖啡豆', status: '拼中', amount: null, savings: '85000' },
        { nickname: '151****2345', productName: '小米手环8', status: '未拼中', amount: '19900' }
      ]
      
      return fixedData.map((item, index) => ({
        id: `fixed-${index}`,
        nickname: item.nickname,
        productName: item.productName,
        status: item.status,
        amount: item.amount,
        savings: item.savings,
        timestamp: Date.now() - (19 - index) * 60000 // 模拟不同时间
      }))
    }

    // 控制滚动动画的开关
    const showScrollTransition = ref(true)
    
    // 启动公告滚动 - 每3秒滚动一次，循环播放20条数据
    const startAnnouncementScroll = () => {
      announcementScrollTimer = setInterval(() => {
        if (visibleAnnouncementPairs.value.length > 1) {
          const realPairsCount = visibleAnnouncementPairs.value.length - 1 // 排除重复的第一组
          const nextIndex = currentAnnouncementIndex.value + 1
          
          // 如果到达重复的第一组（最后一个位置），瞬间重置到真正的第一组
          if (nextIndex >= visibleAnnouncementPairs.value.length) {
            showScrollTransition.value = false
            currentAnnouncementIndex.value = 0
            
            // 短暂延迟后恢复动画
            setTimeout(() => {
              showScrollTransition.value = true
            }, 50)
          } else {
            currentAnnouncementIndex.value = nextIndex
          }
        }
      }, 3000) // 3秒滚动一次
      
      // 固定20条数据循环滚动，不再生成新公告
    }

    // 停止公告滚动
    const stopAnnouncementScroll = () => {
      if (announcementScrollTimer) {
        clearInterval(announcementScrollTimer)
        announcementScrollTimer = null
      }
      if (announcementGenerateTimer) {
        clearInterval(announcementGenerateTimer)
        announcementGenerateTimer = null
      }
    }

    // 初始化公告数据
    const initAnnouncements = () => {
      // 生成固定的20条公告数据
      announcements.value = generateFixedAnnouncements()
      
      // 立即启动滚动
      setTimeout(() => {
        startAnnouncementScroll()
      }, 1000)
    }



    return {
      // 响应式数据
      isDev,
      testCounter,
      currentTime,
      searchKeyword,
      showModal,
      currentCategory,
      currentGroupType,
      currentBannerIndex,
      loading,
      searchLoading,
      categoryTitle,
      currentProducts,
      banners,
      categories,
      products,
      userStatus,
      activities,  // 新增活动数据
      announcements,
      currentAnnouncementIndex,
      visibleAnnouncements,
      visibleAnnouncementPairs,
      displayAnnouncements,
      showScrollTransition,
      
      // 新增：新用户引导相关
      showNewUserGuide,
      newUserGuideCount,
      
      // 方法
      t,
      showSubsidyModal,
      hideSubsidyModal,
      closeNewUserGuide,
      handleNewUserExperience,
      goToActivityPage,
      goToCategory,
      goToProfile,
      filterProducts,
      refreshProducts,
      switchGroupType,
      goToProductDetail,
      handleDirectBuy,
      handleGroupBuy,
      getGroupRemainingText,
      switchBanner,
      handleBannerClick,
      handleSearch,
      formatCurrency,
      displayBanners,
      getBannerBackground,
      getImageUrl,
      activityOptions,
      handleGroupTypeChange,
      getProductActivityName,
      getCategoryTitle,
      formatVietnamDong
    }
  }
}
</script>

<style lang="scss" scoped>
/* 自定义样式 */
body { 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
}

.gradient-bg { 
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
}

.product-card:hover { 
  transform: translateY(-2px); 
}

.shimmer { 
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); 
  background-size: 200% 100%; 
  animation: shimmer 1.5s infinite; 
}

@keyframes shimmer { 
  0% { background-position: -200% 0; } 
  100% { background-position: 200% 0; } 
}

/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
body {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 平台补贴弹窗样式 */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
  to { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

.subsidy-modal {
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
}

.subsidy-coins {
  background: linear-gradient(135deg, #FCD34D 0%, #F59E0B 100%);
}

/* Banner轮播样式 */
#bannerContainer {
  /* 使用aspect-ratio代替固定高度 */
}

.banner-slide {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* 自定义下拉框样式 - 完全隐藏浏览器默认箭头 */
.custom-select {
  /* 隐藏默认箭头 - 兼容多种浏览器 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  
  /* 额外确保隐藏箭头 */
  background-image: none !important;
  
  /* Firefox特殊处理 */
  text-indent: 1px;
  text-overflow: '';
}

/* 兼容IE */
.custom-select::-ms-expand {
  display: none;
}

/* 兼容Safari */
.custom-select::-webkit-appearance {
  -webkit-appearance: none;
}

/* 中奖公告滚动区域样式 */
.announcement-scroll {
  /* 平滑滚动动画 */
  .transition-transform {
    transition: transform 0.5s cubic-bezier(0.4, 0.0, 0.2, 1);
  }

  /* 响应式字体大小 */
  .text-xs {
    @media (max-width: 384px) {
      font-size: 0.65rem; /* 更小屏幕时缩小字体 */
    }
  }

  /* 公告项样式 */
  .announcement-item {
    height: 48px;
    min-height: 48px;
    display: block;
    transition: all 0.3s ease;
  }

  /* 昵称样式 */
  .user-nickname {
    color: #2563EB; /* blue-600 */
    font-weight: 500;
  }

  /* 状态标签样式 */
  .status-badge {
    font-size: 0.625rem;
    padding: 1px 4px;
    border-radius: 4px;
    font-weight: 500;
    
    &.win {
      background-color: #DCFCE7; /* green-100 */
      color: #16A34A; /* green-600 */
    }
    
    &.lose {
      background-color: #FEE2E2; /* red-100 */
      color: #DC2626; /* red-600 */
    }
  }

  /* 奖金金额样式 */
  .prize-amount {
    color: #EA580C; /* orange-600 */
    font-weight: 600;
    white-space: nowrap;
  }

  /* 响应式布局调整 */
  @media (max-width: 640px) {
    /* 小屏幕时调整间距 */
    .flex-1 {
      min-width: 0;
      padding-right: 8px;
    }
  }

  @media (max-width: 384px) {
    /* 超小屏幕时进一步优化 */
    .prize-amount {
      font-size: 0.65rem;
    }
    
    .status-badge {
      font-size: 0.5rem;
      padding: 0.5px 3px;
    }
  }
}

/* 公告标题区域样式 */
.announcement-header {
  .announcement-icon {
    color: #F97316; /* orange-500 */
    font-size: 1.125rem;
  }

  .announcement-title {
    font-weight: 500;
    color: #374151; /* gray-700 */
  }
}

/* 滚动容器样式 */
.announcement-container {
  position: relative;
  overflow: hidden;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
/* HMR测试样式 */
.hmr-test-area {
  margin: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #8b5cf6 0%, #f5576c 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hmr-test-area h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: bold;
}

.hmr-test-area p {
  margin: 8px 0;
  font-size: 14px;
}

.hmr-test-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  margin: 10px 0;
  transition: background 0.3s;
}

.hmr-test-btn:hover {
  background: #059669;
}

.hmr-info {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 13px;
}

.hmr-info p {
  margin: 4px 0;
}

</style> 