package net.lab1024.sa.admin.module.business.orders.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class OrdersQueryForm extends PageParam {

    @Schema(description = "订单号")
    private String orderSn;

    @Schema(description = "创建时间")
    private LocalDate createTimeBegin;

    @Schema(description = "创建时间")
    private LocalDate createTimeEnd;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "结算标记")
    private Integer settleFlag;

    @Schema(description = "删除标记")
    private Integer deletedFlag;

}
