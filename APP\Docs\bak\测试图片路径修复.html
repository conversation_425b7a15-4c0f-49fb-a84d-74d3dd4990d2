<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片路径修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .original { color: #d32f2f; }
        .fixed { color: #388e3c; }
        .test-image { width: 200px; height: 150px; object-fit: cover; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>🖼️ 图片路径修复测试</h1>
    
    <div class="test-item">
        <h3>测试1: iphone14图片路径修复</h3>
        <p class="original">原始路径: /file//file//file/public/common/303f520879aa4998ac91fb40c0fdcfc0_20250704151213.png</p>
        <p class="fixed">修复后: <span id="fixed1"></span></p>
        <img id="img1" class="test-image" alt="iphone14">
    </div>
    
    <div class="test-item">
        <h3>测试2: Mote60图片路径修复</h3>
        <p class="original">原始路径: /file//file/public/common/167b0fde55aa4733b6d22198dd720ad0_20250626134214.png</p>
        <p class="fixed">修复后: <span id="fixed2"></span></p>
        <img id="img2" class="test-image" alt="Mote60">
    </div>
    
    <div class="test-item">
        <h3>测试3: Banner图片路径</h3>
        <p class="original">原始路径: /file/public/common/4e9e884ac8cf47688feba2901bd78a89_20250701125127.png</p>
        <p class="fixed">修复后: <span id="fixed3"></span></p>
        <img id="img3" class="test-image" alt="Banner">
    </div>

    <script>
        // 模拟修复函数
        function getImageUrl(imagePath) {
            console.log('🖼️ 处理图片路径:', imagePath);
            
            if (!imagePath) {
                console.log('⚠️ 图片路径为空，使用默认占位图');
                return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTMwQzEwNS41MjMgMTMwIDExMCAxMjUuNTIzIDExMCAxMjBDMTEwIDExNC40NzcgMTA1LjUyMyAxMTAgMTAwIDExMEM5NC40NzcgMTEwIDkwIDExNC40NzcgOTAgMTIwQzkwIDEyNS41MjMgOTQuNDc3IDEzMCAxMDAgMTMwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDcwSDMwVjE3MEgxNzBWNzBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTUwIDEwMEwxMzAgMTIwTDEwMCA5MEw3MCA5MEw1MCA5MEw3MCA3MEwxMDAgNzBMMTMwIDcwTDE1MCA3MEwxNzAgNzBWMTcwSDMwVjcwSDUwTDcwIDkwTDEwMCA5MEwxMzAgMTIwTDE1MCA5MEwxNzAgNzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
            }
            
            if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
                console.log('✅ 完整URL，直接返回:', imagePath);
                return imagePath;
            }
            
            let cleanPath = imagePath.trim();
            
            // 使用更强大的正则表达式处理所有重复的/file/前缀
            // 先处理连续的 //file/ 模式
            cleanPath = cleanPath.replace(/\/\/file\//g, '/file/');
            
            // 再处理开头的重复 /file/ 前缀，将多个 /file/ 替换为单个
            cleanPath = cleanPath.replace(/^(\/file)+/g, '/file');
            
            console.log('🔧 路径清理后:', cleanPath);
            
            if (!cleanPath.startsWith('/')) {
                cleanPath = '/' + cleanPath;
            }
            
            const fullUrl = `https://pp.kongzhongkouan.com${cleanPath}`;
            console.log('🌐 最终图片URL:', fullUrl);
            
            return fullUrl;
        }

        // 测试路径修复
        const testPaths = [
            '/file//file//file/public/common/303f520879aa4998ac91fb40c0fdcfc0_20250704151213.png',
            '/file//file/public/common/167b0fde55aa4733b6d22198dd720ad0_20250626134214.png',
            '/file/public/common/4e9e884ac8cf47688feba2901bd78a89_20250701125127.png'
        ];

        testPaths.forEach((path, index) => {
            const fixedPath = getImageUrl(path);
            document.getElementById(`fixed${index + 1}`).textContent = fixedPath;
            document.getElementById(`img${index + 1}`).src = fixedPath;
            
            // 添加图片加载事件
            document.getElementById(`img${index + 1}`).onload = function() {
                console.log(`✅ 图片${index + 1}加载成功:`, fixedPath);
            };
            
            document.getElementById(`img${index + 1}`).onerror = function() {
                console.log(`❌ 图片${index + 1}加载失败:`, fixedPath);
                this.style.backgroundColor = '#ffebee';
                this.alt = '图片加载失败';
            };
        });
    </script>
</body>
</html> 