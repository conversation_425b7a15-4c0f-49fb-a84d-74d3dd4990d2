package net.lab1024.sa.admin.module.business.oa.banners.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 横幅管理 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class BannersQueryForm extends PageParam {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "链接类型")
    private String linkType;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDate startTimeBegin;

    @Schema(description = "开始时间")
    private LocalDate startTimeEnd;

}
