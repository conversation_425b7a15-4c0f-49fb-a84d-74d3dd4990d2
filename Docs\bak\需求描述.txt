体验团（10人团），3人团，10人团   （百人团）
低价（500块以内）付款拼团后有2种结果，中或者不中，
    中的可以拿产品（产品可以退货，但要扣20%的手续费和运费），也可以不拿产品（已付的款扣除20%后返还）。
    不中，本金退还，再额外补贴3-5%（这个比例要做成可以后台设置）
    （策略：连续不中的总收益，要基本保持在20%以内，不能亏损，比如补贴是5%的，4次后基本上就要中一次了）（要在后台可以配置必不中的开关）
高价（500以上）
     产品标价基本上都是上万
     预付款模式（预付20%即可参与），
     中的，要产品的话，把80%余款付完，再发货，不要产品，那之前的20%预付款不退
     不中，预付款退还，且再按产品售价（标价）的3-5%返还补贴
==========================================
裂变：
新注册，送体验金（金额可以设置）（刚刚好可以参加一次新手团）（永远不中）（后台要设置一个参与新手团的次数限制）
邀请注册，仅注册是没有奖励的，当下级有消费后（参与），按消费金额的0.5%奖励上级
下级中，当天消费人数达到5人（后台可以设置），按人数来奖励，每人XX元（后台可以设置）
每邀请到一个有效（有消费行为）用户，可奖励一次转盘抽奖机会（一次性有效）（本功能可以稍后再实现）

==========================================
提现：
要设置一个最低提现金额（后台可以设置）

体验金不能提现，可以使用在所有类型的团，参团时优先抵扣
===========================================
个人中心：

团队数据（人数、收益）
积分
============================================
客服

集成一个
============================================
积分：
按消费金额来奖励积分，1:10，消费1元获得10个积分
目前暂时没有积分消耗通道
============================================

抽中概率策略
（优先度从高到低执行，1是优先度最高，序号越大，优先度越低）

1.新用户进来的第1次必不中
2.也可以单独设置某个用户的所有商品的抽中概率
3.当达到充值金额的一定比例内，要必中n次
4.按商品的价格（价格小于500和500以上的两类）来设置默认的抽中概率
