{"测试时间": "2025-07-21T12:54:50.612Z", "页面信息": {"title": "ShowDoc", "url": "https://www.showdoc.com.cn/item/password/qepay?page_id=7090664555712182&redirect=%2Fqepay%2F7090664555712182", "hasContent": false, "sidebarLinks": 1}, "找到的链接数量": 0, "前10个链接": [], "当前页面内容": {"title": "ShowDoc", "content": "<div id=\"app\"><div data-v-4216e1c2=\"\" class=\"hello\"><div data-v-168051ae=\"\" data-v-4216e1c2=\"\"></div> <section data-v-4216e1c2=\"\" class=\"el-container\"><div data-v-4216e1c2=\"\" class=\"el-card center-card is-always-shadow\" onkeydown=\"if(event.keyCode==13)return false;\"><!----><div class=\"el-card__body\"><form data-v-4216e1c2=\"\" class=\"el-form demo-ruleForm\"><h2 data-v-4216e1c2=\"\">请输入访问密码</h2> <div data-v-4216e1c2=\"\" class=\"el-form-item el-form-item--feedback\"><!----><div class=\"el-form-item__content\" style=\"margin-left: 0px;\"><div data-v-4216e1c2=\"\" class=\"el-input\"><!----><input type=\"password\" autocomplete=\"off\" placeholder=\"密码\" class=\"el-input__inner\"><!----><!----><!----><!----></div><!----></div></div> <div data-v-4216e1c2=\"\" class=\"el-form-item el-form-item--feedback\"><!----><div class=\"el-form-item__content\" style=\"margin-left: 0px;\"><div data-v-4216e1c2=\"\" class=\"el-input\"><!----><input type=\"text\" autocomplete=\"off\" placeholder=\"验证码\" class=\"el-input__inner\"><!----><!----><!----><!----></div> <img data-v-4216e1c2=\"\" src=\"https://showdoc-server.cdn.dfyun.com.cn/server/index.php?s=/api/common/showCaptcha&amp;captcha_id=8717581&amp;1753102489000\" class=\"v_code_img\"><!----></div></div> <div data-v-4216e1c2=\"\" class=\"el-form-item el-form-item--feedback\"><!----><div class=\"el-form-item__content\" style=\"margin-left: 0px;\"><button data-v-4216e1c2=\"\" type=\"button\" class=\"el-button el-button--primary\" style=\"width: 100%;\"><!----><!----><span>提交</span></button><!----></div></div> <div data-v-4216e1c2=\"\" class=\"el-form-item el-form-item--feedback\"><!----><div class=\"el-form-item__content\" style=\"margin-left: 0px;\"><a data-v-4216e1c2=\"\" href=\"/user/login?redirect=%2Fqepay%2F7090664555712182\" class=\"\">登录</a>&nbsp;&nbsp;&nbsp;\n        <!----></div></div></form></div></div></section> <div data-v-065ddbfc=\"\" data-v-4216e1c2=\"\"></div></div></div><div class=\"index-item-block\"><div>INDEX_HTML</div><div>ITEM_HTML</div></div><script type=\"text/javascript\" src=\"https://showdoc.cdn.dfyun.com.cn/static/js/manifest.763aada0159bf6d465bd.js\"></script><script type=\"text/javascript\" src=\"https://showdoc.cdn.dfyun.com.cn/static/js/vendor.e06a04bea902a8c6f95c.js\"></script><script type=\"text/javascript\" src=\"https://showdoc.cdn.dfyun.com.cn/static/js/app.8efb3b750dc5c05a073d.js\"></script><script>// CDN容灾\n  if (typeof window.SHOWDOC_CDN_STASTUS == 'undefined') {\n    // 把全局变量中的静态资源路径给改一下\n    window.DocConfig.staticPath = '/static/'\n    changeCDNToRoot()\n    setTimeout(() => {\n      changeCDNToRoot()\n    }, 500)\n}\n  // 修改js路径到Root路径，不使用cdn\n  function changeCDNToRoot() {\n    const linkTags = document.getElementsByTagName('link') // 获取所有 link 标签\n    const scriptTags = document.getElementsByTagName('script') // 获取所有 script 标签\n    const imgTags = document.getElementsByTagName('img') // 获取所有 img 标签\n\n    const loadScript = src => {\n      return new Promise((resolve, reject) => {\n        const scriptTag = document.createElement('script')\n        scriptTag.src = src\n        scriptTag.onload = resolve\n        scriptTag.onerror = reject\n        document.head.appendChild(scriptTag)\n      })\n    }\n\n    const loadStylesheet = href => {\n      return new Promise((resolve, reject) => {\n        const linkTag = document.createElement('link')\n        linkTag.href = href\n        linkTag.rel = 'stylesheet'\n        linkTag.onload = resolve\n        linkTag.onerror = reject\n        document.head.appendChild(linkTag)\n      })\n    }\n\n    const updateLinkTags = async () => {\n      for (let i = 0; i < linkTags.length; i++) {\n        const linkTag = linkTags[i]\n        const href = linkTag.getAttribute('href')\n\n        if (href && href.includes('dfyun')) {\n          const newHref = href.replace(\n            'https://showdoc.cdn.dfyun.com.cn/',\n            '/'\n          )\n          await loadStylesheet(newHref)\n          linkTag.parentNode.removeChild(linkTag)\n        }\n      }\n    }\n\n    const updateScriptTags = async () => {\n      for (let i = 0; i < scriptTags.length; i++) {\n        const scriptTag = scriptTags[i]\n        const src = scriptTag.getAttribute('src')\n\n        if (src && src.includes('dfyun') && !src.includes('showdoc-server')) {\n          const newSrc = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')\n          await loadScript(newSrc)\n          scriptTag.parentNode.removeChild(scriptTag)\n        }\n      }\n    }\n\n    const updateImgTags = () => {\n      for (let i = 0; i < imgTags.length; i++) {\n        const imgTag = imgTags[i]\n        let src = imgTag.getAttribute('src')\n\n        if (src && src.includes('dfyun')) {\n          src = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')\n          imgTag.setAttribute('src', src)\n        }\n      }\n    }\n\n    updateLinkTags()\n      .then(() => {\n        updateScriptTags()\n          .then(() => {\n            console.log('All scripts loaded successfully')\n            updateImgTags()\n          })\n          .catch(error => {\n            console.error('Error loading scripts:', error)\n          })\n      })\n      .catch(error => {\n        console.error('Error loading stylesheets:', error)\n      })\n  }</script><script language=\"JavaScript\">// 防止被镜像站\n  var host = window.location.host\n  if (\n    host.indexOf('localhost') === -1 &&\n    host.indexOf('wu') === -1 &&\n    host.indexOf('showdoc') === -1 &&\n    host.indexOf('star7th.com') === -1 &&\n    host.indexOf('192.168.') === -1 &&\n    host.indexOf('gaoyixia.com') === -1 &&\n    host.indexOf('dongjingyu.cn') === -1 &&\n    host.indexOf('127.0.0.1') === -1\n  ) {\n    var href = window.location.href\n    var j = href.replace(new RegExp(host, 'g'), 'www.showdoc.com.cn')\n    window.location.href = j\n  }</script><script>// 百度统计\n  if (window.location.host == 'www.showdoc.com.cn') {\n    var _hmt = _hmt || []\n    ;(function() {\n      var hm = document.createElement('script')\n      hm.src = 'https://hm.baidu.com/hm.js?84e82fa31c8ca8671f6b6f972b7e54fb'\n      var s = document.getElementsByTagName('script')[0]\n      s.parentNode.insertBefore(hm, s)\n    })()\n  }</script>", "url": "https://www.showdoc.com.cn/item/password/qepay?page_id=7090664555712182&redirect=%2Fqepay%2F7090664555712182", "timestamp": "2025-07-21T12:54:50.612Z"}}