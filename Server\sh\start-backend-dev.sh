#!/bin/bash

echo "🚀 启动开发环境后端服务..."

# 检查MySQL和Redis服务
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "❌ MySQL服务未运行，正在启动..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "❌ Redis服务未运行，正在启动..."
    sudo systemctl start redis-server
fi

# 进入后端目录
cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api

# 编译并打包项目
echo "📦 编译开发环境项目..."
mvn clean package -DskipTests
cd sa-admin

# 启动开发环境后端服务
echo "🌟 启动开发环境后端API服务..."
echo "🔧 使用配置: dev"
java -jar target/tgw-pp.jar --spring.profiles.active=dev --server.port=8686