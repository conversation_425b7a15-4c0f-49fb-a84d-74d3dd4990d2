package net.lab1024.sa.admin.module.business.invitationRecords.controller;

import net.lab1024.sa.admin.module.business.invitationRecords.domain.form.InvitationRecordsQueryForm;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.vo.InvitationRecordsVO;
import net.lab1024.sa.admin.module.business.invitationRecords.service.InvitationRecordsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 邀请记录表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@RestController
@Tag(name = "邀请记录表")
public class InvitationRecordsController {

    @Resource
    private InvitationRecordsService invitationRecordsService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/invitationRecords/queryPage")
    @SaCheckPermission("invitationRecords:query")
    public ResponseDTO<PageResult<InvitationRecordsVO>> queryPage(@RequestBody @Valid InvitationRecordsQueryForm queryForm) {
        return ResponseDTO.ok(invitationRecordsService.queryPage(queryForm));
    }


}
