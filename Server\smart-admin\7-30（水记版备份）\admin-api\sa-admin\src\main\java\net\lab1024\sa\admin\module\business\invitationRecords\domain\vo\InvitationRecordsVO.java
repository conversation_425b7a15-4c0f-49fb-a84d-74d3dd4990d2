package net.lab1024.sa.admin.module.business.invitationRecords.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 邀请记录表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@Data
public class InvitationRecordsVO {


    @Schema(description = "邀请人ID")
    private Long inviterId;

    @Schema(description = "邀请人")
    private String inviterName;

    @Schema(description = "被邀请人ID")
    private Long inviteeId;

    @Schema(description = "被邀请人")
    private String inviteeName;

    @Schema(description = "已获得佣金")
    private BigDecimal commissionEarned;

    @Schema(description = "邀请状态")
    private String status;

    @Schema(description = "首次充值时间")
    private LocalDateTime firstRechargeTime;

    @Schema(description = "首次下单时间")
    private LocalDateTime firstOrderTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
