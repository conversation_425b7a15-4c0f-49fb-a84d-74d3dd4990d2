在Ubuntu服务器上执行MySQL备份的具体步骤：

  🔐 第一步：连接到Ubuntu服务器

  使用SSH连接：

# 从Windows命令行或PowerShell连接

  ssh root@*************

  📂 第二步：在服务器上执行备份命令

  连接成功后，在服务器终端执行：

# 1. 先检查MySQL服务状态

  sudo systemctl status mysql

# 或

  sudo service mysql status

# 2. 备份数据库（会提示输入MySQL root密码）

  mysqldump -u root -p tgw_pp > tgw_pp_backup.sql

# 3. 或者备份并压缩（推荐）

  mysqldump -u root -p tgw_pp | gzip > tgw_pp_backup.sql.gz

# 4. 检查备份文件

  ls -lh tgw_pp_backup.sql*

  🔍 执行过程说明

  当你运行备份命令时：

1. 系统会提示：Enter password:
2. 输入MySQL的root密码（不会显示字符）
3. 按回车键开始备份
4. 等待备份完成（大数据库可能需要几分钟）

  📋 示例操作流程

  ubuntu@server:~$ mysqldump -u root -p tgw_pp > tgw_pp_backup.sql
  Enter password: [输入MySQL密码]
  ubuntu@server:~$ ls -lh tgw_pp_backup.sql
  -rw-rw-r-- 1 <USER> <GROUP> 15M Jul 23 15:35 tgw_pp_backup.sql

  🚨 常见问题解决

  如果提示权限不足：
  sudo mysqldump -u root -p tgw_pp > tgw_pp_backup.sql

  如果不知道MySQL密码：

# 查看MySQL配置文件中的密码

  sudo cat /etc/mysql/debian.cnf

  备份完成后下载到本地Windows：

# 在Windows命令行执行

  scp username@服务器IP:/home/<USER>/tgw_pp_backup.sql.gz D:\

 📤 现在下载备份文件到本地

  在您的Windows电脑上执行以下命令：

  方法1：使用SCP下载（推荐）

# 在Windows命令行或PowerShell中执行

  scp <EMAIL>:/root/tgw_pp_backup.sql D:\

# 或者使用服务器IP（如果知道的话）

  scp root@*************:/root/tgw_pp_backup.sql D:\

  方法2：使用WinSCP工具

1. 打开WinSCP
2. 连接到服务器：iZj6ca9uerlvdgfgzet7ihZ.compute.aliyuncs.com
3. 用户名：root
4. 下载 /root/tgw_pp_backup.sql 到 D:\

  📥 下载完成后还原数据库

  下载完成后，请运行：

# 检查文件是否下载成功

  dir D:\tgw_pp_backup.sql

# 然后还原数据库

  "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -pkzka20220726 tgw_pp < D:\tgw_pp_backup.sql

  🔍 验证还原结果

  还原完成后验证：
  "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -pkzka20220726 -e "USE tgw_pp; SHOW TABLES;"

🔄 现在还原数据库

  我已经为您创建了数据库还原脚本。请运行：

  D:/restore-tgw-database.bat

  📋 脚本功能说明

  这个脚本会：

1. ✅ 检查MySQL服务状态
2. ✅ 验证备份文件存在
3. ✅ 备份现有数据库（安全措施）
4. ✅ 清空并重建 tgw_pp 数据库
5. ✅ 导入服务器数据
6. ✅ 验证导入结果

  🎯 预期结果

  成功后您会看到：

- 数据表列表
- 导入的表数量统计
- "数据库已准备就绪"的提示

  🚀 完成后的下一步

  数据库还原成功后，就可以启动带有真实数据的后端服务了：

  D:/start-tgw-server.bat

  请运行 D:/restore-tgw-database.bat
