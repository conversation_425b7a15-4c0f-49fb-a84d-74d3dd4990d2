<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对接测试商户--ShowDoc</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            line-height: 1.6; 
            color: #333;
            background: #fff;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px; 
            border-radius: 10px; 
            margin-bottom: 30px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header h1 { 
            margin: 0 0 15px 0; 
            font-size: 28px;
            font-weight: bold;
        }
        .meta { 
            font-size: 14px; 
            opacity: 0.9;
        }
        .content {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        pre, code { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 6px; 
            overflow-x: auto; 
            border: 1px solid #e9ecef;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }
        table { 
            border-collapse: collapse; 
            width: 100%; 
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        table, th, td { 
            border: 1px solid #ddd; 
        }
        th, td { 
            padding: 15px 12px; 
            text-align: left; 
        }
        th { 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: bold; 
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        h1, h2, h3, h4, h5, h6 { 
            color: #2c3e50; 
            margin-top: 30px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1; 
        }
        h1 { font-size: 24px; }
        h2 { font-size: 20px; }
        h3 { font-size: 18px; }
        p { margin-bottom: 15px; }
        ul, ol { margin-bottom: 15px; padding-left: 30px; }
        li { margin-bottom: 8px; }
        a { color: #3498db; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>对接测试商户--ShowDoc</h1>
        <div class="meta">
            <p><strong>📅 抓取时间:</strong> 2025/7/21 21:07:15</p>
            <p><strong>🔗 原始链接:</strong> <a href="https://www.showdoc.com.cn/qepay/7090664555712182#%E5%8D%97%E9%9D%9E%E4%B8%80%E7%B1%BB801001001%E6%94%AF%E4%BB%98%E7%B1%BB%E5%9E%8B" target="_blank" style="color: #fff; text-decoration: underline;">https://www.showdoc.com.cn/qepay/7090664555712182#%E5%8D%97%E9%9D%9E%E4%B8%80%E7%B1%BB801001001%E6%94%AF%E4%BB%98%E7%B1%BB%E5%9E%8B</a></p>
        </div>
    </div>
    <div class="content">
        <div id="app"><div data-v-13e7ff14="" class="hello"><div data-v-168051ae="" data-v-13e7ff14=""></div> <div data-v-13e7ff14="" class="back-home"><a data-v-13e7ff14="" href="/">首页</a></div> <div data-v-737ab866="" data-v-13e7ff14="" class="pc"><div data-v-2f58a820="" data-v-737ab866="" class="header grey-bg" id="header"><div data-v-2f58a820="" class="logo float-left mt-6 ml-5  cursor-pointer"><div data-v-2f58a820=""><div data-v-2f58a820="" class="inline-block align-middle"><a data-v-2f58a820="" href="/item/index" class=""><div data-v-2f58a820="" class="icon-item"><i data-v-2f58a820="" class="far fa-arrow-left"></i></div></a></div> <div data-v-2f58a820="" class="inline-block align-middle"><div data-v-2f58a820="" class="font-bold">API文档示例</div></div></div></div> <div data-v-17e3b7cc="" data-v-737ab866="" class="header-right float-right  mt-6 mr-5" data-v-2f58a820=""><div data-v-17e3b7cc=""><!----> <!----> <div data-v-17e3b7cc="" class="el-tooltip icon-item" aria-describedby="el-tooltip-7059" tabindex="0"><i data-v-17e3b7cc="" class="far fa-share-nodes"></i></div> <!----> <!----> <div data-v-17e3b7cc="" class="el-tooltip icon-item" aria-describedby="el-tooltip-5193" tabindex="0"><i data-v-17e3b7cc="" class="far fa-user"></i></div> <div data-v-17e3b7cc="" class="el-tooltip icon-item" aria-describedby="el-tooltip-5906" tabindex="0"><i data-v-17e3b7cc="" class="far fa-circle-info"></i></div> <!----></div> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <!----></div></div> <!----> <div data-v-737ab866="" id="doc-container" class="doc-container "><div data-v-737ab866="" id="left-side"><div data-v-8ef3a8cc="" data-v-737ab866="" class="hide-scrollbar"><div data-v-8ef3a8cc="" id="left-side-menu"><div data-v-8ef3a8cc="" class="search-box el-input el-input--small el-input--suffix"><!----><input type="text" autocomplete="off" placeholder="输入关键字后按回车以搜索" class="el-input__inner"><!----><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-input__icon el-icon-search"></i><!----><!----><!----></span><!----></span><!----><!----></div> <div data-v-8ef3a8cc="" role="tree" class="el-tree"><div role="treeitem" tabindex="0" aria-disabled="" draggable="false" class="el-tree-node is-current is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7090664555712182" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">对接测试商户</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7090665527955945" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">加密规则</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7090665812289507" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">交易下单接口</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7090666413784579" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">交易异步通知</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7092192971148120" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">代付下单接口</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7092635996688727" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">代付异步通知</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7092670404746361" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">代付查询接口</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7092759287888704" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">查询账号余额</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_7092891809543484" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">所有支付类型</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="is-leaf el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-page_9436962570454261" class="custom-tree-node node-page"><i data-v-8ef3a8cc="" class="mr-2 fas fa-file-alt"></i> <span data-v-8ef3a8cc="" class="node-label">错误代码提示</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-cat_3695063" class="custom-tree-node node-folder"><i data-v-8ef3a8cc="" class="mr-2 far fa-folder-closed"></i> <span data-v-8ef3a8cc="" class="node-label">银行编码</span></span> <!----></span></div><!----></div><div role="treeitem" tabindex="-1" aria-disabled="" draggable="false" class="el-tree-node is-focusable"><div class="el-tree-node__content" style="padding-left: 0px;"><span class="el-tree-node__expand-icon el-icon-caret-right"></span><!----><!----><span data-v-8ef3a8cc="" class="custom-tree-node"><span data-v-8ef3a8cc="" id="node-cat_3695167" class="custom-tree-node node-folder"><i data-v-8ef3a8cc="" class="mr-2 far fa-folder-closed"></i> <span data-v-8ef3a8cc="" class="node-label">demo示例</span></span> <!----></span></div><!----></div><!----><div class="el-tree__drop-indicator" style="display: none;"></div></div> <!----> <!----> <!----> <!----></div></div> <div data-v-168b0178="" data-v-737ab866=""><!----></div></div> <div data-v-737ab866="" id="content-side"><div data-v-737ab866="" id="p-content"><!----> <div data-v-737ab866="" id="doc-title-box" class="doc-title-box"><span data-v-737ab866="" id="doc-title" class="v3-font-size-lg font-bold ">对接测试商户</span> <span data-v-737ab866="" class="float-right"><!----> <i data-v-737ab866="" id="full-page" class="el-icon-full-screen"></i></span></div> <div data-v-737ab866="" id="doc-body"><div data-v-737ab866="" id="page_md_content" class="page_content_main"><div data-v-737ab866="" id="editor-md" class="main-editor markdown-body editormd-html-preview"><p><strong>域名</strong><br><a href="https://pay.qeawapay.com">https://pay.qeawapay.com</a></p>
<p><strong>服务器回调IP: <br>**************<br>**************</strong></p>
<p><strong>不同国家测试商户号</strong></p>
<ul>
<li><strong>印度一类   *********    印度二类   400002002 </strong></li><li><strong>越南一类   *********    越南二类   ********* </strong></li><li><strong>墨西哥一类 887003001    墨西哥二类 887003002</strong></li><li><strong>印尼一类   500001001    印尼二类  500001002</strong></li><li><strong>巴西一类  600001001  巴西二类 600001002</strong></li><li><strong>肯尼亚一类  700001001  肯尼亚二类 700001002</strong></li><li><strong>南非一类  801001001  南非二类 801001002</strong></li><li><strong>尼日利亚一类  900001001  尼日利亚二类 900001002</strong>  </li><li><strong>马来西亚一类  101001001  马来西亚二类 101001002</strong></li><li><strong>菲律宾一类  102001001  菲律宾二类 102001002</strong></li><li><strong>泰国一类  103001001  泰国二类 103001002</strong></li><li><strong>欧美一类  105001001  欧美二类 105001002</strong></li><li><strong>土耳其一类 暂无   土耳其二类 106000000</strong></li><li><strong>加纳一类  暂无  加纳二类 107000001</strong></li><li><strong>哥伦比亚一类 暂无   哥伦比亚二类 108000001</strong></li><li><strong>巴基斯坦一类 暂无    巴基斯坦二类 109000001</strong></li><li><strong>秘鲁一类  暂无  秘鲁二类 110110001</strong></li><li><strong>孟加拉一类  暂无  孟加拉二类 112000002</strong></li><li><strong>安哥拉一类  暂无  安哥拉二类 113000000</strong></li><li><strong>俄罗斯一类  暂无  俄罗斯二类 115000000</strong></li><li><strong>厄瓜多尔一类  暂无  厄瓜多尔二类 116000000</strong></li><li><strong>埃及一类  暂无 埃及二类 117000000</strong></li><li><strong>委内瑞拉一类  暂无 委内瑞拉二类 118000000</strong></li><li><strong>约旦一类  暂无 约旦二类 119000000</strong></li><li><strong>区块链一类 、区块链二类 120001000</strong></li><li><strong>澳大利亚一类  暂无 澳大利亚二类 121000000</strong></li><li><strong>伊朗一类  暂无 伊朗二类 122000000</strong></li><li><strong>哈萨克斯坦一类  暂无 哈萨克斯坦二类 123000000</strong></li><li><strong>阿塞拜疆一类  暂无 阿塞拜疆二类 124000000</strong></li><li><strong>玻利维亚一类  暂无 玻利维亚二类 125000000</strong></li><li><strong>危地马拉一类  暂无 危地马拉二类 126000000</strong></li><li><strong>阿尔及利亚一类  暂无 阿尔及利亚二类 127000000</strong></li><li><strong>阿根廷一类  暂无 阿根廷二类 128000000</strong></li><li><strong>科特迪瓦一类  暂无 科特迪瓦二类 129000000</strong></li><li><strong>埃塞俄比亚一类  暂无 埃塞俄比亚二类 130000000</strong></li><li><strong>尼泊尔一类  暂无 尼泊尔二类 131000000</strong></li></ul>
<p><strong>以上测试商户的对接信息</strong></p>
<ul>
<li><p><strong>支付秘钥 a3a0b75ae9964c7ab08d8b8125cb6715</strong></p>
</li><li><p><strong>代付密钥 QMHYTGCP3CSHQROIMRI9FATYKQ1HPQ3Q</strong></p>
</li><li><p><strong>代付限额 1-100</strong></p>
</li></ul>
<p><strong>特别提醒：<br>1.支付秘钥和代付秘钥不同<br>2.支付秘钥商户后台可以领取；<br>3.代付秘钥需要贵司提供IP地址给商务经理，生成代付秘钥。<br>4.正式商户号支付通道编码以商号后台配置为准</strong></p>
<h2 id="h2--*********-"><a name="越南一类*********支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>越南一类*********支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>000</td>
<td>越南网银扫码,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>001</td>
<td>越南网银直连,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>002</td>
<td>越南网银转卡,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>003</td>
<td>越南MOMO</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>004</td>
<td>越南Zalo</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>005</td>
<td>越南Vtpay</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--*********-"><a name="越南二类*********支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>越南二类*********支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>020</td>
<td>越南网银扫码二类,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>021</td>
<td>越南网银直连二类,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>022</td>
<td>越南网银转卡二类,下单需要填写bank_code参数</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>023</td>
<td>越南MOMO二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>024</td>
<td>越南Zalo二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>025</td>
<td>越南Vtpay二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--*********-"><a name="印度一类*********支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>印度一类*********支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>101</td>
<td>Paytm原生一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>102</td>
<td>UPI原生一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>131</td>
<td>Paytm跑分一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>132</td>
<td>UPI跑分一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--400002002-"><a name="印度二类400002002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>印度二类400002002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>121</td>
<td>Paytm跑分二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>122</td>
<td>UPI跑分二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>151</td>
<td>Paytm原生二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>152</td>
<td>UPI原生二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>171</td>
<td>Paytm专户T2</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>172</td>
<td>UPI专户T2</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--500001001-"><a name="印尼一类500001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>印尼一类500001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>200</td>
<td>网银B2C一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>201</td>
<td>便利店一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>202</td>
<td>OVO钱包一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>203</td>
<td>QRIS钱包扫码一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>204</td>
<td>印尼DANA钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>205</td>
<td>印尼SHOPEE钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>206</td>
<td>印尼LINKAJA钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>207</td>
<td>印尼CVS便利店</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--500001002-"><a name="印尼二类500001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>印尼二类500001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>220</td>
<td>网银B2C二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>221</td>
<td>便利店二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>222</td>
<td>OVO钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>223</td>
<td>QRIS钱包扫码二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>224</td>
<td>印尼DANA钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>225</td>
<td>印尼SHOPEE钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>226</td>
<td>印尼LINKAJA钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>227</td>
<td>印尼CVS便利店二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--103001001-"><a name="泰国一类103001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>泰国一类103001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>300</td>
<td>泰国SUPEX一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>301</td>
<td>泰国UPEX一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>302</td>
<td>泰国TRUEMONEY一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>305</td>
<td>泰国AIRPAY一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>306</td>
<td>泰国网银直连一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>307</td>
<td>泰国PromptPay一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--103001002-"><a name="泰国二类103001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>泰国二类103001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>320</td>
<td>泰国SUPEX二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>321</td>
<td>泰国UPEX二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>322</td>
<td>泰国TRUEMONEY二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>325</td>
<td>泰国AIRPAY二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>326</td>
<td>泰国网银直连二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>327</td>
<td>泰国PromptPay二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--101001001-"><a name="马来西亚一类101001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>马来西亚一类101001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>400</td>
<td>马来网银转卡一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>401</td>
<td>马来扫码</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>402</td>
<td>马来钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>403</td>
<td>马来网关一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>404</td>
<td>马来FBD一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--101001002-"><a name="马来西亚二类101001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>马来西亚二类101001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>420</td>
<td>马来网银转卡二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>421</td>
<td>马来扫码二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>422</td>
<td>马来钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>423</td>
<td>马来网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>424</td>
<td>马来FBD二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--900001001-"><a name="尼日利亚一类900001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>尼日利亚一类900001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>500</td>
<td>尼日利亚网银</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>501</td>
<td>尼日利亚卡卡</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>502</td>
<td>尼日利亚OKPAY电子钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>503</td>
<td>尼日利亚PAYSTACK</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--900001002-"><a name="尼日利亚二类900001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>尼日利亚二类900001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>520</td>
<td>尼日利亚网银二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>521</td>
<td>尼日利亚卡卡二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>522</td>
<td>尼日利亚OKPAY电子钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>523</td>
<td>尼日利亚PAYSTACK二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--600001001-"><a name="巴西一类600001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>巴西一类600001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>600</td>
<td>PIX钱包一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>602</td>
<td>巴西PIC钱包</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>603</td>
<td>BOLETO钱包一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--600001002-"><a name="巴西二类600001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>巴西二类600001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>620</td>
<td>PIX钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>622</td>
<td>PIC钱包二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>623</td>
<td>BOLETO钱包二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--887003001-"><a name="墨西哥一类887003001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>墨西哥一类887003001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>700</td>
<td>STP</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>701</td>
<td>OXXO</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>702</td>
<td>OPM</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--887003002-"><a name="墨西哥二类887003002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>墨西哥二类887003002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>720</td>
<td>STP二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>721</td>
<td>OXXO二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>722</td>
<td>OPM二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--700001001-"><a name="肯尼亚一类700001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>肯尼亚一类700001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>800</td>
<td>DarajaPay一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--700001002-"><a name="肯尼亚二类700001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>肯尼亚二类700001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>820</td>
<td>DarajaPay二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--801001001-"><a name="南非一类801001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>南非一类801001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>900</td>
<td>卡转卡一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--801001002-"><a name="南非二类801001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>南非二类801001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>920</td>
<td>卡转卡二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--106000000-"><a name="土耳其二类106000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>土耳其二类106000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1620</td>
<td>土耳其网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1621</td>
<td>土耳其Papara钱包二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--102001001-"><a name="菲律宾一类102001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>菲律宾一类102001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1700</td>
<td>菲律宾网关一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1701</td>
<td>菲律宾纯一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1703</td>
<td>菲律宾网关GCASH一类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1704</td>
<td>菲律宾网关MAYA一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--102001002-"><a name="菲律宾二类102001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>菲律宾二类102001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1720</td>
<td>菲律宾网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1723</td>
<td>菲律宾网关GCASH二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1724</td>
<td>菲律宾网关MAYA二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--105001001-"><a name="欧美一类105001001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>欧美一类105001001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1800</td>
<td>欧美网关一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--105001002-"><a name="欧美二类105001002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>欧美二类105001002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1820</td>
<td>欧美网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u52A0u7EB3u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="加纳一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>加纳一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2100</td>
<td>加纳网关一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--107000001-"><a name="加纳二类107000001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>加纳二类107000001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2120</td>
<td>加纳网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u54E5u4F26u6BD4u4E9Au4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="哥伦比亚一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>哥伦比亚一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1100</td>
<td>哥伦比亚网关</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1101</td>
<td>哥伦比亚网银</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1102</td>
<td>哥伦比亚PSE</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--108000001-"><a name="哥伦比亚二类108000001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>哥伦比亚二类108000001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1120</td>
<td>哥伦比亚网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1121</td>
<td>哥伦比亚网银二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1122</td>
<td>哥伦比亚PSE二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u5DF4u57FAu65AFu5766u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="巴基斯坦一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>巴基斯坦一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td></td>
</tr>
</tbody>
</table></div>
<p>|2400  |巴基斯坦网关|<br>|2401 | 巴基斯坦JazzCash|<br>|2402 | 巴基斯坦EasyPaiSa|</p>
<h2 id="h2--109000001-"><a name="巴基斯坦二类109000001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>巴基斯坦二类109000001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2420</td>
<td>巴基斯坦网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>2421</td>
<td>巴基斯坦JazzCash二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>2422</td>
<td>巴基斯坦EasyPaiSa二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u79D8u9C81u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="秘鲁一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>秘鲁一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1200</td>
<td>秘鲁网关</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1201</td>
<td>秘鲁OPENPAY</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--110110001-"><a name="秘鲁二类110110001支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>秘鲁二类110110001支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1220</td>
<td>秘鲁网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1221</td>
<td>秘鲁OPENPAY二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u5B5Fu52A0u62C9u7F51u5173u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="孟加拉网关一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>孟加拉网关一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2200</td>
<td>孟加拉网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--112000002-"><a name="孟加拉网关二类112000002支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>孟加拉网关二类112000002支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2220</td>
<td>孟加拉网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u5B89u54E5u62C9u7F51u5173u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="安哥拉网关一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>安哥拉网关一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2700</td>
<td>安哥拉网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--113000000-"><a name="安哥拉网关二类113000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>安哥拉网关二类113000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2720</td>
<td>安哥拉网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2-u4FC4u7F57u65AFu7F51u5173u4E00u7C7Bu652Fu4ED8u7C7Bu578B"><a name="俄罗斯网关一类支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>俄罗斯网关一类支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2800</td>
<td>俄罗斯网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--115000000-"><a name="俄罗斯网关二类115000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>俄罗斯网关二类115000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2820</td>
<td>俄罗斯网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--116000000-"><a name="厄瓜多尔一类116000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>厄瓜多尔一类116000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2600</td>
<td>厄瓜多尔网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--116000000-"><a name="厄瓜多尔二类116000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>厄瓜多尔二类116000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2620</td>
<td>厄瓜多尔网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--117000000-"><a name="埃及一类117000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>埃及一类117000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1400</td>
<td>埃及网关</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>1430</td>
<td>埃及网关一类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--117000000-"><a name="埃及二类117000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>埃及二类117000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1420</td>
<td>埃及网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--118000000-"><a name="委内瑞拉一类118000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>委内瑞拉一类118000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2500</td>
<td>委内瑞拉网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--118000000-"><a name="委内瑞拉二类118000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>委内瑞拉二类118000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2520</td>
<td>委内瑞拉网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--119000000-"><a name="约旦一类119000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>约旦一类119000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3000</td>
<td>约旦网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--119000000-"><a name="约旦二类119000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>约旦二类119000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3020</td>
<td>约旦网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--120001000-"><a name="区块链一类120001000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>区块链一类120001000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3100</td>
<td>USDT_TRC</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--120001000-"><a name="区块链二类120001000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>区块链二类120001000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3101</td>
<td>USDT_ERC</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--121000000-"><a name="澳大利亚一类121000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>澳大利亚一类121000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2900</td>
<td>澳大利亚网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--121000000-"><a name="澳大利亚二类121000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>澳大利亚二类121000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>2920</td>
<td>澳大利亚网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--122000000-"><a name="伊朗一类122000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>伊朗一类122000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3500</td>
<td>伊朗网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--122000000-"><a name="伊朗二类122000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>伊朗二类122000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3520</td>
<td>伊朗网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--123000000-"><a name="哈萨克斯坦一类123000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>哈萨克斯坦一类123000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3400</td>
<td>哈萨克斯坦网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--123000000-"><a name="哈萨克斯坦二类123000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>哈萨克斯坦二类123000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>3420</td>
<td>哈萨克斯坦网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--124000000-"><a name="阿塞拜疆一类124000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿塞拜疆一类124000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4200</td>
<td>阿塞拜疆网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--124000000-"><a name="阿塞拜疆二类124000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿塞拜疆二类124000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4220</td>
<td>阿塞拜疆网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--125000000-"><a name="玻利维亚一类125000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>玻利维亚一类125000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4300</td>
<td>玻利维亚网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--125000000-"><a name="玻利维亚二类125000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>玻利维亚二类125000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4320</td>
<td>玻利维亚网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--126000000-"><a name="危地马拉一类126000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>危地马拉一类126000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4500</td>
<td>危地马拉网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--126000000-"><a name="危地马拉二类126000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>危地马拉二类126000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4520</td>
<td>危地马拉网关二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--127000000-"><a name="阿尔及利亚一类127000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿尔及利亚一类127000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4600</td>
<td>阿尔及利亚网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--127000000-"><a name="阿尔及利亚二类127000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿尔及利亚二类127000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4620</td>
<td>阿尔及利亚二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--128000000-"><a name="阿根廷一类128000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿根廷一类128000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1300</td>
<td>阿根廷网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--128000000-"><a name="阿根廷二类128000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>阿根廷二类128000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>1320</td>
<td>阿根廷二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--129000000-"><a name="科特迪瓦一类129000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>科特迪瓦一类129000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4700</td>
<td>科特迪瓦网关</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>4701</td>
<td>科特迪瓦 WAVA</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>4702</td>
<td>科特迪瓦  ORANGE</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--129000000-"><a name="科特迪瓦二类129000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>科特迪瓦二类129000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4720</td>
<td>科特迪瓦网关二类</td>
</tr>
<tr style="background-color: rgb(255, 255, 255);">
<td>4721</td>
<td>科特迪瓦 WAVA二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--130000000-"><a name="埃塞俄比亚一类130000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>埃塞俄比亚一类130000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4800</td>
<td>埃塞俄比亚网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--130000000-"><a name="埃塞俄比亚二类130000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>埃塞俄比亚二类130000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>4820</td>
<td>埃塞俄比亚二类</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--131000000-"><a name="尼泊尔一类131000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>尼泊尔一类131000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>5200</td>
<td>尼泊尔网关</td>
</tr>
</tbody>
</table></div>
<h2 id="h2--131000000-"><a name="尼泊尔二类131000000支付类型" class="reference-link"></a><span class="header-link octicon octicon-link"></span>尼泊尔二类131000000支付类型</h2><div style="width: 100%;overflow-x: auto;"><table>
<thead>
<tr>
<th style="width: 408px;">支付类型</th>
<th style="width: 408px;">描述</th>
</tr>
</thead>
<tbody>
<tr style="background-color: rgb(255, 255, 255);">
<td>5220</td>
<td>尼泊尔网关二类</td>
</tr>
</tbody>
</table></div>
</div></div> <!----></div></div></div> <div data-v-737ab866="" id="right-side"><div data-v-737ab866="" id="toc-pos" style="width: 160px;"><div class="markdown-toc editormd-markdown-toc open-list"><ul class="markdown-toc-list"><li><a class="toc-level-2" href="#越南一类*********支付类型" level="2">越南一类*********支付类型</a></li><li><a class="toc-level-2" href="#越南二类*********支付类型" level="2">越南二类*********支付类型</a></li><li><a class="toc-level-2" href="#印度一类*********支付类型" level="2">印度一类*********支付类型</a></li><li><a class="toc-level-2" href="#印度二类400002002支付类型" level="2">印度二类400002002支付类型</a></li><li><a class="toc-level-2" href="#印尼一类500001001支付类型" level="2">印尼一类500001001支付类型</a></li><li><a class="toc-level-2" href="#印尼二类500001002支付类型" level="2">印尼二类500001002支付类型</a></li><li><a class="toc-level-2" href="#泰国一类103001001支付类型" level="2">泰国一类103001001支付类型</a></li><li><a class="toc-level-2" href="#泰国二类103001002支付类型" level="2">泰国二类103001002支付类型</a></li><li><a class="toc-level-2" href="#马来西亚一类101001001支付类型" level="2">马来西亚一类101001001支付类型</a></li><li><a class="toc-level-2" href="#马来西亚二类101001002支付类型" level="2">马来西亚二类101001002支付类型</a></li><li><a class="toc-level-2" href="#尼日利亚一类900001001支付类型" level="2">尼日利亚一类900001001支付类型</a></li><li><a class="toc-level-2" href="#尼日利亚二类900001002支付类型" level="2">尼日利亚二类900001002支付类型</a></li><li><a class="toc-level-2" href="#巴西一类600001001支付类型" level="2">巴西一类600001001支付类型</a></li><li><a class="toc-level-2" href="#巴西二类600001002支付类型" level="2">巴西二类600001002支付类型</a></li><li><a class="toc-level-2" href="#墨西哥一类887003001支付类型" level="2">墨西哥一类887003001支付类型</a></li><li><a class="toc-level-2" href="#墨西哥二类887003002支付类型" level="2">墨西哥二类887003002支付类型</a></li><li><a class="toc-level-2" href="#肯尼亚一类700001001支付类型" level="2">肯尼亚一类700001001支付类型</a></li><li><a class="toc-level-2" href="#肯尼亚二类700001002支付类型" level="2">肯尼亚二类700001002支付类型</a></li><li><a class="toc-level-2 current" href="#南非一类801001001支付类型" level="2">南非一类801001001支付类型</a></li><li><a class="toc-level-2" href="#南非二类801001002支付类型" level="2">南非二类801001002支付类型</a></li><li><a class="toc-level-2" href="#土耳其二类106000000支付类型" level="2">土耳其二类106000000支付类型</a></li><li><a class="toc-level-2" href="#菲律宾一类102001001支付类型" level="2">菲律宾一类102001001支付类型</a></li><li><a class="toc-level-2" href="#菲律宾二类102001002支付类型" level="2">菲律宾二类102001002支付类型</a></li><li><a class="toc-level-2" href="#欧美一类105001001支付类型" level="2">欧美一类105001001支付类型</a></li><li><a class="toc-level-2" href="#欧美二类105001002支付类型" level="2">欧美二类105001002支付类型</a></li><li><a class="toc-level-2" href="#加纳一类支付类型" level="2">加纳一类支付类型</a></li><li><a class="toc-level-2" href="#加纳二类107000001支付类型" level="2">加纳二类107000001支付类型</a></li><li><a class="toc-level-2" href="#哥伦比亚一类支付类型" level="2">哥伦比亚一类支付类型</a></li><li><a class="toc-level-2" href="#哥伦比亚二类108000001支付类型" level="2">哥伦比亚二类108000001支付类型</a></li><li><a class="toc-level-2" href="#巴基斯坦一类支付类型" level="2">巴基斯坦一类支付类型</a></li><li><a class="toc-level-2" href="#巴基斯坦二类109000001支付类型" level="2">巴基斯坦二类109000001支付类型</a></li><li><a class="toc-level-2" href="#秘鲁一类支付类型" level="2">秘鲁一类支付类型</a></li><li><a class="toc-level-2" href="#秘鲁二类110110001支付类型" level="2">秘鲁二类110110001支付类型</a></li><li><a class="toc-level-2" href="#孟加拉网关一类支付类型" level="2">孟加拉网关一类支付类型</a></li><li><a class="toc-level-2" href="#孟加拉网关二类112000002支付类型" level="2">孟加拉网关二类112000002支付类型</a></li><li><a class="toc-level-2" href="#安哥拉网关一类支付类型" level="2">安哥拉网关一类支付类型</a></li><li><a class="toc-level-2" href="#安哥拉网关二类113000000支付类型" level="2">安哥拉网关二类113000000支付类型</a></li><li><a class="toc-level-2" href="#俄罗斯网关一类支付类型" level="2">俄罗斯网关一类支付类型</a></li><li><a class="toc-level-2" href="#俄罗斯网关二类115000000支付类型" level="2">俄罗斯网关二类115000000支付类型</a></li><li><a class="toc-level-2" href="#厄瓜多尔一类116000000支付类型" level="2">厄瓜多尔一类116000000支付类型</a></li><li><a class="toc-level-2" href="#厄瓜多尔二类116000000支付类型" level="2">厄瓜多尔二类116000000支付类型</a></li><li><a class="toc-level-2" href="#埃及一类117000000支付类型" level="2">埃及一类117000000支付类型</a></li><li><a class="toc-level-2" href="#埃及二类117000000支付类型" level="2">埃及二类117000000支付类型</a></li><li><a class="toc-level-2" href="#委内瑞拉一类118000000支付类型" level="2">委内瑞拉一类118000000支付类型</a></li><li><a class="toc-level-2" href="#委内瑞拉二类118000000支付类型" level="2">委内瑞拉二类118000000支付类型</a></li><li><a class="toc-level-2" href="#约旦一类119000000支付类型" level="2">约旦一类119000000支付类型</a></li><li><a class="toc-level-2" href="#约旦二类119000000支付类型" level="2">约旦二类119000000支付类型</a></li><li><a class="toc-level-2" href="#区块链一类120001000支付类型" level="2">区块链一类120001000支付类型</a></li><li><a class="toc-level-2" href="#区块链二类120001000支付类型" level="2">区块链二类120001000支付类型</a></li><li><a class="toc-level-2" href="#澳大利亚一类121000000支付类型" level="2">澳大利亚一类121000000支付类型</a></li><li><a class="toc-level-2" href="#澳大利亚二类121000000支付类型" level="2">澳大利亚二类121000000支付类型</a></li><li><a class="toc-level-2" href="#伊朗一类122000000支付类型" level="2">伊朗一类122000000支付类型</a></li><li><a class="toc-level-2" href="#伊朗二类122000000支付类型" level="2">伊朗二类122000000支付类型</a></li><li><a class="toc-level-2" href="#哈萨克斯坦一类123000000支付类型" level="2">哈萨克斯坦一类123000000支付类型</a></li><li><a class="toc-level-2" href="#哈萨克斯坦二类123000000支付类型" level="2">哈萨克斯坦二类123000000支付类型</a></li><li><a class="toc-level-2" href="#阿塞拜疆一类124000000支付类型" level="2">阿塞拜疆一类124000000支付类型</a></li><li><a class="toc-level-2" href="#阿塞拜疆二类124000000支付类型" level="2">阿塞拜疆二类124000000支付类型</a></li><li><a class="toc-level-2" href="#玻利维亚一类125000000支付类型" level="2">玻利维亚一类125000000支付类型</a></li><li><a class="toc-level-2" href="#玻利维亚二类125000000支付类型" level="2">玻利维亚二类125000000支付类型</a></li><li><a class="toc-level-2" href="#危地马拉一类126000000支付类型" level="2">危地马拉一类126000000支付类型</a></li><li><a class="toc-level-2" href="#危地马拉二类126000000支付类型" level="2">危地马拉二类126000000支付类型</a></li><li><a class="toc-level-2" href="#阿尔及利亚一类127000000支付类型" level="2">阿尔及利亚一类127000000支付类型</a></li><li><a class="toc-level-2" href="#阿尔及利亚二类127000000支付类型" level="2">阿尔及利亚二类127000000支付类型</a></li><li><a class="toc-level-2" href="#阿根廷一类128000000支付类型" level="2">阿根廷一类128000000支付类型</a></li><li><a class="toc-level-2" href="#阿根廷二类128000000支付类型" level="2">阿根廷二类128000000支付类型</a></li><li><a class="toc-level-2" href="#科特迪瓦一类129000000支付类型" level="2">科特迪瓦一类129000000支付类型</a></li><li><a class="toc-level-2" href="#科特迪瓦二类129000000支付类型" level="2">科特迪瓦二类129000000支付类型</a></li><li><a class="toc-level-2" href="#埃塞俄比亚一类130000000支付类型" level="2">埃塞俄比亚一类130000000支付类型</a></li><li><a class="toc-level-2" href="#埃塞俄比亚二类130000000支付类型" level="2">埃塞俄比亚二类130000000支付类型</a></li><li><a class="toc-level-2" href="#尼泊尔一类131000000支付类型" level="2">尼泊尔一类131000000支付类型</a></li><li><a class="toc-level-2" href="#尼泊尔二类131000000支付类型" level="2">尼泊尔二类131000000支付类型</a><ul></ul></li></ul></div></div></div></div> <div data-v-737ab866="" class="el-backtop" style="right: 40px; bottom: 40px;"><i class="el-icon-caret-top"></i></div> <div data-v-737ab866=""></div> <!----></div> <!----> <!----> <!----> <!----> <div data-v-065ddbfc="" data-v-13e7ff14=""></div></div></div><div class="index-item-block"><div>INDEX_HTML</div><div>ITEM_HTML</div></div><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/manifest.763aada0159bf6d465bd.js"></script><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/vendor.e06a04bea902a8c6f95c.js"></script><script type="text/javascript" src="https://showdoc.cdn.dfyun.com.cn/static/js/app.8efb3b750dc5c05a073d.js"></script><script>// CDN容灾
  if (typeof window.SHOWDOC_CDN_STASTUS == 'undefined') {
    // 把全局变量中的静态资源路径给改一下
    window.DocConfig.staticPath = '/static/'
    changeCDNToRoot()
    setTimeout(() => {
      changeCDNToRoot()
    }, 500)
}
  // 修改js路径到Root路径，不使用cdn
  function changeCDNToRoot() {
    const linkTags = document.getElementsByTagName('link') // 获取所有 link 标签
    const scriptTags = document.getElementsByTagName('script') // 获取所有 script 标签
    const imgTags = document.getElementsByTagName('img') // 获取所有 img 标签

    const loadScript = src => {
      return new Promise((resolve, reject) => {
        const scriptTag = document.createElement('script')
        scriptTag.src = src
        scriptTag.onload = resolve
        scriptTag.onerror = reject
        document.head.appendChild(scriptTag)
      })
    }

    const loadStylesheet = href => {
      return new Promise((resolve, reject) => {
        const linkTag = document.createElement('link')
        linkTag.href = href
        linkTag.rel = 'stylesheet'
        linkTag.onload = resolve
        linkTag.onerror = reject
        document.head.appendChild(linkTag)
      })
    }

    const updateLinkTags = async () => {
      for (let i = 0; i < linkTags.length; i++) {
        const linkTag = linkTags[i]
        const href = linkTag.getAttribute('href')

        if (href && href.includes('dfyun')) {
          const newHref = href.replace(
            'https://showdoc.cdn.dfyun.com.cn/',
            '/'
          )
          await loadStylesheet(newHref)
          linkTag.parentNode.removeChild(linkTag)
        }
      }
    }

    const updateScriptTags = async () => {
      for (let i = 0; i < scriptTags.length; i++) {
        const scriptTag = scriptTags[i]
        const src = scriptTag.getAttribute('src')

        if (src && src.includes('dfyun') && !src.includes('showdoc-server')) {
          const newSrc = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')
          await loadScript(newSrc)
          scriptTag.parentNode.removeChild(scriptTag)
        }
      }
    }

    const updateImgTags = () => {
      for (let i = 0; i < imgTags.length; i++) {
        const imgTag = imgTags[i]
        let src = imgTag.getAttribute('src')

        if (src && src.includes('dfyun')) {
          src = src.replace('https://showdoc.cdn.dfyun.com.cn/', '/')
          imgTag.setAttribute('src', src)
        }
      }
    }

    updateLinkTags()
      .then(() => {
        updateScriptTags()
          .then(() => {
            console.log('All scripts loaded successfully')
            updateImgTags()
          })
          .catch(error => {
            console.error('Error loading scripts:', error)
          })
      })
      .catch(error => {
        console.error('Error loading stylesheets:', error)
      })
  }</script><script language="JavaScript">// 防止被镜像站
  var host = window.location.host
  if (
    host.indexOf('localhost') === -1 &&
    host.indexOf('wu') === -1 &&
    host.indexOf('showdoc') === -1 &&
    host.indexOf('star7th.com') === -1 &&
    host.indexOf('192.168.') === -1 &&
    host.indexOf('gaoyixia.com') === -1 &&
    host.indexOf('dongjingyu.cn') === -1 &&
    host.indexOf('127.0.0.1') === -1
  ) {
    var href = window.location.href
    var j = href.replace(new RegExp(host, 'g'), 'www.showdoc.com.cn')
    window.location.href = j
  }</script><script>// 百度统计
  if (window.location.host == 'www.showdoc.com.cn') {
    var _hmt = _hmt || []
    ;(function() {
      var hm = document.createElement('script')
      hm.src = 'https://hm.baidu.com/hm.js?84e82fa31c8ca8671f6b6f972b7e54fb'
      var s = document.getElementsByTagName('script')[0]
      s.parentNode.insertBefore(hm, s)
    })()
  }</script>
    </div>
    <div style="text-align: center; margin-top: 40px; color: #6c757d; font-size: 12px;">
        <p>📄 此文档由 Playwright 自动抓取生成</p>
    </div>
</body>
</html>