# 团购APP API接口文档

## 目录

- [认证相关接口](#认证相关接口)
- [首页相关接口](#首页相关接口)
- [商品相关接口](#商品相关接口)
- [活动相关接口](#活动相关接口)
- [拼团相关接口](#拼团相关接口)
- [订单相关接口](#订单相关接口)
- [支付相关接口](#支付相关接口)
- [用户相关接口](#用户相关接口)
- [地址管理接口](#地址管理接口)
- [收藏功能接口](#收藏功能接口)
- [钱包相关接口](#钱包相关接口)
- [文件上传接口](#文件上传接口)
- [客服支持接口](#客服支持接口)
- [身份认证接口](#身份认证接口)
- [商品评价接口](#商品评价接口)
- [优惠券接口](#优惠券接口)
- [通知消息接口](#通知消息接口)
- [数据统计接口](#数据统计接口)

按功能查找: 使用目录快速定位到相关接口分类

1. 按用途查找:
   - 首页开发 → 首页相关接口
   - 商品展示 → 商品相关接口 + 活动相关接口
   - 拼团功能 → 拼团相关接口
   - 用户中心 → 用户相关接口 + 地址管理接口 + 收藏功能接口
   - 交易流程 → 订单相关接口 + 支付相关接口
   - 钱包功能 → 钱包相关接口
3. 按开发阶段查找: 认证 → 首页 → 商品 → 拼团 → 订单 → 支付


  📋 API实现状态总结

  ✅ 已实现的接口 (核心功能)

- 认证相关接口 (4个) - 登录、注册、验证码、登出
- 首页相关接口 (5个) - 首页数据、轮播图、搜索、商品列表、分类
- 商品相关接口 (3个) - 商品列表、详情、分类
- 活动相关接口 (2个) - 活动列表、详情
- 拼团相关接口 (6个) - 创建、加入、分享、取消、详情、活跃拼团
- 订单相关接口 (5个) - 订单CRUD、物流信息
- 支付相关接口 (4个) - 支付创建、状态查询、方式列表、下单
- 用户相关接口 (1个) - 我的团队
- 地址管理接口 (4个) - 地址CRUD
- 钱包相关接口 (3个) - 钱包信息、提现记录、提现申请
- 身份认证接口 (4个) - 实名认证全流程

  ⏳ 未实现的接口 (增强功能)

- 收藏功能接口 (3个) - 需要后端实现
- 文件上传接口 (4个) - 需要后端实现
- 客服支持接口 (3个) - 需要后端实现
- 商品评价接口 (2个) - 需要后端实现
- 优惠券接口 (4个) - 需要后端实现
- 通知消息接口 (4个) - 需要后端实现

---

## 认证相关接口

### 1. 用户登录

**接口地址：** `POST /app/v1/auth`

**请求参数：**

```json
{
  "type": "login",
  "phone": "***********",
  "password": "123456",
  "captchaCode": "9256",
  "captchaUuid": "4604cf4dc962467a806efc7357c519c0"
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "employeeId": 81,
    "userType": "H5",
    "loginName": "***********",
    "actualName": "***********",
    "gender": 0,
    "phone": "***********",
    "departmentId": 9,
    "departmentName": "客户",
    "positionId": 5,
    "riskLevel": 0,
    "childCount": 0,
    "noviceCount": 3,
    "disabledFlag": false,
    "administratorFlag": false,
    "token": "09fa852cca4042f4842dc4a9cff9f9e3",
    "menuList": [],
    "needUpdatePwdFlag": false,
    "lastLoginIp": "127.0.0.1",
    "lastLoginIpRegion": "0|0|0|内网IP|内网IP",
    "lastLoginUserAgent": "Mozilla/5.0...",
    "lastLoginTime": "2025-07-02 23:58:33",
    "userName": "***********",
    "userId": 81
  },
  "dataType": 1
}
```

**字段说明：**

- `code`: 0表示成功，其它有错误
- `childCount`: 下级数量
- `noviceCount`: 免费拼团次数
- `token`: 用户登录凭证

---

### 2. 用户注册

**接口地址：** `POST /app/v1/auth`

**请求参数：**

```json
{
  "type": "register",
  "phone": "13833883389",
  "password": "123456",
  "confirmPassword": "123456",
  "captchaCode": "5831",
  "captchaUuid": "684e05cac0814f01a5e1d975fa330cd8",
  "agreed_to_terms": true,
  "invite_code": ""
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "experience": 500
  },
  "dataType": 1
}
```

**字段说明：**

- `experience`: 注册成功获得的体验金

---

### 3. 获取图形验证码

**接口地址：** `GET /app/v1/captcha`

**响应示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {
    "captchaUuid": "4604cf4dc962467a806efc7357c519c0",
    "captchaBase64Image": "data:image/png;base64,/9j/4AAQSkZJRgABAgAAAQAB...",
    "expireSeconds": 65
  },
  "dataType": 1
}
```

**字段说明：**

- `captchaUuid`: 验证码唯一标识
- `captchaBase64Image`: Base64编码的验证码图片
- `expireSeconds`: 验证码过期时间（秒）

---

### 4. 用户登出

**接口地址：** `GET /app/v1/logout`

**响应示例：**

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "dataType": 1
}
```

---

## 首页相关接口

### 1. 获取首页数据

**接口地址：** `GET /api/v1/home` 或 `GET /api/v1/data/home`

**查询参数：**

- `language`: 语言设置（zh-CN/vi，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "banners": [
      {
        "id": 1,
        "title": "限时拼团活动",
        "image": "/uploads/banners/banner1.jpg",
        "link": "/activity/1",
        "sort": 1,
        "status": "active"
      }
    ],
    "categories": [
      {
        "id": 1,
        "name": "数码产品",
        "icon": "/uploads/categories/digital.png",
        "sort": 1,
        "productCount": 25
      }
    ],
    "initialProducts": {
      "list": [
        {
          "id": 1,
          "name": "苹果正品耳机",
          "price": 99.00,
          "groupPrice": 89.00,
          "image": "/uploads/products/headphone.jpg",
          "sales": 1520,
          "redPacketAmount": 4.50,
          "groupSize": 3
        }
      ],
      "pageNum": 1,
      "pageSize": 20,
      "total": 50,
      "pages": 3
    }
  },
  "dataType": 1
}
```

**字段说明：**

- `banners`: 首页轮播图数据
- `categories`: 商品分类数据
- `initialProducts`: 首页初始商品列表

---

### 2. 获取轮播图

**接口地址：** `GET /api/v1/data/banners`

**查询参数：**

- `language`: 语言设置（zh-CN/vi，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 1,
      "title": "限时拼团活动",
      "image": "/uploads/banners/banner1.jpg",
      "link": "/activity/1",
      "sort": 1,
      "status": "active",
      "startTime": "2025-07-27 00:00:00",
      "endTime": "2025-07-30 23:59:59"
    }
  ],
  "dataType": 1
}
```

**字段说明：**

- `id`: 轮播图ID
- `title`: 轮播图标题
- `image`: 轮播图片地址
- `link`: 点击跳转链接
- `sort`: 排序权重
- `status`: 显示状态
- `startTime`: 开始时间
- `endTime`: 结束时间

---

### 3. 商品搜索

**接口地址：** `GET /api/v1/search`

**查询参数：**

- `q`: 搜索关键词（必需）
- `type`: 搜索类型（all/product/brand/activity，可选）
- `page`: 页码（可选）
- `per_page`: 每页数量（可选）
- `category`: 分类筛选（可选）
- `price_min`: 最低价格（可选）
- `price_max`: 最高价格（可选）
- `sort`: 排序方式（price_asc/price_desc/sales_desc，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "苹果正品耳机",
        "price": 99.00,
        "groupPrice": 89.00,
        "image": "/uploads/products/headphone.jpg",
        "categoryName": "数码产品",
        "sales": 1520,
        "redPacketAmount": 4.50,
        "groupSize": 3,
        "matchType": "product"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 15,
    "pages": 1,
    "keyword": "耳机"
  },
  "dataType": 1
}
```

**字段说明：**

- `matchType`: 匹配类型（product/brand/activity）
- `keyword`: 搜索关键词

---

### 4. 获取商品列表（首页用）

**接口地址：** `GET /api/v1/data/products`

**查询参数：**

- `category`: 分类筛选（recommended/special/hot/all，可选）
- `group_type`: 拼团类型筛选（3/10，可选）
- `page`: 页码（可选）
- `per_page`: 每页数量（可选）
- `sort`: 排序方式（可选）
- `price_range`: 价格区间（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "苹果正品耳机",
        "price": 99.00,
        "groupPrice": 89.00,
        "image": "/uploads/products/headphone.jpg",
        "categoryName": "数码产品",
        "sales": 1520,
        "redPacketAmount": 4.50,
        "groupSize": 3,
        "isRecommended": true,
        "isHot": true
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 50,
    "pages": 3
  },
  "dataType": 1
}
```

**字段说明：**

- `isRecommended`: 是否推荐商品
- `isHot`: 是否热门商品

---

### 5. 获取分类数据（首页用）

**接口地址：** `GET /api/v1/data/categories`

**查询参数：**

- `language`: 语言设置（zh-CN/vi，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 1,
      "name": "数码产品",
      "icon": "/uploads/categories/digital.png",
      "sort": 1,
      "status": "active",
      "productCount": 25,
      "isHot": true
    }
  ],
  "dataType": 1
}
```

**字段说明：**

- `isHot`: 是否热门分类

---

## 商品相关接口

### 1. 获取商品列表

**接口地址：** `GET /api/v1/products`

**查询参数：**

- `pageNum`: 页码（从1开始）
- `pageSize`: 每页数量
- `categoryId`: 分类ID（可选）
- `type`: 商品类型（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "苹果正品耳机",
        "price": 99.00,
        "groupPrice": 89.00,
        "image": "/uploads/products/headphone.jpg",
        "categoryId": 1,
        "categoryName": "数码产品",
        "stock": 100,
        "sales": 1520,
        "status": "active",
        "description": "高品质立体声耳机，音质清晰",
        "redPacketAmount": 4.50,
        "groupSize": 3,
        "createTime": "2025-07-20 10:00:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 50,
    "pages": 3
  },
  "dataType": 1
}
```

**字段说明：**

- `id`: 商品ID
- `name`: 商品名称
- `price`: 原价
- `groupPrice`: 拼团价
- `image`: 商品图片
- `categoryId`: 分类ID
- `categoryName`: 分类名称
- `stock`: 库存数量
- `sales`: 销量
- `status`: 商品状态
- `redPacketAmount`: 红包金额
- `groupSize`: 拼团人数

---

### 2. 获取商品详情

**接口地址：** `GET /api/v1/product/{商品ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 1,
    "name": "苹果正品耳机",
    "price": 99.00,
    "groupPrice": 89.00,
    "images": [
      "/uploads/products/headphone1.jpg",
      "/uploads/products/headphone2.jpg"
    ],
    "categoryId": 1,
    "categoryName": "数码产品",
    "stock": 100,
    "sales": 1520,
    "status": "active",
    "description": "高品质立体声耳机，音质清晰，佩戴舒适",
    "specifications": "颜色：黑色，重量：150g",
    "redPacketAmount": 4.50,
    "groupSize": 3,
    "skus": [
      {
        "id": 26,
        "name": "黑色",
        "price": 99.00,
        "groupPrice": 89.00,
        "stock": 50
      }
    ],
    "createTime": "2025-07-20 10:00:00"
  },
  "dataType": 1
}
```

**字段说明：**

- `images`: 商品图片数组
- `specifications`: 商品规格
- `skus`: 商品SKU信息

---

### 3. 获取商品分类

**接口地址：** `GET /api/v1/categories`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 1,
      "name": "数码产品",
      "icon": "/uploads/categories/digital.png",
      "sort": 1,
      "status": "active",
      "productCount": 25
    },
    {
      "id": 2,
      "name": "家居用品",
      "icon": "/uploads/categories/home.png",
      "sort": 2,
      "status": "active",
      "productCount": 18
    }
  ],
  "dataType": 1
}
```

**字段说明：**

- `id`: 分类ID
- `name`: 分类名称
- `icon`: 分类图标
- `sort`: 排序权重
- `status`: 分类状态
- `productCount`: 该分类商品数量

---

## 活动相关接口

### 1. 获取活动列表

**接口地址：** `GET /api/v1/activities`

**查询参数：**

- `pageNum`: 页码（从1开始）
- `pageSize`: 每页数量
- `status`: 活动状态（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 4,
        "title": "限时拼团活动",
        "description": "超值拼团，更多优惠",
        "productId": 1,
        "productName": "苹果正品耳机",
        "productImage": "/uploads/products/headphone.jpg",
        "originalPrice": 99.00,
        "groupPrice": 89.00,
        "groupSize": 3,
        "startTime": "2025-07-27 00:00:00",
        "endTime": "2025-07-30 23:59:59",
        "status": "active",
        "participants": 2,
        "maxParticipants": 100
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 10,
    "pages": 1
  },
  "dataType": 1
}
```

**字段说明：**

- `id`: 活动ID
- `title`: 活动标题
- `productId`: 关联商品ID
- `originalPrice`: 商品原价
- `groupPrice`: 拼团价格
- `groupSize`: 拼团人数要求
- `startTime`: 活动开始时间
- `endTime`: 活动结束时间
- `participants`: 当前参与人数
- `maxParticipants`: 最大参与人数

---

### 2. 获取活动详情

**接口地址：** `GET /api/v1/activities/{活动ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 4,
    "title": "限时拼团活动",
    "description": "超值拼团，更多优惠等你来",
    "productId": 1,
    "productName": "苹果正品耳机",
    "productImage": "/uploads/products/headphone.jpg",
    "productImages": [
      "/uploads/products/headphone1.jpg",
      "/uploads/products/headphone2.jpg"
    ],
    "originalPrice": 99.00,
    "groupPrice": 89.00,
    "groupSize": 3,
    "startTime": "2025-07-27 00:00:00",
    "endTime": "2025-07-30 23:59:59",
    "status": "active",
    "participants": 2,
    "maxParticipants": 100,
    "rules": "活动规则说明...",
    "skus": [
      {
        "id": 26,
        "name": "黑色",
        "price": 99.00,
        "groupPrice": 89.00,
        "stock": 50
      }
    ]
  },
  "dataType": 1
}
```

---

## 拼团相关接口

### 1. 获取拼团详情

**接口地址：** `GET /api/v1/groups/{拼团ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 123,
    "activityId": 4,
    "productId": 1,
    "productName": "苹果正品耳机",
    "productImage": "/uploads/products/headphone.jpg",
    "groupPrice": 89.00,
    "groupSize": 3,
    "currentCount": 2,
    "status": "active",
    "startTime": "2025-07-27 10:00:00",
    "endTime": "2025-07-27 22:00:00",
    "participants": [
      {
        "userId": 81,
        "userName": "用户1",
        "avatar": "/uploads/avatars/user1.jpg",
        "joinTime": "2025-07-27 10:05:00",
        "isLeader": true
      }
    ],
    "timeRemaining": 43200000
  },
  "dataType": 1
}
```

**字段说明：**

- `currentCount`: 当前参与人数
- `participants`: 参与用户列表
- `isLeader`: 是否为团长
- `timeRemaining`: 剩余时间（毫秒）

---

### 2. 创建拼团

**接口地址：** `POST /api/v1/groups`

**请求参数：**

```json
{
  "productId": 1,
  "activityId": 4,
  "skuId": 26,
  "shippingAddressId": 8
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "groupId": 123,
    "orderId": 456,
    "paymentRequired": true,
    "paymentAmount": 89.00
  },
  "dataType": 1
}
```

---

### 3. 加入拼团

**接口地址：** `POST /api/v1/groups/{拼团ID}/join`

**请求参数：**

```json
{
  "skuId": 26,
  "shippingAddressId": 8
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "orderId": 457,
    "paymentRequired": true,
    "paymentAmount": 89.00,
    "groupStatus": "success"
  },
  "dataType": 1
}
```

**字段说明：**

- `groupStatus`: 拼团状态（waiting/success/failed）

---

### 4. 分享拼团

**接口地址：** `POST /api/v1/groups/{拼团ID}/share`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "shareUrl": "https://app.example.com/share/group/123",
    "shareTitle": "快来参加我的拼团吧！",
    "shareDescription": "苹果正品耳机，3人拼团仅需89元",
    "shareImage": "/uploads/products/headphone.jpg"
  },
  "dataType": 1
}
```

---

### 5. 取消拼团

**接口地址：** `POST /api/v1/groups/{拼团ID}/cancel`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "refundAmount": 89.00,
    "refundTime": "2025-07-27 15:30:00"
  },
  "dataType": 1
}
```

---

### 6. 获取商品活跃拼团

**接口地址：** `GET /api/v1/products/{商品ID}/groups`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `status`: 拼团状态（active/waiting）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 123,
        "currentCount": 2,
        "groupSize": 3,
        "timeRemaining": 43200000,
        "leader": {
          "userName": "用户1",
          "avatar": "/uploads/avatars/user1.jpg"
        }
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "total": 5,
    "pages": 1
  },
  "dataType": 1
}
```

---

## 订单相关接口

### 1. 获取订单列表

**接口地址：** `GET /api/v1/orders`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `status`: 订单状态（可选）
- `type`: 订单类型（group/direct，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 456,
        "orderSn": "************",
        "productName": "苹果正品耳机",
        "productImage": "/uploads/products/headphone.jpg",
        "amount": 89.00,
        "status": "paid",
        "type": "group",
        "groupId": 123,
        "createTime": "2025-07-27 10:05:00",
        "payTime": "2025-07-27 10:06:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 10,
    "pages": 1
  },
  "dataType": 1
}
```

**字段说明：**

- `orderSn`: 订单编号
- `status`: 订单状态（unpaid/paid/shipped/completed/cancelled）
- `type`: 订单类型（group/direct）

---

### 2. 获取订单详情

**接口地址：** `GET /api/v1/orders/{订单ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 456,
    "orderSn": "************",
    "productId": 1,
    "productName": "苹果正品耳机",
    "productImage": "/uploads/products/headphone.jpg",
    "skuName": "黑色",
    "quantity": 1,
    "amount": 89.00,
    "status": "paid",
    "type": "group",
    "groupId": 123,
    "shippingAddress": {
      "id": 8,
      "name": "张三",
      "phone": "***********",
      "address": "北京市朝阳区xxx街道xxx号"
    },
    "createTime": "2025-07-27 10:05:00",
    "payTime": "2025-07-27 10:06:00",
    "logistics": {
      "trackingNumber": "123123123654",
      "courierCompany": "飞越物流",
      "status": "shipped"
    }
  },
  "dataType": 1
}
```

---

### 3. 创建订单

**接口地址：** `POST /api/v1/orders`

**请求参数：**

```json
{
  "productId": 1,
  "skuId": 26,
  "quantity": 1,
  "type": "group",
  "groupId": 123,
  "shippingAddressId": 8,
  "paymentMethod": "balance"
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "orderId": 456,
    "orderSn": "************",
    "amount": 89.00,
    "paymentRequired": true
  },
  "dataType": 1
}
```

---

### 4. 取消订单

**接口地址：** `POST /api/v1/orders/{订单ID}/cancel`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "refundAmount": 89.00,
    "refundTime": "2025-07-27 15:30:00"
  },
  "dataType": 1
}
```

## 支付相关接口

### 1. 创建支付订单

**接口地址：** `POST /api/v1/payments`

**请求参数：**

```json
{
  "orderId": 456,
  "paymentMethod": "balance",
  "amount": 89.00,
  "returnUrl": "https://app.example.com/payment/callback"
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "paymentId": "pay_123456789",
    "paymentUrl": "https://payment.example.com/pay/123456789",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expireTime": "2025-07-27 11:00:00"
  },
  "dataType": 1
}
```

**字段说明：**

- `paymentId`: 支付订单ID
- `paymentUrl`: 支付链接（适用于网页支付）
- `qrCode`: 支付二维码（适用于扫码支付）
- `expireTime`: 支付过期时间

---

### 2. 查询支付状态

**接口地址：** `GET /api/v1/payments/{支付ID}/status`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "paymentId": "pay_123456789",
    "status": "success",
    "paidTime": "2025-07-27 10:06:00",
    "amount": 89.00,
    "orderId": 456
  },
  "dataType": 1
}
```

**字段说明：**

- `status`: 支付状态（pending/success/failed/cancelled）

---

### 3. 获取支付方式列表

**接口地址：** `GET /api/v1/payment-methods`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": "balance",
      "name": "余额支付",
      "icon": "/uploads/payment/balance.png",
      "enabled": true,
      "description": "使用账户余额进行支付"
    },
    {
      "id": "alipay",
      "name": "支付宝",
      "icon": "/uploads/payment/alipay.png",
      "enabled": true,
      "description": "支付宝快捷支付"
    }
  ],
  "dataType": 1
}
```

---

### 4. 下单（原有接口）

**接口地址：** `POST /api/v1/placeOrder`

**请求参数：**

```json
{
  "skuId": 26,
  "quantity": 1,
  "shippingAddressId": 8,
  "aloneFlag": 1,
  "useCurrency": 1
}
```

**参数说明：**

- `skuId`: 商品SKU ID
- `quantity`: 数量（后台强制为1）
- `shippingAddressId`: 收货地址ID
- `aloneFlag`: 购买方式（1=直接购买，0=默认）
- `useCurrency`: 支付方式（1=余额支付，2=积分支付）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": "5",
    "dataType": 1
  },
  "dataType": 1
}
```

**字段说明：**

- `data.data`: 返回的订单ID

---

### 2. 订单物流信息

**接口地址：** `GET /app/v1/order/Logistics/{订单ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 2,
    "orderId": 5,
    "trackingNumber": "123123123654",
    "courierCompany": "飞越物流",
    "status": "shipped",
    "trackingInfo": {
      "list": [
        "货物在已达南宁"
      ]
    },
    "shippedTime": "2025-07-06 17:43:04",
    "createTime": "2025-07-06 17:43:04"
  },
  "dataType": 1
}
```

**字段说明：**

- `trackingNumber`: 快递单号
- `courierCompany`: 快递公司
- `status`: 物流状态
- `trackingInfo`: 物流跟踪信息

---

## 用户相关接口

### 1. 我的团队

**接口地址：** `GET /app/v1/myTeam?pageNum=1&pageSize=20`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 82,
        "name": "13833883389",
        "childCount": 0,
        "noviceCount": 0,
        "hasReal": false,
        "contribution": 3000,
        "createTime": "2025-07-01 10:00:00",
        "consume": 1500.00
      }
    ],
    "total": 3,
    "totalTeamReward": 0.00,
    "weekCount": 4
  },
  "dataType": 1
}
```

**字段说明：**

- `id`: 用户ID
- `name`: 用户名
- `childCount`: 用户下级数
- `noviceCount`: 试新数
- `hasReal`: 是否实名认证
- `contribution`: 对上级累计贡献积分
- `createTime`: 创建时间
- `consume`: 消费总金额
- `total`: 下级总数
- `totalTeamReward`: 所有下级的累计奖励积分
- `weekCount`: 本周新增成员数

---

## 钱包相关接口

### 1. 钱包信息

**接口地址：** `GET /api/v1/wallet?pageNum=1&pageSize=3`

**查询参数：**

- `pageNum`: 页码（用于流水查询）
- `pageSize`: 每页数量（用于流水查询）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "balance": {
      "totalBalance": 21742.40,
      "balance": 21242.40,
      "experienceBalance": 500.00,
      "points": 0,
      "totalRecharge": 39300.00,
      "totalWithdraw": 18020.00,
      "status": 1
    },
    "transactions": {
      "pageNum": 1,
      "pageSize": 3,
      "total": 42,
      "pages": 14,
      "list": [
        {
          "id": 74,
          "userId": 81,
          "amount": -10.00,
          "balanceAfter": 21242.40,
          "experienceBalanceAfter": 500.00,
          "pointsAfter": 0,
          "mode": 0,
          "type": "PAYMENT",
          "relatedId": 18,
          "description": "购物支付",
          "createTime": "2025-07-27 16:01:31"
        }
      ],
      "emptyFlag": false
    }
  },
  "dataType": 1
}
```

**余额字段说明：**

- `totalBalance`: 总余额
- `balance`: 现金余额
- `experienceBalance`: 体验金余额
- `points`: 积分余额
- `totalRecharge`: 总充值
- `totalWithdraw`: 总提现

**交易流水字段说明：**

- `amount`: 操作额度
- `balanceAfter`: 操作后现金余额
- `experienceBalanceAfter`: 操作后体验金余额
- `pointsAfter`: 操作后积分余额
- `mode`: 操作模式（0=现金，1=体验金，2=积分）
- `type`: 交易类型
- `relatedId`: 关联ID
- `description`: 操作说明

**交易类型说明：**

- `RECHARGE`: 充值
- `WITHDRAWAL`: 提现
- `PAYMENT`: 支付
- `REFUND_WIN`: 中奖退款
- `REFUND_LOSS`: 未中奖退款
- `SUBSIDY`: 补贴
- `COMMISSION`: 佣金
- `FIRST_REWARD`: 首次奖励
- `TEAM_REWARD`: 团队奖励
- `MANUAL_ADD`: 手动增加
- `MANUAL_REDUCE`: 手动减少
- `EXPERIENCE_GIFT`: 体验金赠送
- `FREE_NOVICE`: 免费新手

---

### 2. 提现申请记录

**接口地址：** `GET /api/v1/withdrawals`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "pageNum": 1,
    "pageSize": 30,
    "total": 2,
    "pages": 1,
    "list": [
      {
        "id": 4,
        "userId": 81,
        "userName": "***********",
        "amount": 2000.00,
        "fee": 0.00,
        "actualAmount": 2000.00,
        "status": "approved",
        "bankName": "越南银行",
        "bankAccount": "**********",
        "accountHolder": "布洛林",
        "processedBy": 1,
        "createTime": "2025-07-06 10:38:48",
        "processedTime": "2025-07-06 10:38:58"
      },
      {
        "id": 3,
        "userId": 81,
        "userName": "***********",
        "amount": 20000.00,
        "fee": 0.00,
        "actualAmount": 0.00,
        "status": "rejected",
        "bankName": "越南银行",
        "bankAccount": "**********",
        "accountHolder": "布洛林",
        "rejectionReason": "余额不足",
        "processedBy": 1,
        "createTime": "2025-07-06 10:15:37",
        "processedTime": "2025-07-06 10:38:21"
      }
    ],
    "emptyFlag": false
  },
  "dataType": 1
}
```

**字段说明：**

- `amount`: 提现金额
- `fee`: 手续费
- `actualAmount`: 实际到账金额
- `status`: 状态（approved=已批准，rejected=已拒绝）
- `bankName`: 银行名称
- `bankAccount`: 银行账号
- `accountHolder`: 账户持有人
- `rejectionReason`: 拒绝理由（仅拒绝状态有）
- `processedBy`: 处理人
- `processedTime`: 处理时间

---

### 3. 提现申请

**接口地址：** `POST /api/v1/withdrawalsApply`

**请求参数：**

```json
{
  "amount": "2000",
  "bankName": "越南银行",
  "bankAccount": "**********",
  "accountHolder": "布洛林"
}
```

**参数说明：**

- `amount`: 提现金额
- `bankName`: 银行名称
- `bankAccount`: 银行账号
- `accountHolder`: 账户持有人

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

## 响应码说明

所有接口统一响应格式：

```json
{
  "code": 0,
  "msg": "操作成功",
  "ok": true,
  "data": {},
  "dataType": 1
}
```

**通用字段说明：**

- `code`: 响应码（0=成功，其他=失败）
- `msg`: 响应消息
- `ok`: 是否成功
- `data`: 响应数据
- `dataType`: 数据类型

---

## 地址管理接口

### 1. 获取地址列表

**接口地址：** `GET /api/v1/user/addresses`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 8,
      "name": "张三",
      "phone": "***********",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "address": "xxx街道xxx号",
      "isDefault": true,
      "createTime": "2025-07-20 10:00:00",
      "updateTime": "2025-07-25 15:30:00"
    }
  ],
  "dataType": 1
}
```

**字段说明：**

- `isDefault`: 是否为默认地址

---

### 2. 添加地址

**接口地址：** `POST /api/v1/user/addresses`

**请求参数：**

```json
{
  "name": "李四",
  "phone": "13900139000",
  "province": "上海市",
  "city": "上海市",
  "district": "浦东新区",
  "address": "xxx路xxx号",
  "isDefault": false
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "id": 9
  },
  "dataType": 1
}
```

---

### 3. 更新地址

**接口地址：** `PUT /api/v1/user/addresses/{地址ID}`

**请求参数：**

```json
{
  "name": "李四",
  "phone": "13900139000",
  "province": "上海市",
  "city": "上海市",
  "district": "浦东新区",
  "address": "xxx路xxx号",
  "isDefault": true
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

### 4. 删除地址

**接口地址：** `DELETE /api/v1/user/addresses/{地址ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

## 收藏功能接口（未实现）

### 1. 获取收藏列表

**接口地址：** `GET /api/v1/user/favorites`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "productId": 1,
        "productName": "苹果正品耳机",
        "productImage": "/uploads/products/headphone.jpg",
        "price": 99.00,
        "groupPrice": 89.00,
        "sales": 1520,
        "createTime": "2025-07-25 14:20:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 5,
    "pages": 1
  },
  "dataType": 1
}
```

---

### 2. 添加收藏

**接口地址：** `POST /api/v1/products/{商品ID}/favorite`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "favoriteId": 1
  },
  "dataType": 1
}
```

---

### 3. 取消收藏

**接口地址：** `DELETE /api/v1/user/favorites/{收藏ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

## 文件上传接口（未实现）

### 1. 上传单个文件

**接口地址：** `POST /api/v1/upload/file`

**请求参数：**

- Content-Type: multipart/form-data
- file: 文件数据
- folder: 文件夹名称（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "fileId": "file_123456",
    "fileName": "avatar.jpg",
    "fileUrl": "/uploads/avatars/avatar_123456.jpg",
    "fileSize": 51200,
    "mimeType": "image/jpeg"
  },
  "dataType": 1
}
```

**字段说明：**

- `fileId`: 文件唯一标识
- `fileUrl`: 文件访问地址
- `fileSize`: 文件大小（字节）
- `mimeType`: 文件MIME类型

---

### 2. 上传多个文件

**接口地址：** `POST /api/v1/upload/files`

**请求参数：**

- Content-Type: multipart/form-data
- files[0], files[1]...: 多个文件数据
- folder: 文件夹名称（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "fileId": "file_123456",
      "fileName": "image1.jpg",
      "fileUrl": "/uploads/images/image1_123456.jpg",
      "fileSize": 51200,
      "mimeType": "image/jpeg"
    },
    {
      "fileId": "file_123457",
      "fileName": "image2.jpg",
      "fileUrl": "/uploads/images/image2_123457.jpg",
      "fileSize": 48300,
      "mimeType": "image/jpeg"
    }
  ],
  "dataType": 1
}
```

---

### 3. 获取文件信息

**接口地址：** `GET /api/v1/upload/files/{文件ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "fileId": "file_123456",
    "fileName": "avatar.jpg",
    "fileUrl": "/uploads/avatars/avatar_123456.jpg",
    "fileSize": 51200,
    "mimeType": "image/jpeg",
    "uploadTime": "2025-07-27 10:15:00"
  },
  "dataType": 1
}
```

---

### 4. 上传头像

**接口地址：** `POST /api/v1/user/avatar`

**请求参数：**

- Content-Type: multipart/form-data
- avatar: 头像文件

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "avatarUrl": "/uploads/avatars/user_81_123456.jpg"
  },
  "dataType": 1
}
```

---

## 客服支持接口（未实现）

### 1. 提交客服工单

**接口地址：** `POST /api/v1/support/tickets`

**请求参数：**

```json
{
  "subject": "账户余额问题",
  "category": "account",
  "description": "我的账户余额显示不正确，请帮助处理",
  "priority": "normal",
  "attachments": ["file_123456", "file_123457"]
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "ticketId": "ticket_789012",
    "ticketNumber": "TK************",
    "estimatedResponse": "24小时内回复"
  },
  "dataType": 1
}
```

**字段说明：**

- `category`: 工单分类（account/payment/product/technical/other）
- `priority`: 优先级（low/normal/high/urgent）

---

### 2. 获取工单列表

**接口地址：** `GET /api/v1/support/tickets`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `status`: 工单状态（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": "ticket_789012",
        "ticketNumber": "TK************",
        "subject": "账户余额问题",
        "status": "open",
        "priority": "normal",
        "createTime": "2025-07-27 10:30:00",
        "updateTime": "2025-07-27 10:30:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 3,
    "pages": 1
  },
  "dataType": 1
}
```

**字段说明：**

- `status`: 工单状态（open/pending/resolved/closed）

---

### 3. 获取常见问题

**接口地址：** `GET /api/v1/support/faq`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 1,
      "question": "如何参与拼团？",
      "answer": "选择心仪商品，点击拼团按钮，选择地址并支付即可参与拼团。",
      "category": "group_buying",
      "sort": 1
    },
    {
      "id": 2,
      "question": "拼团失败后如何退款？",
      "answer": "拼团失败后，支付金额会自动退回到您的账户余额中。",
      "category": "refund",
      "sort": 2
    }
  ],
  "dataType": 1
}
```

---

## 身份认证接口

### 1. 提交实名认证资料

**接口地址：** `POST /api/v1/user/identity-verification`

**请求参数：**

- Content-Type: multipart/form-data
- realName: 真实姓名
- idNumber: 身份证号码
- idCardFront: 身份证正面照片文件
- idCardBack: 身份证反面照片文件

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "verificationId": "verify_123456",
    "status": "pending",
    "estimatedTime": "1-3个工作日"
  },
  "dataType": 1
}
```

**字段说明：**

- `status`: 认证状态（pending/approved/rejected）

---

### 2. 获取实名认证状态

**接口地址：** `GET /api/v1/user/identity-verification/status`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "status": "approved",
    "realName": "张***",
    "idNumber": "110***********1234",
    "verifiedTime": "2025-07-25 16:30:00",
    "rejectReason": null
  },
  "dataType": 1
}
```

**字段说明：**

- `status`: 认证状态（none/pending/approved/rejected）
- `rejectReason`: 拒绝原因（仅拒绝状态有）

---

### 3. 获取实名认证信息

**接口地址：** `GET /api/v1/user/identity-verification`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "verificationId": "verify_123456",
    "status": "approved",
    "realName": "张三",
    "idNumber": "110101199001011234",
    "idCardFrontUrl": "/uploads/id/front_123456.jpg",
    "idCardBackUrl": "/uploads/id/back_123456.jpg",
    "submitTime": "2025-07-25 10:00:00",
    "verifiedTime": "2025-07-25 16:30:00"
  },
  "dataType": 1
}
```

---

### 4. 检查用户是否已完成实名认证

**接口地址：** `GET /api/v1/user/identity-verification/check`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "isVerified": true,
    "verificationLevel": "basic",
    "capabilities": ["withdraw", "high_amount_payment"]
  },
  "dataType": 1
}
```

**字段说明：**

- `isVerified`: 是否已通过实名认证
- `verificationLevel`: 认证级别（basic/advanced）
- `capabilities`: 认证后可使用的功能

---

## 商品评价接口（未实现）

### 1. 获取商品评价列表

**接口地址：** `GET /api/v1/products/{商品ID}/reviews`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `rating`: 评分筛选（1-5星，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "userId": 81,
        "userName": "用户***",
        "userAvatar": "/uploads/avatars/user_81.jpg",
        "rating": 5,
        "content": "商品质量很好，物流很快，很满意！",
        "images": [
          "/uploads/reviews/review_1_1.jpg",
          "/uploads/reviews/review_1_2.jpg"
        ],
        "createTime": "2025-07-25 18:30:00",
        "isAnonymous": false
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 128,
    "pages": 7,
    "statistics": {
      "averageRating": 4.8,
      "totalReviews": 128,
      "ratingDistribution": {
        "5": 95,
        "4": 20,
        "3": 8,
        "2": 3,
        "1": 2
      }
    }
  },
  "dataType": 1
}
```

**字段说明：**

- `rating`: 评分（1-5星）
- `images`: 评价图片
- `isAnonymous`: 是否匿名评价
- `statistics`: 评价统计信息
- `ratingDistribution`: 各星级评价数量分布

---

### 2. 提交商品评价

**接口地址：** `POST /api/v1/products/{商品ID}/reviews`

**请求参数：**

```json
{
  "orderId": 456,
  "rating": 5,
  "content": "商品质量很好，物流很快，很满意！",
  "images": ["file_123456", "file_123457"],
  "isAnonymous": false
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "reviewId": 1,
    "rewardPoints": 10
  },
  "dataType": 1
}
```

**字段说明：**

- `rewardPoints`: 评价奖励积分

---

## 优惠券接口（未实现）

### 1. 获取用户优惠券列表

**接口地址：** `GET /api/v1/user/coupons`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `status`: 优惠券状态（available/used/expired，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "新用户专享优惠券",
        "type": "discount",
        "value": 10.00,
        "minAmount": 50.00,
        "status": "available",
        "startTime": "2025-07-20 00:00:00",
        "endTime": "2025-08-20 23:59:59",
        "description": "满50元减10元",
        "applicableProducts": "all"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 5,
    "pages": 1,
    "summary": {
      "available": 3,
      "used": 1,
      "expired": 1
    }
  },
  "dataType": 1
}
```

**字段说明：**

- `type`: 优惠券类型（discount/percentage/free_shipping）
- `value`: 优惠金额或百分比
- `minAmount`: 最低使用金额
- `status`: 状态（available/used/expired）
- `applicableProducts`: 适用商品（all/category/specific）

---

### 2. 获取可用优惠券

**接口地址：** `GET /api/v1/coupons/available`

**查询参数：**

- `productId`: 商品ID（可选）
- `amount`: 订单金额（可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": [
    {
      "id": 1,
      "name": "满减优惠券",
      "type": "discount",
      "value": 10.00,
      "minAmount": 50.00,
      "endTime": "2025-08-20 23:59:59",
      "description": "满50元减10元",
      "canUse": true,
      "saveAmount": 10.00
    }
  ],
  "dataType": 1
}
```

**字段说明：**

- `canUse`: 当前订单是否可使用
- `saveAmount`: 使用该优惠券可节省的金额

---

### 3. 领取优惠券

**接口地址：** `POST /api/v1/coupons/{优惠券ID}/claim`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "userCouponId": 123,
    "endTime": "2025-08-20 23:59:59"
  },
  "dataType": 1
}
```

---

### 4. 使用优惠券

**接口地址：** `POST /api/v1/user/coupons/{用户优惠券ID}/use`

**请求参数：**

```json
{
  "orderId": 456
}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "discountAmount": 10.00,
    "finalAmount": 79.00
  },
  "dataType": 1
}
```

**字段说明：**

- `discountAmount`: 优惠金额
- `finalAmount`: 使用优惠券后的最终金额

---

## 通知消息接口（未实现）

### 1. 获取消息列表

**接口地址：** `GET /api/v1/user/messages`

**查询参数：**

- `pageNum`: 页码
- `pageSize`: 每页数量
- `type`: 消息类型（system/order/activity，可选）
- `status`: 读取状态（read/unread，可选）

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "list": [
      {
        "id": 1,
        "type": "order",
        "title": "拼团成功通知",
        "content": "恭喜您参与的苹果正品耳机拼团已成功，订单正在准备发货。",
        "isRead": false,
        "createTime": "2025-07-27 10:30:00",
        "relatedId": "456",
        "actionUrl": "/order/456"
      }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "total": 15,
    "pages": 1,
    "unreadCount": 3
  },
  "dataType": 1
}
```

**字段说明：**

- `type`: 消息类型（system/order/activity/payment）
- `isRead`: 是否已读
- `relatedId`: 关联ID（如订单ID）
- `actionUrl`: 点击跳转地址

---

### 2. 标记消息为已读

**接口地址：** `POST /api/v1/user/messages/{消息ID}/read`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

### 3. 标记所有消息为已读

**接口地址：** `POST /api/v1/user/messages/read-all`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "updatedCount": 3
  },
  "dataType": 1
}
```

---

### 4. 删除消息

**接口地址：** `DELETE /api/v1/user/messages/{消息ID}`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "dataType": 1
}
```

---

## 数据统计接口

### 1. 获取用户统计数据

**接口地址：** `GET /api/v1/user/statistics`

**响应示例：**

```json
{
  "code": 0,
  "msg": "success",
  "ok": true,
  "data": {
    "orderStats": {
      "totalOrders": 25,
      "successfulGroups": 20,
      "totalSpent": 1580.00,
      "totalSaved": 320.00
    },
    "walletStats": {
      "totalRecharge": 2000.00,
      "totalWithdraw": 500.00,
      "totalRewards": 150.00
    },
    "teamStats": {
      "directInvites": 8,
      "totalTeam": 25,
      "totalCommission": 120.00
    },
    "activityStats": {
      "participatedActivities": 15,
      "wonActivities": 12,
      "winRate": 0.8
    }
  },
  "dataType": 1
}
```

**字段说明：**

- `orderStats`: 订单统计
- `walletStats`: 钱包统计
- `teamStats`: 团队统计
- `activityStats`: 活动参与统计

---

## 注意事项

1. 所有接口需要在请求头中携带认证token（除登录、注册、验证码接口外）
2. 分页参数：`pageNum`从1开始，`pageSize`表示每页数量
3. 时间格式统一为：`YYYY-MM-DD HH:mm:ss`
4. 金额字段统一使用数字类型，保留两位小数
5. 布尔值字段统一使用 `true/false`

---

*文档更新时间：2025-07-28*
