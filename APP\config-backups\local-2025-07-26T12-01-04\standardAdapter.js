/**
 * 标准API适配器 - 本地开发环境
 * 基础URL: /api/v1
 * 目标服务器: http://localhost:8686
 * 认证方式: Token
 * 数据格式: JSON
 * 响应格式: { code: 0, msg: "操作成功", ok: true, data: {...}, dataType: 1 }
 */

// 🚫 Mock API已完全移除 - 只支持真实API

export class StandardApiAdapter {
  constructor(baseURL = '/api/v1') {
    this.baseURL = baseURL
    
    console.log('🔧 StandardApiAdapter initialized (REAL API ONLY):', {
      baseURL: this.baseURL,
      realAPIOnly: true
    })
  }

  /**
   * 获取当前token (REAL API ONLY)
   */
  getToken() {
    return localStorage.getItem('token')
  }

  /**
   * 标准HTTP请求方法
   * @param {string} method - HTTP方法
   * @param {string} url - API路径
   * @param {Object} data - 请求数据
   * @param {Object} headers - 额外请求头
   * @returns {Promise} API响应
   */
  async request(method, url, data = null, headers = {}) {
    const token = this.getToken()
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    }

    // 添加认证头（如果有token）- 修正：后端可能不使用Bearer格式
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
      // 也添加简单的token头，以防后端需要
      config.headers['token'] = token
    }

    // 处理请求数据
    if (data) {
      if (method.toUpperCase() === 'GET') {
        // GET请求使用查询参数
        const params = new URLSearchParams()
        Object.keys(data).forEach(key => {
          if (data[key] !== null && data[key] !== undefined) {
            params.append(key, data[key])
          }
        })
        if (params.toString()) {
          url += (url.includes('?') ? '&' : '?') + params.toString()
        }
      } else {
        // POST/PUT/DELETE请求使用请求体
        if (data instanceof FormData) {
          // 如果是FormData，直接使用，并移除Content-Type让浏览器自动设置
          config.body = data
          delete config.headers['Content-Type']
        } else {
          // 普通数据使用JSON
          config.body = JSON.stringify(data)
        }
      }
    }

    try {
      const fullUrl = `${this.baseURL}${url}`
      
      // 调试：打印请求信息
      console.log('🚀 API Request Debug:', {
        method: method.toUpperCase(),
        url: fullUrl,
        headers: config.headers,
        data: data
      })
      
      // 🚫 只使用真实API
      const response = await fetch(fullUrl, config)
        
        // 调试：打印响应信息
        console.log('🔍 API Response Debug:', {
          url: fullUrl,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        })
        
        // 获取响应文本
        const responseText = await response.text()
        console.log('📄 Response Text (first 500 chars):', responseText.substring(0, 500))
        
        // 尝试解析JSON
        let result
        try {
          result = JSON.parse(responseText)
        } catch (jsonError) {
          console.error('❌ JSON Parse Error:', jsonError)
          console.error('📄 Full Response Text:', responseText)
          throw new Error(`API返回了非JSON格式的响应: ${response.status} ${response.statusText}`)
        }
        
        // 修正：适配后端响应格式 { code: 0, msg: "操作成功", ok: true, data: {...} }
        if (!response.ok) {
          throw new Error(result.msg || result.message || `HTTP ${response.status}`)
        }
        
        // 检查后端业务状态码
        if (result.code !== 0) {
          // 对于首页数据接口，即使出错也返回结构化响应，避免页面崩溃
          if (url === '/data/home' || url === '/data/products' || url === '/data/banners') {
            console.warn(`⚠️ 首页API业务错误 [${url}]:`, result);
            return {
              code: result.code, // 保持原始错误码
              message: result.msg || result.message || '请求失败',
              data: {
                banners: [],
                categories: [],
                initial_products: { items: [], pagination: {} }
              },
              error: true,
              _original: result
            };
          }
          // 其他接口仍然抛出异常
          throw new Error(result.msg || result.message || '请求失败')
        }
        
        // 对于验证码接口，需要保持后端原始格式以匹配Login.vue的期望
        if (url === '/captcha') {
          return result; // 直接返回后端格式 {code: 0, msg: "...", ok: true, data: {...}}
        }
        
        // 对于其他接口，转换为前端期望的格式
        return {
          code: 200, // 转换为前端期望的成功状态码
          message: result.msg || 'success',
          data: result.data,
          timestamp: new Date().toISOString(),
          // 保留原始响应用于调试
          _original: result
        }
    } catch (error) {
      console.error(`API请求失败 [${method.toUpperCase()} ${url}]:`, error)
      throw error
    }
  }

  // 🚫 Mock功能已完全移除，所有方法使用真实API
  
  // ==================== 认证相关接口 ==================== 
  
     /**
    * 统一认证接口 (REAL API ONLY)
    * @param {string} type - 认证类型 (login/register/forgot_password/oauth)
    * @param {Object} data - 认证数据
    * @returns {Promise} 认证结果
    */
  async auth(type, data) {
    return await this.request('POST', '/auth', {
      type,
      ...data
    })
  }

  /**
   * 用户登录 - 修正：适配后端实际接口格式
   */
  async login(phone, password, captchaCode = '', captchaUuid = '') {
    // 修正：后端需要验证码参数
    return await this.auth('login', { 
      phone, 
      password,
      captchaCode,
      captchaUuid
    })
  }

  /**
   * 用户注册 - 修正：适配后端实际接口格式
   */
  async register(registerData) {
    // 修正：使用后端期望的字段名
    return await this.auth('register', {
      phone: registerData.phone,
      password: registerData.password,
      confirmPassword: registerData.confirmPassword, // 后端使用confirmPassword而非confirm_password
      captchaCode: registerData.captchaCode || '', // 后端需要验证码
      captchaUuid: registerData.captchaUuid || '',
      agreed_to_terms: registerData.agreed_to_terms || true,
      invite_code: registerData.invite_code || ''
    })
  }

  /**
   * 获取图形验证码 - 新增：后端提供的验证码接口
   */
  async getCaptcha() {
    return await this.request('GET', '/captcha')
  }

  /**
   * 登出 - 新增：后端提供的登出接口
   */
  async logout() {
    return await this.request('GET', '/logout')
  }

  /**
   * 刷新Token - 新增：Token刷新接口
   */
  async refreshToken(refreshToken) {
    return await this.request('POST', '/auth/refresh', {
      refresh_token: refreshToken
    })
  }

  /**
   * 获取用户状态 - 新增：轻量级用户状态检查
   */
  async getUserStatus() {
    return await this.request('GET', '/user/status')
  }

  /**
   * 获取用户资料 - 新增：获取完整用户信息
   */
  async getUserProfile() {
    return await this.request('GET', '/user/profile')
  }

  /**
   * 忘记密码
   */
  async forgotPassword(phone, verificationCode, newPassword, confirmPassword) {
    return await this.auth('forgot_password', {
      phone,
      verification_code: verificationCode,
      new_password: newPassword,
      confirm_password: confirmPassword
    })
  }

  /**
   * 第三方登录
   */
  async oauthLogin(provider, token, userInfo = null) {
    return await this.auth('oauth', {
      provider,
      token,
      user_info: userInfo
    })
  }

  // ==================== 首页相关接口 ====================

  /**
   * 获取首页数据 - 修正：使用最新的API路径
   */
  async getHomeData(params = {}) {
    console.log('🏠 获取首页数据，参数:', params)
    try {
      const result = await this.request('GET', '/home', params)
      console.log('✅ 首页数据获取成功:', result)
      return result
    } catch (error) {
      console.error('❌ 首页数据获取失败:', error)
      // 返回默认结构，避免页面崩溃
      return {
        code: 500,
        message: `首页数据获取失败: ${error.message}`,
        data: {
          banners: [],
          categories: [],
          initialProducts: { list: [], pageNum: 1, pageSize: 20, total: 0, pages: 0 }
        },
        error: true
      }
    }
  }

  /**
   * 获取用户状态 - 新增：分离的用户状态接口
   */
  async getUserStatus(params = {}) {
    return await this.request('GET', '/userStatus', params)
  }

  /**
   * 获取轮播图数据 - 修正：使用最新的API路径
   */
  async getBanners(params = {}) {
    console.log('🎨 获取Banner数据，参数:', params)
    try {
      const result = await this.request('GET', '/banners', params)
      console.log('✅ Banner数据获取成功:', result)
      return result
    } catch (error) {
      console.error('❌ Banner数据获取失败:', error)
      // 返回默认结构，避免页面崩溃
      return {
        code: 500,
        message: `Banner数据获取失败: ${error.message}`,
        data: [],
        error: true
      }
    }
  }

  /**
   * 商品搜索 - 修正：需要认证
   */
  async search(params) {
    return await this.request('GET', '/search', params)
  }

  /**
   * 获取商品列表 - 修正：使用最新的API路径
   * 支持分类筛选和拼团类型筛选
   */
  async getProducts(params = {}) {
    console.log('🛍️ 获取商品列表，参数:', params)
    try {
      const result = await this.request('GET', '/products', params)
      console.log('✅ 商品列表获取成功:', result)
      return result
    } catch (error) {
      console.error('❌ 商品列表获取失败:', error)
      // 返回默认结构，避免页面崩溃
      return {
        code: 500,
        message: `商品列表获取失败: ${error.message}`,
        data: {
          list: [],
          pageNum: 1,
          pageSize: 20,
          total: 0,
          pages: 0
        },
        error: true
      }
    }
  }

  /**
   * 获取分类数据 - 修正：使用最新的API路径
   */
  async getCategories(params = {}) {
    return await this.request('GET', '/categories', params)
  }

  // ==================== 商品相关接口 ====================

  /**
   * 获取商品详情
   */
  async getProductDetail(productId) {
    return await this.request('GET', `/product/${productId}`)
  }

  /**
   * 商品操作统一接口
   */
  async productAction(productId, action, params = {}) {
    return await this.request('POST', `/product/${productId}/action`, {
      action,
      params
    })
  }

  /**
   * 收藏/取消收藏商品
   */
  async toggleFavorite(productId, isFavorite) {
    const action = isFavorite ? 'unfavorite' : 'favorite'
    return await this.productAction(productId, action)
  }

  /**
   * 获取商品评价
   */
  async getProductReviews(productId, params = {}) {
    return await this.productAction(productId, 'get_reviews', params)
  }

  /**
   * 获取正在拼团
   */
  async getActiveGroups(productId, params = {}) {
    return await this.productAction(productId, 'get_groups', params)
  }

  // ==================== 拼团相关接口 ====================

  /**
   * 获取拼团详情
   */
  async getGroupDetail(groupId) {
    return await this.request('GET', `/groups/${groupId}`)
  }

  /**
   * 拼团操作统一接口
   */
  async groupAction(action, data) {
    return await this.request('POST', '/groups/action', {
      action,
      ...data
    })
  }

  /**
   * 创建拼团
   */
  async createGroup(productId, params) {
    return await this.groupAction('create', {
      product_id: productId,
      params
    })
  }

  /**
   * 参与拼团
   */
  async joinGroup(groupId, params) {
    return await this.groupAction('join', {
      group_id: groupId,
      params
    })
  }

  /**
   * 分享拼团
   */
  async shareGroup(groupId) {
    return await this.groupAction('share', {
      group_id: groupId
    })
  }

  /**
   * 取消拼团
   */
  async cancelGroup(groupId) {
    return await this.groupAction('cancel', {
      group_id: groupId
    })
  }

  // ==================== 订单相关接口 ====================

  /**
   * 获取订单列表
   */
  async getOrders(params = {}) {
    return await this.request('GET', '/orders', params)
  }

  /**
   * 订单操作统一接口
   */
  async orderAction(orderId, action, params = {}) {
    return await this.request('POST', `/orders/${orderId}/action`, {
      action,
      params
    })
  }

  /**
   * 确认收货
   */
  async confirmOrder(orderId) {
    return await this.orderAction(orderId, 'confirm')
  }

  /**
   * 再次购买
   */
  async repurchaseOrder(orderId) {
    return await this.orderAction(orderId, 'repurchase')
  }

  /**
   * 查看物流
   */
  async trackOrder(orderId) {
    return await this.orderAction(orderId, 'track')
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId) {
    return await this.orderAction(orderId, 'cancel')
  }

  // ==================== 支付相关接口 ====================

  /**
   * 获取支付数据
   */
  async getPaymentData(params = {}) {
    return await this.request('GET', '/payment', params)
  }

  /**
   * 处理支付
   */
  async processPayment(paymentData) {
    return await this.request('POST', '/payment', paymentData)
  }

  // ==================== 钱包相关接口 ====================

  /**
   * 获取钱包信息
   */
  async getWallet(params = {}) {
    return await this.request('GET', '/wallet', params)
  }

  /**
   * 钱包操作统一接口
   */
  async walletAction(action, data) {
    return await this.request('POST', '/wallet/action', {
      action,
      ...data
    })
  }

  /**
   * 充值
   */
  async recharge(amount, methodId, returnUrl, clientIp) {
    return await this.walletAction('recharge', {
      amount,
      method_id: methodId,
      return_url: returnUrl,
      client_ip: clientIp
    })
  }

  /**
   * 提现
   */
  async withdraw(amount, methodId, targetAccount, password) {
    return await this.walletAction('withdraw', {
      amount,
      method_id: methodId,
      target_account: targetAccount,
      password
    })
  }

  /**
   * 转账
   */
  async transfer(amount, targetUser, message, password) {
    return await this.walletAction('transfer', {
      amount,
      target_user: targetUser,
      message,
      password
    })
  }

  // ==================== 用户相关接口 ====================

  /**
   * 获取用户中心数据
   */
  async getUserDashboard() {
    return await this.request('GET', '/user/dashboard')
  }

  /**
   * 更新用户信息
   */
  async updateUserProfile(profileData) {
    return await this.request('PUT', '/user/profile', profileData)
  }

  /**
   * 获取用户设置
   */
  async getUserSettings() {
    return await this.request('GET', '/user/settings')
  }

  /**
   * 更新用户设置
   */
  async updateUserSettings(settingsData) {
    return await this.request('PUT', '/user/settings', settingsData)
  }

  /**
   * 获取收藏列表
   */
  async getFavorites(params = {}) {
    return await this.request('GET', '/user/favorites', params)
  }

  /**
   * 删除收藏
   */
  async deleteFavorite(favoriteId) {
    return await this.request('DELETE', `/user/favorites/${favoriteId}`)
  }

  /**
   * 获取浏览历史
   */
  async getHistory(params = {}) {
    return await this.request('GET', '/user/history', params)
  }

  /**
   * 清空浏览历史
   */
  async clearHistory() {
    return await this.request('DELETE', '/user/history')
  }

  /**
   * 获取地址列表
   */
  async getAddresses() {
    return await this.request('GET', '/user/addresses')
  }

  /**
   * 添加地址
   */
  async addAddress(addressData) {
    return await this.request('POST', '/user/addresses', addressData)
  }

  /**
   * 更新地址
   */
  async updateAddress(addressId, addressData) {
    return await this.request('PUT', `/user/addresses/${addressId}`, addressData)
  }

  /**
   * 删除地址
   */
  async deleteAddress(addressId) {
    return await this.request('DELETE', `/user/addresses/${addressId}`)
  }

  /**
   * 获取优惠券
   */
  async getCoupons(params = {}) {
    return await this.request('GET', '/user/coupons', params)
  }

  // ==================== 其他接口 ====================

  /**
   * 获取活动列表 - 新增：获取全部活动数据
   */
  async getActivities(params = {}) {
    return await this.request('GET', '/activities', params)
  }

  /**
   * 获取活动详情 - 修正：后端活动详情接口
   */
  async getActivityDetail(activityId) {
    return await this.request('GET', `/activities/${activityId}`)
  }

  /**
   * 获取支持信息
   */
  async getSupport() {
    return await this.request('GET', '/support')
  }

  /**
   * 提交反馈
   */
  async submitFeedback(feedbackData) {
    return await this.request('POST', '/support', feedbackData)
  }

  /**
   * 文件上传
   */
  async uploadFile(file, type = 'image') {
    return await this.request('POST', '/upload', { file, type })
  }

  /**
   * 上报指标
   */
  async reportMetrics(metricsData) {
    return await this.request('POST', '/metrics', metricsData)
  }

  /**
   * 实名认证相关API
   */
  
  /**
   * 提交实名认证资料
   */
  async submitIdentityVerification(formData) {
    // 对于文件上传，让浏览器自动设置Content-Type
    return await this.request('POST', '/user/identity-verification', formData)
  }

  /**
   * 获取实名认证状态
   */
  async getIdentityVerificationStatus() {
    return await this.request('GET', '/user/identity-verification/status')
  }

  /**
   * 获取实名认证信息
   */
  async getIdentityVerificationInfo() {
    return await this.request('GET', '/user/identity-verification')
  }

  /**
   * 检查用户是否已完成实名认证
   */
  async checkIdentityVerification() {
    return await this.request('GET', '/user/identity-verification/check')
  }

  // 🚫 环境切换方法已移除，只支持真实API
  // 当前版本只支持真实API模式
}

// 创建标准API实例
export const standardApi = new StandardApiAdapter()

// 默认导出
export default standardApi 