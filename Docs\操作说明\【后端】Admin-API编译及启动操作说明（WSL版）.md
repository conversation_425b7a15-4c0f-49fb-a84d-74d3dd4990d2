启动admin-api

cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin && mvn spring-boot:run -Dspring-boot.run.profiles=dev -X


## 手动编译命令

  首先，进入正确的目录并编译项目：

进入admin-api根目录

  cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api

### 清理并编译打包（这个可能需要几分钟）

  mvn clean package -DskipTests

  如果Maven编译太慢，可以尝试只编译不打包：

### 仅编译

  mvn clean compile -DskipTests

## 手动启动命令

  编译完成后，有两种启动方式：

### 方式1：使用JAR包启动（推荐）

#### 进入sa-admin目录

  cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin

#### 启动应用

  java -jar target/tgw-pp.jar --spring.profiles.active=dev --server.port=8686

### 方式2：使用Maven启动

#### 进入sa-admin目录下

  cd /mnt/d/Dev/团购网/Server/smart-admin/admin-api/sa-admin

#### 使用Maven启动

  mvn spring-boot:run -Dspring-boot.run.profiles=dev

### 检查启动状态

  启动后，您可以使用以下命令检查服务状态：

#### 检查端口8686是否被占用

  ss -tlnp | grep :8686

#### 检查Java进程

  ps aux | grep java

#### 检查应用日志（如果有输出到日志文件）

  tail -f /tmp/logs/smart_admin_v3/sa-admin/dev/admin.log

#### 访问测试

  如果启动成功，您可以访问：

- API文档：http://localhost:8686/doc.html
- 健康检查：http://localhost:8686/actuator/health

## 手动停止命令

### 方式1：使用Ctrl+C停止
如果在前台运行，直接按 `Ctrl+C` 停止服务

### 方式2：查找并终止进程
```bash
# 查找Java进程
ps aux | grep java | grep tgw-pp

# 终止指定进程（替换PID为实际进程ID）
kill -9 <PID>

# 或者直接终止所有相关进程
pkill -f "tgw-pp.jar"
pkill -f "spring-boot:run"
```

### 方式3：按端口查找并终止
```bash
# 查找占用8686端口的进程
lsof -ti:8686

# 终止占用8686端口的进程
lsof -ti:8686 | xargs kill -9
```
