#!/bin/bash
# Post-commit hook - 记录提交信息

# 创建提交记录目录
mkdir -p .git/commit-logs

# 获取最新提交信息
commit_hash=$(git rev-parse HEAD)
commit_message=$(git log -1 --pretty=format:"%s")
commit_author=$(git log -1 --pretty=format:"%an")
commit_date=$(git log -1 --pretty=format:"%ai")
current_branch=$(git branch --show-current)

# 创建提交记录文件
commit_log_file=".git/commit-logs/${commit_hash:0:8}_$(date +%Y%m%d_%H%M%S).json"

cat > "$commit_log_file" << EOF
{
  "hash": "$commit_hash",
  "shortHash": "${commit_hash:0:8}",
  "message": "$commit_message",
  "author": "$commit_author",
  "date": "$commit_date",
  "branch": "$current_branch",
  "files": [
$(git diff-tree --no-commit-id --name-only -r $commit_hash | sed 's/.*/"&"/' | paste -sd ',' -)
  ],
  "stats": $(git show --stat --format="" $commit_hash | tail -1 | sed 's/.*/"&"/')
}
EOF

echo "✅ 提交记录已保存: $commit_log_file"