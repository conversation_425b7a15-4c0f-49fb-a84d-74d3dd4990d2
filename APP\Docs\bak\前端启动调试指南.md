# 前端启动调试指南

## 🚀 项目启动步骤

### 1. 环境准备

#### 检查Node.js版本
```bash
node --version
npm --version
```
**要求**: Node.js >= 16.0.0, npm >= 8.0.0

#### 如果Node.js版本过低，请升级：
```bash
# 使用nvm管理Node.js版本（推荐）
nvm install 18
nvm use 18

# 或者直接从官网下载最新版本
# https://nodejs.org/
```

### 2. 安装依赖

#### 进入项目目录
```bash
cd APP
```

#### 安装所有依赖包
```bash
npm install
```

如果遇到网络问题，可以使用国内镜像：
```bash
npm install --registry=https://registry.npmmirror.com
```

#### 验证依赖安装
```bash
npm list --depth=0
```

### 3. 启动开发服务器

#### 启动Vue开发服务器
```bash
npm run dev
```

或者
```bash
npm run serve
```

#### 预期输出
```bash
  VITE v4.x.x  ready in xxx ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: http://*************:3000/
  ➜  press h to show help
```

### 4. 访问应用

#### 打开浏览器访问
```
http://localhost:3000
```

#### 如果端口被占用，Vite会自动选择其他端口：
```
http://localhost:3001
http://localhost:3002
...
```

## 🛠️ Mock调试启动

### 方法一：自动启动Mock模式

#### 1. 创建启动脚本
在项目根目录创建 `start-dev-mock.bat`（Windows）或 `start-dev-mock.sh`（Mac/Linux）：

**Windows (start-dev-mock.bat):**
```batch
@echo off
echo 🚀 启动Mock调试模式...
echo.
echo 📋 测试账号信息:
echo 账号1: +84123456789 / 123456 (老用户)
echo 账号2: +84987654321 / password123 (新用户)
echo.
echo 📱 可用页面:
echo - 登录页面: http://localhost:3000/login
echo - Mock测试页面: http://localhost:3000/mock-test
echo.
start "" "http://localhost:3000/login"
npm run dev
```

**Mac/Linux (start-dev-mock.sh):**
```bash
#!/bin/bash
echo "🚀 启动Mock调试模式..."
echo ""
echo "📋 测试账号信息:"
echo "账号1: +84123456789 / 123456 (老用户)"
echo "账号2: +84987654321 / password123 (新用户)"
echo ""
echo "📱 可用页面:"
echo "- 登录页面: http://localhost:3000/login"
echo "- Mock测试页面: http://localhost:3000/mock-test"
echo ""
open "http://localhost:3000/login"
npm run dev
```

#### 2. 运行启动脚本
```bash
# Windows
start-dev-mock.bat

# Mac/Linux
chmod +x start-dev-mock.sh
./start-dev-mock.sh
```

### 方法二：手动启动Mock模式

#### 1. 启动开发服务器
```bash
npm run dev
```

#### 2. 打开浏览器访问
```
http://localhost:3000/login
```

#### 3. 启用Mock模式
- 点击右下角的紫色齿轮图标
- 勾选"启用Mock模式"
- 页面会自动刷新

#### 4. 开始测试
访问以下页面进行测试：
- 登录页面：`http://localhost:3000/login`
- Mock测试页面：`http://localhost:3000/mock-test`

## 🔧 开发环境配置

### 1. 创建环境配置文件

#### .env.development（开发环境）
```env
# 开发环境配置
NODE_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# Mock配置
VITE_USE_MOCK=true

# 调试配置
VITE_DEBUG=true
```

#### .env.production（生产环境）
```env
# 生产环境配置
NODE_ENV=production

# API配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_API_TIMEOUT=5000

# Mock配置
VITE_USE_MOCK=false

# 调试配置
VITE_DEBUG=false
```

### 2. 修改package.json脚本

#### 添加便捷启动脚本
```json
{
  "scripts": {
    "dev": "vite",
    "dev:mock": "vite --mode development",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .vue,.js,.jsx,.ts,.tsx",
    "test:mock": "echo '启动Mock测试模式' && npm run dev:mock"
  }
}
```

## 🧪 调试测试流程

### 1. 基础功能测试

#### 启动项目后按以下顺序测试：

1. **访问登录页面**
   ```
   http://localhost:3000/login
   ```

2. **启用Mock模式**
   - 点击右下角调试面板按钮
   - 勾选"启用Mock模式"

3. **测试登录功能**
   - 使用测试账号：`+84123456789` / `123456`
   - 点击登录按钮
   - 观察登录流程

4. **测试注册功能**
   - 切换到注册标签
   - 输入新手机号：`+84999888777`
   - 点击发送验证码
   - 在控制台查看验证码
   - 完成注册流程

### 2. 高级功能测试

#### 访问Mock测试页面
```
http://localhost:3000/mock-test
```

#### 测试所有API功能：
- 登录测试
- 注册测试
- 第三方登录测试
- 用户信息获取测试

### 3. 调试工具使用

#### 浏览器开发者工具
```bash
# 打开开发者工具
F12 或 Ctrl+Shift+I (Windows)
Cmd+Option+I (Mac)
```

#### 关键调试信息：
- **Console**: 查看API调用日志和验证码
- **Network**: 确认请求是否走Mock服务
- **Application**: 查看localStorage数据
- **Vue DevTools**: 查看组件状态

## 🔍 常见问题解决

### 1. 端口被占用

**问题**: `Error: listen EADDRINUSE: address already in use :::3000`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000  # Windows
lsof -ti:3000                 # Mac/Linux

# 杀死进程
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Mac/Linux

# 或者使用其他端口
npm run dev -- --port 3001
```

### 2. 依赖安装失败

**问题**: `npm install` 失败

**解决方案**:
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json  # Mac/Linux
rmdir /s node_modules & del package-lock.json  # Windows
npm install

# 使用yarn替代npm
npm install -g yarn
yarn install
yarn dev
```

### 3. Mock模式无法启用

**问题**: 点击启用Mock模式没有效果

**解决方案**:
```javascript
// 在浏览器控制台手动执行
localStorage.setItem('use_mock_api', 'true')
window.location.reload()

// 检查当前状态
console.log('Mock模式:', localStorage.getItem('use_mock_api'))
```

### 4. 页面无法访问

**问题**: 404错误或页面空白

**解决方案**:
```bash
# 检查路由配置
# 确认访问正确的URL
http://localhost:3000/login
http://localhost:3000/mock-test

# 检查控制台错误信息
# 确认所有依赖已正确安装
```

### 5. API请求失败

**问题**: API调用失败或返回错误

**解决方案**:
```javascript
// 检查Mock模式状态
console.log('Mock模式:', localStorage.getItem('use_mock_api'))

// 检查网络请求
// 在Network面板查看请求详情

// 手动测试Mock API
import MockApiService from './src/api/mock.js'
MockApiService.login('+84123456789', '123456')
```

## 📱 移动端调试

### 1. 移动端预览

#### 在同一网络下的移动设备访问：
```
http://*************:3000
```
（替换为你的实际IP地址）

#### 查看本机IP地址：
```bash
# Windows
ipconfig

# Mac/Linux
ifconfig
```

### 2. 移动端调试工具

#### 使用Chrome DevTools移动端模拟：
1. 打开Chrome开发者工具 (F12)
2. 点击设备图标切换到移动端视图
3. 选择目标设备型号进行测试

#### 使用vconsole进行移动端调试：
```bash
npm install vconsole --save-dev
```

在main.js中添加：
```javascript
if (process.env.NODE_ENV === 'development') {
  import('vconsole').then(VConsole => {
    new VConsole.default()
  })
}
```

## 🎯 最佳实践

### 1. 开发流程建议

1. **启动项目** → 检查基础功能
2. **启用Mock模式** → 测试API功能
3. **使用调试面板** → 监控数据状态
4. **访问测试页面** → 进行全面测试
5. **查看控制台** → 检查错误和日志

### 2. 调试技巧

- **使用Vue DevTools**: 安装Vue浏览器扩展
- **保持控制台开放**: 随时查看日志信息
- **使用调试面板**: 实时监控Mock数据
- **测试多种场景**: 正常流程和异常情况
- **记录问题**: 保存错误截图和日志

### 3. 性能优化

- **热重载**: 修改代码后自动刷新
- **懒加载**: 路由组件按需加载
- **缓存优化**: 合理使用浏览器缓存
- **网络优化**: 使用Mock数据减少网络请求

## 📋 快速启动检查清单

### 启动前检查
- [ ] Node.js版本 >= 16.0.0
- [ ] npm版本 >= 8.0.0
- [ ] 项目依赖已安装
- [ ] 端口3000未被占用

### 启动后检查
- [ ] 开发服务器正常启动
- [ ] 浏览器能正常访问
- [ ] 登录页面正常显示
- [ ] Mock调试面板可见
- [ ] 控制台无错误信息

### Mock模式检查
- [ ] Mock模式已启用
- [ ] 测试账号可正常登录
- [ ] 验证码功能正常
- [ ] 调试面板数据正确
- [ ] Mock测试页面可访问

---

**完成以上步骤后，你就可以开始愉快地调试Mock数据了！** 🎉

如果遇到任何问题，请检查控制台错误信息，或参考上面的常见问题解决方案。 