# 社交拼团APP产品需求文档 (PRD) - 新版

| 版本 | 日期       | 作者   | 审核人 | 变更描述                                               |
| :--- | :--------- | :----- | :----- | :----------------------------------------------------- |
| V2.1 | 2025-06-21 | Gemini |        | 基于业务流程调用逻辑优化，明确页面跳转关系和用户路径 |

---

## 1. 引言 (Introduction)

### 1.1. 文档目的

本文档旨在明确"社交拼团APP"的产品需求，定义其核心功能、业务逻辑、用户角色及非功能性需求。旨在为产品、设计、开发、测试等团队提供统一的指导和依据，确保产品能够高效、高质量地完成。

### 1.2. 产品愿景

打造一个以社交拼团为核心模式的直营电商平台。我们的愿景是：

*   **对用户：** 成为用户通过社交分享发现和购买高性价比商品的首选入口，提供"更低价格、更好服务"的购物体验。
*   **对平台：** 构建一个高效、精选、可持续发展的B2C直营商业生态系统。

### 1.3. 目标用户

- **C端用户 (消费者):**
  - **价格敏感型用户：** 追求高性价比，乐于发现和分享优惠。
  - **社交活跃型用户：** 享受与朋友共同购物的乐趣，乐于在社交网络中传播。
  - **品质生活追求者：** 相信平台精选的商品，寻求高品质的直营产品。

### 1.4. 名词解释

| 术语 | 解释 |
| :--- | :--- |
| **SPU (Standard Product Unit)** | 标准化产品单元，如 "某品牌T恤"。 |
| **SKU (Stock Keeping Unit)** | 库存量单位，如 "某品牌T恤 白色 L码"。 |
| **拼团** | 由一人开团，邀请好友在规定时间内成团，成功后享受优惠价。 |
| **活动专区** | 聚合了多种营销拼团活动的特定页面，如2人团、7人团、拉新团等。 |
| **7人团** | 一种需要7人参与才能成团的普通拼团模式。 |
| **拉新7人团** | 一种以获取新用户为目的的特殊7人拼团活动，通常由平台弹窗广告触发，可能包含对新参团用户的特殊优惠或要求。 |
| **订单确认页** | 统一的订单确认界面，支持购买确认、参与确认、拼团确认等多种场景。 |
| **拼团等待页** | 用户支付后等待拼团成功的页面，根据拼团类型分为2人团等待页和7人团等待页。 |
| **结算弹窗** | 拼团结束后弹出的模态窗口，告知用户拼团成功或失败的结果。 |
| **分享引导弹窗** | 在结算弹窗关闭后触发的引导用户分享的模态窗口。 |
| **账户余额** | 用户在平台内的虚拟资金账户，主要用于接收退款和支付订单。 |
| **GMV (Gross Merchandise Volume)** | 商品交易总额，是衡量电商平台规模的核心指标。 |

---

## 2. 产品功能 (Product Features)

### 2.1. 功能总览 (Feature Overview)

```mermaid
graph TD;
    A["社交拼团APP平台"] --> B["C端用户APP"];
    A --> E["平台管理后台 (Web)"];
    A --> F["通用底层服务"];

    subgraph C端用户APP
        B1["用户中心 (资料, 订单, 收藏)"];
        B2["首页浏览 (搜索, 推荐, 活动专区)"];
        B3["交易流程 (下单, 支付, 物流)"];
        B4["核心玩法 (社交拼团, 多人拼团)"];
    end

    subgraph 平台管理后台
        E1["用户管理"];
        E2["商品与库存管理"];
        E3["订单与售后管理"];
        E4["营销与内容配置"];
        E5["数据分析报表"];
    end

    subgraph 通用底层服务
        F1["LBS定位服务"];
        F2["支付网关"];
        F3["消息推送"];
        F4["智能推荐引擎"];
        F5["仓储物流接口"];
    end

    B --> B1;
    B --> B2;
    B --> B3;
    B --> B4;
  
    E --> E1;
    E --> E2;
    E --> E3;
    E --> E4;
    E --> E5;

    F --> F1;
    F --> F2;
    F --> F3;
    F --> F4;
    F --> F5;
```

### 2.2. 用户角色与权限 (User Roles & Permissions)

| 角色 | 核心权限 |
| :--- | :--- |
| **消费者 (未登录)** | 浏览首页、商品列表、详情页；查看评价；无法下单、评论、收藏。 |
| **消费者 (已登录)** | 拥有未登录用户所有权限；下单支付；发表评价；管理订单；收藏商品；发起或参与拼团。 |
| **平台管理员** | 访问平台管理后台；管理用户；管理商品(SPU/SKU)、库存、分类；管理订单；配置营销活动；系统设置；数据监控。 |

### 2.3. 功能详述 (Detailed Features)

#### 2.3.1. C端 - 消费者APP (iOS/Android)

**模块一：用户中心模块 (UC)**

| 功能ID | 功能名称 | 用户故事 | 功能描述 | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| UC-001 | 注册/登录 | 作为新用户，我希望能通过手机号快速注册，并通过密码或验证码登录。 | 1. 支持（国家区号）+手机号+验证码注册/登录。`<br>`2. 支持Zalo、Facebook等第三方授权登录。`<br>`3. 包含《用户协议》和《隐私政策》的同意流程。 | 高 |
| UC-002 | 个人资料管理 | 作为用户，我希望能编辑我的昵称、头像和收货地址。 | 1. 用户可自定义头像、昵称等。`<br>`2. 收货地址管理：支持新增、删除、修改、设置默认地址。 | 高 |
| UC-003 | 我的订单 | 作为用户，我希望能方便地查看我所有的订单状态。 | 1. 列表展示所有订单，按"待付款"、"待成团"、"待发货"、"待收货"、"待评价"、"退款/售后"等状态分类。`<br>`2. 订单详情页展示商品信息、金额、支付方式、物流信息。`<br>`3. 对于拼团订单，可直接跳转到对应的拼团等待页面查看进度。 | 高 |
| UC-004 | 收藏/足迹 | 作为用户，我想收藏感兴趣的商品，并能看到我最近浏览过的记录。 | 1. "我的收藏"：可收藏商品。`<br>`2. "我的足迹"：按时间倒序展示最近浏览过的商品。 | 中 |
| UC-005 | 优惠券/红包 | 作为用户，我想查看我领取的平台优惠券和红包。 | 列表展示可用、已使用、已过期的优惠券和红包，并显示使用规则。 | 高 |
| UC-006 | 我的钱包 | 作为用户，我希望能管理我的账户余额和查看所有资金流水。 | 1.**余额展示**：清晰显示当前账户可用余额。`<br>`2. **支付密码**：支持设置和修改支付密码，用于余额支付时的身份验证。`<br>`3. **账单/消费记录**：提供详细的资金流水列表，记录每一笔收入（如退款）和支出（如订单支付），并可按类型筛选。 | 高 |

**模块二：首页及浏览模块 (Browse)**

| 功能ID | 功能名称 | 用户故事 | 功能描述 | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| BR-001 | 首页 | 作为用户，我希望首页内容丰富，能快速找到我感兴趣的东西。 | 1.**顶部搜索框**：支持关键词搜索。`<br>`2. **Banner广告位**：展示平台精选活动或商品，点击可跳转到活动专区或商品详情页。`<br>`3. **金刚区**：包含**活动专区**、商品品类（如服饰、家居）等快捷入口。`<br>`4. **信息流推荐**：基于算法的"猜你喜欢"瀑布流，展示精选拼团商品，点击商品卡片跳转到商品详情页。`<br>`5. **拉新弹窗广告**：在特定条件下（如首次打开App）触发，推广"拉新7人团"等特殊活动。 | 高 |
| BR-002 | 搜索功能 | 作为用户，我希望能通过关键词搜索到我想要的商品。 | 1. 提供搜索历史和热门搜索推荐。`<br>`2. 搜索结果页支持排序（综合、销量、价格）、筛选（品类、价格区间）。`<br>`3. 搜索结果商品点击后跳转到商品详情页。 | 高 |
| BR-003 | 商品详情页 (PDP) | 作为用户，我想了解商品的全部信息，以决定是否购买。 | 1.**商品信息**：轮播图、视频、标题、价格（原价、拼团价）、已售数量。`<br>`2. **拼团规则**：根据商品参与的活动类型，展示不同规则（如2人团、7人团成团时限等）。`<br>`3. **正在拼团列表**：展示其他用户发起的拼团，用户可选择参与。`<br>`4. **用户评价**：展示其他用户的图文评价和评分。`<br>`5. **图文详情**：由平台上传的官方详细介绍。`<br>`6. **底部操作栏**：提供"单独购买"和"发起拼团"按钮，点击后跳转到订单确认页。 | 高 |
| BR-004 | 活动专区页 | 作为用户，我希望能在一个页面看到所有正在进行的优惠拼团活动。 | 1. 聚合展示所有特殊拼团活动商品。`<br>`2. 页面内可按活动类型（如2人团专区、7人团专区、拉新活动专区）进行Tab切换和筛选。`<br>`3. 列表展示活动商品，并突出显示其拼团类型和价格。`<br>`4. 点击商品或"参与"按钮后，跳转到订单确认页。 | 高 |

**模块三：交易流程模块 (Transaction)**

| 功能ID | 功能名称 | 用户故事 | 功能描述 | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| TR-001 | 订单确认流程 | 作为用户，我选好商品后，希望能快速完成订单确认。 | 1.**统一确认页面**：支持"购买确认"、"参与确认"、"拼团确认"等多种场景的订单确认。`<br>`2. **信息确认**：展示商品信息、选择SKU（规格、数量）、确认收货地址和联系电话。`<br>`3. **价格计算**：显示商品总价、运费、优惠券抵扣等费用明细。`<br>`4. **优惠选择**：用户可选择可用的优惠券/红包进行抵扣。`<br>`5. **提交订单**：确认信息无误后，点击"提交订单"跳转到支付页面。 | 高 |
| TR-002 | 支付功能 | 作为用户，我希望能用我习惯的支付方式安全地付款。 | 1.**余额支付**：优先使用账户余额进行支付（需输入支付密码）。`<br>`2. **组合支付**：用户可选择第三方支付渠道（如ZaloPay, Momo, 银行卡）支付。`<br>`3. **支付完成跳转**：根据拼团类型，支付成功后自动跳转到对应的拼团等待页（2人团等待页或7人团等待页）。`<br>`4. 支付有时效限制（如15分钟），超时自动取消订单。 | 高 |
| TR-003 | 拼团等待管理 | 作为用户，我希望能清楚地看到拼团进度和剩余时间。 | 1.**2人团等待页**：展示2人团的拼团进度、成员信息、倒计时，提供邀请好友功能。`<br>`2. **7人团等待页**：展示7人团的拼团进度、成员网格、倒计时，提供邀请好友功能。`<br>`3. **实时更新**：页面实时更新拼团状态和成员信息。`<br>`4. **分享功能**：提供分享链接/海报，邀请好友参团。`<br>`5. **拼团结束处理**：倒计时结束后，触发结算弹窗显示拼团结果。 | 高 |
| TR-004 | 拼团结果反馈 | 作为用户，我希望能及时了解拼团的最终结果。 | 1.**结算弹窗**：拼团结束后弹出模态窗口，显示拼团成功或失败的结果。`<br>`2. **成功处理**：显示订单信息、预计发货时间等，提供"查看订单"和"再拼一单"操作。`<br>`3. **失败处理**：说明失败原因、退款流程，提供"返回首页"和"看其他活动"操作。`<br>`4. **分享引导**：关闭结算弹窗后，触发分享引导弹窗，鼓励用户分享给好友。 | 高 |
| TR-005 | 拉新7人团特殊流程 | 作为用户，我被拉新弹窗吸引，希望参与这个特殊的7人团活动。 | 1. **弹窗触发入口**：用户在浏览APP时，由平台推送的弹窗广告作为活动入口，点击后直接跳转到订单确认页。`<br>`2. **新用户导向**：活动规则要求参团成员中必须包含一定数量的**新注册用户**。`<br>`3. **特殊标识**：在订单确认页和等待页中明确标识为"拉新7人团"活动。`<br>`4. **流程统一**：后续支付、等待、结果反馈流程与普通7人团保持一致。 | 高 |
| TR-006 | 物流跟踪 | 作为用户，我希望能追踪我的包裹。 | 平台发货后，订单状态变为"待收货"，用户可在订单详情中查看物流轨迹。 | 高 |
| TR-007 | 退款/售后 | 作为用户，如果我不满意，希望能方便地申请退款。 | 1. 用户在订单页发起退款/售后申请，填写原因和凭证。`<br>`2. 平台管理员审核后，原路退回款项至**用户账户余额**。 | 高 |

#### 2.3.2. 平台管理后台 (Web)

此为内部系统，用于平台日常运营和管理。

| 功能模块 | 功能点 | 详细描述 |
| :--- | :--- | :--- |
| **主面板 (Dashboard)** | 核心数据概览 | 1.**实时数据**：今日GMV、支付订单数、新增用户数、在线用户数。`<br>`2. **核心指标统计**：展示昨日、近7日、近30日的核心数据曲线图。`<br>`3. **待办事项**：快速入口，显示待处理的退款申请、待发货订单等。 |
| **用户管理** | 用户列表与查询 | 1. 展示所有用户列表（昵称、手机号、注册时间、用户状态）。`<br>`2. 支持按手机号、昵称进行搜索。`<br>`3. 支持按状态（正常、已禁用）进行筛选。 |
| | 用户详情与操作 | 1. 查看用户详细资料、收货地址、历史订单记录。`<br>`2. 可对用户账户进行"禁用"或"解禁"操作。 |
| **商品管理** | 商品分类管理 | 1. 创建、编辑、删除商品分类。`<br>`2. 支持二级分类结构。`<br>`3. 可调整分类的显示顺序。 |
| | 商品列表 (SPU) | 1. 以SPU（标准产品单元）维度展示商品列表。`<br>`2. 支持按商品名称、分类、状态（上架/下架）进行搜索和筛选。 |
| | 商品发布/编辑 | 1.**基本信息**：填写商品名称、分类、描述、角标。`<br>`2. **媒体管理**：上传商品主图、轮播图、详情页长图/视频。`<br>`3. **规格与库存 (SKU)**：设置不同规格，并为每个SKU设定价格、库存、编码。 |
| **订单管理** | 订单列表 | 1. 集中展示所有用户订单，包含订单号、下单用户、商品信息、实付金额、订单状态。`<br>`2. **多维度查询**：支持按订单号、用户手机号、商品名称进行精确搜索。`<br>`3. **状态筛选**：支持按各种订单状态筛选。 |
| | 订单详情与操作 | 1. 查看订单完整信息，包括商品、收货人、支付、物流信息。`<br>`2. **发货处理**：对"待发货"订单，可录入物流公司及单号。`<br>`3. **售后处理**：审核用户的退款/售后申请。 |
| **营销管理** | 拼团活动管理 | 1. 创建拼团活动，关联指定商品。`<br>`2. **设置拼团参数**：可选择**拼团类型（如2人团、7人团）**、成团人数、有效时长、活动时间范围。`<br>`3. 可查看活动列表，对活动进行"暂停"或"结束"操作。 |
| | 拉新拼团活动 | 1. 创建特殊的"拉新7人团"活动，关联引流款商品。`<br>`2. **设置拉新规则**：可配置成团成员中必须包含的新用户数量。`<br>`3. **关联弹窗广告**：选择此活动需要触发的弹窗广告样式。`<br>`4. 管理活动列表，监控各拉新团的参与进度。 |
| | 优惠券管理 | 1. 创建优惠券：设置优惠券名称、类型（满减/折扣）、面额、使用门槛、有效期、发放总量。`<br>`2. 管理优惠券列表。 |
| **内容管理** | Banner管理 | 1. 上传首页Banner图片。`<br>`2. 设置Banner的跳转链接（商品详情页、活动专区页等）。`<br>`3. 可拖拽调整Banner的显示顺序。 |
| | 公告管理 | 1. 发布、编辑、删除平台公告。 |
| | **弹窗广告管理** | 1. **创建弹窗**：上传弹窗广告图片素材，设置标题和文案。<br>2. **设置触发规则**：配置弹窗的显示页面、显示频率（如每个用户仅显示一次）。<br>3. **关联活动**：将弹窗链接至指定的"拉新拼团活动"。 |
| **财务管理** | 支付对账 | 1. 查看支付流水，与第三方支付平台的账单进行核对。 |
| | 数据统计 | 1. 提供每日、每周、每月的财务结算报表。 |
| **系统设置** | 管理员与角色 | 1. 创建、编辑、删除后台管理员账号。`<br>`2. **角色管理**：创建不同角色（如运营、客服），并分配不同的功能模块权限（RBAC）。 |
| | 操作日志 | 1. 记录所有管理员在后台的关键操作，便于审计和问题追溯。 |

---

## 3. 用户流程与页面跳转逻辑

### 3.1. 核心用户路径

基于业务流程分析，用户在APP中主要有以下三种路径：

#### 路径一：普通商品购买流程
```
首页 → 点击商品 → 商品详情页 → 发起拼团 → 订单确认页 → 支付页 → 拼团等待页(2人/7人) → 结算弹窗 → 分享引导弹窗
```

#### 路径二：活动专区参与流程
```
首页 → 点击活动广告 → 活动专区页 → 选择活动参与 → 订单确认页 → 支付页 → 拼团等待页(2人/7人) → 结算弹窗 → 分享引导弹窗
```

#### 路径三：拉新活动流程
```
首页 → 拉新弹窗广告 → 点击参与 → 订单确认页 → 支付页 → 7人团等待页 → 结算弹窗 → 分享引导弹窗
```

### 3.2. 页面跳转规则

| 源页面 | 触发操作 | 目标页面 | 备注 |
| :--- | :--- | :--- | :--- |
| 首页 | 点击商品卡片 | 商品详情页 | 普通商品浏览流程 |
| 首页 | 点击Banner/活动入口 | 活动专区页 | 营销活动聚合页 |
| 首页 | 弹窗广告触发 | 拉新弹窗 | 特殊营销活动 |
| 商品详情页 | 点击"发起拼团" | 订单确认页 | 开团购买流程 |
| 商品详情页 | 点击"单独购买" | 订单确认页 | 非拼团购买流程 |
| 活动专区页 | 点击"参与拼团" | 订单确认页 | 参团购买流程 |
| 拉新弹窗 | 点击"立即参与" | 订单确认页 | 拉新活动流程 |
| 订单确认页 | 点击"提交订单" | 支付页 | 统一支付入口 |
| 支付页 | 支付成功(2人团) | 2人团等待页 | 根据拼团类型跳转 |
| 支付页 | 支付成功(7人团) | 7人团等待页 | 根据拼团类型跳转 |
| 拼团等待页 | 拼团结束 | 结算弹窗 | 成功/失败结果展示 |
| 结算弹窗 | 关闭弹窗 | 分享引导弹窗 | 引导用户分享 |

---

## 4. 非功能性需求 (Non-Functional Requirements)

| 类别 | 需求描述 |
| :--- | :--- |
| **性能需求** | - 核心页面加载时间在3G网络下应小于3秒。`<br>`- 服务器对95%的用户请求响应时间应在200ms以内。`<br>`- 系统能支持至少10,000人/秒的并发访问。 |
| **安全性需求** | - 用户密码、支付信息等敏感数据必须加密存储。`<br>`- 所有API接口需进行身份验证和权限校验。`<br>`- 有效防止SQL注入、XSS、CSRF等常见Web攻击。 |
| **兼容性需求** | -**APP端：** 兼容近3年发布的iOS和Android主流版本及主流分辨率机型。`<br>`- **Web端：** 兼容Chrome、Firefox、Safari、Edge等主流浏览器的最新版本。 |
| **易用性需求** | - 界面设计简洁美观，交互流程符合用户习惯。`<br>`- 关键操作有明确的引导和反馈提示。`<br>`- 提供便捷的客服入口。 |
| **扩展性需求** | - 系统架构采用模块化设计，便于未来功能扩展。`<br>`- 数据库设计应考虑未来数据量增长，支持分库分表。 |

---

## 5. 未来规划 (Future Roadmap)

### V2.2 - 深化运营与社交属性

- **直播带货：** 引入平台官方或达人直播，更直观地展示精选商品，提高转化率。
- **内容社区：** 增加"好物说"板块，鼓励用户发布开箱测评、好物推荐等UGC内容，增强用户粘性。
- **会员体系：** 建立付费会员体系，提供"大额优惠券"、"会员专属价"、"包邮"等权益。

### V3.0 - 智能化与效率提升

- **智能推荐升级：** 引入更复杂的机器学习算法，实现"千人千面"的精准推荐。
- **仓储物流体系：** 建立或合作建立高效的仓储和配送体系，提升履约效率和用户体验。
- **API生态建设：** 提供API接口，支持与第三方仓储、物流、营销工具集成，提升运营效率。

---

**文档结束** 