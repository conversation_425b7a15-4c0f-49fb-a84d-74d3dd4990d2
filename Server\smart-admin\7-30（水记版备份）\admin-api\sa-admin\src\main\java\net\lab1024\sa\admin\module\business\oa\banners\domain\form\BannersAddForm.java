package net.lab1024.sa.admin.module.business.oa.banners.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 横幅管理 新建表单
 *
 * <AUTHOR>
 * @Date 2025-07-01 12:14:47
 * @Copyright -
 */

@Data
public class BannersAddForm {

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Banner标题 不能为空")
    private String title;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图片URL 不能为空")
    private String imageUrl;

    @Schema(description = "跳转链接")
    private String linkUrl;

    @Schema(description = "链接类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "链接类型 不能为空")
    private String linkType;

    @Schema(description = "排序权重", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序权重 不能为空")
    private Integer sortOrder;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态")
    private Integer status;

}