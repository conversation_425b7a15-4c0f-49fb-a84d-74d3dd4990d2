package net.lab1024.sa.admin.module.business.activities.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;

/**
 * 抽奖活动表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */

@Data
public class ActivitiesAddForm {

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "活动名称 不能为空")
    private String name;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "活动类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "活动类型 不能为空")
    private String type;

    @Schema(description = "活动状态")
    private Integer status;

    @Schema(description = "返还比例%")
    private Integer returnRatio;

    @Schema(description = "活动配置参数")
    private Map<String, Object> configInfo;

    @Schema(description = "必不中开关", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "必不中开关 不能为空")
    private Integer forceLossFlag;

    @Schema(description = "参与人数限制")
    private Integer participantLimit;

    @Schema(description = "当前参与人数")
    private Integer currentParticipants;

    @Schema(description = "活动开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动开始时间 不能为空")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动结束时间 不能为空")
    private LocalDateTime endTime;

    @Schema(description = "开奖计时")
    @NotNull(message = "开奖计时 不能为空")
    private Integer remainingTime;

}