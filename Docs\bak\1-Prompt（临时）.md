智能弹窗逻辑中

每日只显示一次（通过localStorage控制）改为无限制，只要是新用户和未登录用户，每次都打开弹窗

普通商品改为全部商品

网页要根据浏览器的语言来自动切换语言，目前只支持中文和越南语两种语言

导航栏中的购物车去掉

商品列表要分成两个标签页：3人团、10人团

弹窗内容完全按照您提供的越南语文字改为：也按浏览器语言自动切换中文和越南语

" 限时活动

平台补贴专区

每日签到领奖励

立即参与" 这个区域（Banner区域？）要做成多个内容轮换的

Banner的高度增加30px

页面顶部的信号、电池状态栏去掉

搜索框高度缩小20%

采用与首页同样的UI风格来生成二级页面：分类（商品分类列表页面），同时要链接到导航按钮中的“分类 ”按钮中

商品标签中的“还差x人成团”改为“已有xx人参与拼团”

特惠商品标签中，加上“平台补贴xx元”的内容

倒计时结束后，不需要弹出提示“拼团时间已结束”，直接跳转到

APP/原型3/settlement_failure.html（拼团结果显示页面）中的“再次参与抽奖”按钮链接到APP/原型3/group_buying_confirmation.html，弹窗中的“再试一次”和“继续领取奖励”按钮链接到APP/原型3/group_buying_confirmation.html，


APP/原型3/product_detail.html  去掉购物车入口，去掉单独购买按钮，“发起拼团”按钮链接到group_buying_confirmation.html页面


把 APP/原型2/profile.html 复制到 APP/原型3 目录下，链接到APP/原型3/home.html 页面中底部导航栏中的“我的”入口按钮，然后在页面的“帐户余额”这行文字下一行添加两个按钮“提现”和“充值”。把我的订单中的“待付款”、“待成团”去掉


你现在要把 APP/原型3/profile.html 这个页面所有的二级页面都生成，且要链接上对应 的按钮或者入口，这些二级页面的UI风格都要延续profile.html的且保持统一
