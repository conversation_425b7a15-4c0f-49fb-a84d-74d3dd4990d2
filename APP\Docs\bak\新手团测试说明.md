# 新手团功能测试说明

## 概述
已在mock数据中添加了新手用户数据，并创建了开发环境下的用户切换工具，方便测试新手团功能。

## 测试用户数据

### 1. 普通用户（老用户）
- **手机号：** 13800000001
- **密码：** 123456  
- **昵称：** 张三
- **特征：** 
  - 有充值记录（hasChargeRecord: true）
  - 余额：299.50元
  - 积分：1280
  - 优惠券：3张
  - **无法参与新手团**

### 2. 新手用户
- **手机号：** 13900000001
- **密码：** 123456
- **昵称：** 新手小白
- **特征：**
  - 无充值记录（hasChargeRecord: false）
  - 余额：0元
  - 积分：0
  - 优惠券：0张
  - **可以参与新手团**

## 用户切换工具

### 位置
开发环境下，在应用右上角有一个红色的"🧪 开发测试用户切换"面板。

### 功能
1. **当前用户显示：** 显示当前登录用户信息及用户类型
2. **用户列表：** 显示所有可用测试用户
3. **快速切换：** 点击用户项或快速按钮即可切换
4. **状态标识：** 
   - 新手/老用户标识
   - 有充值/无充值标识

### 使用方法
1. 点击"展开"按钮打开面板
2. 选择要切换的用户，或使用快速按钮：
   - "快速登录老用户" - 切换到张三（普通用户）
   - "快速登录新手用户" - 切换到新手小白（新手用户）
3. 系统会自动刷新页面完成切换

## 新手团测试流程

### 使用新手用户测试
1. 切换到新手用户（13900000001）
2. 访问首页，应该能看到新手团弹窗
3. 点击新手团弹窗中的商品
4. 进入新手团专属流程

### 使用普通用户测试
1. 切换到普通用户（13800000001）
2. 访问首页，不应该看到新手团弹窗
3. 只能看到三人团、五人团、活动专区等入口

## 新手团业务逻辑

### 判断条件
- 用户必须是新注册用户（isNewUser: true）
- 用户必须无充值记录（hasChargeRecord: false）
- 两个条件同时满足才能参与新手团

### 限制规则
- 新手团为5人团
- 只有符合条件的新手用户才能看到新手团弹窗
- 老用户或有充值记录的用户无法参与新手团

## 技术实现

### Mock数据结构
```javascript
users: [
  {
    id: 10001,
    nickname: '张三',
    phone: '13800000001',
    password: '123456',
    isNewUser: false,
    hasChargeRecord: true, // 有充值记录
    balance: 299.50,
    // ... 其他字段
  },
  {
    id: 10002,
    nickname: '新手小白',
    phone: '13900000001',
    password: '123456',
    isNewUser: true,
    hasChargeRecord: false, // 无充值记录
    balance: 0,
    // ... 其他字段
  }
]
```

### 用户切换函数
- `switchUser(phone)` - 切换到指定手机号的用户
- `getUserByPhone(phone)` - 根据手机号获取用户信息
- `getAllUsers()` - 获取所有用户列表（开发测试用）

## 注意事项

1. **开发环境专用：** 用户切换工具只在开发环境显示
2. **自动刷新：** 切换用户后会自动刷新页面，确保所有组件状态更新
3. **密码统一：** 所有测试用户密码都是 `123456`
4. **数据隔离：** 不同用户的钱包数据、订单数据等完全隔离

## 测试建议

1. **先测试新手用户：** 验证新手团弹窗是否正常显示
2. **再测试普通用户：** 确认普通用户无法看到新手团功能
3. **流程完整性：** 测试完整的新手团购买流程
4. **边界情况：** 测试用户状态变化对新手团权限的影响

通过以上配置，您现在可以完整测试新手团功能了！ 