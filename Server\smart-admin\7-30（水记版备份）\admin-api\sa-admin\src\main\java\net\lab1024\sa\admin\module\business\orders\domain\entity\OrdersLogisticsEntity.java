package net.lab1024.sa.admin.module.business.orders.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@TableName(value = "t_order_logistics", autoResultMap = true)
public class OrdersLogisticsEntity {

    /**
     * 订单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 快递单号
     */
    private String trackingNumber;

    /**
     * 快递公司
     */
    private String courierCompany;

    /**
     * 物流状态
     */
    private String status;

    /**
     * 物流跟踪信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> trackingInfo;

    /**
     * 发货时间
     */
    private LocalDateTime shippedTime;

    /**
     * 签收时间
     */
    private LocalDateTime deliverTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
