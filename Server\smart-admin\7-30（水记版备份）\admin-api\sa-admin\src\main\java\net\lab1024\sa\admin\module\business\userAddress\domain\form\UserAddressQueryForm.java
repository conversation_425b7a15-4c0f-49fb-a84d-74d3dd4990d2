package net.lab1024.sa.admin.module.business.userAddress.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户收货地址表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-28 15:09:41
 * @Copyright -
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class UserAddressQueryForm extends PageParam {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "姓名或电话")
    private String blurry;

}
