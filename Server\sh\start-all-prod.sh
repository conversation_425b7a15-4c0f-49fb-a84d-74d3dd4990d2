#!/bin/bash

echo "🏭 启动生产环境全套服务..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs/prod"
mkdir -p "$LOG_DIR"

# 检查并启动MySQL和Redis
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "🔄 启动MySQL服务..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "🔄 启动Redis服务..."
    sudo systemctl start redis-server
fi

# 编译生产版本
echo "📦 编译生产版本..."
cd "$PROJECT_ROOT/Server/smart-admin"
mvn clean package -Pprod -DskipTests

# 前端生产构建
echo "📦 构建前端生产版本..."
cd "$PROJECT_ROOT/APP"
npm run build

# 管理端生产构建
echo "📦 构建管理端生产版本..."
cd "$PROJECT_ROOT/Server/smart-admin/admin-web"
npm run build:prod

# 启动后端生产服务
echo "🚀 启动后端生产服务..."
cd "$PROJECT_ROOT/Server/smart-admin/admin-api/sa-admin"

# 检查是否已有运行的实例
PID=$(ps -ef | grep tgw-pp.jar | grep -v grep | awk '{ print $2 }')
if [ ! -z "$PID" ]; then
    echo "⚠️  发现已运行的后端服务 (PID: $PID)，正在停止..."
    kill -9 $PID
    sleep 3
fi

nohup java -jar target/tgw-pp.jar --spring.profiles.active=prod > "$LOG_DIR/backend-prod.log" 2>&1 &
BACKEND_PID=$!
echo "✅ 后端生产服务已启动 (PID: $BACKEND_PID)"

# 保存PID到文件
echo $BACKEND_PID > "$LOG_DIR/backend-prod.pid"

echo ""
echo "🎉 生产环境服务启动完成！"
echo ""
echo "📍 访问地址："
echo "   后端API:  http://localhost:8686"
echo "   API文档:  http://localhost:8686/doc.html"
echo "   健康检查: http://localhost:8686/actuator/health"
echo ""
echo "📁 前端构建文件: $PROJECT_ROOT/APP/dist/"
echo "📁 管理端构建文件: $PROJECT_ROOT/Server/smart-admin/admin-web/dist/"
echo ""
echo "📝 生产日志: $LOG_DIR/backend-prod.log"
echo ""
echo "💡 部署说明："
echo "   1. 将前端构建文件复制到Web服务器"
echo "   2. 将管理端构建文件复制到Web服务器"
echo "   3. 配置Nginx反向代理到后端API"
echo ""
echo "🛑 停止服务请运行: $SCRIPT_DIR/stop-all-prod.sh"