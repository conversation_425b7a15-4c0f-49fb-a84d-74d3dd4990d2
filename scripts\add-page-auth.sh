#!/bin/bash

# 为用户页面批量添加登录检查脚本
echo "🔐 开始为用户页面添加登录检查..."

# 需要添加登录检查的页面列表及对应错误信息
declare -A pages_and_messages=(
  ["APP/src/views/user/WalletPage.vue"]="访问钱包页面需要先登录"
  ["APP/src/views/user/TeamPage.vue"]="访问团队页面需要先登录"
  ["APP/src/views/user/OrdersPage.vue"]="访问订单页面需要先登录"
  ["APP/src/views/user/AddressPage.vue"]="访问地址管理需要先登录"
  ["APP/src/views/user/WithdrawPage.vue"]="访问提现页面需要先登录"
  ["APP/src/views/user/SettingsPage.vue"]="访问设置页面需要先登录"
)

for file in "${!pages_and_messages[@]}"; do
  full_path="/mnt/d/Dev/团购网/$file"
  error_message="${pages_and_messages[$file]}"
  
  if [ -f "$full_path" ]; then
    echo "📄 处理文件: $file"
    
    # 检查是否已经导入了usePageAuth
    if ! grep -q "usePageAuth" "$full_path"; then
      # 添加import语句
      sed -i "/import.*@\/utils\/message/a import { requireAuth } from '@/composables/usePageAuth'" "$full_path"
      
      # 在setup函数开始后添加requireAuth调用
      sed -i "/setup() {/,/const/ {
        /const.*useRouter\|const.*useAuthStore/ {
          a\\    \\    // 启用页面级登录检查\\    requireAuth({\\      errorMessage: '$error_message'\\    })\\
        }
      }" "$full_path"
      
      echo "✅ 已添加登录检查: $file"
    else
      echo "⏭️ 已存在登录检查: $file"
    fi
  else
    echo "⚠️ 文件不存在: $file"
  fi
done

echo "🎉 登录检查添加完成！"