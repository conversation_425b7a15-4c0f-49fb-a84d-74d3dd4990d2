package net.lab1024.sa.admin.module.business.teamRewards.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.teamRewards.domain.entity.TeamRewardsEntity;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsQueryForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.vo.TeamRewardsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 团队奖励表 Dao
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@Mapper
public interface TeamRewardsDao extends BaseMapper<TeamRewardsEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<TeamRewardsVO> queryPage(Page page, @Param("queryForm") TeamRewardsQueryForm queryForm);

}
