package net.lab1024.sa.admin.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class WalletConst {
    /**
     * 支付类型
     */
    public static class PayMode {
        /**
         * 余额
         */
        public static final Integer BALANCE = 0;
        /**
         * 体验金
         */
        public static final Integer EXPERIENCE = 1;
        /**
         * 积分
         */
        public static final Integer POINTS = 2;
    }

    /**
     * 流水状态
     */
    public static class TransactionsStatus {
        /**
         * 减钱的
         * */
        public static final List<String> SubtractList = Arrays.asList("WITHDRAWAL", "PAYMENT", "MANUAL_REDUCE");
        /**
         * 充值
         */
        public static final String RECHARGE = "RECHARGE";
        /**
         * 提现
         */
        public static final String WITHDRAWAL = "WITHDRAWAL";
        /**
         * 支付
         */
        public static final String PAYMENT = "PAYMENT";
        /**
         * 未中奖励
         */
        public static final String REFUND_WIN = "REFUND_WIN";
        /**
         * 未中返还
         */
        public static final String REFUND_LOSS = "REFUND_LOSS";
        /**
         * 退单返还
         */
        public static final String ORDER_REFUND = "ORDER_REFUND";
        /**
         * 退单违约金
         */
        public static final String ORDER_PENALTY = "ORDER_PENALTY";
        /**
         * 补贴
         */
        public static final String SUBSIDY = "SUBSIDY";
        /**
         * 佣金
         */
        public static final String COMMISSION = "COMMISSION";
        /**
         * 首单上级获得的奖励
         */
        public static final String FIRST_REWARD = "FIRST_REWARD";
        /**
         * 团队奖金
         */
        public static final String TEAM_REWARD = "TEAM_REWARD";
        /**
         * 手动减少
         */
        public static final String MANUAL_ADJUST = "MANUAL_REDUCE";
        /**
         * 手动添加
         */
        public static final String MANUAL_ADD = "MANUAL_ADD";
        /**
         * 体验赠送
         */
        public static final String EXPERIENCE_GIFT = "EXPERIENCE_GIFT";

        /**
         * 新手赠送
         */
        public static final String FREE_NOVICE = "FREE_NOVICE";
    }
}
