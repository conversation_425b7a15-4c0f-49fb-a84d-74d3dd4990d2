package net.lab1024.sa.admin.module.business.teamRewards.controller;

import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsAddForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsQueryForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.form.TeamRewardsUpdateForm;
import net.lab1024.sa.admin.module.business.teamRewards.domain.vo.TeamRewardsVO;
import net.lab1024.sa.admin.module.business.teamRewards.service.TeamRewardsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 团队奖励表 Controller
 *
 * <AUTHOR>
 * @Date 2025-07-01 08:24:07
 * @Copyright -
 */

@RestController
@Tag(name = "团队奖励表")
public class TeamRewardsController {

    @Resource
    private TeamRewardsService teamRewardsService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/teamRewards/queryPage")
    @SaCheckPermission("teamRewards:query")
    public ResponseDTO<PageResult<TeamRewardsVO>> queryPage(@RequestBody @Valid TeamRewardsQueryForm queryForm) {
        return ResponseDTO.ok(teamRewardsService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/teamRewards/add")
    @SaCheckPermission("teamRewards:add")
    public ResponseDTO<String> add(@RequestBody @Valid TeamRewardsAddForm addForm) {
        return teamRewardsService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/teamRewards/update")
    @SaCheckPermission("teamRewards:update")
    public ResponseDTO<String> update(@RequestBody @Valid TeamRewardsUpdateForm updateForm) {
        return teamRewardsService.update(updateForm);
    }

}
