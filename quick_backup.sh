#!/bin/bash

# 快速备份脚本 - 简化版
# 使用方法: ./quick_backup.sh [数据库名]

SERVER_IP="*************"
DATABASE_NAME=${1:-"tgw_pp"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_${TIMESTAMP}.sql"
LOCAL_BACKUP_DIR="DataBackup"

echo "正在备份数据库: $DATABASE_NAME"
echo "备份文件名: $BACKUP_FILENAME"
echo "本地保存目录: $LOCAL_BACKUP_DIR"

# 创建本地备份目录
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "创建备份目录: $LOCAL_BACKUP_DIR"
    mkdir -p "$LOCAL_BACKUP_DIR"
fi

# 1. 远程备份
echo "步骤1: 在服务器上创建备份..."
ssh root@$SERVER_IP "mysqldump -u root -p $DATABASE_NAME > /root/$BACKUP_FILENAME"

# 2. 下载到本地
echo "步骤2: 下载备份文件到 $LOCAL_BACKUP_DIR 目录..."
scp root@$SERVER_IP:/root/$BACKUP_FILENAME "$LOCAL_BACKUP_DIR/"

# 3. 显示结果
LOCAL_FILE_PATH="$LOCAL_BACKUP_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    echo "备份成功！文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"
    echo "本地文件: $(pwd)/$LOCAL_FILE_PATH"

    # 询问是否删除远程文件
    read -p "删除服务器上的备份文件吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ssh root@$SERVER_IP "rm -f /root/$BACKUP_FILENAME"
        echo "远程文件已删除"
    fi

    # 显示 DataBackup 目录中的所有备份文件
    echo ""
    echo "=== DataBackup 目录中的备份文件 ==="
    ls -lt "$LOCAL_BACKUP_DIR"/*.sql 2>/dev/null | head -5
else
    echo "备份失败！"
fi
