-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: tgw_pp
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_activities`
--

DROP TABLE IF EXISTS `t_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_activities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '活动名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '活动描述',
  `type` enum('NOVICE','LOW_PRICE','HIGH_PRICE') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '活动类型',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '活动状态 0 pending, 1 active, 2 finish, 3 cancel',
  `return_ratio` int unsigned DEFAULT '0' COMMENT '返还比例%',
  `config_info` json NOT NULL COMMENT '活动配置参数',
  `force_loss_flag` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '必不中开关',
  `participant_limit` int unsigned DEFAULT '0' COMMENT '参与人数限制',
  `current_participants` int unsigned NOT NULL DEFAULT '0' COMMENT '当前参与人数',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `remaining_time` int unsigned DEFAULT '0' COMMENT '开奖倒计(秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_flag` tinyint unsigned DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_type_status` (`type`,`status`) USING BTREE,
  KEY `idx_time_range` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='抽奖活动表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_activities`
--

LOCK TABLES `t_activities` WRITE;
/*!40000 ALTER TABLE `t_activities` DISABLE KEYS */;
INSERT INTO `t_activities` VALUES (3,'3人团','3人团','LOW_PRICE',1,3,'{\"sec\": 15}',1,3,0,'2025-06-28 23:43:40','2025-07-31 23:43:41',5,'2025-06-28 23:44:39',0),(4,'5人团','5人团','LOW_PRICE',1,3,'{\"sec\": 15}',1,5,0,'2025-06-29 13:01:33','2025-07-31 13:01:36',5,'2025-06-29 13:01:46',0),(5,'10人团','10人团','HIGH_PRICE',1,3,'{\"sec\": 15}',1,10,0,'2025-06-29 13:02:28','2025-07-31 13:02:29',5,'2025-06-29 13:02:33',0),(6,'3人团','3人团','LOW_PRICE',1,3,'{\"sec\": 15}',1,3,0,'2025-06-29 13:03:15','2025-07-31 13:03:17',10,'2025-06-29 13:03:22',1),(7,'5人团','5人团','LOW_PRICE',1,3,'{\"sec\": 15}',1,5,0,'2025-06-29 13:05:22','2025-07-31 13:05:23',10,'2025-06-29 13:05:27',1),(8,'7人团','7人团','LOW_PRICE',1,3,'{\"sec\": 15}',1,7,0,'2025-06-29 15:53:10','2025-07-31 13:05:49',10,'2025-06-29 13:05:53',1);
/*!40000 ALTER TABLE `t_activities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_activities_count`
--

DROP TABLE IF EXISTS `t_activities_count`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_activities_count` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `novice` decimal(13,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '新手区计数',
  `novice_count` int unsigned NOT NULL DEFAULT '0',
  `low_price` decimal(13,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '低价区计数',
  `low_price_count` int unsigned NOT NULL DEFAULT '0',
  `high_price` decimal(13,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '高价区计数',
  `high_price_count` int unsigned NOT NULL DEFAULT '0',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  CONSTRAINT `fk_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='参与活动计数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_activities_count`
--

LOCK TABLES `t_activities_count` WRITE;
/*!40000 ALTER TABLE `t_activities_count` DISABLE KEYS */;
INSERT INTO `t_activities_count` VALUES (73,55992.00,8,73982.00,18,83988.00,12,'2025-07-22 23:34:15'),(79,0.00,0,198.00,2,0.00,0,'2025-07-09 21:12:05'),(81,0.00,0,594.00,6,0.00,0,'2025-07-11 20:11:12'),(83,0.00,0,6197.00,3,6999.00,1,'2025-07-21 22:39:06');
/*!40000 ALTER TABLE `t_activities_count` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_banners`
--

DROP TABLE IF EXISTS `t_banners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_banners` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Banner ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Banner标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '图片URL',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '跳转链接',
  `link_type` enum('product','activity','h5','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'none' COMMENT '链接类型',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序权重',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态1 开, 0 关',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_status_sort` (`status`,`sort_order`) USING BTREE,
  KEY `idx_time_range` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='横幅管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_banners`
--

LOCK TABLES `t_banners` WRITE;
/*!40000 ALTER TABLE `t_banners` DISABLE KEYS */;
INSERT INTO `t_banners` VALUES (1,'test','public/common/67c558bb7e884068a0682a630d5dafe1_20250723004853.jpg',NULL,'product',0,1,NULL,NULL,'2025-07-01 12:51:35'),(2,'test2','public/common/8c48b6063e804848a09bbb3f386324dc_20250723143143.jpg',NULL,'h5',0,1,NULL,NULL,'2025-07-23 14:31:53');
/*!40000 ALTER TABLE `t_banners` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_category`
--

DROP TABLE IF EXISTS `t_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_category` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT '分类id',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `category_type` smallint NOT NULL COMMENT '分类类型',
  `parent_id` int NOT NULL COMMENT '父级id',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `disabled_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分类表，主要用于商品分类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_category`
--

LOCK TABLES `t_category` WRITE;
/*!40000 ALTER TABLE `t_category` DISABLE KEYS */;
INSERT INTO `t_category` VALUES (1,'手机',1,0,0,0,0,NULL,'2022-10-10 22:27:24','2022-07-14 20:55:15'),(2,'数码产品',1,0,0,0,0,NULL,'2025-07-21 23:14:13','2022-07-14 20:55:48'),(3,'自定义1',2,0,0,0,0,NULL,'2022-09-14 22:01:06','2022-07-14 20:56:03'),(4,'自定义2',2,0,0,0,0,NULL,'2022-09-14 22:01:10','2022-07-14 20:56:09'),(351,'箱包',1,0,0,0,0,NULL,'2025-07-21 23:14:21','2022-09-14 21:39:06'),(352,'苹果',1,1,0,0,0,NULL,'2022-09-14 21:39:25','2022-09-14 21:39:25'),(353,'华为',1,1,0,0,0,NULL,'2022-09-14 21:39:32','2022-09-14 21:39:32'),(354,'IKBC',1,2,0,0,1,NULL,'2025-07-21 23:14:44','2022-09-14 21:39:38'),(355,'双飞燕',1,2,0,0,1,NULL,'2025-07-21 23:14:47','2022-09-14 21:39:47'),(356,'罗技',1,351,0,0,1,NULL,'2025-07-21 23:15:10','2022-09-14 21:39:57'),(357,'小米',1,1,0,0,0,NULL,'2022-10-10 22:27:39','2022-10-10 22:27:39'),(360,'iphone',1,352,0,0,0,NULL,'2023-12-04 21:26:55','2023-12-01 19:54:22'),(381,'服装',1,0,0,0,0,NULL,'2025-07-21 23:15:01','2025-07-08 11:02:58'),(382,'特价活动专区',1,0,0,0,1,NULL,'2025-07-21 23:13:57','2025-07-21 23:05:20'),(383,'国际品牌专区',1,0,0,0,1,NULL,'2025-07-21 23:13:55','2025-07-21 23:05:35'),(384,'饰品',1,0,0,0,0,NULL,'2025-07-21 23:15:38','2025-07-21 23:15:38'),(385,'摩托车',1,0,0,0,0,NULL,'2025-07-21 23:15:47','2025-07-21 23:15:47'),(386,'香水',1,0,0,0,0,NULL,'2025-07-21 23:15:54','2025-07-21 23:15:54'),(387,'手表',1,0,0,0,0,NULL,'2025-07-21 23:16:00','2025-07-21 23:16:00'),(388,'美妆',1,0,0,0,0,NULL,'2025-07-21 23:16:12','2025-07-21 23:16:12'),(389,'百货',1,0,0,0,0,NULL,'2025-07-21 23:16:20','2025-07-21 23:16:20');
/*!40000 ALTER TABLE `t_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_change_log`
--

DROP TABLE IF EXISTS `t_change_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_change_log` (
  `change_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '更新日志id',
  `update_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本',
  `type` int NOT NULL COMMENT '更新类型:[1:特大版本功能更新;2:功能更新;3:bug修复]',
  `publish_author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发布人',
  `public_date` date NOT NULL COMMENT '发布日期',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新内容',
  `link` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '跳转链接',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`change_log_id`) USING BTREE,
  UNIQUE KEY `version_unique` (`update_version`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统更新日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_change_log`
--

LOCK TABLES `t_change_log` WRITE;
/*!40000 ALTER TABLE `t_change_log` DISABLE KEYS */;
INSERT INTO `t_change_log` VALUES (2,'v1.1.0',2,'卓大','2020-05-09','SmartAdmin中后台系统 v1.1.0 版本（20200422）正式更新上线，更新内容如下：\n\n1.【新增】增加员工姓名查询\n\n2.【新增】增加文件预览组件\n\n3.【新增】新增四级菜单\n','http://smartadmin.1024lab.net/views/1.x/base/About.html','2022-10-04 21:33:50','2022-10-04 21:33:50'),(8,'v1.0.0',1,'卓大','2019-11-01','SmartAdmin中后台系统 v1.0.0 版本（20191101）正式更新上线，更新内容如下：\n\n1.【新增】人员管理\n\n2.【新增】系统设置\n\n3.【新增】心跳服务\n\n4.【新增】动态加载\n\n5.【新增】缓存策略\n\n6.【新增】定时任务','http://smartadmin.1024lab.net/views/1.x/base/About.html','2022-10-04 21:33:50','2022-10-04 21:33:50'),(9,'v1.2.0',2,'卓大','2020-05-23','SmartAdmin中后台系统 v1.2.0 版本（20200515）正式更新上线，更新内容如下：\n\n1.【新增】增加数据权限\n\n2.【新增】帮助文档',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(10,'v1.2.1',3,'卓大','2020-05-24','SmartAdmin中后台系统 v1.2.1 版本（20200524）正式更新上线，更新内容如下：\n\n1.【修复】四级菜单权限bug\n\n2.【修复】缓存keepalive的Bug\n\n',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(11,'v1.3.0',2,'卓大','2020-06-01','SmartAdmin中后台系统 v1.3.0 版本（20200601）正式更新上线，更新内容如下：\n\n1.【新增】工作台看板功能\n\n2.【新增】天气预报功能\n\n3.【新增】记录上次登录IP功能',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(12,'v1.4.0',2,'卓大','2020-06-06','SmartAdmin中后台系统 v1.4.0 版本（20200606）正式更新上线，更新内容如下：\n\n1.【新增】联系客服功能\n\n2.【新增】意见反馈功能',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(13,'v1.5.0',2,'卓大','2020-06-14','SmartAdmin中后台系统 v1.5.0 版本（20200614）正式更新上线，更新内容如下：\n\n1.【新增】OA系统\n\n2.【新增】通知公告',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(14,'v1.6.0',2,'卓大','2020-06-17','SmartAdmin中后台系统 v1.6.0 版本（20200617）正式更新上线，更新内容如下：\n\n1.【新增】代码生成\n\n2.【新增】通知公告',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(15,'v2.0.0',1,'卓大','2022-10-22','SmartAdmin中后台系统 v2.0.0 版本（20191101）正式更新上线，更新内容如下：\n\n1.【新增】人员管理\n\n2.【新增】系统设置\n\n3.【新增】心跳服务\n\n4.【新增】动态加载\n\n5.【新增】缓存策略\n\n6.【新增】定时任务','http://smartadmin.1024lab.net/views/1.x/base/About.html','2022-10-04 21:33:50','2022-10-04 21:33:50'),(16,'v1.7.0',2,'卓大','2022-10-22','SmartAdmin中后台系统 v1.7.0 版本（20200624）正式更新上线，更新内容如下：\n\n1.【新增】商品管理\n\n2.【新增】商品分类',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50'),(18,'v3.0.0',1,'卓大','2024-01-01','SmartAdmin中后台系统 v3.0.0 版本（20240101）正式更新上线，更新内容如下：\n\n\n1、【新增】权限从SpringSecurity 转成 Sa-Token\n\n2、【新增】增加接口 加密、解密功能\n\n3、【新增】增加网络安全相关功能：登录限制、密码复杂度、最大在线时长等\n\n4、【新增】ant desgin vue 为 4.x 最新版本\n\n5、【新增】升级 vite5\n\n6、【新增】swagger增加knife4j接口文档\n\n7、【优化】后端sa-common 改名为 sa-base\n\n8、【优化】优化官网文档说明\n',NULL,'2022-10-04 21:33:50','2022-10-04 21:33:50');
/*!40000 ALTER TABLE `t_change_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_code_generator_config`
--

DROP TABLE IF EXISTS `t_code_generator_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_code_generator_config` (
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表名',
  `basic` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '基础命名信息',
  `fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '字段列表',
  `insert_and_update` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '新建、修改',
  `delete_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '删除',
  `query_fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '查询',
  `table_fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '列表',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '详情',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`table_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成器的每个表的配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_code_generator_config`
--

LOCK TABLES `t_code_generator_config` WRITE;
/*!40000 ALTER TABLE `t_code_generator_config` DISABLE KEYS */;
INSERT INTO `t_code_generator_config` VALUES ('t_activities','{\"backendAuthor\":\"-\",\"backendDate\":1751110805000,\"copyright\":\"-\",\"description\":\"抽奖活动表\",\"frontAuthor\":\"-\",\"frontDate\":1751110805000,\"javaPackageName\":\"-\",\"moduleName\":\"Activities\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"活动ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"活动ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动名称\",\"columnName\":\"name\",\"fieldName\":\"name\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"活动名称\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动描述\",\"columnName\":\"description\",\"fieldName\":\"description\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"活动描述\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动类型\",\"columnName\":\"type\",\"fieldName\":\"type\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"活动类型\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动状态 0 \'pending\', 1 \'active\', 2 \'finished\', 3\'cancelled\'\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"活动状态 0 \'pending\', 1 \'active\', 2 \'finished\', 3\'cancelled\'\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动配置参数\",\"columnName\":\"config\",\"fieldName\":\"config\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"活动配置参数\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"必不中开关\",\"columnName\":\"force_loss_flag\",\"fieldName\":\"forceLossFlag\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"必不中开关\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"参与人数限制\",\"columnName\":\"participant_limit\",\"fieldName\":\"participantLimit\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"参与人数限制\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"当前参与人数\",\"columnName\":\"current_participants\",\"fieldName\":\"currentParticipants\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"当前参与人数\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动开始时间\",\"columnName\":\"start_time\",\"fieldName\":\"startTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"活动开始时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动结束时间\",\"columnName\":\"end_time\",\"fieldName\":\"endTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"活动结束时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"开奖时间\",\"columnName\":\"draw_time\",\"fieldName\":\"drawTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"开奖时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"删除标记\",\"columnName\":\"deleted_flag\",\"fieldName\":\"deletedFlag\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"删除标记\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"name\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"description\",\"frontComponent\":\"Textarea\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"type\",\"frontComponent\":\"SmartEnumSelect\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"config\",\"frontComponent\":\"Textarea\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"force_loss_flag\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"participant_limit\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"current_participants\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"start_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"end_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"draw_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"deleted_flag\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":false,\"isSupportDelete\":true}','[{\"columnNameList\":[\"id\"],\"fieldName\":\"id\",\"label\":\"活动ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"start_time\"],\"fieldName\":\"startTime\",\"label\":\"活动开始时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"},{\"columnNameList\":[\"type\"],\"fieldName\":\"type\",\"label\":\"活动类型\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"活动ID\",\"showFlag\":true},{\"columnName\":\"name\",\"ellipsisFlag\":true,\"fieldName\":\"name\",\"label\":\"活动名称\",\"showFlag\":true},{\"columnName\":\"description\",\"ellipsisFlag\":true,\"fieldName\":\"description\",\"label\":\"活动描述\",\"showFlag\":true},{\"columnName\":\"type\",\"ellipsisFlag\":true,\"fieldName\":\"type\",\"label\":\"活动类型\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"活动状态 0 \'pending\', 1 \'active\', 2 \'finished\', 3\'cancelled\'\",\"showFlag\":true},{\"columnName\":\"config\",\"ellipsisFlag\":true,\"fieldName\":\"config\",\"label\":\"活动配置参数\",\"showFlag\":true},{\"columnName\":\"force_loss_flag\",\"ellipsisFlag\":true,\"fieldName\":\"forceLossFlag\",\"label\":\"必不中开关\",\"showFlag\":true},{\"columnName\":\"participant_limit\",\"ellipsisFlag\":true,\"fieldName\":\"participantLimit\",\"label\":\"参与人数限制\",\"showFlag\":true},{\"columnName\":\"current_participants\",\"ellipsisFlag\":true,\"fieldName\":\"currentParticipants\",\"label\":\"当前参与人数\",\"showFlag\":true},{\"columnName\":\"start_time\",\"ellipsisFlag\":true,\"fieldName\":\"startTime\",\"label\":\"活动开始时间\",\"showFlag\":true},{\"columnName\":\"end_time\",\"ellipsisFlag\":true,\"fieldName\":\"endTime\",\"label\":\"活动结束时间\",\"showFlag\":true},{\"columnName\":\"draw_time\",\"ellipsisFlag\":true,\"fieldName\":\"drawTime\",\"label\":\"开奖时间\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true},{\"columnName\":\"deleted_flag\",\"ellipsisFlag\":true,\"fieldName\":\"deletedFlag\",\"label\":\"删除标记\",\"showFlag\":false}]',NULL,'2025-06-28 19:43:39','2025-06-28 19:43:39'),('t_banners','{\"backendAuthor\":\"-\",\"backendDate\":1751343287000,\"copyright\":\"-\",\"description\":\"横幅管理\",\"frontAuthor\":\"-\",\"frontDate\":1751343287000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.oa.banners\",\"moduleName\":\"Banners\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"Banner ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"Banner ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"Banner标题\",\"columnName\":\"title\",\"fieldName\":\"title\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"Banner标题\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"图片URL\",\"columnName\":\"image_url\",\"fieldName\":\"imageUrl\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"图片URL\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"跳转链接\",\"columnName\":\"link_url\",\"fieldName\":\"linkUrl\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"跳转链接\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"链接类型\",\"columnName\":\"link_type\",\"fieldName\":\"linkType\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"链接类型\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"排序权重\",\"columnName\":\"sort_order\",\"fieldName\":\"sortOrder\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"排序权重\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"状态1 开, 0 关\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"状态1 开, 0 关\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"开始时间\",\"columnName\":\"start_time\",\"fieldName\":\"startTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"开始时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"结束时间\",\"columnName\":\"end_time\",\"fieldName\":\"endTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"结束时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"title\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"image_url\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"link_url\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"link_type\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"sort_order\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"start_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"end_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"\'100%\'\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":true}','[{\"columnNameList\":[\"title\"],\"fieldName\":\"title\",\"label\":\"Banner标题\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"start_time\"],\"fieldName\":\"startTime\",\"label\":\"开始时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"Banner ID\",\"showFlag\":true},{\"columnName\":\"title\",\"ellipsisFlag\":true,\"fieldName\":\"title\",\"label\":\"Banner标题\",\"showFlag\":true},{\"columnName\":\"image_url\",\"ellipsisFlag\":true,\"fieldName\":\"imageUrl\",\"label\":\"图片URL\",\"showFlag\":true},{\"columnName\":\"link_url\",\"ellipsisFlag\":true,\"fieldName\":\"linkUrl\",\"label\":\"跳转链接\",\"showFlag\":true},{\"columnName\":\"link_type\",\"ellipsisFlag\":true,\"fieldName\":\"linkType\",\"label\":\"链接类型\",\"showFlag\":true},{\"columnName\":\"sort_order\",\"ellipsisFlag\":true,\"fieldName\":\"sortOrder\",\"label\":\"排序权重\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"状态1 开, 0 关\",\"showFlag\":true},{\"columnName\":\"start_time\",\"ellipsisFlag\":true,\"fieldName\":\"startTime\",\"label\":\"开始时间\",\"showFlag\":true},{\"columnName\":\"end_time\",\"ellipsisFlag\":true,\"fieldName\":\"endTime\",\"label\":\"结束时间\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":false}]',NULL,'2025-07-01 12:17:33','2025-07-01 12:17:33'),('t_goods_skus','{\"backendAuthor\":\"-\",\"backendDate\":1750918643000,\"copyright\":\"2025\",\"description\":\"商品规格表(SKU)\",\"frontAuthor\":\"-\",\"frontDate\":1750918643000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.goodsSkus\",\"moduleName\":\"GoodsSkus\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"SKU ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"SKU ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"商品ID\",\"columnName\":\"goods_id\",\"fieldName\":\"goodsId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"商品ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"SKU编码\",\"columnName\":\"sku_code\",\"fieldName\":\"skuCode\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"SKU编码\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"规格属性\",\"columnName\":\"attributes\",\"fieldName\":\"attributes\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"规格属性\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"商品标价\",\"columnName\":\"price\",\"fieldName\":\"price\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"商品标价\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"库存数量\",\"columnName\":\"stock\",\"fieldName\":\"stock\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"库存数量\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"销售数量\",\"columnName\":\"sales_count\",\"fieldName\":\"salesCount\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"销售数量\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"SKU状态\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"SKU状态\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"goods_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"sku_code\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"attributes\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"price\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"stock\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"sales_count\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":true}','[]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"SKU ID\",\"showFlag\":true},{\"columnName\":\"goods_id\",\"ellipsisFlag\":true,\"fieldName\":\"goodsId\",\"label\":\"商品ID\",\"showFlag\":true},{\"columnName\":\"sku_code\",\"ellipsisFlag\":true,\"fieldName\":\"skuCode\",\"label\":\"SKU编码\",\"showFlag\":true},{\"columnName\":\"attributes\",\"ellipsisFlag\":true,\"fieldName\":\"attributes\",\"label\":\"规格属性\",\"showFlag\":true},{\"columnName\":\"price\",\"ellipsisFlag\":true,\"fieldName\":\"price\",\"label\":\"商品标价\",\"showFlag\":true},{\"columnName\":\"stock\",\"ellipsisFlag\":true,\"fieldName\":\"stock\",\"label\":\"库存数量\",\"showFlag\":true},{\"columnName\":\"sales_count\",\"ellipsisFlag\":true,\"fieldName\":\"salesCount\",\"label\":\"销售数量\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"SKU状态\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-06-26 14:20:29','2025-06-26 14:20:29'),('t_invitation_records','{\"backendAuthor\":\"-\",\"backendDate\":1751291016000,\"copyright\":\"-\",\"description\":\"邀请记录表\",\"frontAuthor\":\"-\",\"frontDate\":1751291016000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.invitationRecords\",\"moduleName\":\"InvitationRecords\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"记录ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"记录ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"邀请人ID\",\"columnName\":\"inviter_id\",\"fieldName\":\"inviterId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"邀请人ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"被邀请人ID\",\"columnName\":\"invitee_id\",\"fieldName\":\"inviteeId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"被邀请人ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"已获得佣金\",\"columnName\":\"commission_earned\",\"fieldName\":\"commissionEarned\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"已获得佣金\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"邀请状态\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"邀请状态\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"首次下单时间\",\"columnName\":\"first_order_time\",\"fieldName\":\"firstOrderTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"首次下单时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"inviter_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"invitee_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"commission_earned\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"first_order_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":false,\"pageType\":\"modal\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":false,\"isSupportDelete\":false}','[{\"columnNameList\":[\"inviter_id\"],\"fieldName\":\"inviterId\",\"label\":\"邀请人ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"invitee_id\"],\"fieldName\":\"inviteeId\",\"label\":\"被邀请人ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"create_time\"],\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"记录ID\",\"showFlag\":false},{\"columnName\":\"inviter_id\",\"ellipsisFlag\":true,\"fieldName\":\"inviterId\",\"label\":\"邀请人ID\",\"showFlag\":true},{\"columnName\":\"invitee_id\",\"ellipsisFlag\":true,\"fieldName\":\"inviteeId\",\"label\":\"被邀请人ID\",\"showFlag\":true},{\"columnName\":\"commission_earned\",\"ellipsisFlag\":true,\"fieldName\":\"commissionEarned\",\"label\":\"已获得佣金\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"邀请状态\",\"showFlag\":true},{\"columnName\":\"first_order_time\",\"ellipsisFlag\":true,\"fieldName\":\"firstOrderTime\",\"label\":\"首次下单时间\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-06-30 21:46:50','2025-06-30 21:46:50'),('t_orders','{\"backendAuthor\":\"-\",\"backendDate\":1751184221000,\"copyright\":\"-\",\"description\":\"订单表\",\"frontAuthor\":\"-\",\"frontDate\":1751184221000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.orders\",\"moduleName\":\"Orders\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"订单ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"订单ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"订单号\",\"columnName\":\"order_sn\",\"fieldName\":\"orderSn\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"订单号\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"活动ID\",\"columnName\":\"activity_id\",\"fieldName\":\"activityId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"活动ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"订单状态\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"订单状态\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"开奖结果\",\"columnName\":\"draw_result\",\"fieldName\":\"drawResult\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"开奖结果\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"中签后选择\",\"columnName\":\"win_option\",\"fieldName\":\"winOption\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"中签后选择\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户实付金额\",\"columnName\":\"amount_paid\",\"fieldName\":\"amountPaid\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"用户实付金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"使用体验金\",\"columnName\":\"experience_paid\",\"fieldName\":\"experiencePaid\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"使用体验金\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"补贴金额\",\"columnName\":\"subsidy_paid\",\"fieldName\":\"subsidyPaid\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"补贴金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"使用积分\",\"columnName\":\"points_paid\",\"fieldName\":\"pointsPaid\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"使用积分\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"收货地址ID\",\"columnName\":\"shipping_address_id\",\"fieldName\":\"shippingAddressId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"收货地址ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"支付方式\",\"columnName\":\"payment_method\",\"fieldName\":\"paymentMethod\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"支付方式\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"支付时间\",\"columnName\":\"payment_time\",\"fieldName\":\"paymentTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"支付时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"开奖时间\",\"columnName\":\"draw_time\",\"fieldName\":\"drawTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"开奖时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"完成时间\",\"columnName\":\"complete_time\",\"fieldName\":\"completeTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"完成时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"删除标记\",\"columnName\":\"deleted_flag\",\"fieldName\":\"deletedFlag\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"删除标记\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"更新时间\",\"columnName\":\"update_time\",\"fieldName\":\"updateTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"更新时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"order_sn\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"activity_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"draw_result\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"win_option\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"amount_paid\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"experience_paid\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"subsidy_paid\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"points_paid\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"shipping_address_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"payment_method\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"payment_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"draw_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"complete_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"deleted_flag\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"update_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":false,\"isSupportDelete\":true}','[{\"columnNameList\":[\"order_sn\"],\"fieldName\":\"orderSn\",\"label\":\"订单号\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"create_time\"],\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"},{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userId\",\"label\":\"用户ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"订单ID\",\"showFlag\":true},{\"columnName\":\"order_sn\",\"ellipsisFlag\":true,\"fieldName\":\"orderSn\",\"label\":\"订单号\",\"showFlag\":true},{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userId\",\"label\":\"用户ID\",\"showFlag\":true},{\"columnName\":\"activity_id\",\"ellipsisFlag\":true,\"fieldName\":\"activityId\",\"label\":\"活动ID\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"订单状态\",\"showFlag\":true},{\"columnName\":\"draw_result\",\"ellipsisFlag\":true,\"fieldName\":\"drawResult\",\"label\":\"开奖结果\",\"showFlag\":true},{\"columnName\":\"win_option\",\"ellipsisFlag\":true,\"fieldName\":\"winOption\",\"label\":\"中签后选择\",\"showFlag\":true},{\"columnName\":\"amount_paid\",\"ellipsisFlag\":true,\"fieldName\":\"amountPaid\",\"label\":\"用户实付金额\",\"showFlag\":true},{\"columnName\":\"experience_paid\",\"ellipsisFlag\":true,\"fieldName\":\"experiencePaid\",\"label\":\"使用体验金\",\"showFlag\":true},{\"columnName\":\"subsidy_paid\",\"ellipsisFlag\":true,\"fieldName\":\"subsidyPaid\",\"label\":\"补贴金额\",\"showFlag\":true},{\"columnName\":\"points_paid\",\"ellipsisFlag\":true,\"fieldName\":\"pointsPaid\",\"label\":\"使用积分\",\"showFlag\":true},{\"columnName\":\"shipping_address_id\",\"ellipsisFlag\":true,\"fieldName\":\"shippingAddressId\",\"label\":\"收货地址ID\",\"showFlag\":true},{\"columnName\":\"payment_method\",\"ellipsisFlag\":true,\"fieldName\":\"paymentMethod\",\"label\":\"支付方式\",\"showFlag\":true},{\"columnName\":\"payment_time\",\"ellipsisFlag\":true,\"fieldName\":\"paymentTime\",\"label\":\"支付时间\",\"showFlag\":true},{\"columnName\":\"draw_time\",\"ellipsisFlag\":true,\"fieldName\":\"drawTime\",\"label\":\"开奖时间\",\"showFlag\":true},{\"columnName\":\"complete_time\",\"ellipsisFlag\":true,\"fieldName\":\"completeTime\",\"label\":\"完成时间\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true},{\"columnName\":\"deleted_flag\",\"ellipsisFlag\":true,\"fieldName\":\"deletedFlag\",\"label\":\"删除标记\",\"showFlag\":true},{\"columnName\":\"update_time\",\"ellipsisFlag\":true,\"fieldName\":\"updateTime\",\"label\":\"更新时间\",\"showFlag\":true}]',NULL,'2025-06-29 16:28:01','2025-06-29 16:28:01'),('t_popups','{\"backendAuthor\":\"-\",\"backendDate\":1751347179000,\"copyright\":\"-\",\"description\":\"弹窗管理\",\"frontAuthor\":\"-\",\"frontDate\":1751347179000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.oa.popups\",\"moduleName\":\"Popups\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"弹窗ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"弹窗ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"弹窗标题\",\"columnName\":\"title\",\"fieldName\":\"title\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"弹窗标题\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"图片URL\",\"columnName\":\"image_url\",\"fieldName\":\"imageUrl\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"图片URL\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"跳转链接\",\"columnName\":\"link_url\",\"fieldName\":\"linkUrl\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"跳转链接\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"触发规则\",\"columnName\":\"trigger_rules\",\"fieldName\":\"triggerRules\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"触发规则\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"状态 1 开, 0 关\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"状态 1 开, 0 关\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"开始时间\",\"columnName\":\"start_time\",\"fieldName\":\"startTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"开始时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"结束时间\",\"columnName\":\"end_time\",\"fieldName\":\"endTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"结束时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"title\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"image_url\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"link_url\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"trigger_rules\",\"frontComponent\":\"Textarea\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"status\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"start_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"end_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"\'100%\'\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":true}','[{\"columnNameList\":[\"title\"],\"fieldName\":\"title\",\"label\":\"弹窗标题\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"start_time\"],\"fieldName\":\"startTime\",\"label\":\"开始时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"弹窗ID\",\"showFlag\":true},{\"columnName\":\"title\",\"ellipsisFlag\":true,\"fieldName\":\"title\",\"label\":\"弹窗标题\",\"showFlag\":true},{\"columnName\":\"image_url\",\"ellipsisFlag\":true,\"fieldName\":\"imageUrl\",\"label\":\"图片URL\",\"showFlag\":true},{\"columnName\":\"link_url\",\"ellipsisFlag\":true,\"fieldName\":\"linkUrl\",\"label\":\"跳转链接\",\"showFlag\":true},{\"columnName\":\"trigger_rules\",\"ellipsisFlag\":true,\"fieldName\":\"triggerRules\",\"label\":\"触发规则\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"状态 1 开, 0 关\",\"showFlag\":true},{\"columnName\":\"start_time\",\"ellipsisFlag\":true,\"fieldName\":\"startTime\",\"label\":\"开始时间\",\"showFlag\":true},{\"columnName\":\"end_time\",\"ellipsisFlag\":true,\"fieldName\":\"endTime\",\"label\":\"结束时间\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-07-01 13:22:04','2025-07-01 13:22:04'),('t_team_rewards','{\"backendAuthor\":\"-\",\"backendDate\":1751329447000,\"copyright\":\"-\",\"description\":\"团队奖励表\",\"frontAuthor\":\"-\",\"frontDate\":1751329447000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.teamRewards\",\"moduleName\":\"TeamRewards\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"奖励ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"奖励ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"奖励日期\",\"columnName\":\"reward_date\",\"fieldName\":\"rewardDate\",\"javaType\":\"LocalDate\",\"jsType\":\"Date\",\"label\":\"奖励日期\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"达标人数\",\"columnName\":\"qualified_count\",\"fieldName\":\"qualifiedCount\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"达标人数\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"奖励金额\",\"columnName\":\"reward_amount\",\"fieldName\":\"rewardAmount\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"奖励金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"发放状态\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"发放状态\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"reward_date\",\"frontComponent\":\"Date\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"qualified_count\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"reward_amount\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"\'100%\'\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":false}','[{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userName\",\"label\":\"用户名\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"create_time\"],\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"奖励ID\",\"showFlag\":true},{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userName\",\"label\":\"用户ID\",\"showFlag\":true},{\"columnName\":\"reward_date\",\"ellipsisFlag\":true,\"fieldName\":\"rewardDate\",\"label\":\"奖励日期\",\"showFlag\":true},{\"columnName\":\"qualified_count\",\"ellipsisFlag\":true,\"fieldName\":\"qualifiedCount\",\"label\":\"达标人数\",\"showFlag\":true},{\"columnName\":\"reward_amount\",\"ellipsisFlag\":true,\"fieldName\":\"rewardAmount\",\"label\":\"奖励金额\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"发放状态\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-07-01 08:26:23','2025-07-01 08:26:23'),('t_user_address','{\"backendAuthor\":\"-\",\"backendDate\":1751094581000,\"copyright\":\"-\",\"description\":\"用户收货地址表\",\"frontAuthor\":\"-\",\"frontDate\":1751094581000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.userAddress\",\"moduleName\":\"UserAddress\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"地址ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"地址ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"收件人姓名\",\"columnName\":\"recipient_name\",\"fieldName\":\"recipientName\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"收件人姓名\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"收件人电话\",\"columnName\":\"phone_number\",\"fieldName\":\"phoneNumber\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"收件人电话\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"省份\",\"columnName\":\"province\",\"fieldName\":\"province\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"省份\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"城市\",\"columnName\":\"city\",\"fieldName\":\"city\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"城市\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"区县\",\"columnName\":\"district\",\"fieldName\":\"district\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"区县\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"详细地址\",\"columnName\":\"address_line\",\"fieldName\":\"addressLine\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"详细地址\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"是否默认地址\",\"columnName\":\"is_default\",\"fieldName\":\"isDefault\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"是否默认地址\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"created_at\",\"fieldName\":\"createdAt\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"recipient_name\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"phone_number\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"province\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"city\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"district\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"address_line\",\"frontComponent\":\"Input\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"is_default\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"created_at\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":true}','[{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userId\",\"label\":\"用户ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"recipient_name\",\"phone_number\"],\"fieldName\":\"blury\",\"label\":\"姓名或电话\",\"queryTypeEnum\":\"Like\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"地址ID\",\"showFlag\":true},{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userId\",\"label\":\"用户ID\",\"showFlag\":true},{\"columnName\":\"recipient_name\",\"ellipsisFlag\":true,\"fieldName\":\"recipientName\",\"label\":\"收件人姓名\",\"showFlag\":true},{\"columnName\":\"phone_number\",\"ellipsisFlag\":true,\"fieldName\":\"phoneNumber\",\"label\":\"收件人电话\",\"showFlag\":true},{\"columnName\":\"province\",\"ellipsisFlag\":true,\"fieldName\":\"province\",\"label\":\"省份\",\"showFlag\":true},{\"columnName\":\"city\",\"ellipsisFlag\":true,\"fieldName\":\"city\",\"label\":\"城市\",\"showFlag\":true},{\"columnName\":\"district\",\"ellipsisFlag\":true,\"fieldName\":\"district\",\"label\":\"区县\",\"showFlag\":true},{\"columnName\":\"address_line\",\"ellipsisFlag\":true,\"fieldName\":\"addressLine\",\"label\":\"详细地址\",\"showFlag\":true},{\"columnName\":\"is_default\",\"ellipsisFlag\":true,\"fieldName\":\"isDefault\",\"label\":\"是否默认地址\",\"showFlag\":true},{\"columnName\":\"created_at\",\"ellipsisFlag\":true,\"fieldName\":\"createdAt\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-06-28 15:14:51','2025-06-28 15:14:51'),('t_wallets','{\"backendAuthor\":\"-\",\"backendDate\":1750998137000,\"copyright\":\"-\",\"description\":\"用户钱包表\",\"frontAuthor\":\"-\",\"frontDate\":1750998137000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.wallets\",\"moduleName\":\"Wallets\"}','[{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"账户余额(可提现)\",\"columnName\":\"balance\",\"fieldName\":\"balance\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"账户余额(可提现)\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"体验金(不可提现)\",\"columnName\":\"experience_balance\",\"fieldName\":\"experienceBalance\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"体验金(不可提现)\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户积分\",\"columnName\":\"points\",\"fieldName\":\"points\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户积分\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"累计充值金额\",\"columnName\":\"total_recharge\",\"fieldName\":\"totalRecharge\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"累计充值金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"累计提现金额\",\"columnName\":\"total_withdraw\",\"fieldName\":\"totalWithdraw\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"累计提现金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"created_time\",\"fieldName\":\"createdTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"更新时间\",\"columnName\":\"updated_time\",\"fieldName\":\"updatedTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"更新时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"balance\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"experience_balance\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"points\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"total_recharge\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"total_withdraw\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"created_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"updated_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":false,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":false}','[{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userId\",\"label\":\"用户ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"created_time\"],\"fieldName\":\"createdTime\",\"label\":\"创建时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userId\",\"label\":\"用户ID\",\"showFlag\":true},{\"columnName\":\"balance\",\"ellipsisFlag\":true,\"fieldName\":\"balance\",\"label\":\"账户余额(可提现)\",\"showFlag\":true},{\"columnName\":\"experience_balance\",\"ellipsisFlag\":true,\"fieldName\":\"experienceBalance\",\"label\":\"体验金(不可提现)\",\"showFlag\":true},{\"columnName\":\"points\",\"ellipsisFlag\":true,\"fieldName\":\"points\",\"label\":\"用户积分\",\"showFlag\":true},{\"columnName\":\"total_recharge\",\"ellipsisFlag\":true,\"fieldName\":\"totalRecharge\",\"label\":\"累计充值金额\",\"showFlag\":true},{\"columnName\":\"total_withdraw\",\"ellipsisFlag\":true,\"fieldName\":\"totalWithdraw\",\"label\":\"累计提现金额\",\"showFlag\":true},{\"columnName\":\"created_time\",\"ellipsisFlag\":true,\"fieldName\":\"createdTime\",\"label\":\"创建时间\",\"showFlag\":true},{\"columnName\":\"updated_time\",\"ellipsisFlag\":true,\"fieldName\":\"updatedTime\",\"label\":\"更新时间\",\"showFlag\":true}]',NULL,'2025-06-27 12:23:35','2025-06-27 12:23:35'),('t_wallet_transactions','{\"backendAuthor\":\"-\",\"backendDate\":1751083305000,\"copyright\":\"-\",\"description\":\"钱包流水表\",\"frontAuthor\":\"-\",\"frontDate\":1751083305000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.walletTransactions\",\"moduleName\":\"WalletTransactions\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"流水ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"流水ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"变动金额(正数收入，负数支出)\",\"columnName\":\"amount\",\"fieldName\":\"amount\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"变动金额(正数收入，负数支出)\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"变动后可用余额\",\"columnName\":\"balance_after\",\"fieldName\":\"balanceAfter\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"变动后可用余额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"变动后不可用余额\",\"columnName\":\"experience_balance_after\",\"fieldName\":\"experienceBalanceAfter\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"变动后不可用余额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"变动后积分\",\"columnName\":\"points_after\",\"fieldName\":\"pointsAfter\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"变动后积分\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"操作类型: 0 可用余额,1 不可用余额, 2 积分\",\"columnName\":\"mode\",\"fieldName\":\"mode\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"操作类型: 0 可用余额,1 不可用余额, 2 积分\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"交易类型\",\"columnName\":\"type\",\"fieldName\":\"type\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"交易类型\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"关联业务ID\",\"columnName\":\"related_id\",\"fieldName\":\"relatedId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"关联业务ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"交易描述\",\"columnName\":\"description\",\"fieldName\":\"description\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"交易描述\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"创建时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"创建时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"amount\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"balance_after\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"experience_balance_after\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"points_after\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"mode\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"type\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"related_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"description\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"100%\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":false}','[{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userId\",\"label\":\"用户ID\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"流水ID\",\"showFlag\":true},{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userId\",\"label\":\"用户ID\",\"showFlag\":true},{\"columnName\":\"amount\",\"ellipsisFlag\":true,\"fieldName\":\"amount\",\"label\":\"变动金额(正数收入，负数支出)\",\"showFlag\":true},{\"columnName\":\"balance_after\",\"ellipsisFlag\":true,\"fieldName\":\"balanceAfter\",\"label\":\"变动后可用余额\",\"showFlag\":true},{\"columnName\":\"experience_balance_after\",\"ellipsisFlag\":true,\"fieldName\":\"experienceBalanceAfter\",\"label\":\"变动后不可用余额\",\"showFlag\":true},{\"columnName\":\"points_after\",\"ellipsisFlag\":true,\"fieldName\":\"pointsAfter\",\"label\":\"变动后积分\",\"showFlag\":true},{\"columnName\":\"mode\",\"ellipsisFlag\":true,\"fieldName\":\"mode\",\"label\":\"操作类型: 0 可用余额,1 不可用余额, 2 积分\",\"showFlag\":true},{\"columnName\":\"type\",\"ellipsisFlag\":true,\"fieldName\":\"type\",\"label\":\"交易类型\",\"showFlag\":true},{\"columnName\":\"related_id\",\"ellipsisFlag\":true,\"fieldName\":\"relatedId\",\"label\":\"关联业务ID\",\"showFlag\":true},{\"columnName\":\"description\",\"ellipsisFlag\":true,\"fieldName\":\"description\",\"label\":\"交易描述\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"创建时间\",\"showFlag\":true}]',NULL,'2025-06-28 12:04:28','2025-06-28 12:04:28'),('t_withdrawals','{\"backendAuthor\":\"-\",\"backendDate\":1751329277000,\"copyright\":\"-\",\"description\":\"提现申请表\",\"frontAuthor\":\"-\",\"frontDate\":1751329277000,\"javaPackageName\":\"net.lab1024.sa.admin.module.business.withdrawals\",\"moduleName\":\"Withdrawals\"}','[{\"autoIncreaseFlag\":true,\"columnComment\":\"申请ID\",\"columnName\":\"id\",\"fieldName\":\"id\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"申请ID\",\"primaryKeyFlag\":true},{\"autoIncreaseFlag\":false,\"columnComment\":\"用户ID\",\"columnName\":\"user_id\",\"fieldName\":\"userId\",\"javaType\":\"Long\",\"jsType\":\"Number\",\"label\":\"用户ID\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"申请金额\",\"columnName\":\"amount\",\"fieldName\":\"amount\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"申请金额\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"手续费\",\"columnName\":\"fee\",\"fieldName\":\"fee\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"手续费\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"实际到账\",\"columnName\":\"actual_amount\",\"fieldName\":\"actualAmount\",\"javaType\":\"BigDecimal\",\"jsType\":\"Number\",\"label\":\"实际到账\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"申请状态\",\"columnName\":\"status\",\"fieldName\":\"status\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"申请状态\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"银行名称\",\"columnName\":\"bank_name\",\"fieldName\":\"bankName\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"银行名称\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"银行账号\",\"columnName\":\"bank_account\",\"fieldName\":\"bankAccount\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"银行账号\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"账户姓名\",\"columnName\":\"account_holder\",\"fieldName\":\"accountHolder\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"账户姓名\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"驳回原因\",\"columnName\":\"rejection_reason\",\"fieldName\":\"rejectionReason\",\"javaType\":\"String\",\"jsType\":\"String\",\"label\":\"驳回原因\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"处理人\",\"columnName\":\"processed_by\",\"fieldName\":\"processedBy\",\"javaType\":\"Integer\",\"jsType\":\"Number\",\"label\":\"处理人\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"申请时间\",\"columnName\":\"create_time\",\"fieldName\":\"createTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"申请时间\",\"primaryKeyFlag\":false},{\"autoIncreaseFlag\":false,\"columnComment\":\"处理时间\",\"columnName\":\"processed_time\",\"fieldName\":\"processedTime\",\"javaType\":\"LocalDateTime\",\"jsType\":\"Date\",\"label\":\"处理时间\",\"primaryKeyFlag\":false}]','{\"countPerLine\":1,\"fieldList\":[{\"columnName\":\"id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"user_id\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"amount\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"fee\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"actual_amount\",\"frontComponent\":\"InputNumber\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"status\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"bank_name\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"bank_account\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"account_holder\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"rejection_reason\",\"frontComponent\":\"Input\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"processed_by\",\"frontComponent\":\"InputNumber\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false},{\"columnName\":\"create_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":true,\"requiredFlag\":true,\"updateFlag\":false},{\"columnName\":\"processed_time\",\"frontComponent\":\"DateTime\",\"insertFlag\":false,\"requiredFlag\":false,\"updateFlag\":false}],\"isSupportInsertAndUpdate\":true,\"pageType\":\"modal\",\"width\":\"\'100%\'\"}','{\"deleteEnum\":\"SingleAndBatch\",\"isPhysicallyDeleted\":true,\"isSupportDelete\":false}','[{\"columnNameList\":[\"user_id\"],\"fieldName\":\"userName\",\"label\":\"用户名\",\"queryTypeEnum\":\"Equal\",\"width\":\"200px\"},{\"columnNameList\":[\"create_time\"],\"fieldName\":\"createTime\",\"label\":\"申请时间\",\"queryTypeEnum\":\"DateRange\",\"width\":\"200px\"}]','[{\"columnName\":\"id\",\"ellipsisFlag\":true,\"fieldName\":\"id\",\"label\":\"申请ID\",\"showFlag\":true},{\"columnName\":\"user_id\",\"ellipsisFlag\":true,\"fieldName\":\"userName\",\"label\":\"用户名\",\"showFlag\":true},{\"columnName\":\"amount\",\"ellipsisFlag\":true,\"fieldName\":\"amount\",\"label\":\"申请金额\",\"showFlag\":true},{\"columnName\":\"fee\",\"ellipsisFlag\":true,\"fieldName\":\"fee\",\"label\":\"手续费\",\"showFlag\":true},{\"columnName\":\"actual_amount\",\"ellipsisFlag\":true,\"fieldName\":\"actualAmount\",\"label\":\"实际到账\",\"showFlag\":true},{\"columnName\":\"status\",\"ellipsisFlag\":true,\"fieldName\":\"status\",\"label\":\"申请状态\",\"showFlag\":true},{\"columnName\":\"bank_name\",\"ellipsisFlag\":true,\"fieldName\":\"bankName\",\"label\":\"银行名称\",\"showFlag\":true},{\"columnName\":\"bank_account\",\"ellipsisFlag\":true,\"fieldName\":\"bankAccount\",\"label\":\"银行账号\",\"showFlag\":true},{\"columnName\":\"account_holder\",\"ellipsisFlag\":true,\"fieldName\":\"accountHolder\",\"label\":\"账户姓名\",\"showFlag\":true},{\"columnName\":\"rejection_reason\",\"ellipsisFlag\":true,\"fieldName\":\"rejectionReason\",\"label\":\"驳回原因\",\"showFlag\":true},{\"columnName\":\"processed_by\",\"ellipsisFlag\":true,\"fieldName\":\"processedBy\",\"label\":\"处理人\",\"showFlag\":true},{\"columnName\":\"create_time\",\"ellipsisFlag\":true,\"fieldName\":\"createTime\",\"label\":\"申请时间\",\"showFlag\":true},{\"columnName\":\"processed_time\",\"ellipsisFlag\":true,\"fieldName\":\"processedTime\",\"label\":\"处理时间\",\"showFlag\":true}]',NULL,'2025-07-01 08:24:04','2025-07-01 08:24:04');
/*!40000 ALTER TABLE `t_code_generator_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_config`
--

DROP TABLE IF EXISTS `t_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数名字',
  `config_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数key',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '上次修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_config`
--

LOCK TABLES `t_config` WRITE;
/*!40000 ALTER TABLE `t_config` DISABLE KEYS */;
INSERT INTO `t_config` VALUES (1,'万能密码','super_password','1024ok','执行示例任务2','2025-06-25 16:28:03','2021-12-16 23:32:46'),(2,'三级等保','level3_protect_config','{\n	\"fileDetectFlag\":true,\n	\"loginActiveTimeoutMinutes\":30,\n	\"loginFailLockMinutes\":30,\n	\"loginFailMaxTimes\":3,\n	\"maxUploadFileSizeMb\":30,\n	\"passwordComplexityEnabled\":true,\n	\"regularChangePasswordMonths\":3,\n	\"regularChangePasswordNotAllowRepeatTimes\":3,\n	\"twoFactorLoginEnabled\":false\n}','SmartJob Sample2 update','2025-06-25 16:28:03','2024-08-13 11:44:49'),(10,'最低提现额','lowest_withdraw','200000','最低提现200K','2025-07-07 21:31:27','2025-06-30 10:51:17'),(11,'赠送新手现金','free_novice','1000','','2025-07-08 20:45:22','2025-06-30 11:56:13'),(12,'新手团参加次数','novice_group_limit','3','','2025-06-30 12:01:57','2025-06-30 12:01:57'),(13,'消费奖励积分','pay_reward_points','1','1:1，消费1元获得1个积分','2025-07-08 15:37:43','2025-06-30 12:05:46'),(14,'低价区中奖几率%','low_price_win_ratio','30','','2025-06-30 13:11:12','2025-06-30 13:11:12'),(15,'高价区中奖几率%','high_price_win_ratio','10','','2025-06-30 13:11:39','2025-06-30 13:11:39'),(16,'首次消费上级奖励现金（固定数值）','first_reward','1000','','2025-07-08 20:15:38','2025-07-08 15:53:16'),(17,'下级消费奖励上级积分百份比','team_reward','5','','2025-07-08 20:15:53','2025-07-08 15:55:02'),(18,'新手不中返还现金','novice_refund_win','100','','2025-07-08 21:29:51','2025-07-08 21:29:51');
/*!40000 ALTER TABLE `t_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_data_tracer`
--

DROP TABLE IF EXISTS `t_data_tracer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_data_tracer` (
  `data_tracer_id` bigint NOT NULL AUTO_INCREMENT,
  `data_id` bigint NOT NULL COMMENT '各种单据的id',
  `type` int NOT NULL COMMENT '单据类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '操作内容',
  `diff_old` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '差异：旧的数据',
  `diff_new` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '差异：新的数据',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '额外信息',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_type` int NOT NULL COMMENT '用户类型：1 后管用户 ',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ip',
  `ip_region` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ip地区',
  `user_agent` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ua',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`data_tracer_id`) USING BTREE,
  KEY `order_id_order_type` (`data_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='各种单据操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_data_tracer`
--

LOCK TABLES `t_data_tracer` WRITE;
/*!40000 ALTER TABLE `t_data_tracer` DISABLE KEYS */;
INSERT INTO `t_data_tracer` VALUES (35,10,1,'新增',NULL,NULL,NULL,47,1,'善逸','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.61','2023-10-07 19:02:24','2023-10-07 19:02:24'),(36,11,1,'新增',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 19:55:53','2023-12-01 19:55:53'),(37,12,1,'新增',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 19:57:26','2023-12-01 19:57:26'),(38,11,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 19:58:09','2023-12-01 19:58:09'),(39,2,3,'修改企业信息','统一社会信用代码:\"1024lab\"<br/>详细地址:\"1024大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"卓大\"<br/>省份名称:\"河南省\"<br/>企业logo:\"public/common/fb827d63dda74a60ab8b4f70cc7c7d0a_20221022145641_jpg\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新实验室\"<br/>邮箱:\"<EMAIL>\"','营业执照:\"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg\"<br/>统一社会信用代码:\"1024lab1\"<br/>详细地址:\"1024大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"卓大1\"<br/>省份名称:\"河南省\"<br/>企业logo:\"\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新实验室1\"<br/>邮箱:\"<EMAIL>\"',NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 20:05:05','2023-12-01 20:05:05'),(40,2,3,'修改企业信息','营业执照:\"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg\"<br/>统一社会信用代码:\"1024lab1\"<br/>详细地址:\"1024大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"卓大1\"<br/>省份名称:\"河南省\"<br/>企业logo:\"\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新实验室1\"<br/>邮箱:\"<EMAIL>\"','营业执照:\"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg\"<br/>统一社会信用代码:\"1024lab\"<br/>详细地址:\"1024大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"卓大\"<br/>省份名称:\"河南省\"<br/>企业logo:\"\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新实验室\"<br/>邮箱:\"<EMAIL>\"',NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 20:05:54','2023-12-01 20:05:54'),(41,2,3,'更新银行:<br/>',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 20:09:17','2023-12-01 20:09:17'),(42,2,3,'更新发票：<br/>删除状态:由【false】变更为【】',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2023-12-01 20:09:20','2023-12-01 20:09:20'),(49,1,3,'修改企业信息','营业执照:\"public/common/852b7e19bef94af39c1a6156edf47cfb_20221022170332_jpg\"<br/>统一社会信用代码:\"1024lab_block\"<br/>详细地址:\"区块链大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"开云\"<br/>省份名称:\"河南省\"<br/>企业logo:\"public/common/f4a76fa720814949a610f05f6f9545bf_20221022170256_jpg\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新区块链实验室\"','营业执照:\"public/common/1d89055e5680426280446aff1e7e627c_20240306112451.jpeg\"<br/>统一社会信用代码:\"1024lab_block\"<br/>详细地址:\"区块链大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"开云\"<br/>省份名称:\"河南省\"<br/>企业logo:\"public/common/34f5ac0fc097402294aea75352c128f0_20240306112435.png\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新区块链实验室\"',NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2024-03-06 11:24:55','2024-03-06 11:24:55'),(99,12,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2024-09-03 21:06:32','2024-09-03 21:06:32'),(100,12,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 11:10:45','2025-06-26 11:10:45'),(101,10,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 11:10:56','2025-06-26 11:10:56'),(102,8,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 11:12:11','2025-06-26 11:12:11'),(103,8,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 11:12:15','2025-06-26 11:12:15'),(104,10,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 11:12:57','2025-06-26 11:12:57'),(105,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 13:42:21','2025-06-26 13:42:21'),(106,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 13:54:11','2025-06-26 13:54:11'),(107,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 13:59:59','2025-06-26 13:59:59'),(108,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 14:00:20','2025-06-26 14:00:20'),(109,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 15:02:18','2025-06-26 15:02:18'),(110,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 15:04:15','2025-06-26 15:04:15'),(111,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 15:05:09','2025-06-26 15:05:09'),(112,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 15:06:19','2025-06-26 15:06:19'),(113,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 21:47:53','2025-06-26 21:47:53'),(114,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 22:05:44','2025-06-26 22:05:44'),(115,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 22:14:29','2025-06-26 22:14:29'),(118,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 22:34:43','2025-06-26 22:34:43'),(121,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-26 22:58:39','2025-06-26 22:58:39'),(122,1,3,'删除',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 11:17:13','2025-06-28 11:17:13'),(123,2,3,'修改企业信息','营业执照:\"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg\"<br/>统一社会信用代码:\"1024lab\"<br/>详细地址:\"1024大楼\"<br/>区县名称:\"洛龙区\"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:\"洛阳市\"<br/>删除状态:false<br/>联系人:\"卓大\"<br/>省份名称:\"河南省\"<br/>企业logo:\"\"<br/>联系人电话:\"18637925892\"<br/>企业名称:\"1024创新实验室\"<br/>邮箱:\"<EMAIL>\"','营业执照:\"\"<br/>统一社会信用代码:\"88888888\"<br/>详细地址:\"加和大楼\"<br/>区县名称:\"\"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:\"九龙城区\"<br/>删除状态:false<br/>联系人:\"大大\"<br/>省份名称:\"香港\"<br/>企业logo:\"\"<br/>联系人电话:\"1*********8\"<br/>企业名称:\"西大实验室\"<br/>邮箱:\"<EMAIL>\"',NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-28 11:18:20','2025-06-28 11:18:20'),(124,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 14:07:06','2025-06-29 14:07:06'),(125,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 15:25:42','2025-06-29 15:25:42'),(126,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 15:26:31','2025-06-29 15:26:31'),(127,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 15:29:02','2025-06-29 15:29:02'),(128,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 15:34:43','2025-06-29 15:34:43'),(129,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-29 15:35:18','2025-06-29 15:35:18'),(130,12,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-30 11:23:01','2025-06-30 11:23:01'),(131,12,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-30 11:27:03','2025-06-30 11:27:03'),(132,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-30 11:28:48','2025-06-30 11:28:48'),(133,1,1,'',NULL,NULL,NULL,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-06-30 11:33:04','2025-06-30 11:33:04'),(134,34,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:11:06','2025-07-03 23:11:06'),(135,35,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:25:18','2025-07-03 23:25:18'),(136,36,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:27:37','2025-07-03 23:27:37'),(137,37,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:28:01','2025-07-03 23:28:01'),(138,38,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:29:18','2025-07-03 23:29:18'),(139,39,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:30:12','2025-07-03 23:30:12'),(140,40,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:31:13','2025-07-03 23:31:13'),(141,41,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:31:48','2025-07-03 23:31:48'),(142,42,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:32:41','2025-07-03 23:32:41'),(143,43,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:33:21','2025-07-03 23:33:21'),(144,44,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:34:10','2025-07-03 23:34:10'),(145,45,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:34:51','2025-07-03 23:34:51'),(146,46,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:35:31','2025-07-03 23:35:31'),(147,47,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:36:27','2025-07-03 23:36:27'),(148,48,1,'新增',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-07-03 23:37:16','2025-07-03 23:37:16'),(149,48,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 14:49:52','2025-07-04 14:49:52'),(150,7,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 14:51:52','2025-07-04 14:51:52'),(151,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 15:12:34','2025-07-04 15:12:34'),(152,1,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 15:12:55','2025-07-04 15:12:55'),(153,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 15:17:58','2025-07-04 15:17:58'),(154,7,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 15:19:28','2025-07-04 15:19:28'),(155,1,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 15:59:42','2025-07-04 15:59:42'),(156,7,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 16:03:00','2025-07-04 16:03:00'),(157,7,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-04 21:22:58','2025-07-04 21:22:58'),(158,8,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:10:17','2025-07-05 10:10:17'),(159,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:18:40','2025-07-05 10:18:40'),(160,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:24:12','2025-07-05 10:24:12'),(161,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:24:37','2025-07-05 10:24:37'),(162,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:27:40','2025-07-05 10:27:40'),(163,1,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:32:41','2025-07-05 10:32:41'),(164,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:34:06','2025-07-05 10:34:06'),(165,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:35:30','2025-07-05 10:35:30'),(166,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:36:53','2025-07-05 10:36:53'),(167,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:37:17','2025-07-05 10:37:17'),(168,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:37:34','2025-07-05 10:37:34'),(169,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:37:48','2025-07-05 10:37:48'),(170,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:52:48','2025-07-05 10:52:48'),(171,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 10:53:16','2025-07-05 10:53:16'),(172,1,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 11:02:00','2025-07-05 11:02:00'),(173,7,1,'',NULL,NULL,NULL,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 11:03:32','2025-07-05 11:03:32'),(174,34,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:24:37','2025-07-05 13:24:37'),(175,46,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:25:41','2025-07-05 13:25:41'),(176,47,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:25:45','2025-07-05 13:25:45'),(177,44,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:25:50','2025-07-05 13:25:50'),(178,35,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:25:55','2025-07-05 13:25:55'),(179,10,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:26:05','2025-07-05 13:26:05'),(180,40,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:26:11','2025-07-05 13:26:11'),(181,38,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:26:31','2025-07-05 13:26:31'),(182,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:29:55','2025-07-05 13:29:55'),(183,48,1,'',NULL,NULL,NULL,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 13:30:26','2025-07-05 13:30:26'),(184,1,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-05 18:41:33','2025-07-05 18:41:33'),(185,34,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-08 16:32:52','2025-07-08 16:32:52'),(186,34,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-08 16:40:49','2025-07-08 16:40:49'),(187,34,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-08 16:42:29','2025-07-08 16:42:29'),(188,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 16:16:34','2025-07-18 16:16:34'),(189,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 16:18:28','2025-07-18 16:18:28'),(190,7,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 16:18:41','2025-07-18 16:18:41'),(191,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 16:20:47','2025-07-18 16:20:47'),(192,8,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-18 16:21:55','2025-07-18 16:21:55'),(193,48,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:05:49','2025-07-22 22:05:49'),(194,8,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:06:19','2025-07-22 22:06:19'),(195,7,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:06:33','2025-07-22 22:06:33'),(196,1,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:06:49','2025-07-22 22:06:49'),(197,48,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:08:15','2025-07-22 22:08:15'),(198,7,1,'',NULL,NULL,NULL,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-22 22:08:57','2025-07-22 22:08:57'),(199,34,1,'',NULL,NULL,NULL,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-23 13:53:13','2025-07-23 13:53:13');
/*!40000 ALTER TABLE `t_data_tracer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_department`
--

DROP TABLE IF EXISTS `t_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_department` (
  `department_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门主键id',
  `department_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门名称',
  `manager_id` bigint DEFAULT NULL COMMENT '部门负责人id',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '部门的父级id',
  `sort` int NOT NULL COMMENT '部门排序',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`department_id`) USING BTREE,
  KEY `parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_department`
--

LOCK TABLES `t_department` WRITE;
/*!40000 ALTER TABLE `t_department` DISABLE KEYS */;
INSERT INTO `t_department` VALUES (1,'南团',1,0,1,'2022-10-19 20:17:09','2022-10-19 20:17:09'),(2,'开发部',44,1,1000,'2022-10-19 20:22:23','2022-10-19 20:22:23'),(3,'产品部',2,1,99,'2022-10-21 10:25:30','2022-10-21 10:25:30'),(4,'销售部',64,1,9,'2022-10-21 10:25:47','2022-10-21 10:25:47'),(5,'测试部',48,1,0,'2022-11-05 10:54:18','2022-11-05 10:54:18'),(7,'客服部',44,1,1111,'2024-07-02 19:38:15','2024-07-02 19:38:15'),(9,'客户',NULL,1,0,'2025-06-27 12:12:24','2025-06-27 12:12:24');
/*!40000 ALTER TABLE `t_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_dict`
--

DROP TABLE IF EXISTS `t_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_dict` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典id',
  `dict_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名字',
  `dict_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典编码',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典备注',
  `disabled_flag` tinyint NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE KEY `unique_code` (`dict_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_dict`
--

LOCK TABLES `t_dict` WRITE;
/*!40000 ALTER TABLE `t_dict` DISABLE KEYS */;
INSERT INTO `t_dict` VALUES (1,'商品地区','GOODS_PLACE','用于商品管理中的商品地区1',0,'2025-03-27 14:42:26','2025-03-31 11:23:03'),(4,'活动状态','ACTIVITIES_STATUS','',0,'2025-06-28 20:51:04','2025-06-28 20:51:04'),(5,'物流状态','LOGISTICS_STATUS','',0,'2025-06-29 16:35:43','2025-06-29 16:35:43'),(6,'支付方式','PAYMENT_METHOD','',0,'2025-06-29 19:05:01','2025-06-29 19:05:01'),(7,'商品类型','GOODS_TYPE','',0,'2025-07-05 10:02:45','2025-07-05 10:02:45'),(8,'货币类型','GOODS_CURRENCY','',0,'2025-07-05 23:54:37','2025-07-05 23:54:37');
/*!40000 ALTER TABLE `t_dict` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_dict_data`
--

DROP TABLE IF EXISTS `t_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_dict_data` (
  `dict_data_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典数据id',
  `dict_id` bigint NOT NULL COMMENT '字典id',
  `data_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项值',
  `data_label` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典项显示名称',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `sort_order` int NOT NULL COMMENT '排序（越大越靠前）',
  `disabled_flag` tinyint NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`dict_data_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_dict_data`
--

LOCK TABLES `t_dict_data` WRITE;
/*!40000 ALTER TABLE `t_dict_data` DISABLE KEYS */;
INSERT INTO `t_dict_data` VALUES (2,1,'LUO_YANG','洛阳','sad',2,0,'2025-03-27 15:52:39','2025-03-27 20:53:21'),(3,1,'ZHENG_ZHOU','郑州','',0,0,'2025-03-27 18:58:16','2025-03-27 20:53:32'),(7,1,'BEI_JING','北京','',0,0,'2025-03-27 20:53:45','2025-03-27 20:53:45'),(8,1,'SHANG_HAI','上海','',0,0,'2025-03-27 20:53:45','2025-03-27 20:53:45'),(9,4,'0','待定','',0,0,'2025-06-28 20:51:45','2025-06-28 20:59:50'),(10,4,'1','活跃','',0,0,'2025-06-28 20:52:29','2025-06-28 20:59:54'),(11,4,'2','完成','',0,0,'2025-06-28 20:52:56','2025-06-28 20:59:56'),(12,4,'3','取消','',0,0,'2025-06-28 20:53:45','2025-06-28 21:00:01'),(13,5,'pending','待定','',0,0,'2025-06-29 16:36:02','2025-06-29 16:36:02'),(14,5,'shipped','已发货','',0,0,'2025-06-29 16:36:12','2025-06-29 16:36:12'),(15,5,'in_transit','在途中','',0,0,'2025-06-29 16:37:04','2025-06-29 16:37:04'),(16,5,'delivered','已签收','',0,0,'2025-06-29 16:37:19','2025-06-29 16:37:19'),(17,5,'returned','已退回','',0,0,'2025-06-29 16:37:32','2025-06-29 16:37:32'),(18,6,'0','余额','',0,0,'2025-06-29 19:05:14','2025-06-29 19:05:14'),(19,6,'1','体验金','',0,0,'2025-06-29 19:05:24','2025-06-29 19:05:46'),(20,6,'2','积分','',0,0,'2025-06-29 19:05:35','2025-06-29 19:05:35'),(21,6,'3','线下汇款','',0,0,'2025-06-29 19:05:54','2025-06-29 19:06:20'),(22,6,'4','支付平台','',0,0,'2025-06-29 19:06:30','2025-06-29 19:06:30'),(23,7,'recommended','国际品牌专区','',0,0,'2025-07-05 10:03:14','2025-07-22 21:57:19'),(24,7,'special','特价活动专区','',0,0,'2025-07-05 10:03:27','2025-07-22 21:56:51'),(25,7,'hot','热门','',0,0,'2025-07-05 10:03:36','2025-07-05 10:03:36'),(26,8,'cash','现金','',0,0,'2025-07-05 23:55:02','2025-07-05 23:55:02'),(27,8,'points','积分','',0,0,'2025-07-05 23:55:14','2025-07-05 23:55:14'),(28,7,'newUser','新手奖励','',99,0,'2025-07-08 11:08:52','2025-07-08 16:01:52');
/*!40000 ALTER TABLE `t_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_employee`
--

DROP TABLE IF EXISTS `t_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_employee` (
  `employee_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `login_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '登录帐号',
  `login_pwd` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '登录密码',
  `actual_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '员工名称',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像地址',
  `gender` tinyint(1) NOT NULL DEFAULT '0' COMMENT '性别',
  `phone` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '手机号码',
  `department_id` bigint NOT NULL COMMENT '部门id',
  `position_id` bigint DEFAULT NULL COMMENT '职务ID',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `inviter_id` bigint unsigned DEFAULT NULL COMMENT '邀请人ID',
  `child_count` int unsigned DEFAULT '0' COMMENT '下级数',
  `novice_count` int DEFAULT '0' COMMENT '试新次数',
  `risk_level` tinyint unsigned DEFAULT '0' COMMENT '风控等级: 0 normal, 1 high, 2 watch',
  `disabled_flag` tinyint unsigned NOT NULL COMMENT '是否被禁用 0否1是',
  `deleted_flag` tinyint unsigned NOT NULL COMMENT '是否删除0否 1是',
  `administrator_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否为超级管理员: 0 不是，1是',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`employee_id`) USING BTREE,
  KEY `idx_inviter_id` (`inviter_id`) USING BTREE,
  KEY `idx_disabled_flag` (`disabled_flag`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=86 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_employee`
--

LOCK TABLES `t_employee` WRITE;
/*!40000 ALTER TABLE `t_employee` DISABLE KEYS */;
INSERT INTO `t_employee` VALUES (1,'admin','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','管理员','public/common/f89afb6083a84cb78b8a61494bffb1a7_20250625170451.png',0,'13500000000',1,3,'<EMAIL>',NULL,0,0,0,0,0,1,NULL,'2025-06-25 17:05:43','2022-10-04 21:33:50'),(2,'huke','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','胡克',NULL,0,'13123123121',1,4,NULL,NULL,0,0,0,0,0,0,NULL,'2024-09-03 21:36:09','2022-10-04 21:33:50'),(44,'zhuoda','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','卓大',NULL,1,'18637925892',1,6,NULL,NULL,0,0,0,0,0,0,NULL,'2024-09-03 21:36:10','2022-10-04 21:33:50'),(47,'shanyi','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','善逸','public/common/f823b00873684f0a9d31f0d62316cc8e_20240630015141.jpg',1,'17630506613',2,5,NULL,NULL,0,0,0,0,0,0,'这个是备注','2024-09-03 21:36:11','2022-10-04 21:33:50'),(48,'qinjiu','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','琴酒',NULL,2,'14112343212',9,6,NULL,NULL,0,0,0,0,0,0,NULL,'2025-06-28 11:16:11','2022-10-04 21:33:50'),(63,'kaiyun','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','开云',NULL,0,'13112312346',2,5,NULL,NULL,0,0,0,0,1,0,NULL,'2025-06-28 11:11:28','2022-10-04 21:33:50'),(64,'qingye','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','清野',NULL,1,'13123123111',2,4,NULL,NULL,0,0,0,0,1,0,NULL,'2025-06-28 11:11:28','2022-10-04 21:33:50'),(65,'feiye','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','飞叶',NULL,1,'13123123112',9,3,NULL,NULL,0,0,0,1,0,0,NULL,'2025-06-28 11:16:17','2022-10-04 21:33:50'),(66,'luoyi','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','罗伊',NULL,1,'13123123142',4,2,NULL,NULL,0,0,0,1,1,0,NULL,'2025-06-28 11:11:28','2022-10-04 21:33:50'),(67,'chuxiao','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','初晓',NULL,1,'13*********',9,2,NULL,NULL,0,0,0,1,0,0,NULL,'2025-06-28 11:16:06','2022-10-04 21:33:50'),(68,'xuanpeng','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','玄朋',NULL,1,'13123123124',1,3,NULL,NULL,0,0,0,0,1,0,NULL,'2025-06-28 11:11:10','2022-10-04 21:33:50'),(69,'peixian','$argon2id$v=19$m=16384,t=2,p=1$Lb+f4GG7ACZmpemDp2qlPg$6xvpC+o5RQYolwMWUg4M2zC5vcDaCxmywKQkcrrTZW4','玄朋',NULL,1,'18377482773',1,4,NULL,NULL,0,0,0,1,1,0,NULL,'2025-06-28 11:11:19','2022-10-04 21:33:50'),(73,'limbo','$argon2id$v=19$m=16384,t=2,p=1$wN7sWXncBQw1ZsWWJWVolg$8RCHOXYyl3kbkuRfFwmB4JLslxg/UzPTJJJq8fJgfIM','陈琳博',NULL,0,'18906662339',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-03 18:18:46','2024-07-17 10:36:16'),(74,'xzh','$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc','admin1',NULL,1,'13654567897',5,6,NULL,NULL,0,0,0,0,0,0,NULL,'2024-09-03 21:36:21','2024-08-09 09:49:56'),(77,'***********','$argon2id$v=19$m=16384,t=2,p=1$qYrxNQPRIMf3JFWbW4nTQg$dVskAoU1LgfSWmyXbroJYfzPYtYEw/Imes/lhszyVhY','***********',NULL,0,'***********',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-09 13:12:10','2025-07-09 13:12:10'),(78,'***********','$argon2id$v=19$m=16384,t=2,p=1$2aK5SRmuBfhw1NG/6zMENA$DMDLMvbmi7FKvyiP6Exqr0iH6gZzRD6Yw4sAU/nGluk','***********',NULL,0,'***********',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-09 19:46:01','2025-07-09 19:46:01'),(79,'123456','$argon2id$v=19$m=16384,t=2,p=1$mnt3EN3bEveyTZyjqUb5Bg$Pu5Unmi58/crfZtBVK72CvILABn3k4KWGxRUrw4lWAg','123456',NULL,0,'123456',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-09 21:08:45','2025-07-09 21:08:45'),(80,'1*********8','$argon2id$v=19$m=16384,t=2,p=1$D6EIJ/AnQGzCDMYlPq/w9w$aZHIi+6UT4jsWDLmbVLUAqWz3FYWjAxc9eY7lvg9unY','1*********8',NULL,0,'1*********8',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-09 21:27:42','2025-07-09 21:27:42'),(81,'*********','$argon2id$v=19$m=16384,t=2,p=1$PqpsRXHWZvJEPP2WAW3rrg$rLa/xCXv8HcLCW2QAST0A/JUV67lZT8ibnnZqpt9PB4','*********',NULL,0,'*********',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-11 19:52:16','2025-07-11 19:52:16'),(82,'*********','$argon2id$v=19$m=16384,t=2,p=1$nmcLTKMcQ8eTQ2iIwX/mLg$g3XDsAH9Q+W9zPHT9jrf2cl3iaYzcxhFbYwib8Y8lvw','*********',NULL,0,'*********',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-12 14:57:12','2025-07-12 14:57:12'),(83,'88888888','$argon2id$v=19$m=16384,t=2,p=1$u9q1LjslbNLQvsbvP2HGow$L4UpXRHHtPbpZkzU+k9nRh88TblKnyV+acNITev3uOQ','88888888',NULL,0,'88888888',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-14 14:22:50','2025-07-14 14:22:50'),(84,'18530957595','$argon2id$v=19$m=16384,t=2,p=1$DFfeIag8aDCx0TMJgx9KfQ$Hm248P+KPH/QgLpTLBQgXVBwPqV8VS7w2x8w90uhAnY','18530957595',NULL,0,'18530957595',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-17 09:56:36','2025-07-17 09:56:36'),(85,'189012341234','$argon2id$v=19$m=16384,t=2,p=1$2a8VRkBd5KAoF5s3mNHpAg$e1VLOg7mKybJSoe099SnTUNKJ2ROVXfJLVve+HeQCFg','189012341234',NULL,0,'189012341234',9,5,NULL,NULL,0,0,0,0,0,0,NULL,'2025-07-18 23:10:43','2025-07-18 23:10:43');
/*!40000 ALTER TABLE `t_employee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_feedback`
--

DROP TABLE IF EXISTS `t_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_feedback` (
  `feedback_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `feedback_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '反馈内容',
  `feedback_attachment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '反馈图片',
  `user_id` bigint NOT NULL COMMENT '创建人id',
  `user_type` int NOT NULL COMMENT '创建人用户类型',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`feedback_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='意见反馈';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_feedback`
--

LOCK TABLES `t_feedback` WRITE;
/*!40000 ALTER TABLE `t_feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_file`
--

DROP TABLE IF EXISTS `t_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_file` (
  `file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `folder_type` tinyint unsigned NOT NULL COMMENT '文件夹类型',
  `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名称',
  `file_size` int DEFAULT NULL COMMENT '文件大小',
  `file_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件key，用于文件下载',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件类型',
  `creator_id` bigint DEFAULT NULL COMMENT '创建人，即上传人',
  `creator_user_type` int DEFAULT NULL COMMENT '创建人用户类型',
  `creator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人姓名',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上次更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`file_id`) USING BTREE,
  UNIQUE KEY `uk_file_key` (`file_key`) USING BTREE,
  KEY `module_id_module_type` (`folder_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_file`
--

LOCK TABLES `t_file` WRITE;
/*!40000 ALTER TABLE `t_file` DISABLE KEYS */;
INSERT INTO `t_file` VALUES (108,1,'玉-头像.png',364599,'public/common/d2e8ecfdb5694022b4d83df94332a950_20250625170150.png','png',1,1,'管理员','2025-06-25 17:01:50','2025-06-25 17:01:50'),(109,1,'锦鲤.png',135699,'public/common/f89afb6083a84cb78b8a61494bffb1a7_20250625170451.png','png',1,1,'管理员','2025-06-25 17:04:51','2025-06-25 17:04:51'),(110,1,'玉-头像.png',364599,'public/common/cf2db4aa2c474b6f91d91a161cdb84b6_20250626133909.png','png',1,1,'管理员','2025-06-26 13:39:09','2025-06-26 13:39:09'),(111,1,'玉-头像.png',364599,'public/common/167b0fde55aa4733b6d22198dd720ad0_20250626134214.png','png',1,1,'管理员','2025-06-26 13:42:14','2025-06-26 13:42:14'),(112,1,'商务头像.jpg',57005,'public/common/5d5a914d105d413aaa36763cbb814979_20250626135325.jpg','jpg',1,1,'管理员','2025-06-26 13:53:25','2025-06-26 13:53:25'),(113,1,'锦鲤.png',135699,'public/common/78f3ec3d00de45cda76ec3aafd14be8e_20250626135546.png','png',1,1,'管理员','2025-06-26 13:55:46','2025-06-26 13:55:46'),(114,1,'锦鲤.png',135699,'public/common/faa9fdf7cd3648cf856fe45adf06a823_20250626140019.png','png',1,1,'管理员','2025-06-26 14:00:19','2025-06-26 14:00:19'),(115,1,'微信图片_20250417164332.png',560530,'public/common/02af279c42cf4aa8a995a440d4962010_20250626150216.png','png',1,1,'管理员','2025-06-26 15:02:16','2025-06-26 15:02:16'),(116,1,'微信图片_20250417164332.png',560530,'public/common/020899d80d3a47dea06887956e4ac941_20250626150502.png','png',1,1,'管理员','2025-06-26 15:05:02','2025-06-26 15:05:02'),(117,1,'锦鲤.png',135699,'public/common/b6322515ec7142ecb8f5fdb0bd102dc2_20250701124459.png','png',1,1,'管理员','2025-07-01 12:44:59','2025-07-01 12:44:59'),(118,1,'锦鲤.png',135699,'public/common/4e9e884ac8cf47688feba2901bd78a89_20250701125127.png','png',1,1,'管理员','2025-07-01 12:51:27','2025-07-01 12:51:27'),(119,1,'商务头像.jpg',57005,'public/common/e6180328d4244fdf98ad2978c389af66_20250701133750.jpg','jpg',1,1,'管理员','2025-07-01 13:37:50','2025-07-01 13:37:50'),(120,1,'商务头像.jpg',57005,'public/common/5305738fce6a4c2b8d1bf7545d04c8b8_20250701134231.jpg','jpg',1,1,'管理员','2025-07-01 13:42:31','2025-07-01 13:42:31'),(121,1,'微信图片_20250703230717.jpg',80329,'public/common/4e1b472cd94042b388c203b0ba9a6e80_20250703230903.jpg','jpg',1,1,'管理员','2025-07-03 23:09:03','2025-07-03 23:09:03'),(122,1,'微信图片_20250703230914.jpg',60641,'public/common/b24c7eb5760846429150540bf6fb28ff_20250703230947.jpg','jpg',1,1,'管理员','2025-07-03 23:09:47','2025-07-03 23:09:47'),(123,1,'微信图片_20250703230922.jpg',57668,'public/common/b5e7a209df1e498dbc748bead448969f_20250703230951.jpg','jpg',1,1,'管理员','2025-07-03 23:09:51','2025-07-03 23:09:51'),(124,1,'微信图片_20250703230926.jpg',59761,'public/common/a75b0509c37c4dc085dc1f19125c1f1a_20250703230954.jpg','jpg',1,1,'管理员','2025-07-03 23:09:54','2025-07-03 23:09:54'),(125,1,'微信图片_20250703230930.jpg',80228,'public/common/021d138f388049e6b76b0cbef7ada948_20250703230957.jpg','jpg',1,1,'管理员','2025-07-03 23:09:57','2025-07-03 23:09:57'),(126,1,'微信图片_20250703230918.jpg',105995,'public/common/b8b9512fb1144214a947e4602492d24e_20250703230959.jpg','jpg',1,1,'管理员','2025-07-03 23:09:59','2025-07-03 23:09:59'),(127,1,'微信图片_20250703232414.jpg',154178,'public/common/3e68715524e243adb8a24baa7ca3f4b4_20250703232423.jpg','jpg',1,1,'管理员','2025-07-03 23:24:23','2025-07-03 23:24:23'),(128,1,'微信图片_20250703232647.jpg',69117,'public/common/e057c6b18df04956b2596e978154a285_20250703232706.jpg','jpg',1,1,'管理员','2025-07-03 23:27:06','2025-07-03 23:27:06'),(129,1,'微信图片_20250703232659.jpg',53809,'public/common/374338135b7247c0a035b03acb8af1d1_20250703232742.jpg','jpg',1,1,'管理员','2025-07-03 23:27:42','2025-07-03 23:27:42'),(130,1,'微信图片_20250703232834.jpg',57822,'public/common/8e8d8b9fef974cd3b0216fa01a48015a_20250703232850.jpg','jpg',1,1,'管理员','2025-07-03 23:28:50','2025-07-03 23:28:50'),(131,1,'微信图片_20250703232933.jpg',83284,'public/common/5fc9bf9a3d894d4da1476dedc11d1c0b_20250703232938.jpg','jpg',1,1,'管理员','2025-07-03 23:29:38','2025-07-03 23:29:38'),(132,1,'微信图片_20250703233031.jpg',143111,'public/common/65f51f065cb44894b98538d5c2bfc434_20250703233042.jpg','jpg',1,1,'管理员','2025-07-03 23:30:42','2025-07-03 23:30:42'),(133,1,'微信图片_20250703233034.png',188473,'public/common/ff99de037d6f495eb1521019da43d1cf_20250703233118.png','png',1,1,'管理员','2025-07-03 23:31:18','2025-07-03 23:31:18'),(134,1,'微信图片_20250703233208.jpg',78978,'public/common/16e4a014b0d143749fbd71233256f325_20250703233219.jpg','jpg',1,1,'管理员','2025-07-03 23:32:19','2025-07-03 23:32:19'),(135,1,'微信图片_20250703233251.jpg',206222,'public/common/5fae5387787f4aa29f67ec451a575686_20250703233257.jpg','jpg',1,1,'管理员','2025-07-03 23:32:57','2025-07-03 23:32:57'),(136,1,'微信图片_20250703233340.jpg',110646,'public/common/038c68bc4aee452989e0d9a52b54da38_20250703233347.jpg','jpg',1,1,'管理员','2025-07-03 23:33:47','2025-07-03 23:33:47'),(137,1,'微信图片_20250703233422.jpg',189416,'public/common/35a5f2ffd66c4a5ea377c38cbb73582c_20250703233427.jpg','jpg',1,1,'管理员','2025-07-03 23:34:27','2025-07-03 23:34:27'),(138,1,'微信图片_20250703233422.jpg',189416,'public/common/a9f93b95ae7146b0b84e59c3cb31e4e0_20250703233507.jpg','jpg',1,1,'管理员','2025-07-03 23:35:07','2025-07-03 23:35:07'),(139,1,'微信图片_20250703233546.jpg',55792,'public/common/dd48ee9fdb5b4b429adfb679794db2c4_20250703233552.jpg','jpg',1,1,'管理员','2025-07-03 23:35:52','2025-07-03 23:35:52'),(140,1,'微信图片_20250703233636.jpg',119864,'public/common/98f9b9deb700406f98673338ef7bdd73_20250703233643.jpg','jpg',1,1,'管理员','2025-07-03 23:36:43','2025-07-03 23:36:43'),(141,1,'QQ20250704-151126.png',362962,'public/common/303f520879aa4998ac91fb40c0fdcfc0_20250704151213.png','png',1,1,'管理员','2025-07-04 15:12:13','2025-07-04 15:12:13'),(142,1,'QQ20250704-155901.png',307551,'public/common/e08654a452f64efd8c27eaa332a24ff1_20250704155935.png','png',1,1,'管理员','2025-07-04 15:59:35','2025-07-04 15:59:35'),(143,1,'QQ20250704-160241.png',418040,'public/common/84be4c2a9de4474980870c7c44c44fcf_20250704160255.png','png',1,1,'管理员','2025-07-04 16:02:55','2025-07-04 16:02:55'),(144,1,'QQ20250704-160241.png',418040,'public/common/fbdbc312426b4fe3a7dfdf84c32bfa25_20250705103525.png','png',1,1,'管理员','2025-07-05 10:35:25','2025-07-05 10:35:25'),(145,1,'QQ20250704-160241.png',418040,'public/common/7060acac1e8a4995a63e06a4b8c3cde6_20250705103707.png','png',1,1,'管理员','2025-07-05 10:37:07','2025-07-05 10:37:07'),(146,1,'QQ20250704-160241.png',418040,'public/common/f7c4abc3586c4000b9d90139a751c935_20250705103746.png','png',1,1,'管理员','2025-07-05 10:37:46','2025-07-05 10:37:46'),(147,1,'QQ20250704-160241.png',418040,'public/common/d1875d3f530445638c1944cbb1bc7534_20250705105242.png','png',1,1,'管理员','2025-07-05 10:52:42','2025-07-05 10:52:42'),(148,1,'QQ20250704-160241.png',418040,'public/common/f3a7083d3ea443da82a2d7c4a80fda9a_20250705105313.png','png',1,1,'管理员','2025-07-05 10:53:13','2025-07-05 10:53:13'),(149,1,'mate60详情图片1.png',686502,'public/common/49c1fdec9d56468ba0d6ec169df07c56_20250705184008.png','png',1,1,'管理员','2025-07-05 18:40:08','2025-07-05 18:40:08'),(150,1,'889912e3e3ff555b.png',645324,'public/common/0950371b077b4777bbb67f36d9073112_20250705184127.png','png',1,1,'管理员','2025-07-05 18:41:27','2025-07-05 18:41:27'),(151,1,'021d138f388049e6b76b0cbef7ada948_20250703230957.jpg',80228,'public/common/1da01dd176184f7a8336590b7903ad55_20250708163233.jpg','jpg',1,1,'管理员','2025-07-08 16:32:33','2025-07-08 16:32:33'),(152,1,'889912e3e3ff555b.png',645324,'public/common/4641bcd4b198418084165d8d2700dbf3_20250721232239.png','png',1,1,'管理员','2025-07-21 23:22:39','2025-07-21 23:22:39'),(153,1,'Banner1-1.jpg',180919,'public/common/2690173e8f944655b465c5e3b742eb02_20250721233829.jpg','jpg',1,1,'管理员','2025-07-21 23:38:29','2025-07-21 23:38:29'),(154,1,'Banner1-1.jpg',180919,'public/common/430c6fe9d38e4d80b725b9e1a41b6e34_20250721233853.jpg','jpg',1,1,'管理员','2025-07-21 23:38:53','2025-07-21 23:38:53'),(155,1,'Banner1-1.jpg',180919,'public/common/78dc4ad2d3e84208bd615d4e161dd3af_20250722221056.jpg','jpg',1,1,'管理员','2025-07-22 22:10:56','2025-07-22 22:10:56'),(156,1,'Banner1-1.jpg',213534,'public/common/67c558bb7e884068a0682a630d5dafe1_20250723004853.jpg','jpg',1,1,'管理员','2025-07-23 00:48:53','2025-07-23 00:48:53'),(157,1,'Banner1-2.jpg',185806,'public/common/8c48b6063e804848a09bbb3f386324dc_20250723143143.jpg','jpg',1,1,'管理员','2025-07-23 14:31:43','2025-07-23 14:31:43');
/*!40000 ALTER TABLE `t_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_goods`
--

DROP TABLE IF EXISTS `t_goods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_goods` (
  `goods_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `goods_status` int DEFAULT NULL COMMENT '商品状态:[1:预约中,2:售卖中,3:售罄]',
  `category_id` int NOT NULL COMMENT '商品类目',
  `activity_id` bigint unsigned DEFAULT NULL COMMENT '活动ID',
  `goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `goods_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品类型',
  `goods_currency` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'cash' COMMENT '商品货币(cash现金, points积分)',
  `place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产地',
  `alone_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '单独购买标记',
  `alone_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单独价格',
  `pay_mode` tinyint DEFAULT NULL COMMENT '付款模式 1 全额, 2 预付',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品描述',
  `images` json DEFAULT NULL COMMENT '商品图片数组',
  `detail_images` json DEFAULT NULL COMMENT '详情图片数组',
  `price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '价格',
  `shelves_flag` tinyint unsigned NOT NULL COMMENT '上架状态',
  `deleted_flag` tinyint unsigned NOT NULL COMMENT '删除状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`goods_id`) USING BTREE,
  KEY `activity_id` (`activity_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_goods`
--

LOCK TABLES `t_goods` WRITE;
/*!40000 ALTER TABLE `t_goods` DISABLE KEYS */;
INSERT INTO `t_goods` VALUES (1,1,1,5,'Mote60','hot','cash','BEI_JING',0,NULL,1,'mate60  详情详情详情详情详情详情详情','[{\"url\": \"public/common/e08654a452f64efd8c27eaa332a24ff1_20250704155935.png\"}]','[{\"url\": \"public/common/49c1fdec9d56468ba0d6ec169df07c56_20250705184008.png\"}, {\"url\": \"public/common/0950371b077b4777bbb67f36d9073112_20250705184127.png\"}]','9999.00',1,0,NULL,'2025-07-22 22:06:49','2021-09-01 22:25:30'),(7,1,1,4,'iphone15 pro','special','cash','LUO_YANG',0,NULL,1,'详情','[{\"url\": \"public/common/f3a7083d3ea443da82a2d7c4a80fda9a_20250705105313.png\"}]','[]','5999',1,0,'备注','2025-07-22 22:08:57','2022-10-21 19:58:07'),(8,1,1,3,'iphone14','special','cash','ZHENG_ZHOU',0,NULL,1,'详情','[{\"url\": \"public/common/303f520879aa4998ac91fb40c0fdcfc0_20250704151213.png\"}]','[]','4999',1,0,'','2025-07-22 22:06:19','2022-10-21 19:00:11'),(10,1,357,NULL,'小米15','recommended','cash','LUO_YANG',0,NULL,1,'详情','[]','[]','7999.00',1,0,'','2025-07-05 23:25:35','2023-10-07 19:02:24'),(11,1,354,NULL,'青轴键盘',NULL,'cash','ZHENG_ZHOU',0,NULL,NULL,'详情',NULL,NULL,'199.00',1,0,'支持usb','2025-07-05 23:25:35','2023-12-01 19:55:53'),(12,1,356,NULL,'罗技双模鼠标',NULL,'cash','BEI_JING',0,NULL,NULL,'详情','[]','[]','99.00',0,0,'支持蓝牙','2025-07-05 23:25:36','2023-12-01 19:57:25'),(34,1,2,4,'苹果正品耳机','special','cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/4e1b472cd94042b388c203b0ba9a6e80_20250703230903.jpg\"}, {\"url\": \"public/common/b24c7eb5760846429150540bf6fb28ff_20250703230947.jpg\"}, {\"url\": \"public/common/b5e7a209df1e498dbc748bead448969f_20250703230951.jpg\"}, {\"url\": \"public/common/a75b0509c37c4dc085dc1f19125c1f1a_20250703230954.jpg\"}, {\"url\": \"public/common/021d138f388049e6b76b0cbef7ada948_20250703230957.jpg\"}, {\"url\": \"public/common/b8b9512fb1144214a947e4602492d24e_20250703230959.jpg\"}]','[{\"url\": \"public/common/1da01dd176184f7a8336590b7903ad55_20250708163233.jpg\"}]','99',1,0,NULL,'2025-07-23 13:53:13','2025-07-03 23:11:06'),(35,1,1,NULL,'YSL香水','recommended','cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/3e68715524e243adb8a24baa7ca3f4b4_20250703232423.jpg\"}]','[]','800',1,0,NULL,'2025-07-05 23:25:36','2025-07-03 23:25:18'),(36,1,1,NULL,'GUCCI香水',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/e057c6b18df04956b2596e978154a285_20250703232706.jpg\"}]','[]','1200',1,0,NULL,'2025-07-05 23:25:36','2025-07-03 23:27:37'),(37,1,1,NULL,'爱马仕腰带',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/374338135b7247c0a035b03acb8af1d1_20250703232742.jpg\"}]','[]','7600',1,0,NULL,'2025-07-05 23:25:37','2025-07-03 23:28:01'),(38,1,1,NULL,'香奈儿包包','hot','cash','LUO_YANG',0,NULL,1,NULL,'[{\"url\": \"public/common/8e8d8b9fef974cd3b0216fa01a48015a_20250703232850.jpg\"}]','[]','38000',1,0,NULL,'2025-07-05 23:25:37','2025-07-03 23:29:18'),(39,1,1,NULL,'蔻驰包包',NULL,'cash','BEI_JING',0,NULL,1,NULL,'[{\"url\": \"public/common/5fc9bf9a3d894d4da1476dedc11d1c0b_20250703232938.jpg\"}]','[]','19000',1,0,NULL,'2025-07-05 23:25:37','2025-07-03 23:30:12'),(40,1,1,NULL,'迪奥经典款包包','recommended','cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/65f51f065cb44894b98538d5c2bfc434_20250703233042.jpg\"}]','[]','76000',1,0,NULL,'2025-07-05 23:25:38','2025-07-03 23:31:13'),(41,1,1,NULL,'迪奥限量款包包',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/ff99de037d6f495eb1521019da43d1cf_20250703233118.png\"}]','[]','138000',1,0,NULL,'2025-07-05 23:25:38','2025-07-03 23:31:48'),(42,1,1,NULL,'浪琴手表',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/16e4a014b0d143749fbd71233256f325_20250703233219.jpg\"}]','[]','18600',1,0,NULL,'2025-07-05 23:25:38','2025-07-03 23:32:41'),(43,1,1,NULL,'浪琴经典款手表',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/5fae5387787f4aa29f67ec451a575686_20250703233257.jpg\"}]','[]','12800',1,0,NULL,'2025-07-05 23:25:38','2025-07-03 23:33:21'),(44,1,1,NULL,'百达翡丽经典款','special','cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/038c68bc4aee452989e0d9a52b54da38_20250703233347.jpg\"}]','[]','890000',1,0,NULL,'2025-07-05 23:25:39','2025-07-03 23:34:10'),(45,1,1,NULL,'劳力士经典款',NULL,'cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/35a5f2ffd66c4a5ea377c38cbb73582c_20250703233427.jpg\"}]','[]','196666',1,0,NULL,'2025-07-05 23:25:39','2025-07-03 23:34:51'),(46,1,1,NULL,'劳力士机械款','recommended','cash','SHANG_HAI',0,NULL,1,NULL,'[{\"url\": \"public/common/a9f93b95ae7146b0b84e59c3cb31e4e0_20250703233507.jpg\"}]','[]','256000',1,0,NULL,'2025-07-05 23:25:39','2025-07-03 23:35:31'),(47,1,1,NULL,'普拉达男士商务包','special','cash','BEI_JING',0,NULL,1,NULL,'[{\"url\": \"public/common/dd48ee9fdb5b4b429adfb679794db2c4_20250703233552.jpg\"}]','[]','59800',1,0,NULL,'2025-07-05 23:25:40','2025-07-03 23:36:27'),(48,1,351,5,'普拉达经典款','recommended','cash','美国',0,NULL,1,NULL,'[{\"url\": \"public/common/98f9b9deb700406f98673338ef7bdd73_20250703233643.jpg\"}]','[]','56800',1,0,NULL,'2025-07-22 22:05:49','2025-07-03 23:37:16');
/*!40000 ALTER TABLE `t_goods` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_goods_skus`
--

DROP TABLE IF EXISTS `t_goods_skus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_goods_skus` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
  `goods_id` bigint unsigned DEFAULT NULL COMMENT '商品ID',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'SKU编码',
  `attributes` json DEFAULT NULL COMMENT '规格属性',
  `price` decimal(10,2) NOT NULL COMMENT '商品标价',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `alone_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '单独购买标记',
  `alone_price` decimal(10,2) DEFAULT NULL COMMENT '单独购买价格',
  `stock` int unsigned NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales_count` int unsigned NOT NULL DEFAULT '0' COMMENT '销售数量',
  `status` int NOT NULL DEFAULT '1' COMMENT 'SKU状态:[1:预约中,2:售卖中,3:售罄]',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`goods_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  CONSTRAINT `fk_goods_sku` FOREIGN KEY (`goods_id`) REFERENCES `t_goods` (`goods_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商品规格表(SKU)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_goods_skus`
--

LOCK TABLES `t_goods_skus` WRITE;
/*!40000 ALTER TABLE `t_goods_skus` DISABLE KEYS */;
INSERT INTO `t_goods_skus` VALUES (4,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-26 22:58:40',NULL),(5,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-26 22:58:40',NULL),(6,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 14:07:06',NULL),(7,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 14:07:06',NULL),(8,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:25:42',NULL),(9,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:25:42',NULL),(10,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:26:31',NULL),(11,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:26:31',NULL),(12,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:29:02',NULL),(13,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:29:02',NULL),(14,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:34:44',NULL),(15,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:34:44',NULL),(16,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:35:18',NULL),(17,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-29 15:35:18',NULL),(18,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-30 11:28:49',NULL),(19,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-30 11:28:49',NULL),(20,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-30 11:33:05',NULL),(21,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-06-30 11:33:05',NULL),(22,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 14:51:52',NULL),(23,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 15:12:34',NULL),(24,NULL,'5e5c2da9d3ad49069ba96a1f5e538be9','{\"size\": \"M\", \"color\": \"红色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 15:12:34',NULL),(25,NULL,'72db0c5fcaec409494a5455d6e40d9e5','{\"size\": \"M\", \"color\": \"白色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 15:12:34',NULL),(26,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-07-04 15:12:56',NULL),(27,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-07-04 15:12:56',NULL),(28,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 15:17:58',NULL),(29,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 15:19:29',NULL),(30,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-07-04 15:59:43',NULL),(31,NULL,'35a1e914e7df4de583a6b2b0fd728c53','{\"size\": \"L\", \"color\": \"黑色\"}',10.00,NULL,0,NULL,999,0,1,'2025-07-04 15:59:43',NULL),(32,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 16:03:01',NULL),(33,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',0.00,NULL,0,NULL,0,0,1,'2025-07-04 21:22:59',NULL),(34,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,0,0,1,'2025-07-05 10:10:18',NULL),(35,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',6999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:32:41',NULL),(36,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:34:07',NULL),(37,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:35:30',NULL),(38,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:37:35',NULL),(39,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:37:49',NULL),(40,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:52:49',NULL),(41,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 10:53:17',NULL),(42,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',6999.00,7999.00,0,NULL,999,0,1,'2025-07-05 11:02:00',NULL),(43,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,999,0,1,'2025-07-05 11:03:33',NULL),(44,NULL,'aaa1331a63b24e70a8515080b0245ba0','{\"size\": \"50x30\", \"color\": \"棕色\"}',56800.00,76800.00,0,NULL,99,0,1,'2025-07-05 13:29:56',NULL),(45,NULL,'aaa1331a63b24e70a8515080b0245ba0','{\"size\": \"50x30\", \"color\": \"brown\"}',56800.00,76800.00,0,NULL,99,0,1,'2025-07-05 13:30:27',NULL),(46,NULL,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',6999.00,7999.00,0,NULL,979,20,1,'2025-07-05 18:41:34',NULL),(47,NULL,'9ca461f589b34c5db8a2dff0e1644182','{\"size\": \"M\", \"color\": \"黑色\"}',99.00,1099.00,0,NULL,999,0,1,'2025-07-08 16:32:53',NULL),(48,NULL,'9ca461f589b34c5db8a2dff0e1644182','{\"size\": \"M\", \"color\": \"黑色\"}',99.00,1099.00,0,NULL,999,0,1,'2025-07-08 16:40:49',NULL),(49,NULL,'9ca461f589b34c5db8a2dff0e1644182','{\"size\": \"M\", \"color\": \"黑色\"}',99.00,1099.00,0,NULL,989,10,1,'2025-07-08 16:42:29',NULL),(50,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,0,0,1,'2025-07-18 16:16:34',NULL),(51,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,9999,0,1,'2025-07-18 16:18:28',NULL),(52,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,997,2,1,'2025-07-18 16:18:41',NULL),(53,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,9999,0,1,'2025-07-18 16:20:48',NULL),(54,NULL,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,9982,17,1,'2025-07-18 16:21:56',NULL),(55,NULL,'aaa1331a63b24e70a8515080b0245ba0','{\"size\": \"50x30\", \"color\": \"brown\"}',56800.00,76800.00,0,NULL,99,0,1,'2025-07-22 22:05:50',NULL),(56,8,'02f88083a0a5445ab29fb7e5bec31c21','{\"size\": \"M\", \"color\": \"黑色\"}',3999.00,4999.00,0,NULL,9982,17,1,'2025-07-22 22:06:20',NULL),(57,NULL,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,997,2,1,'2025-07-22 22:06:34',NULL),(58,1,'c6d170d50ad94720a4e19ccd9994dc97','{\"size\": \"M\", \"color\": \"黑色\"}',6999.00,7999.00,0,NULL,978,21,1,'2025-07-22 22:06:49',NULL),(59,48,'aaa1331a63b24e70a8515080b0245ba0','{\"size\": \"50x30\", \"color\": \"brown\"}',56800.00,76800.00,0,NULL,99,0,1,'2025-07-22 22:08:15',NULL),(60,7,'1d4270238ffb4bfcbc5d5ece964b1679','{\"size\": \"M\", \"color\": \"黑色\"}',5999.00,7999.00,0,NULL,997,2,1,'2025-07-22 22:08:57',NULL),(61,34,'9ca461f589b34c5db8a2dff0e1644182','{\"size\": \"M\", \"color\": \"黑色\"}',99.00,1099.00,0,NULL,989,10,1,'2025-07-23 13:53:14',NULL);
/*!40000 ALTER TABLE `t_goods_skus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_heart_beat_record`
--

DROP TABLE IF EXISTS `t_heart_beat_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_heart_beat_record` (
  `heart_beat_record_id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `project_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `server_ip` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器ip',
  `process_no` int NOT NULL COMMENT '进程号',
  `process_start_time` datetime NOT NULL COMMENT '进程开启时间',
  `heart_beat_time` datetime NOT NULL COMMENT '心跳时间',
  PRIMARY KEY (`heart_beat_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=422 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公用服务 - 服务心跳';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_heart_beat_record`
--

LOCK TABLES `t_heart_beat_record` WRITE;
/*!40000 ALTER TABLE `t_heart_beat_record` DISABLE KEYS */;
INSERT INTO `t_heart_beat_record` VALUES (396,'/home/<USER>','*************;127.0.0.1',2127348,'2025-07-01 14:34:25','2025-07-01 15:27:41'),(397,'/home/<USER>','*************;127.0.0.1',2127667,'2025-07-01 15:28:27','2025-07-01 15:32:41'),(398,'/home/<USER>','*************;127.0.0.1',2127802,'2025-07-01 15:33:19','2025-07-03 13:26:48'),(399,'/home/<USER>','*************;127.0.0.1',2138150,'2025-07-03 13:27:02','2025-07-03 20:46:19'),(400,'/home/<USER>','*************;127.0.0.1',2139574,'2025-07-03 20:47:23','2025-07-04 09:26:41'),(401,'/home/<USER>','*************;127.0.0.1',2141978,'2025-07-04 09:27:11','2025-07-04 11:18:26'),(402,'/home/<USER>','*************;127.0.0.1',2142556,'2025-07-04 11:18:40','2025-07-04 13:54:54'),(403,'/home/<USER>','*************;127.0.0.1',2143199,'2025-07-04 13:55:05','2025-07-04 15:53:20'),(404,'/home/<USER>','*************;127.0.0.1',2143615,'2025-07-04 15:53:50','2025-07-04 16:11:05'),(405,'/home/<USER>','*************;127.0.0.1',2143794,'2025-07-04 16:11:16','2025-07-04 21:50:32'),(406,'/home/<USER>','*************;127.0.0.1',2144888,'2025-07-04 21:50:42','2025-07-04 22:19:55'),(407,'/home/<USER>','*************;127.0.0.1',2145123,'2025-07-04 22:20:20','2025-07-04 22:46:35'),(408,'/home/<USER>','*************;127.0.0.1',2145288,'2025-07-04 22:47:28','2025-07-05 17:42:47'),(409,'/home/<USER>','*************;127.0.0.1',2149326,'2025-07-05 17:42:54','2025-07-05 23:52:12'),(410,'/home/<USER>','*************;127.0.0.1',2150665,'2025-07-05 23:52:55','2025-07-06 09:21:12'),(411,'/home/<USER>','*************;127.0.0.1',2152533,'2025-07-06 09:21:50','2025-07-06 10:40:04'),(412,'/home/<USER>','*************;127.0.0.1',2152871,'2025-07-06 10:40:58','2025-07-06 17:26:15'),(413,'/home/<USER>','*************;127.0.0.1',2154464,'2025-07-06 17:27:01','2025-07-06 18:28:16'),(414,'/home/<USER>','*************;127.0.0.1',2154831,'2025-07-06 18:28:49','2025-07-06 23:24:05'),(415,'/home/<USER>','*************;127.0.0.1',2156000,'2025-07-06 23:25:06','2025-07-07 21:07:28'),(416,'/home/<USER>','*************;127.0.0.1',2161265,'2025-07-07 21:08:31','2025-07-07 21:26:46'),(417,'/home/<USER>','*************;127.0.0.1',2161441,'2025-07-07 21:27:48','2025-07-08 21:43:10'),(418,'/home/<USER>','*************;127.0.0.1',2167467,'2025-07-08 21:43:28','2025-07-08 22:31:44'),(419,'/home/<USER>','*************;127.0.0.1',2168162,'2025-07-08 22:32:18','2025-07-17 23:09:44'),(420,'/home/<USER>','*************;127.0.0.1',2205414,'2025-07-17 23:10:42','2025-07-22 18:23:36'),(421,'/home/<USER>','172.30.199.100;127.0.0.1',2813001,'2025-07-22 18:32:19','2025-07-23 15:48:45');
/*!40000 ALTER TABLE `t_heart_beat_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_help_doc`
--

DROP TABLE IF EXISTS `t_help_doc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_help_doc` (
  `help_doc_id` bigint NOT NULL AUTO_INCREMENT,
  `help_doc_catalog_id` bigint NOT NULL COMMENT '类型1公告 2动态',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `content_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文本内容',
  `content_html` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'html内容',
  `attachment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `page_view_count` int NOT NULL DEFAULT '0' COMMENT '页面浏览量，传说中的pv',
  `user_view_count` int NOT NULL DEFAULT '0' COMMENT '用户浏览量，传说中的uv',
  `author` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`help_doc_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='帮助文档';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_help_doc`
--

LOCK TABLES `t_help_doc` WRITE;
/*!40000 ALTER TABLE `t_help_doc` DISABLE KEYS */;
INSERT INTO `t_help_doc` VALUES (32,6,'企业名称该写什么？','需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；','<ul><li style=\"text-align: start;\">需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改</li><li style=\"text-align: start;\">需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改</li><li style=\"text-align: start;\">需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改</li><li style=\"text-align: start;\">需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；</li></ul>','',0,56,2,'卓大','2025-06-25 16:33:19','2022-11-22 10:41:48'),(33,6,'谁有权限查看企业信息','需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；','<ul><li style=\"text-align: start;\">需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改</li><li style=\"text-align: start;\">需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改</li><li style=\"text-align: start;\">需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改</li><li style=\"text-align: start;\">需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；</li></ul>','',0,14,2,'卓大','2025-06-25 16:33:42','2022-11-22 10:42:19');
/*!40000 ALTER TABLE `t_help_doc` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_help_doc_catalog`
--

DROP TABLE IF EXISTS `t_help_doc_catalog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_help_doc_catalog` (
  `help_doc_catalog_id` bigint NOT NULL AUTO_INCREMENT COMMENT '帮助文档目录',
  `name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序字段',
  `parent_id` bigint NOT NULL COMMENT '父级id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`help_doc_catalog_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='帮助文档-目录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_help_doc_catalog`
--

LOCK TABLES `t_help_doc_catalog` WRITE;
/*!40000 ALTER TABLE `t_help_doc_catalog` DISABLE KEYS */;
INSERT INTO `t_help_doc_catalog` VALUES (6,'企业信息',0,0,'2022-11-05 10:52:40','2022-11-22 10:37:38'),(9,'企业信用',0,6,'2023-12-01 20:16:54','2023-12-01 20:16:54'),(10,'采购文档',0,11,'2023-12-01 20:17:08','2023-12-01 20:17:29'),(11,'进销存',0,0,'2023-12-01 20:17:23','2023-12-01 20:17:23');
/*!40000 ALTER TABLE `t_help_doc_catalog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_help_doc_relation`
--

DROP TABLE IF EXISTS `t_help_doc_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_help_doc_relation` (
  `relation_id` bigint NOT NULL COMMENT '关联id',
  `relation_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联名称',
  `help_doc_id` bigint NOT NULL COMMENT '文档id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`relation_id`,`help_doc_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='帮助文档-关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_help_doc_relation`
--

LOCK TABLES `t_help_doc_relation` WRITE;
/*!40000 ALTER TABLE `t_help_doc_relation` DISABLE KEYS */;
INSERT INTO `t_help_doc_relation` VALUES (0,'首页',32,'2023-12-04 13:34:17','2023-12-04 13:34:17'),(0,'首页',33,'2023-12-04 13:34:21','2023-12-04 13:34:21');
/*!40000 ALTER TABLE `t_help_doc_relation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_help_doc_view_record`
--

DROP TABLE IF EXISTS `t_help_doc_view_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_help_doc_view_record` (
  `help_doc_id` bigint NOT NULL COMMENT '通知公告id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名称',
  `page_view_count` int DEFAULT '0' COMMENT '查看次数',
  `first_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首次ip',
  `first_user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首次用户设备等标识',
  `last_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后一次ip',
  `last_user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后一次用户设备等标识',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`help_doc_id`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='帮助文档-查看记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_help_doc_view_record`
--

LOCK TABLES `t_help_doc_view_record` WRITE;
/*!40000 ALTER TABLE `t_help_doc_view_record` DISABLE KEYS */;
INSERT INTO `t_help_doc_view_record` VALUES (32,1,'管理员',1,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,'2025-06-25 16:33:19','2025-06-25 16:33:19'),(33,1,'管理员',1,'127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,'2025-06-25 16:33:42','2025-06-25 16:33:42');
/*!40000 ALTER TABLE `t_help_doc_view_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_invitation_records`
--

DROP TABLE IF EXISTS `t_invitation_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_invitation_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `inviter_id` bigint DEFAULT NULL COMMENT '邀请人ID',
  `invitee_id` bigint NOT NULL COMMENT '被邀请人ID',
  `commission_earned` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已获得佣金',
  `status` enum('pending','active','invalid') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'pending' COMMENT '邀请状态',
  `first_recharge_time` datetime DEFAULT NULL COMMENT '首次充值时间',
  `first_order_time` datetime DEFAULT NULL COMMENT '首次下单时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_invitee_id` (`invitee_id`) USING BTREE,
  KEY `idx_inviter_id` (`inviter_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  CONSTRAINT `fk_invitation_invitee` FOREIGN KEY (`invitee_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_invitation_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='邀请记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_invitation_records`
--

LOCK TABLES `t_invitation_records` WRITE;
/*!40000 ALTER TABLE `t_invitation_records` DISABLE KEYS */;
INSERT INTO `t_invitation_records` VALUES (1,1,2,6.00,'pending',NULL,'2025-06-30 22:54:05','2025-06-30 22:54:02'),(2,NULL,73,0.00,'pending',NULL,'2025-07-07 15:55:40','2025-07-07 15:55:40'),(3,NULL,79,0.00,'pending',NULL,'2025-07-09 21:10:22','2025-07-09 21:10:22'),(4,NULL,81,0.00,'pending',NULL,'2025-07-11 19:55:39','2025-07-11 19:55:39'),(5,NULL,83,0.00,'pending',NULL,'2025-07-14 14:25:46','2025-07-14 14:25:46');
/*!40000 ALTER TABLE `t_invitation_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_login_fail`
--

DROP TABLE IF EXISTS `t_login_fail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_login_fail` (
  `login_fail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_type` int NOT NULL COMMENT '用户类型',
  `login_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录名',
  `login_fail_count` int DEFAULT NULL COMMENT '连续登录失败次数',
  `lock_flag` tinyint DEFAULT '0' COMMENT '锁定状态:1锁定，0未锁定',
  `login_lock_begin_time` datetime DEFAULT NULL COMMENT '连续登录失败锁定开始时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`login_fail_id`) USING BTREE,
  UNIQUE KEY `uid_and_utype` (`user_id`,`user_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录失败次数记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_login_fail`
--

LOCK TABLES `t_login_fail` WRITE;
/*!40000 ALTER TABLE `t_login_fail` DISABLE KEYS */;
INSERT INTO `t_login_fail` VALUES (87,82,2,'*********',2,0,NULL,'2025-07-12 14:57:50','2025-07-12 14:57:50'),(88,73,2,'limbo',1,0,NULL,'2025-07-20 23:49:50','2025-07-20 23:49:50');
/*!40000 ALTER TABLE `t_login_fail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_login_log`
--

DROP TABLE IF EXISTS `t_login_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_login_log` (
  `login_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int NOT NULL COMMENT '用户id',
  `user_type` int NOT NULL COMMENT '用户类型',
  `user_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `login_ip` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ip',
  `login_ip_region` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ip地区',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'user-agent信息',
  `login_device` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录设备',
  `login_result` int NOT NULL COMMENT '登录结果：0成功 1失败 2 退出',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`login_log_id`) USING BTREE,
  KEY `customer_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户登录日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_login_log`
--

LOCK TABLES `t_login_log` WRITE;
/*!40000 ALTER TABLE `t_login_log` DISABLE KEYS */;
INSERT INTO `t_login_log` VALUES (1905,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 16:27:51','2025-06-25 16:27:51'),(1906,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 17:25:46','2025-06-25 17:25:46'),(1907,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 17:26:07','2025-06-25 17:26:08'),(1908,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 17:56:11','2025-06-25 17:56:12'),(1909,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 17:56:18','2025-06-25 17:56:19'),(1910,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 17:57:06','2025-06-25 17:57:06'),(1911,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 17:57:13','2025-06-25 17:57:14'),(1912,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 17:57:56','2025-06-25 17:57:56'),(1913,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 17:58:06','2025-06-25 17:58:06'),(1914,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 20:01:01','2025-06-25 20:01:02'),(1915,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 20:21:19','2025-06-25 20:21:20'),(1916,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 20:22:52','2025-06-25 20:22:52'),(1917,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-25 20:50:49','2025-06-25 20:50:50'),(1918,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-25 20:50:57','2025-06-25 20:50:58'),(1919,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-26 00:00:54','2025-06-26 00:00:55'),(1920,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-26 10:10:57','2025-06-26 10:10:58'),(1921,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-26 20:45:41','2025-06-26 20:45:42'),(1922,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 00:00:15','2025-06-27 00:00:16'),(1923,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-27 00:00:27','2025-06-27 00:00:28'),(1924,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'万能密码登录','2025-06-27 00:00:38','2025-06-27 00:00:38'),(1925,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 10:10:22','2025-06-27 10:10:22'),(1926,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 11:39:52','2025-06-27 11:39:53'),(1927,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-06-27 11:59:38','2025-06-27 11:59:38'),(1928,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 11:59:46','2025-06-27 11:59:46'),(1929,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',1,'密码错误','2025-06-27 12:06:22','2025-06-27 12:06:22'),(1930,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 12:06:32','2025-06-27 12:06:33'),(1931,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 16:16:21','2025-06-27 16:16:22'),(1932,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 16:57:02','2025-06-27 16:57:02'),(1933,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 18:21:38','2025-06-27 18:21:39'),(1934,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-27 23:33:59','2025-06-27 23:33:59'),(1935,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 00:19:22','2025-06-28 00:19:22'),(1936,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 01:07:11','2025-06-28 01:07:11'),(1937,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 10:09:00','2025-06-28 10:09:00'),(1938,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 11:51:08','2025-06-28 11:51:08'),(1939,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 15:05:42','2025-06-28 15:05:43'),(1940,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 19:36:35','2025-06-28 19:36:36'),(1941,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-28 23:31:41','2025-06-28 23:31:41'),(1942,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 12:57:49','2025-06-29 12:57:50'),(1943,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 13:00:48','2025-06-29 13:00:49'),(1944,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 15:24:34','2025-06-29 15:24:34'),(1945,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 18:33:08','2025-06-29 18:33:09'),(1946,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 22:14:38','2025-06-29 22:14:38'),(1947,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-29 23:19:56','2025-06-29 23:19:57'),(1948,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-30 08:17:39','2025-06-30 08:17:40'),(1949,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-30 10:16:58','2025-06-30 10:16:59'),(1950,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-30 12:44:47','2025-06-30 12:44:48'),(1951,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-30 21:28:05','2025-06-30 21:28:06'),(1952,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-06-30 22:34:39','2025-06-30 22:34:39'),(1953,1,1,'管理员','127.0.0.1','0|0|0|内网IP|内网IP','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-01 07:58:27','2025-07-01 07:58:28'),(1954,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-01 15:25:57','2025-07-01 15:25:57'),(1955,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-07-01 15:37:04','2025-07-01 15:37:05'),(1956,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-01 15:38:38','2025-07-01 15:38:38'),(1957,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-07-01 16:12:45','2025-07-01 16:12:46'),(1958,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-01 16:19:18','2025-07-01 16:19:18'),(1959,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-02 09:06:31','2025-07-02 09:06:32'),(1960,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-02 20:30:33','2025-07-02 20:30:33'),(1961,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-07-02 20:30:57','2025-07-02 20:30:58'),(1962,1,1,'管理员','39.144.136.136','中国|0|0|0|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 11:05:28','2025-07-03 11:05:28'),(1963,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 11:35:30','2025-07-03 11:35:30'),(1964,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 13:33:09','2025-07-03 13:33:09'),(1965,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 14:52:05','2025-07-03 14:52:05'),(1966,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','电脑端',0,'','2025-07-03 15:41:57','2025-07-03 15:41:58'),(1967,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 17:51:34','2025-07-03 17:51:34'),(1968,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 18:18:14','2025-07-03 18:18:15'),(1969,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-03 18:19:26','2025-07-03 18:19:26'),(1970,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-03 19:13:44','2025-07-03 19:13:44'),(1971,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','电脑端',0,'','2025-07-03 19:23:17','2025-07-03 19:23:17'),(1972,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-03 19:40:27','2025-07-03 19:40:28'),(1973,1,1,'管理员','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','电脑端',0,'','2025-07-03 23:08:41','2025-07-03 23:08:41'),(1974,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 09:49:03','2025-07-04 09:49:03'),(1975,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 10:19:04','2025-07-04 10:19:05'),(1976,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 10:37:01','2025-07-04 10:37:01'),(1977,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 11:18:29','2025-07-04 11:18:30'),(1978,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 11:48:28','2025-07-04 11:48:28'),(1979,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 11:49:42','2025-07-04 11:49:42'),(1980,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 12:57:50','2025-07-04 12:57:51'),(1981,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-04 14:08:31','2025-07-04 14:08:32'),(1982,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-04 14:48:03','2025-07-04 14:48:03'),(1983,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-04 15:40:20','2025-07-04 15:40:21'),(1984,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-04 20:10:32','2025-07-04 20:10:32'),(1985,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-04 21:22:25','2025-07-04 21:22:26'),(1986,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 08:30:36','2025-07-05 08:30:37'),(1987,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 09:53:32','2025-07-05 09:53:33'),(1988,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 10:09:11','2025-07-05 10:09:11'),(1989,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 10:18:23','2025-07-05 10:18:23'),(1990,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 10:30:22','2025-07-05 10:30:23'),(1991,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 13:19:27','2025-07-05 13:19:27'),(1992,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',1,'密码错误','2025-07-05 18:33:50','2025-07-05 18:33:50'),(1993,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 18:34:04','2025-07-05 18:34:04'),(1994,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 21:28:24','2025-07-05 21:28:24'),(1995,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-05 21:58:10','2025-07-05 21:58:11'),(1996,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-05 23:54:01','2025-07-05 23:54:01'),(1997,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-06 10:35:56','2025-07-06 10:35:56'),(1998,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 10:45:39','2025-07-06 10:45:40'),(1999,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-06 10:45:41','2025-07-06 10:45:42'),(2000,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 10:46:30','2025-07-06 10:46:31'),(2001,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-06 10:46:35','2025-07-06 10:46:36'),(2002,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 11:12:55','2025-07-06 11:12:55'),(2003,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-06 11:13:10','2025-07-06 11:13:11'),(2004,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 11:31:33','2025-07-06 11:31:34'),(2005,1,1,'管理员','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-06 12:00:25','2025-07-06 12:00:26'),(2006,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-06 12:02:17','2025-07-06 12:02:17'),(2007,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 12:15:54','2025-07-06 12:15:55'),(2008,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 14:10:36','2025-07-06 14:10:37'),(2009,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 15:14:24','2025-07-06 15:14:25'),(2010,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 17:49:53','2025-07-06 17:49:54'),(2011,73,2,'陈琳博','**************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-06 18:17:29','2025-07-06 18:17:29'),(2012,73,2,'陈琳博','**************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 18:18:47','2025-07-06 18:18:48'),(2013,73,2,'陈琳博','**************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 19:31:58','2025-07-06 19:31:59'),(2014,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-06 21:42:47','2025-07-06 21:42:48'),(2015,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 09:50:58','2025-07-07 09:50:58'),(2016,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 15:45:21','2025-07-07 15:45:22'),(2017,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-07 15:58:11','2025-07-07 15:58:12'),(2018,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 16:03:20','2025-07-07 16:03:21'),(2019,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 20:43:27','2025-07-07 20:43:28'),(2020,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-07 21:30:57','2025-07-07 21:30:57'),(2021,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 21:35:00','2025-07-07 21:35:01'),(2022,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-07 21:53:56','2025-07-07 21:53:57'),(2023,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-07 22:37:28','2025-07-07 22:37:28'),(2024,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-08 10:28:16','2025-07-08 10:28:17'),(2025,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-08 10:58:48','2025-07-08 10:58:48'),(2026,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-08 11:00:04','2025-07-08 11:00:05'),(2027,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-07-08 11:07:15','2025-07-08 11:07:16'),(2028,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-08 11:07:32','2025-07-08 11:07:33'),(2029,1,1,'管理员','***********','中国|0|0|0|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-08 15:28:03','2025-07-08 15:28:03'),(2030,73,2,'陈琳博','***********','中国|0|0|0|移动','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-08 15:29:37','2025-07-08 15:29:37'),(2031,73,2,'陈琳博','***********','中国|0|0|0|移动','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-08 16:51:26','2025-07-08 16:51:27'),(2032,73,2,'陈琳博','***********','中国|0|0|0|移动','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-08 17:30:48','2025-07-08 17:30:49'),(2033,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-08 20:01:42','2025-07-08 20:01:43'),(2034,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-08 20:19:37','2025-07-08 20:19:38'),(2035,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-08 21:17:58','2025-07-08 21:17:59'),(2036,73,2,'陈琳博','**************','中国|0|0|0|移动','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 09:21:30','2025-07-09 09:21:31'),(2037,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-09 09:29:28','2025-07-09 09:29:29'),(2038,1,1,'管理员','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-09 09:35:56','2025-07-09 09:35:57'),(2039,73,2,'陈琳博','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 13:07:02','2025-07-09 13:07:02'),(2040,77,2,'***********','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 13:12:28','2025-07-09 13:12:28'),(2041,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 16:09:55','2025-07-09 16:09:56'),(2042,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-09 16:10:24','2025-07-09 16:10:25'),(2043,73,2,'陈琳博','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 16:11:03','2025-07-09 16:11:04'),(2044,1,1,'管理员','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-09 16:11:56','2025-07-09 16:11:57'),(2045,77,2,'***********','**********','中国|0|广西|贵港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-09 16:48:44','2025-07-09 16:48:44'),(2046,78,2,'***********','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36','H5',0,'','2025-07-09 19:46:42','2025-07-09 19:46:42'),(2047,78,2,'***********','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',NULL,2,NULL,'2025-07-09 19:54:22','2025-07-09 19:54:23'),(2048,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-09 19:57:07','2025-07-09 19:57:08'),(2049,1,1,'管理员','*************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,2,NULL,'2025-07-09 19:58:30','2025-07-09 19:58:31'),(2050,79,2,'123456','***************','中国|0|广东省|广州市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-09 21:09:04','2025-07-09 21:09:05'),(2051,79,2,'123456','***************','中国|0|广东省|广州市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-09 21:11:57','2025-07-09 21:11:58'),(2052,80,2,'1*********8','***************','中国|0|广东省|广州市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','H5',0,'','2025-07-09 21:28:01','2025-07-09 21:28:01'),(2053,81,2,'*********','************','中国|0|香港|0|电讯盈科','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-11 19:52:41','2025-07-11 19:52:42'),(2054,81,2,'*********','************','中国|0|香港|0|电讯盈科','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-11 19:54:34','2025-07-11 19:54:35'),(2055,81,2,'*********','************','中国|0|香港|0|电讯盈科','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-11 19:59:23','2025-07-11 19:59:24'),(2056,82,2,'*********','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d28) NetType/WIFI Language/zh_CN','H5',1,'密码错误','2025-07-12 14:57:50','2025-07-12 14:57:51'),(2057,82,2,'*********','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d28) NetType/WIFI Language/zh_CN','H5',1,'密码错误','2025-07-12 14:58:07','2025-07-12 14:58:08'),(2058,81,2,'*********','************','中国|0|香港|0|电讯盈科','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-12 17:28:05','2025-07-12 17:28:06'),(2059,79,2,'123456','*************','中国|0|广东省|广州市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-12 21:02:20','2025-07-12 21:02:20'),(2060,73,2,'陈琳博','**************','中国|0|广东省|广州市|移动','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-14 14:09:26','2025-07-14 14:09:27'),(2061,83,2,'88888888','*************','中国|0|广东省|广州市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-14 14:23:08','2025-07-14 14:23:08'),(2062,1,1,'管理员','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-14 21:47:56','2025-07-14 21:47:57'),(2063,84,2,'18530957595','***************','中国|0|河南省|郑州市|联通','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-17 09:57:12','2025-07-17 09:57:12'),(2064,1,1,'管理员','**************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-17 22:38:34','2025-07-17 22:38:34'),(2065,77,2,'***********','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-18 11:06:15','2025-07-18 11:06:15'),(2066,77,2,'***********','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',NULL,2,NULL,'2025-07-18 11:39:01','2025-07-18 11:39:01'),(2067,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-18 11:42:51','2025-07-18 11:42:51'),(2068,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-18 15:53:06','2025-07-18 15:53:06'),(2069,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',NULL,2,NULL,'2025-07-18 16:06:46','2025-07-18 16:06:46'),(2070,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-18 16:07:07','2025-07-18 16:07:07'),(2071,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-18 16:15:20','2025-07-18 16:15:20'),(2072,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-18 20:04:29','2025-07-18 20:04:30'),(2073,85,2,'189012341234','**************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Linux; Android 12; ALN-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36','H5',0,'','2025-07-18 23:11:00','2025-07-18 23:11:01'),(2074,83,2,'88888888','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-18 23:28:05','2025-07-18 23:28:06'),(2075,83,2,'88888888','**************','中国|0|广西|防城港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-19 16:33:16','2025-07-19 16:33:17'),(2076,83,2,'88888888','*************','中国|0|广西|防城港市|电信','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-20 17:15:23','2025-07-20 17:15:24'),(2077,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-20 22:05:19','2025-07-20 22:05:19'),(2078,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',1,'密码错误','2025-07-20 23:49:50','2025-07-20 23:49:51'),(2079,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-20 23:50:24','2025-07-20 23:50:25'),(2080,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-21 11:00:19','2025-07-21 11:00:19'),(2081,79,2,'123456','**************','中国|0|广东省|广州市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-21 13:29:09','2025-07-21 13:29:10'),(2082,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-21 16:02:49','2025-07-21 16:02:49'),(2083,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-21 18:13:21','2025-07-21 18:13:22'),(2084,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********','H5',0,'','2025-07-21 19:30:00','2025-07-21 19:30:00'),(2085,83,2,'88888888','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-21 21:53:27','2025-07-21 21:53:27'),(2086,83,2,'88888888','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1',NULL,2,NULL,'2025-07-21 21:54:16','2025-07-21 21:54:16'),(2087,83,2,'88888888','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-21 21:54:34','2025-07-21 21:54:34'),(2088,73,2,'陈琳博','**************','中国|0|0|0|移动','Mozilla/5.0 (Linux; Android 12; ALN-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36','H5',0,'','2025-07-21 22:04:49','2025-07-21 22:04:50'),(2089,83,2,'88888888','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','H5',0,'','2025-07-21 22:13:56','2025-07-21 22:13:57'),(2090,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-21 22:18:28','2025-07-21 22:18:29'),(2091,1,1,'管理员','**************','中国|0|广西|南宁市|广西广电','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-21 23:02:06','2025-07-21 23:02:07'),(2092,1,1,'管理员','180.140.7.150','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-22 09:01:26','2025-07-22 09:01:27'),(2093,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36','H5',0,'','2025-07-22 17:10:05','2025-07-22 17:10:05'),(2094,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36','H5',0,'','2025-07-22 20:01:28','2025-07-22 20:01:29'),(2095,1,1,'管理员','117.183.120.30','中国|0|广西|贵港市|移动','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-22 21:54:27','2025-07-22 21:54:27'),(2096,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36','H5',0,'','2025-07-22 23:34:03','2025-07-22 23:34:03'),(2097,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-23 00:48:32','2025-07-23 00:48:32'),(2098,73,2,'陈琳博','*******','美国|0|0|0|Level3','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36','H5',0,'','2025-07-23 09:19:05','2025-07-23 09:19:05'),(2099,1,1,'管理员','***************','中国|0|广西|贵港市|电信','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-23 09:23:55','2025-07-23 09:23:55'),(2100,83,2,'88888888','**************','中国|0|广西|南宁市|联通','Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1','H5',0,'','2025-07-23 13:43:38','2025-07-23 13:43:39'),(2101,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-23 13:51:49','2025-07-23 13:51:50'),(2102,1,1,'管理员','**************','中国|0|香港|0|0','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','电脑端',0,'','2025-07-23 14:31:24','2025-07-23 14:31:24');
/*!40000 ALTER TABLE `t_login_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_mail_template`
--

DROP TABLE IF EXISTS `t_mail_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_mail_template` (
  `template_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `template_subject` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `template_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板内容',
  `template_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '解析类型 string，freemarker',
  `disable_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`template_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_mail_template`
--

LOCK TABLES `t_mail_template` WRITE;
/*!40000 ALTER TABLE `t_mail_template` DISABLE KEYS */;
INSERT INTO `t_mail_template` VALUES ('login_verification_code','登录验证码','<!DOCTYPE HTML>\r\n<html>\r\n<head>\r\n  <title>登录提醒</title>\r\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>\r\n  <style>\r\n      * {\r\n          font-family: SimSun;\r\n          /* 4号字体 */\r\n          font-size: 18px;\r\n          /* 22磅行间距 */\r\n          line-height: 29px;\r\n      }\r\n\r\n      .main_font_size {\r\n          font-size: 12.0pt;\r\n      }\r\n\r\n      .mainContent {\r\n          line-height: 28px;\r\n      }\r\n\r\n      p {\r\n          margin: 0 auto;\r\n          text-align: justify;\r\n      }\r\n  </style>\r\n\r\n</head>\r\n<body>\r\n<div>\r\n  <div style=\"margin: 0px auto;width: 690px;\">\r\n    <div class=\"mainContent\">\r\n      <h1>验证码</h1>\r\n      <p>请在验证页面输入此验证码</p>\r\n      <p><b>${code}</b></p>\r\n      <p>验证码将于此电子邮件发出 5 分钟后过期。</p>\r\n      <p>如果你未曾提出此请求，可以忽略这封电子邮件。</p>\r\n    </div>\r\n\r\n  </div>\r\n</div>\r\n</body>\r\n</html>','freemarker',0,'2024-08-06 09:13:08','2024-07-28 13:56:06');
/*!40000 ALTER TABLE `t_mail_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_menu`
--

DROP TABLE IF EXISTS `t_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `menu_type` int NOT NULL COMMENT '类型',
  `parent_id` bigint NOT NULL COMMENT '父菜单ID',
  `sort` int DEFAULT NULL COMMENT '显示顺序',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径',
  `perms_type` int DEFAULT NULL COMMENT '权限类型',
  `api_perms` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '后端权限字符串',
  `web_perms` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '前端权限字符串',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单图标',
  `context_menu_id` bigint DEFAULT NULL COMMENT '功能点关联菜单ID',
  `frame_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为外链',
  `frame_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '外链地址',
  `cache_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缓存',
  `visible_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示状态',
  `disabled_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `create_user_id` bigint NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=346 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_menu`
--

LOCK TABLES `t_menu` WRITE;
/*!40000 ALTER TABLE `t_menu` DISABLE KEYS */;
INSERT INTO `t_menu` VALUES (26,'菜单管理',2,50,1,'/menu/list','/system/menu/menu-list.vue',NULL,NULL,NULL,'CopyOutlined',NULL,0,NULL,1,1,0,0,2,'2021-08-09 15:04:35',1,'2023-12-01 19:39:03'),(40,'删除',3,26,NULL,NULL,NULL,1,'system:menu:batchDelete','system:menu:batchDelete',NULL,26,0,NULL,0,1,0,0,1,'2021-08-12 09:45:56',1,'2023-10-07 18:15:50'),(45,'用户管理',1,0,3,'/organization',NULL,1,NULL,NULL,'UserSwitchOutlined',NULL,0,NULL,0,1,0,0,1,'2021-08-12 16:13:27',1,'2025-07-01 08:32:19'),(46,'用户列表',2,45,9,'/organization/employee','/system/employee/index.vue',1,NULL,NULL,'AuditOutlined',NULL,0,NULL,0,1,0,0,1,'2021-08-12 16:21:50',1,'2025-07-01 08:32:27'),(47,'商品列表',2,48,1,'/erp/goods/list','/business/erp/goods/goods-list.vue',NULL,NULL,NULL,'AliwangwangOutlined',NULL,0,NULL,1,1,0,0,1,'2021-08-12 17:58:39',1,'2025-06-26 11:05:21'),(48,'商品管理',1,0,1,'/goods',NULL,NULL,NULL,NULL,'BarcodeOutlined',NULL,0,NULL,0,1,0,0,1,'2021-08-12 18:02:59',1,'2025-06-25 17:42:53'),(50,'系统设置',1,0,6,'/setting',NULL,NULL,NULL,NULL,'SettingOutlined',NULL,0,NULL,0,1,0,0,1,'2021-08-13 16:41:33',1,'2023-12-01 19:38:03'),(76,'角色管理',2,45,4,'/organization/role','/system/role/index.vue',NULL,NULL,NULL,'SlidersOutlined',NULL,0,NULL,0,1,0,0,1,'2021-08-26 10:31:00',1,'2024-07-02 20:15:28'),(78,'商品分类',2,48,2,'/erp/catalog/goods','/business/erp/catalog/goods-catalog.vue',NULL,NULL,NULL,'ApartmentOutlined',NULL,0,NULL,1,1,0,0,1,'2022-05-18 23:34:14',1,'2023-12-01 19:33:13'),(79,'自定义分组',2,48,3,'/erp/catalog/custom','/business/erp/catalog/custom-catalog.vue',1,NULL,NULL,'AppstoreAddOutlined',NULL,0,NULL,0,0,0,0,1,'2022-05-18 23:37:53',1,'2025-07-01 08:03:48'),(81,'用户操作记录',2,213,6,'/support/operate-log/operate-log-list','/support/operate-log/operate-log-list.vue',NULL,NULL,NULL,'VideoCameraOutlined',NULL,0,NULL,0,1,0,0,1,'2022-05-20 12:37:24',44,'2024-08-13 14:34:10'),(85,'组件演示',2,84,NULL,'/demonstration/index','/support/demonstration/index.vue',NULL,NULL,NULL,'ClearOutlined',NULL,0,NULL,0,1,0,0,1,'2022-05-20 23:16:46',NULL,'2022-05-20 23:16:46'),(86,'添加部门',3,46,1,NULL,NULL,1,'system:department:add','system:department:add',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-26 23:33:37',1,'2023-10-07 18:26:35'),(87,'修改部门',3,46,2,NULL,NULL,1,'system:department:update','system:department:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-26 23:34:11',1,'2023-10-07 18:26:44'),(88,'删除部门',3,46,3,NULL,NULL,1,'system:department:delete','system:department:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-26 23:34:49',1,'2023-10-07 18:26:49'),(91,'添加员工',3,46,NULL,NULL,NULL,1,'system:employee:add','system:employee:add',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:11:38',1,'2023-10-07 18:27:46'),(92,'编辑员工',3,46,NULL,NULL,NULL,1,'system:employee:update','system:employee:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:12:10',1,'2023-10-07 18:27:49'),(93,'禁用启用员工',3,46,NULL,NULL,NULL,1,'system:employee:disabled','system:employee:disabled',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:12:37',1,'2023-10-07 18:27:53'),(94,'调整员工部门',3,46,NULL,NULL,NULL,1,'system:employee:department:update','system:employee:department:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:12:59',1,'2023-10-07 18:27:34'),(95,'重置密码',3,46,NULL,NULL,NULL,1,'system:employee:password:reset','system:employee:password:reset',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:13:30',1,'2023-10-07 18:27:57'),(96,'删除员工',3,46,NULL,NULL,NULL,1,'system:employee:delete','system:employee:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:14:08',1,'2023-10-07 18:28:01'),(97,'添加角色',3,76,NULL,NULL,NULL,1,'system:role:add','system:role:add',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:34:00',1,'2023-10-07 18:42:31'),(98,'删除角色',3,76,NULL,NULL,NULL,1,'system:role:delete','system:role:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:34:19',1,'2023-10-07 18:42:35'),(99,'编辑角色',3,76,NULL,NULL,NULL,1,'system:role:update','system:role:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:34:55',1,'2023-10-07 18:42:44'),(100,'更新数据范围',3,76,NULL,NULL,NULL,1,'system:role:dataScope:update','system:role:dataScope:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:37:03',1,'2023-10-07 18:41:49'),(101,'批量移除员工',3,76,NULL,NULL,NULL,1,'system:role:employee:batch:delete','system:role:employee:batch:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:39:05',1,'2023-10-07 18:43:32'),(102,'移除员工',3,76,NULL,NULL,NULL,1,'system:role:employee:delete','system:role:employee:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:39:21',1,'2023-10-07 18:43:37'),(103,'添加员工',3,76,NULL,NULL,NULL,1,'system:role:employee:add','system:role:employee:add',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:39:38',1,'2023-10-07 18:44:05'),(104,'修改权限',3,76,NULL,NULL,NULL,1,'system:role:menu:update','system:role:menu:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-05-27 00:41:55',1,'2023-10-07 18:44:11'),(105,'添加',3,26,NULL,NULL,NULL,1,'system:menu:add','system:menu:add',NULL,26,0,NULL,0,1,0,0,1,'2022-05-27 00:44:37',1,'2023-10-07 17:35:35'),(106,'编辑',3,26,NULL,NULL,NULL,1,'system:menu:update','system:menu:update',NULL,26,0,NULL,0,1,0,0,1,'2022-05-27 00:44:59',1,'2023-10-07 17:35:48'),(109,'参数配置',2,50,3,'/config/config-list','/support/config/config-list.vue',NULL,NULL,NULL,'AntDesignOutlined',NULL,0,NULL,0,1,0,0,1,'2022-05-27 13:34:41',1,'2022-06-23 16:24:16'),(110,'数据字典',2,50,4,'/setting/dict','/support/dict/index.vue',NULL,NULL,NULL,'BarcodeOutlined',NULL,0,NULL,0,1,0,0,1,'2022-05-27 17:53:00',1,'2022-05-27 18:09:14'),(111,'监控服务',1,0,100,'/monitor',NULL,NULL,NULL,NULL,'BarChartOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-17 11:13:23',1,'2023-11-28 17:43:56'),(113,'查询',3,112,NULL,NULL,NULL,NULL,NULL,'ad',NULL,NULL,0,NULL,0,1,0,0,1,'2022-06-17 11:31:36',NULL,'2022-06-17 11:31:36'),(114,'运维工具',1,0,200,NULL,NULL,NULL,NULL,NULL,'NodeCollapseOutlined',NULL,0,NULL,0,1,0,1,1,'2022-06-20 10:09:16',1,'2023-12-01 19:36:18'),(117,'Reload',2,50,12,'/hook','/support/reload/reload-list.vue',NULL,NULL,NULL,'ReloadOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-20 10:16:49',1,'2023-12-01 19:39:17'),(122,'数据库监控',2,111,4,'/support/druid/index',NULL,NULL,NULL,NULL,'ConsoleSqlOutlined',NULL,1,'http://localhost:8686/druid',1,1,0,0,1,'2022-06-20 14:49:33',1,'2025-06-26 11:33:10'),(130,'单号管理',2,50,6,'/support/serial-number/serial-number-list','/support/serial-number/serial-number-list.vue',NULL,NULL,NULL,'NumberOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-24 14:45:22',1,'2022-06-28 16:23:41'),(132,'公告管理',2,138,2,'/oa/notice/notice-list','/business/oa/notice/notice-list.vue',NULL,NULL,NULL,'SoundOutlined',NULL,0,NULL,1,1,0,0,1,'2022-06-24 18:23:09',1,'2024-07-08 13:58:51'),(133,'缓存管理',2,50,11,'/support/cache/cache-list','/support/cache/cache-list.vue',NULL,NULL,NULL,'BorderInnerOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-24 18:52:25',1,'2023-12-01 19:39:13'),(138,'企业管理',1,0,1,NULL,NULL,NULL,NULL,NULL,'BankOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-24 20:09:18',1,'2025-06-26 10:44:39'),(142,'公告详情',2,132,NULL,'/oa/notice/notice-detail','/business/oa/notice/notice-detail.vue',NULL,NULL,NULL,NULL,NULL,0,NULL,0,0,0,0,1,'2022-06-25 16:38:47',1,'2022-09-14 19:46:17'),(143,'登录登出记录',2,213,5,'/support/login-log/login-log-list','/support/login-log/login-log-list.vue',NULL,NULL,NULL,'LoginOutlined',NULL,0,NULL,0,1,0,0,1,'2022-06-28 15:01:38',44,'2024-08-13 14:33:49'),(144,'企业管理',2,138,1,'/oa/enterprise/enterprise-list','/business/oa/enterprise/enterprise-list.vue',NULL,NULL,NULL,'ShopOutlined',NULL,0,NULL,0,1,0,0,1,'2022-09-14 17:00:07',1,'2024-07-08 13:48:24'),(145,'企业详情',2,138,NULL,'/oa/enterprise/enterprise-detail','/business/oa/enterprise/enterprise-detail.vue',NULL,NULL,NULL,NULL,NULL,0,NULL,0,0,0,0,1,'2022-09-14 18:52:52',1,'2022-11-22 10:39:07'),(147,'帮助文档',2,218,1,'/help-doc/help-doc-manage-list','/support/help-doc/management/help-doc-manage-list.vue',NULL,NULL,NULL,'FolderViewOutlined',NULL,0,NULL,0,1,0,0,1,'2022-09-14 19:59:01',1,'2023-12-01 19:38:23'),(148,'意见反馈',2,218,2,'/feedback/feedback-list','/support/feedback/feedback-list.vue',NULL,NULL,NULL,'CoffeeOutlined',NULL,0,NULL,0,1,0,0,1,'2022-09-14 19:59:52',1,'2023-12-01 19:38:40'),(149,'我的通知',2,132,NULL,'/oa/notice/notice-employee-list','/business/oa/notice/notice-employee-list.vue',NULL,NULL,NULL,NULL,NULL,0,NULL,0,0,0,0,1,'2022-09-14 20:29:41',1,'2022-09-14 20:31:23'),(150,'我的通知公告详情',2,132,NULL,'/oa/notice/notice-employee-detail','/business/oa/notice/notice-employee-detail.vue',NULL,NULL,NULL,NULL,NULL,0,NULL,0,0,0,0,1,'2022-09-14 20:30:25',1,'2022-09-14 20:31:38'),(151,'代码生成',2,0,600,'/support/code-generator','/support/code-generator/code-generator-list.vue',NULL,NULL,NULL,'CoffeeOutlined',NULL,0,NULL,0,1,0,0,1,'2022-09-21 18:25:05',1,'2022-10-22 11:27:58'),(152,'更新日志',2,218,3,'/support/change-log/change-log-list','/support/change-log/change-log-list.vue',NULL,NULL,NULL,'HeartOutlined',NULL,0,NULL,0,1,0,0,44,'2022-10-10 10:31:20',1,'2023-12-01 19:38:51'),(153,'清除缓存',3,133,NULL,NULL,NULL,1,'support:cache:delete','support:cache:delete',NULL,133,0,NULL,0,1,1,0,1,'2022-10-15 22:45:13',1,'2023-10-07 16:22:29'),(154,'获取缓存key',3,133,NULL,NULL,NULL,1,'support:cache:keys','support:cache:keys',NULL,133,0,NULL,0,1,1,0,1,'2022-10-15 22:45:48',1,'2023-10-07 16:22:35'),(156,'查看结果',3,117,NULL,NULL,NULL,1,'support:reload:result','support:reload:result',NULL,117,0,NULL,0,1,0,0,1,'2022-10-15 23:17:23',1,'2023-10-07 14:31:47'),(157,'单号生成',3,130,NULL,NULL,NULL,1,'support:serialNumber:generate','support:serialNumber:generate',NULL,130,0,NULL,0,1,0,0,1,'2022-10-15 23:21:06',1,'2023-10-07 18:22:46'),(158,'生成记录',3,130,NULL,NULL,NULL,1,'support:serialNumber:record','support:serialNumber:record',NULL,130,0,NULL,0,1,0,0,1,'2022-10-15 23:21:34',1,'2023-10-07 18:22:55'),(159,'查询',3,110,NULL,NULL,NULL,1,'support:dict:query','support:dict:query',NULL,110,0,NULL,0,1,0,0,1,'2022-10-15 23:23:51',1,'2025-04-08 19:42:25'),(160,'添加',3,110,NULL,NULL,NULL,1,'support:dict:add','support:dict:add',NULL,110,0,NULL,0,1,0,0,1,'2022-10-15 23:24:05',1,'2025-04-08 19:43:02'),(161,'更新',3,110,NULL,NULL,NULL,1,'support:dict:update','support:dict:update',NULL,110,0,NULL,0,1,0,0,1,'2022-10-15 23:24:34',1,'2025-04-08 19:43:34'),(162,'删除',3,110,NULL,NULL,NULL,1,'support:dict:delete','support:dict:delete',NULL,110,0,NULL,0,1,0,0,1,'2022-10-15 23:24:55',1,'2025-04-08 19:43:52'),(163,'新建',3,109,NULL,NULL,NULL,1,'support:config:add','support:config:add',NULL,109,0,NULL,0,1,0,0,1,'2022-10-15 23:26:56',1,'2023-10-07 18:16:17'),(164,'编辑',3,109,NULL,NULL,NULL,1,'support:config:update','support:config:update',NULL,109,0,NULL,0,1,0,0,1,'2022-10-15 23:27:07',1,'2023-10-07 18:16:24'),(165,'查询',3,47,NULL,NULL,NULL,1,'goods:query','goods:query',NULL,47,0,NULL,0,1,0,0,1,'2022-10-16 19:55:39',1,'2023-10-07 13:58:28'),(166,'新建',3,47,NULL,NULL,NULL,1,'goods:add','goods:add',NULL,47,0,NULL,0,1,0,0,1,'2022-10-16 19:56:00',1,'2023-10-07 13:58:32'),(167,'批量删除',3,47,NULL,NULL,NULL,1,'goods:batchDelete','goods:batchDelete',NULL,47,0,NULL,0,1,0,0,1,'2022-10-16 19:56:15',1,'2023-10-07 13:58:35'),(168,'查询',3,147,11,NULL,NULL,1,'support:helpDoc:query','support:helpDoc:query',NULL,147,0,NULL,0,1,0,0,1,'2022-10-16 20:12:13',1,'2023-10-07 14:05:49'),(169,'新建',3,147,12,NULL,NULL,1,'support:helpDoc:add','support:helpDoc:add',NULL,147,0,NULL,0,1,0,0,1,'2022-10-16 20:12:37',1,'2023-10-07 14:05:56'),(170,'新建目录',3,147,1,NULL,NULL,1,'support:helpDocCatalog:addCategory','support:helpDocCatalog:addCategory',NULL,147,0,NULL,0,1,0,0,1,'2022-10-16 20:12:57',1,'2023-10-07 14:06:38'),(171,'修改目录',3,147,2,NULL,NULL,1,'support:helpDocCatalog:update','support:helpDocCatalog:update',NULL,147,0,NULL,0,1,0,0,1,'2022-10-16 20:13:46',1,'2023-10-07 14:06:49'),(173,'新建',3,78,NULL,NULL,NULL,1,'category:add','category:add',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:02',1,'2023-10-07 13:54:01'),(174,'查询',3,78,NULL,NULL,NULL,1,'category:tree','category:tree',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:22',1,'2023-10-07 13:54:33'),(175,'编辑',3,78,NULL,NULL,NULL,1,'category:update','category:update',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:38',1,'2023-10-07 13:54:18'),(176,'删除',3,78,NULL,NULL,NULL,1,'category:delete','category:delete',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:50',1,'2023-10-07 13:54:27'),(177,'新建',3,79,NULL,NULL,NULL,1,'category:add','custom:category:add',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:02',1,'2023-10-07 13:57:32'),(178,'查询',3,79,NULL,NULL,NULL,1,'category:tree','custom:category:tree',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:22',1,'2023-10-07 13:57:50'),(179,'编辑',3,79,NULL,NULL,NULL,1,'category:update','custom:category:update',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:38',1,'2023-10-07 13:58:02'),(180,'删除',3,79,NULL,NULL,NULL,1,'category:delete','custom:category:delete',NULL,78,0,NULL,0,1,0,0,1,'2022-10-16 20:17:50',1,'2023-10-07 13:58:12'),(181,'查询',3,144,NULL,NULL,NULL,1,'oa:enterprise:query','oa:enterprise:query',NULL,144,0,NULL,0,1,0,0,1,'2022-10-16 20:25:14',1,'2023-10-07 12:00:09'),(182,'新建',3,144,NULL,NULL,NULL,1,'oa:enterprise:add','oa:enterprise:add',NULL,144,0,NULL,0,1,0,0,1,'2022-10-16 20:25:25',1,'2023-10-07 12:00:17'),(183,'编辑',3,144,NULL,NULL,NULL,1,'oa:enterprise:update','oa:enterprise:update',NULL,144,0,NULL,0,1,0,0,1,'2022-10-16 20:25:36',1,'2023-10-07 12:00:38'),(184,'删除',3,144,NULL,NULL,NULL,1,'oa:enterprise:delete','oa:enterprise:delete',NULL,144,0,NULL,0,1,0,0,1,'2022-10-16 20:25:53',1,'2023-10-07 12:00:46'),(185,'查询',3,132,NULL,NULL,NULL,1,'oa:notice:query','oa:notice:query',NULL,132,0,NULL,0,1,0,0,1,'2022-10-16 20:26:38',1,'2023-10-07 11:43:01'),(186,'新建',3,132,NULL,NULL,NULL,1,'oa:notice:add','oa:notice:add',NULL,132,0,NULL,0,1,0,0,1,'2022-10-16 20:27:04',1,'2023-10-07 11:43:07'),(187,'编辑',3,132,NULL,NULL,NULL,1,'oa:notice:update','oa:notice:update',NULL,132,0,NULL,0,1,0,0,1,'2022-10-16 20:27:15',1,'2023-10-07 11:43:12'),(188,'删除',3,132,NULL,NULL,NULL,1,'oa:notice:delete','oa:notice:delete',NULL,132,0,NULL,0,1,0,0,1,'2022-10-16 20:27:23',1,'2023-10-07 11:43:18'),(190,'查询',3,152,NULL,NULL,NULL,1,'','support:changeLog:query',NULL,152,0,NULL,0,1,0,0,1,'2022-10-16 20:28:33',1,'2023-10-07 14:25:05'),(191,'新建',3,152,NULL,NULL,NULL,1,'support:changeLog:add','support:changeLog:add',NULL,152,0,NULL,0,1,0,0,1,'2022-10-16 20:28:46',1,'2023-10-07 14:24:15'),(192,'批量删除',3,152,NULL,NULL,NULL,1,'support:changeLog:batchDelete','support:changeLog:batchDelete',NULL,152,0,NULL,0,1,0,0,1,'2022-10-16 20:29:10',1,'2023-10-07 14:24:22'),(193,'文件管理',2,50,20,'/support/file/file-list','/support/file/file-list.vue',NULL,NULL,NULL,'FolderOpenOutlined',NULL,0,NULL,0,1,0,0,1,'2022-10-21 11:26:11',1,'2022-10-22 11:29:22'),(194,'删除',3,47,NULL,NULL,NULL,1,'goods:delete','goods:delete',NULL,47,0,NULL,0,1,0,0,1,'2022-10-21 20:00:12',1,'2023-10-07 13:58:39'),(195,'修改',3,47,NULL,NULL,NULL,1,'goods:update','goods:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 20:05:23',1,'2023-10-07 13:58:42'),(196,'查看详情',3,145,NULL,NULL,NULL,1,'oa:enterprise:detail','oa:enterprise:detail',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 20:16:47',1,'2023-10-07 11:48:59'),(198,'删除',3,152,NULL,NULL,NULL,1,'support:changeLog:delete','support:changeLog:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 20:42:34',1,'2023-10-07 14:24:32'),(199,'查询',3,109,NULL,NULL,NULL,1,'support:config:query','support:config:query',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 20:45:14',1,'2023-10-07 18:16:27'),(200,'查询',3,193,NULL,NULL,NULL,1,'support:file:query','support:file:query',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 20:47:23',1,'2023-10-07 18:24:43'),(201,'删除',3,147,14,NULL,NULL,1,'support:helpDoc:delete','support:helpDoc:delete',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 21:03:20',1,'2023-10-07 14:07:02'),(202,'更新',3,147,13,NULL,NULL,1,'support:helpDoc:update','support:helpDoc:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 21:03:32',1,'2023-10-07 14:06:56'),(203,'查询',3,143,NULL,NULL,NULL,1,'support:loginLog:query','support:loginLog:query',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-21 21:05:11',1,'2023-10-07 14:27:23'),(204,'查询',3,81,NULL,NULL,NULL,1,'support:operateLog:query','support:operateLog:query',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-22 10:33:31',1,'2023-10-07 14:27:56'),(205,'详情',3,81,NULL,NULL,NULL,1,'support:operateLog:detail','support:operateLog:detail',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-22 10:33:49',1,'2023-10-07 14:28:04'),(206,'心跳监控',2,111,1,'/support/heart-beat/heart-beat-list','/support/heart-beat/heart-beat-list.vue',1,NULL,NULL,'FallOutlined',NULL,0,NULL,0,1,0,0,1,'2022-10-22 10:47:03',1,'2022-10-22 18:32:52'),(207,'更新',3,152,NULL,NULL,NULL,1,'support:changeLog:update','support:changeLog:update',NULL,NULL,0,NULL,0,1,0,0,1,'2022-10-22 11:51:32',1,'2023-10-07 14:24:39'),(212,'查询',3,117,NULL,NULL,NULL,1,'support:reload:query','support:reload:query',NULL,NULL,0,NULL,1,1,1,0,1,'2023-10-07 14:31:36',NULL,'2023-10-07 14:31:36'),(213,'网络安全',1,0,5,NULL,NULL,1,NULL,NULL,'SafetyCertificateOutlined',NULL,0,NULL,1,1,0,0,1,'2023-10-17 19:03:08',1,'2023-12-01 19:38:00'),(214,'登录失败锁定',2,213,4,'/support/login-fail','/support/login-fail/login-fail-list.vue',1,NULL,NULL,'LockOutlined',NULL,0,NULL,1,1,0,0,1,'2023-10-17 19:04:24',44,'2024-08-13 14:16:26'),(215,'接口加解密',2,213,2,'/support/api-encrypt','/support/api-encrypt/api-encrypt-index.vue',1,NULL,NULL,'CodepenCircleOutlined',NULL,0,NULL,1,1,0,0,1,'2023-10-24 11:49:28',44,'2024-08-13 12:00:14'),(216,'导出',3,47,NULL,NULL,NULL,1,'goods:exportGoods','goods:exportGoods',NULL,NULL,0,NULL,1,1,0,0,1,'2023-12-01 19:34:03',NULL,'2023-12-01 19:34:03'),(217,'导入',3,47,3,NULL,NULL,1,'goods:importGoods','goods:importGoods',NULL,NULL,0,NULL,1,1,0,0,1,'2023-12-01 19:34:22',NULL,'2023-12-01 19:34:22'),(218,'文档中心',1,0,4,NULL,NULL,1,NULL,NULL,'FileSearchOutlined',NULL,0,NULL,1,1,0,0,1,'2023-12-01 19:37:28',1,'2023-12-01 19:37:51'),(219,'部门管理',2,45,1,'/organization/department','/system/department/department-list.vue',1,NULL,NULL,'ApartmentOutlined',NULL,0,NULL,0,1,0,0,1,'2024-06-22 16:40:21',1,'2024-07-02 20:15:17'),(221,'定时任务',2,50,25,'/job/list','/support/job/job-list.vue',1,NULL,NULL,'AppstoreOutlined',NULL,0,NULL,1,1,0,0,2,'2024-06-25 17:57:40',2,'2024-06-25 19:49:21'),(228,'职务管理',2,45,2,'/organization/position','/system/position/position-list.vue',1,NULL,NULL,'ApartmentOutlined',NULL,0,NULL,1,1,0,0,1,'2024-06-29 11:11:09',1,'2024-07-02 20:15:11'),(229,'查询任务',3,221,NULL,NULL,NULL,1,'support:job:query','support:job:query',NULL,221,0,NULL,1,1,0,0,2,'2024-06-29 11:14:15',2,'2024-06-29 11:15:00'),(230,'更新任务',3,221,NULL,NULL,NULL,1,'support:job:update','support:job:update',NULL,221,0,NULL,1,1,0,0,2,'2024-06-29 11:15:40',NULL,'2024-06-29 11:15:40'),(231,'执行任务',3,221,NULL,NULL,NULL,1,'support:job:execute','support:job:execute',NULL,221,0,NULL,1,1,0,0,2,'2024-06-29 11:16:03',NULL,'2024-06-29 11:16:03'),(232,'查询记录',3,221,NULL,NULL,NULL,1,'support:job:log:query','support:job:log:query',NULL,221,0,NULL,1,1,0,0,2,'2024-06-29 11:16:37',NULL,'2024-06-29 11:16:37'),(233,'knife4j文档',2,218,4,'/knife4j',NULL,1,NULL,NULL,'FileWordOutlined',NULL,1,'http://localhost:1024/doc.html',1,1,0,0,1,'2024-07-02 20:23:50',1,'2024-07-08 13:49:15'),(234,'swagger文档',2,218,5,'/swagger','http://localhost:1024/swagger-ui/index.html',1,NULL,NULL,'ApiOutlined',NULL,1,'http://localhost:1024/swagger-ui/index.html',1,1,0,0,1,'2024-07-02 20:35:43',1,'2024-07-08 13:49:26'),(250,'三级等保设置',2,213,1,'/support/level3protect/level3-protect-config-index','/support/level3protect/level3-protect-config-index.vue',1,NULL,NULL,'SafetyOutlined',NULL,0,NULL,1,1,0,0,44,'2024-08-13 11:41:02',44,'2024-08-13 11:58:12'),(251,'敏感数据脱敏',2,213,3,'/support/level3protect/data-masking-list','/support/level3protect/data-masking-list.vue',1,NULL,NULL,'FileProtectOutlined',NULL,0,NULL,1,1,0,0,44,'2024-08-13 11:58:00',44,'2024-08-13 11:59:49'),(252,'启用/禁用',3,110,NULL,NULL,NULL,1,'support:dict:updateDisabled','support:dict:updateDisabled',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:44:12',1,'2025-04-08 19:46:03'),(253,'查询字典数据',3,110,NULL,NULL,NULL,1,'support:dictData:query','support:dictData:query',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:46:47',NULL,'2025-04-08 19:46:47'),(254,'添加字典数据',3,110,NULL,NULL,NULL,1,'support:dictData:add','support:dictData:add',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:48:00',NULL,'2025-04-08 19:48:00'),(255,'更新字典数据',3,110,NULL,NULL,NULL,1,'support:dictData:update','support:dictData:update',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:48:19',NULL,'2025-04-08 19:48:19'),(256,'删除字典数据',3,110,NULL,NULL,NULL,1,'support:dictData:delete','support:dictData:delete',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:48:38',NULL,'2025-04-08 19:48:38'),(257,'启用/禁用字典数据',3,110,NULL,NULL,NULL,1,'support:dictData:updateDisabled','support:dictData:updateDisabled',NULL,110,0,NULL,0,1,0,0,1,'2025-04-08 19:48:57',NULL,'2025-04-08 19:48:57'),(258,'查询企业员工',3,145,NULL,NULL,NULL,1,'oa:enterprise:queryEmployee','oa:enterprise:queryEmployee',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:11:46',75,'2025-04-08 21:12:24'),(259,'查询银行信息',3,145,NULL,NULL,NULL,1,'oa:bank:query','oa:bank:query',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:12:40',NULL,'2025-04-08 21:12:40'),(260,'查询发票信息',3,145,NULL,NULL,NULL,1,'oa:invoice:query','oa:invoice:query',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:12:56',NULL,'2025-04-08 21:12:56'),(261,'添加企业员工',3,145,NULL,NULL,NULL,1,'oa:enterprise:addEmployee','oa:enterprise:addEmployee',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:35:34',NULL,'2025-04-08 21:35:34'),(262,'删除企业员工',3,145,NULL,NULL,NULL,1,'oa:enterprise:deleteEmployee','oa:enterprise:deleteEmployee',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:40:17',NULL,'2025-04-08 21:40:17'),(263,'添加银行信息',3,145,NULL,NULL,NULL,1,'oa:bank:add','oa:bank:add',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:45:44',NULL,'2025-04-08 21:45:44'),(264,'更新银行信息',3,145,NULL,NULL,NULL,1,'oa:bank:update','oa:bank:update',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:46:02',NULL,'2025-04-08 21:46:02'),(265,'删除银行信息',3,145,NULL,NULL,NULL,1,'oa:bank:delete','oa:bank:delete',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:46:12',NULL,'2025-04-08 21:46:12'),(266,'添加发票信息',3,145,NULL,NULL,NULL,1,'oa:invoice:add','oa:invoice:add',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:46:30',NULL,'2025-04-08 21:46:30'),(267,'更新发票信息',3,145,NULL,NULL,NULL,1,'oa:invoice:update','oa:invoice:update',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:46:47',NULL,'2025-04-08 21:46:47'),(268,'删除发票信息',3,145,NULL,NULL,NULL,1,'oa:invoice:delete','oa:invoice:delete',NULL,145,0,NULL,0,1,0,0,75,'2025-04-08 21:46:59',NULL,'2025-04-08 21:46:59'),(300,'消息管理',2,50,30,'/message','/support/message/message-list.vue',1,NULL,NULL,'MailOutlined',NULL,0,NULL,0,1,0,0,1,'2025-04-09 14:30:04',1,'2025-04-10 20:19:36'),(301,'添加/编辑',2,47,1,'/goodsAddEdit','/business/erp/goods/goods-add-edit.vue',1,'goods:addEdit','goods:addEdit','LinkOutlined',47,0,NULL,1,0,0,0,1,'2025-06-26 00:13:19',1,'2025-06-26 11:40:38'),(302,'钱包管理',2,45,5,'/erp/wallets/list','/business/erp/wallets/wallets-list.vue',1,'wallets:list','wallets:list','LinkOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-27 12:31:41',1,'2025-06-27 12:33:21'),(303,'收货地址',2,45,4,'/user-address/list','/business/erp/user-address/user-address-list.vue',1,NULL,NULL,'CarOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-28 15:17:33',1,'2025-07-01 08:33:02'),(304,'查询',3,303,NULL,NULL,NULL,1,'userAddress:query','userAddress:query',NULL,303,0,NULL,0,1,0,0,1,'2025-06-28 15:17:33',NULL,'2025-06-28 15:17:33'),(305,'添加',3,303,NULL,NULL,NULL,1,'userAddress:add','userAddress:add',NULL,303,0,NULL,0,1,0,0,1,'2025-06-28 15:17:33',NULL,'2025-06-28 15:17:33'),(306,'更新',3,303,NULL,NULL,NULL,1,'userAddress:update','userAddress:update',NULL,303,0,NULL,0,1,0,0,1,'2025-06-28 15:17:33',NULL,'2025-06-28 15:17:33'),(307,'删除',3,303,NULL,NULL,NULL,1,'userAddress:delete','userAddress:delete',NULL,303,0,NULL,0,1,0,0,1,'2025-06-28 15:17:33',NULL,'2025-06-28 15:17:33'),(308,'APP客户端',3,0,101,NULL,NULL,1,'app:client','app:client',NULL,NULL,0,NULL,0,1,0,0,1,'2025-06-28 16:35:43',1,'2025-06-28 16:37:04'),(309,'抽奖活动',2,48,NULL,'/activities/list','/business/erp/activities/activities-list.vue',1,NULL,NULL,'ChromeOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-28 19:46:32',1,'2025-06-28 23:31:56'),(310,'查询',3,309,NULL,NULL,NULL,1,'activities:query','activities:query',NULL,309,0,NULL,0,1,0,0,1,'2025-06-28 19:46:32',NULL,'2025-06-28 19:46:32'),(311,'添加',3,309,NULL,NULL,NULL,1,'activities:add','activities:add',NULL,309,0,NULL,0,1,0,0,1,'2025-06-28 19:46:32',NULL,'2025-06-28 19:46:32'),(312,'更新',3,309,NULL,NULL,NULL,1,'activities:update','activities:update',NULL,309,0,NULL,0,1,0,0,1,'2025-06-28 19:46:32',NULL,'2025-06-28 19:46:32'),(313,'删除',3,309,NULL,NULL,NULL,1,'activities:delete','activities:delete',NULL,309,0,NULL,0,1,0,0,1,'2025-06-28 19:46:32',NULL,'2025-06-28 19:46:32'),(314,'订单管理',1,0,NULL,NULL,NULL,1,NULL,NULL,'BarsOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-29 16:38:33',NULL,'2025-06-29 16:38:33'),(316,'订单列表',2,314,NULL,'/orders/list','/business/erp/orders/orders-list.vue',1,'orders:list','orders:list','BarsOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-29 16:43:13',1,'2025-06-30 22:43:41'),(317,'查询',3,316,NULL,NULL,NULL,1,'orders:query','orders:query',NULL,316,0,NULL,0,1,0,0,1,'2025-06-29 16:43:13',NULL,'2025-06-29 16:43:13'),(318,'添加',3,316,NULL,NULL,NULL,1,'orders:add','orders:add',NULL,316,0,NULL,0,1,0,0,1,'2025-06-29 16:43:13',NULL,'2025-06-29 16:43:13'),(319,'更新',3,316,NULL,NULL,NULL,1,'orders:update','orders:update',NULL,316,0,NULL,0,1,0,0,1,'2025-06-29 16:43:13',NULL,'2025-06-29 16:43:13'),(320,'删除',3,316,NULL,NULL,NULL,1,'orders:delete','orders:delete',NULL,316,0,NULL,0,1,0,0,1,'2025-06-29 16:43:13',NULL,'2025-06-29 16:43:13'),(321,'邀请记录',2,45,10,'/invitation-records/list','/business/erp/invitation-records/invitation-records-list.vue',1,NULL,NULL,'UsbOutlined',NULL,0,NULL,0,1,0,0,1,'2025-06-30 22:38:44',1,'2025-06-30 22:44:57'),(322,'查询',3,321,NULL,NULL,NULL,1,'invitationRecords:query','invitationRecords:query',NULL,321,0,NULL,0,1,0,0,1,'2025-06-30 22:38:44',NULL,'2025-06-30 22:38:44'),(323,'添加',3,321,NULL,NULL,NULL,1,'invitationRecords:add','invitationRecords:add',NULL,321,0,NULL,0,1,0,0,1,'2025-06-30 22:38:44',NULL,'2025-06-30 22:38:44'),(324,'更新',3,321,NULL,NULL,NULL,1,'invitationRecords:update','invitationRecords:update',NULL,321,0,NULL,0,1,0,0,1,'2025-06-30 22:38:44',NULL,'2025-06-30 22:38:44'),(325,'删除',3,321,NULL,NULL,NULL,1,'invitationRecords:delete','invitationRecords:delete',NULL,321,0,NULL,0,1,0,0,1,'2025-06-30 22:38:44',NULL,'2025-06-30 22:38:44'),(326,'提现申请',2,45,8,'/withdrawals/list','/business/erp/withdrawals/withdrawals-list.vue',1,NULL,NULL,'DollarOutlined',NULL,0,NULL,0,1,0,0,1,'2025-07-01 08:29:44',1,'2025-07-01 08:31:45'),(327,'查询',3,326,NULL,NULL,NULL,1,'withdrawals:query','withdrawals:query',NULL,326,0,NULL,0,1,0,0,1,'2025-07-01 08:29:44',NULL,'2025-07-01 08:29:44'),(328,'添加',3,326,NULL,NULL,NULL,1,'withdrawals:add','withdrawals:add',NULL,326,0,NULL,0,1,0,0,1,'2025-07-01 08:29:44',NULL,'2025-07-01 08:29:44'),(329,'更新',3,326,NULL,NULL,NULL,1,'withdrawals:update','withdrawals:update',NULL,326,0,NULL,0,1,0,0,1,'2025-07-01 08:29:44',NULL,'2025-07-01 08:29:44'),(330,'删除',3,326,NULL,NULL,NULL,1,'withdrawals:delete','withdrawals:delete',NULL,326,0,NULL,0,1,0,0,1,'2025-07-01 08:29:44',NULL,'2025-07-01 08:29:44'),(331,'团队奖励',2,45,8,'/team-rewards/list','/business/erp/team-rewards/team-rewards-list.vue',1,NULL,NULL,'DollarOutlined',NULL,0,NULL,0,1,0,0,1,'2025-07-01 09:07:14',1,'2025-07-01 09:13:03'),(332,'查询',3,331,NULL,NULL,NULL,1,'teamRewards:query','teamRewards:query',NULL,331,0,NULL,0,1,0,0,1,'2025-07-01 09:07:14',NULL,'2025-07-01 09:07:14'),(333,'添加',3,331,NULL,NULL,NULL,1,'teamRewards:add','teamRewards:add',NULL,331,0,NULL,0,1,0,0,1,'2025-07-01 09:07:14',NULL,'2025-07-01 09:07:14'),(334,'更新',3,331,NULL,NULL,NULL,1,'teamRewards:update','teamRewards:update',NULL,331,0,NULL,0,1,0,0,1,'2025-07-01 09:07:14',NULL,'2025-07-01 09:07:14'),(335,'删除',3,331,NULL,NULL,NULL,1,'teamRewards:delete','teamRewards:delete',NULL,331,0,NULL,0,1,0,0,1,'2025-07-01 09:07:14',NULL,'2025-07-01 09:07:14'),(336,'横幅管理',2,138,NULL,'/banners/list','/business/oa/banners/banners-list.vue',1,NULL,NULL,'AntDesignOutlined',NULL,0,NULL,0,1,0,0,1,'2025-07-01 12:20:05',1,'2025-07-01 12:21:29'),(337,'查询',3,336,NULL,NULL,NULL,1,'banners:query','banners:query',NULL,336,0,NULL,0,1,0,0,1,'2025-07-01 12:20:05',NULL,'2025-07-01 12:20:05'),(338,'添加',3,336,NULL,NULL,NULL,1,'banners:add','banners:add',NULL,336,0,NULL,0,1,0,0,1,'2025-07-01 12:20:05',NULL,'2025-07-01 12:20:05'),(339,'更新',3,336,NULL,NULL,NULL,1,'banners:update','banners:update',NULL,336,0,NULL,0,1,0,0,1,'2025-07-01 12:20:05',NULL,'2025-07-01 12:20:05'),(340,'删除',3,336,NULL,NULL,NULL,1,'banners:delete','banners:delete',NULL,336,0,NULL,0,1,0,0,1,'2025-07-01 12:20:05',NULL,'2025-07-01 12:20:05'),(341,'弹窗管理',2,138,NULL,'/popups/list','/business/oa/popups/popups-list.vue',1,NULL,NULL,'AntDesignOutlined',NULL,0,NULL,0,1,0,0,1,'2025-07-01 13:25:29',NULL,'2025-07-01 13:26:01'),(342,'查询',3,341,NULL,NULL,NULL,1,'popups:query','popups:query',NULL,341,0,NULL,0,1,0,0,1,'2025-07-01 13:25:29',NULL,'2025-07-01 13:25:29'),(343,'添加',3,341,NULL,NULL,NULL,1,'popups:add','popups:add',NULL,341,0,NULL,0,1,0,0,1,'2025-07-01 13:25:29',NULL,'2025-07-01 13:25:29'),(344,'更新',3,341,NULL,NULL,NULL,1,'popups:update','popups:update',NULL,341,0,NULL,0,1,0,0,1,'2025-07-01 13:25:29',NULL,'2025-07-01 13:25:29'),(345,'删除',3,341,NULL,NULL,NULL,1,'popups:delete','popups:delete',NULL,341,0,NULL,0,1,0,0,1,'2025-07-01 13:25:29',NULL,'2025-07-01 13:25:29');
/*!40000 ALTER TABLE `t_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_message`
--

DROP TABLE IF EXISTS `t_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_message` (
  `message_id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息id',
  `message_type` smallint NOT NULL COMMENT '消息类型',
  `receiver_user_type` int NOT NULL COMMENT '接收者用户类型',
  `receiver_user_id` bigint NOT NULL COMMENT '接收者用户id',
  `data_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '相关数据id',
  `title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `read_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读',
  `read_time` datetime DEFAULT NULL COMMENT '已读时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`) USING BTREE,
  KEY `idx_msg` (`message_type`,`receiver_user_type`,`receiver_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知消息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_message`
--

LOCK TABLES `t_message` WRITE;
/*!40000 ALTER TABLE `t_message` DISABLE KEYS */;
INSERT INTO `t_message` VALUES (1,1,1,1,'null','张三的对公付款单 【3000元】','尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市',0,'2024-09-02 23:00:54','2024-06-27 01:14:07','2024-09-03 20:44:19'),(2,2,1,1,'234','刘备的请假单【本周四】','尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市',1,'2025-06-25 16:53:39','2024-07-04 16:09:49','2025-06-25 16:53:39'),(3,1,1,1,'23','武松的物资采购单【Macbook Pro】','尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市',0,'2024-09-02 23:00:36','2024-07-07 22:03:14','2024-09-03 20:44:21'),(4,1,1,1,'23','孙悟空的出差申请【出差洛阳】','尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市',1,'2025-06-25 16:53:31','2024-07-07 22:03:14','2025-06-25 16:53:31');
/*!40000 ALTER TABLE `t_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_notice`
--

DROP TABLE IF EXISTS `t_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_notice` (
  `notice_id` bigint NOT NULL AUTO_INCREMENT,
  `notice_type_id` bigint NOT NULL COMMENT '类型1公告 2动态',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `all_visible_flag` tinyint(1) NOT NULL COMMENT '是否全部可见',
  `scheduled_publish_flag` tinyint(1) NOT NULL COMMENT '是否定时发布',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `content_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文本内容',
  `content_html` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'html内容',
  `attachment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件',
  `page_view_count` int NOT NULL DEFAULT '0' COMMENT '页面浏览量，传说中的pv',
  `user_view_count` int NOT NULL DEFAULT '0' COMMENT '用户浏览量，传说中的uv',
  `source` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源',
  `author` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
  `document_number` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文号，如：1024创新实验室发〔2022〕字第36号',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_notice`
--

LOCK TABLES `t_notice` WRITE;
/*!40000 ALTER TABLE `t_notice` DISABLE KEYS */;
INSERT INTO `t_notice` VALUES (49,1,'Spring Boot 3.0.0 首个 RC 发布',1,0,'2024-01-01 20:22:23','Spring Boot 3.0.0 首个 RC 发布\nSpring Boot 3.0 首个 RC 已发布，此外还为两个分支发布了更新：2.7.5 & 2.6.13。\n3.0.0-RC1\n发布公告写道，此版本包含 135 项功能增强、文档改进、依赖升级和 Bugfix。\nSpring Boot 3.0 的开发工作始于实验性的 Spring Native，旨在为 GraalVM 原生镜像提供支持。在该版本中，开发者现在可以使用标准 Spring Boot Maven 或 Gradle 插件将 Spring Boot 应用程序转换为原生可执行文件，而无需任何特殊配置。\n此版本还在参考文档中添加新内容来解释 AOT 处理背后的概念以及如何开始生成第一个 GraalVM 原生镜像。\n除此之外，Spring Boot 3.0 还完成了迁移到 JakartaEE 9 的工作，以及将使用的 Java 版本升级到 Java 17。\n其他新特性：\n为 Spring Data JDBC 提供更灵活的自动配置为 Prometheus 示例提供自动配置增强 Log4j2 功能，包括配置文件支持和环境属性查找\n详情查看 Release Note。\nSpring Boot 2.7.5 和 2.6.13 的更新内容主要是修复错误，优化文档和升级依赖，详情查看 Release Note (2.7.5、2.6.13)。\n相关链接\nSpring Boot 的详细介绍：点击查看Spring Boot 的下载地址：点击下载','<h1 style=\"text-indent: 0px; text-align: start;\"><a href=\"https://www.oschina.net/news/214401/spring-boot-3-0-0-rc1-released\" target=\"_blank\">Spring&nbsp;Boot&nbsp;3.0.0&nbsp;首个&nbsp;RC&nbsp;发布</a></h1><p>Spring&nbsp;Boot&nbsp;3.0 首个&nbsp;RC 已发布，此外还为两个分支发布了更新：2.7.5 & 2.6.13。</p><p>3.0.0-RC1</p><p>发布公告写道，此版本包含 135&nbsp;项功能增强、文档改进、依赖升级和&nbsp;Bugfix。</p><p>Spring&nbsp;Boot&nbsp;3.0&nbsp;的开发工作始于实验性的&nbsp;Spring&nbsp;Native，旨在为&nbsp;GraalVM&nbsp;原生镜像提供支持。在该版本中，开发者现在可以使用标准&nbsp;Spring&nbsp;Boot&nbsp;Maven&nbsp;或&nbsp;Gradle&nbsp;插件将&nbsp;Spring&nbsp;Boot&nbsp;应用程序转换为原生可执行文件，而无需任何特殊配置。</p><p>此版本还在参考文档中添加新内容来解释 AOT&nbsp;处理背后的概念以及如何开始生成第一个&nbsp;GraalVM&nbsp;原生镜像。</p><p>除此之外，Spring&nbsp;Boot&nbsp;3.0&nbsp;还完成了迁移到 JakartaEE&nbsp;9&nbsp;的工作，以及将使用的&nbsp;Java&nbsp;版本升级到&nbsp;Java&nbsp;17。</p><p>其他新特性：</p><p>为&nbsp;Spring&nbsp;Data&nbsp;JDBC&nbsp;提供更灵活的自动配置为&nbsp;Prometheus&nbsp;示例提供自动配置增强&nbsp;Log4j2&nbsp;功能，包括配置文件支持和环境属性查找</p><p>详情查看&nbsp;Release&nbsp;Note。</p><p>Spring&nbsp;Boot&nbsp;2.7.5&nbsp;和&nbsp;2.6.13&nbsp;的更新内容主要是修复错误，优化文档和升级依赖，详情查看&nbsp;Release&nbsp;Note&nbsp;(2.7.5、2.6.13)。</p><p>相关链接</p><p>Spring&nbsp;Boot&nbsp;的详细介绍：点击查看Spring&nbsp;Boot&nbsp;的下载地址：点击下载</p>','',0,0,'开源中国','卓大',NULL,0,1,'2024-03-02 18:53:26','2022-10-22 14:27:33'),(50,1,'Oracle 推出 JDK 8 的直接替代品',1,0,'2024-01-01 20:22:23','Oracle 推出 JDK 8 的直接替代品\n来源: OSCHINA\n编辑: 白开水不加糖\n2022-10-20 08:14:29\n 0\n为了向传统的 Java 8 服务器工作负载提供 Java 17 级别的性能，Oracle 宣布推出 Java SE Subscription Enterprise Performance Pack (Enterprise Performance Pack)。并声称这是 JDK 8 的直接替代品，现已在 MyOracleSupport 上面向所有 Java SE 订阅客户和 Oracle 云基础设施 (OCI) 用户免费提供。\n“Enterprise Performance Pack 为 JDK 8 用户提供了在 JDK 8 和 JDK 17 发布之间的 7 年时间里，为 Java 带来的重大内存管理和性能改进。这些改进包括：现代垃圾回收算法、紧凑字符串、增强的可观察性和数十种其他优化。”\nJava 8 发布于 2014 年，和 Java 17 一样都是长期支持 (LTS) 版本；尽管发布距今已有近九年的历史，但仍被很多开发人员和组织所广泛应用。New Relic 发布的一份 “2022 年 Java 生态系统状况报告” 数据表明，Java 8 仍被 46.45% 的 Java 应用程序在生产中使用。\n根据介绍，Enterprise Performance Pack 在 Intel 和基于 Arm 的系统（如 Ampere Altra）上支持 headless Linux 64 位工作负载。\nOracle 方面称，使用 Enterprise Performance Pack 的客户将可以立即看到以或接近内存或 CPU 容量运行的 JDK 8 工作负载的好处。在 Oracle 自己的产品和云服务进行的测试表明，高负载应用程序的内存和性能都提高了大约 40%。即使没有接近容量运行的 JDK 8 应用程序，也可以会看到高达 5% 的性能提升。\n虽然 Enterprise Performance Pack 中包含的许多改进可以通过默认选项获得，但 Oracle 建议用户还是自己研究文档，以最大限度地提高性能并最大限度地降低内存使用率。例如，通过启用可扩展的低延迟 ZGC 垃圾收集器来提高应用程序响应能力，需要通过 -XX:+UseZGC 选项。','<h3>Oracle&nbsp;推出&nbsp;JDK&nbsp;8&nbsp;的直接替代品</h3><p>来源:&nbsp;OSCHINA</p><p>编辑: 白开水不加糖</p><p>2022-10-20&nbsp;08:14:29</p><p> 0</p><p>为了向传统的&nbsp;Java&nbsp;8&nbsp;服务器工作负载提供&nbsp;Java&nbsp;17&nbsp;级别的性能，Oracle 宣布推出&nbsp;Java&nbsp;SE&nbsp;Subscription&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;(Enterprise&nbsp;Performance&nbsp;Pack)。并声称这是 JDK&nbsp;8&nbsp;的直接替代品，现已在 MyOracleSupport 上面向所有&nbsp;Java&nbsp;SE&nbsp;订阅客户和&nbsp;Oracle&nbsp;云基础设施&nbsp;(OCI)&nbsp;用户免费提供。</p><p>“Enterprise&nbsp;Performance&nbsp;Pack&nbsp;为&nbsp;JDK&nbsp;8&nbsp;用户提供了在&nbsp;JDK&nbsp;8&nbsp;和&nbsp;JDK&nbsp;17&nbsp;发布之间的&nbsp;7&nbsp;年时间里，为&nbsp;Java&nbsp;带来的重大内存管理和性能改进。这些改进包括：现代垃圾回收算法、紧凑字符串、增强的可观察性和数十种其他优化。”</p><p>Java&nbsp;8&nbsp;发布于&nbsp;2014&nbsp;年，和&nbsp;Java&nbsp;17&nbsp;一样都是长期支持&nbsp;(LTS)&nbsp;版本；尽管发布距今已有近九年的历史，但仍被很多开发人员和组织所广泛应用。New&nbsp;Relic&nbsp;发布的一份 “2022&nbsp;年&nbsp;Java&nbsp;生态系统状况报告”&nbsp;数据表明，Java&nbsp;8&nbsp;仍被&nbsp;46.45%&nbsp;的&nbsp;Java&nbsp;应用程序在生产中使用。</p><p>根据介绍，Enterprise&nbsp;Performance&nbsp;Pack&nbsp;在&nbsp;Intel&nbsp;和基于&nbsp;Arm&nbsp;的系统（如&nbsp;Ampere&nbsp;Altra）上支持 headless&nbsp;Linux&nbsp;64&nbsp;位工作负载。</p><p>Oracle 方面称，使用&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;的客户将可以立即看到以或接近内存或&nbsp;CPU&nbsp;容量运行的&nbsp;JDK&nbsp;8&nbsp;工作负载的好处。在&nbsp;Oracle&nbsp;自己的产品和云服务进行的测试表明，高负载应用程序的内存和性能都提高了大约&nbsp;40%。即使没有接近容量运行的&nbsp;JDK&nbsp;8&nbsp;应用程序，也可以会看到高达&nbsp;5%&nbsp;的性能提升。</p><p>虽然&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;中包含的许多改进可以通过默认选项获得，但 Oracle 建议用户还是自己研究文档，以最大限度地提高性能并最大限度地降低内存使用率。例如，通过启用可扩展的低延迟&nbsp;ZGC&nbsp;垃圾收集器来提高应用程序响应能力，需要通过&nbsp;-XX:+UseZGC&nbsp;选项。</p>','',0,0,'OSChina','卓大',NULL,0,1,'2024-01-08 19:02:12','2022-10-22 14:29:56'),(51,1,'Spring Framework 6.0.0 RC2 发布',1,0,'2024-01-01 20:22:23','Spring Framework 6.0.0 RC2 发布\nSpring Framework 6.0.0 发布了第二个 RC 版本。\n新特性\n确保可以在构建时评估 classpath 检查 #29352为 JPA 持久化回调引入 Register 反射提示 #29348检查 @RegisterReflectionForBinding 是否至少指定一个类 #29346为 AOT 引擎设置引入 builder API #29341支持检测正在进行的 AOT 处理 #29340重新组织 HTTP Observation 类型 #29334支持在没有 java.beans.Introspector 的前提下，执行基本属性判断 #29320为BindingReflectionHintsRegistrar 添加 Kotlin 数据类组件支持 #29316将 HttpServiceFactory 和 RSocketServiceProxyFactory 切换到 builder 模型，以便优先进行可编程配置 #29296引入基于 GraalVM FieldValueTransformer API 的 PreComputeFieldFeature#29081在 TestContext 框架中引入 SPI 来处理 ApplicationContext 故障 #28826SimpleEvaluationContext 支持禁用 array 分配 #28808DateTimeFormatterRegistrar 支持默认回退到 ISO 解析 #26985\nSpring Framework 6.0 作为重大更新，要求使用 Java 17 或更高版本，并且已迁移到 Jakarta EE 9+（在 jakarta 命名空间中取代了以前基于 javax 的 EE API），以及对其他基础设施的修改。基于这些变化，Spring Framework 6.0 支持最新 Web 容器，如 Tomcat 10 / Jetty 11，以及最新的持久性框架 Hibernate ORM 6.1。这些特性仅可用于 Servlet API 和 JPA 的 jakarta 命名空间变体。\n值得一提的是，开发者可通过此版本在基于 Spring 的应用中体验 “虚拟线程”（JDK 19 中的预览版 “Project Loom”），查看此文章了解更多细节。现在提供了自定义选项来插入基于虚拟线程的 Executor 实现，目标是在 Project Loom 正式可用时提供 “一等公民” 的配置选项。\n除了上述的变化，Spring Framework 6.0 还包含许多其他改进和特性，例如：\n提供基于 @HttpExchange 服务接口的 HTTP 接口客户端对 RFC 7807 问题详细信息的支持Spring HTTP 客户端提供基于 Micrometer 的可观察性……\n详情查看 Release Note。\n按照发布计划，Spring Framework 6.0 将于 11 月正式 GA。','<h1 style=\"text-indent: 0px; text-align: start;\"><a href=\"https://www.oschina.net/news/214472/spring-framework-6-0-0-rc2-released\" target=\"_blank\">Spring&nbsp;Framework&nbsp;6.0.0&nbsp;RC2&nbsp;发布</a></h1><p style=\"text-indent: 0px; text-align: left;\">Spring&nbsp;Framework&nbsp;6.0.0&nbsp;发布了<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fspring.io%2Fblog%2F2022%2F10%2F20%2Fspring-framework-6-0-0-rc2-available-now\" target=\"_blank\">第二个&nbsp;RC&nbsp;版本</a>。</p><p style=\"text-indent: 0px; text-align: left;\"><strong>新特性</strong></p><ul style=\"text-indent: 0px; text-align: left;\"><li>确保可以在构建时评估&nbsp;classpath&nbsp;检查&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29352\" target=\"_blank\">#29352</a></li><li>为&nbsp;JPA&nbsp;持久化回调引入&nbsp;Register&nbsp;反射提示&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29348\" target=\"_blank\">#29348</a></li><li>检查&nbsp;<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>@RegisterReflectionForBinding</code></span>&nbsp;是否至少指定一个类&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29346\" target=\"_blank\">#29346</a></li><li>为&nbsp;AOT&nbsp;引擎设置引入&nbsp;builder&nbsp;API&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29341\" target=\"_blank\">#29341</a></li><li>支持检测正在进行的&nbsp;AOT&nbsp;处理&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29340\" target=\"_blank\">#29340</a></li><li>重新组织&nbsp;HTTP&nbsp;Observation&nbsp;类型&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29334\" target=\"_blank\">#29334</a></li><li>支持在没有&nbsp;java.beans.Introspector&nbsp;的前提下，执行基本属性判断&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29320\" target=\"_blank\">#29320</a></li><li>为<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>BindingReflectionHintsRegistrar</code></span>&nbsp;添加&nbsp;Kotlin&nbsp;数据类组件支持&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29316\" target=\"_blank\">#29316</a></li><li>将&nbsp;HttpServiceFactory&nbsp;和&nbsp;RSocketServiceProxyFactory&nbsp;切换到&nbsp;builder&nbsp;模型，以便优先进行可编程配置&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29296\" target=\"_blank\">#29296</a></li><li>引入基于&nbsp;GraalVM&nbsp;<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>FieldValueTransformer</code></span>&nbsp;API&nbsp;的&nbsp;<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>PreComputeFieldFeature</code></span><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29081\" target=\"_blank\">#29081</a></li><li>在&nbsp;TestContext&nbsp;框架中引入&nbsp;SPI&nbsp;来处理&nbsp;ApplicationContext&nbsp;故障&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F28826\" target=\"_blank\">#28826</a></li><li>SimpleEvaluationContext&nbsp;支持禁用&nbsp;array&nbsp;分配&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F28808\" target=\"_blank\">#28808</a></li><li>DateTimeFormatterRegistrar&nbsp;支持默认回退到&nbsp;ISO&nbsp;解析&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F26985\" target=\"_blank\">#26985</a></li></ul><p style=\"text-indent: 0px; text-align: left;\"><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">Spring&nbsp;Framework&nbsp;6.0&nbsp;作为重大更新，要求</span><span style=\"color: rgb(51, 51, 51);\"><strong>使用&nbsp;Java&nbsp;17&nbsp;或更高版本</strong></span><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">，并且已迁移到&nbsp;Jakarta&nbsp;EE&nbsp;9+（在&nbsp;</span><span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>jakarta</code></span><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">&nbsp;命名空间中取代了以前基于&nbsp;</span><span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>javax</code></span><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">&nbsp;的&nbsp;EE&nbsp;API），以及对其他基础设施的修改。基于这些变化，Spring&nbsp;Framework&nbsp;6.0&nbsp;支持最新&nbsp;Web&nbsp;容器，如&nbsp;</span><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Ftomcat.apache.org%2Fwhichversion.html\" target=\"_blank\">Tomcat&nbsp;10</a><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">&nbsp;/&nbsp;</span><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.eclipse.org%2Fjetty%2Fdownload.php\" target=\"_blank\">Jetty&nbsp;11</a><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">，以及最新的持久性框架&nbsp;</span><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fhibernate.org%2Form%2Freleases%2F6.1%2F\" target=\"_blank\">Hibernate&nbsp;ORM&nbsp;6.1</a><span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">。这些特性仅可用于&nbsp;Servlet&nbsp;API&nbsp;和&nbsp;JPA&nbsp;的&nbsp;jakarta&nbsp;命名空间变体。</span></p><p style=\"text-indent: 0px; text-align: left;\">值得一提的是，开发者可通过此版本在基于&nbsp;Spring&nbsp;的应用中体验&nbsp;“虚拟线程”（JDK&nbsp;19&nbsp;中的预览版&nbsp;“Project&nbsp;Loom”），<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fspring.io%2Fblog%2F2022%2F10%2F11%2Fembracing-virtual-threads\" target=\"_blank\">查看此文章</a>了解更多细节。现在提供了自定义选项来插入基于虚拟线程的&nbsp;<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>Executor</code></span>&nbsp;实现，目标是在&nbsp;Project&nbsp;Loom&nbsp;正式可用时提供&nbsp;“一等公民”&nbsp;的配置选项。</p><p style=\"text-indent: 0px; text-align: left;\">除了上述的变化，Spring&nbsp;Framework&nbsp;6.0&nbsp;还包含许多其他改进和特性，例如：</p><ul style=\"text-indent: 0px; text-align: left;\"><li>提供基于&nbsp;<span style=\"color: rgb(51, 51, 51); font-size: 13px;\"><code>@HttpExchange</code></span>&nbsp;服务接口的&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdocs.spring.io%2Fspring-framework%2Fdocs%2F6.0.0-RC1%2Freference%2Fhtml%2Fintegration.html%23rest-http-interface\" target=\"_blank\">HTTP&nbsp;接口客户端</a></li><li>对&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdocs.spring.io%2Fspring-framework%2Fdocs%2F6.0.0-RC1%2Freference%2Fhtml%2Fweb.html%23mvc-ann-rest-exceptions\" target=\"_blank\">RFC&nbsp;7807&nbsp;问题详细信息</a>的支持</li><li>Spring&nbsp;HTTP&nbsp;客户端提供基于&nbsp;Micrometer&nbsp;的可观察性</li><li>……</li></ul><p style=\"text-indent: 0px; text-align: left;\"><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Freleases%2Ftag%2Fv6.0.0-RC2\" target=\"_blank\">详情查看&nbsp;Release&nbsp;Note</a>。</p><p style=\"text-indent: 0px; text-align: left;\">按照发布计划，Spring&nbsp;Framework&nbsp;6.0&nbsp;将于&nbsp;11&nbsp;月正式&nbsp;GA。</p>','',0,0,'CSDN','罗伊',NULL,0,1,'2024-01-08 19:02:12','2022-10-22 14:30:45'),(52,1,'Windows Terminal 正式成为 Windows 11 默认终端',1,0,'2024-01-01 20:22:23','今年 7 月 ，微软在 Windows 11 的 Beta 版本测试了将系统默认终端设置为 Windows Terminal 。如今该设置已登录稳定版本，从 Windows 11 22H2 版本开始，Windows Terminal 将正式成为 Windows 11 的默认设置。\n默认终端是在打开命令行应用程序时默认启动的终端模拟器。从 Windows 诞生之日起，其默认终端一直是 Windows 控制台主机 conhost.exe。此次更新则意味着，以后 Windows 11 的所有命令行应用程序都将在 Windows Terminal 中自动打开。\nWindows Terminal 拥有非常多现代化的功能，毕竟它很新（ 2019 年 5 月在 Microsoft Build 上首次发布），吸取了很多现代终端的灵感。它支持多选项卡和窗格、命令面板等现代化的 UI 和操作方式，以及大量的自定义选项，比如目录、配置文件图标、自定义背景图像、配色方案、字体和透明度。\n当然，如果不想用 Windows Terminal，用户也可以在 Windows 设置中的 隐私和安全 > 开发人员页面和 Windows 终端设置 中调整默认终端设置，（此更新使用 “让 Windows 决定” 作为默认选择，即默认采用 Windows Terminal） 。\n此外，如果在更新之前就已设置其他默认终端，此次更新不会覆盖你的偏好。\n关于 Windows 11 默认终端的更多详情可查看微软博客。','<p style=\"text-indent: 0px; text-align: left;\">今年&nbsp;7&nbsp;月&nbsp;，微软在&nbsp;Windows&nbsp;11&nbsp;的&nbsp;Beta&nbsp;版本<a href=\"https://www.oschina.net/news/204429/wt-default-terminal-in-win11-beta-channel\" target=\"\">测试</a>了将系统默认终端设置为&nbsp;Windows&nbsp;Terminal&nbsp;。如今该设置已登录稳定版本，从&nbsp;Windows&nbsp;11&nbsp;22H2&nbsp;版本开始，Windows&nbsp;Terminal&nbsp;将<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdevblogs.microsoft.com%2Fcommandline%2Fwindows-terminal-is-now-the-default-in-windows-11%2F\" target=\"_blank\">正式成为</a>&nbsp;Windows&nbsp;11&nbsp;的默认设置。</p><p style=\"text-indent: 0px; text-align: left;\">默认终端是在打开命令行应用程序时默认启动的终端模拟器。从&nbsp;Windows&nbsp;诞生之日起，其默认终端一直是&nbsp;Windows&nbsp;控制台主机&nbsp;conhost.exe。此次更新则意味着，以后&nbsp;Windows&nbsp;11&nbsp;的所有命令行应用程序都将在&nbsp;Windows&nbsp;Terminal&nbsp;中自动打开。</p><p style=\"text-indent: 0px; text-align: left;\">Windows&nbsp;Terminal&nbsp;拥有非常多现代化的功能，毕竟它<span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);\">很新（&nbsp;2019&nbsp;年&nbsp;5&nbsp;月在&nbsp;Microsoft&nbsp;Build&nbsp;上首次发布），吸取了很多现代终端的灵感。它支持多</span>选项卡和窗格、命令面板等现代化的&nbsp;UI&nbsp;和操作方式，以及大量的自定义选项，比如目录、配置文件图标、自定义背景图像、配色方案、字体和透明度。</p><p style=\"text-indent: 0px; text-align: left;\">当然，如果不想用&nbsp;Windows&nbsp;Terminal，用户也可以在&nbsp;Windows&nbsp;设置中的&nbsp;<em>隐私和安全&nbsp;&gt;&nbsp;开发人员页面和&nbsp;Windows&nbsp;终端设置&nbsp;</em>中调整默认终端设置，（此更新使用&nbsp;“让&nbsp;Windows&nbsp;决定”&nbsp;作为默认选择，即默认采用&nbsp;Windows&nbsp;Terminal）&nbsp;。</p><p style=\"text-indent: 0px; text-align: left;\">此外，如果在更新之前就已设置其他默认终端，此次更新<strong>不会覆盖</strong>你的偏好。</p><p style=\"text-indent: 0px; text-align: left;\">关于&nbsp;Windows&nbsp;11&nbsp;默认终端的更多详情可查看<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdevblogs.microsoft.com%2Fcommandline%2Fwindows-terminal-is-now-the-default-in-windows-11%2F\" target=\"_blank\">微软博客</a>。</p>','',0,0,'开源中国','善逸',NULL,0,1,'2024-01-08 19:02:12','2022-10-22 14:33:03'),(53,1,'TypeScript 诞生 10 周年',1,0,'2024-01-01 20:22:23','TypeScript 已经诞生 10 年了。10 年前 ——2012 年 10 月 1 日，TypeScript 首次公开亮相。当时主导 TypeScript 开发的 Anders Hejlsberg 这样描述 TypeScript：\n它是 JavaScript 的类型化超集，可被编译成常用的 JavaScript。TypeScript 还可以通过启用丰富的工具体验来极大地帮助提升生产力，与此同时开发者保持不变维护现有的代码，并继续使用喜爱的 JavaScript 库。TypeScript is a typed superset of JavaScript that compiles to idiomatic (normal) JavaScript, can dramatically improve your productivity by enabling rich tooling experiences, all while maintaining your existing code and continuing to use the same JavaScript libraries you already love.\n微软在博客中回顾了 TypeScript 刚亮相时受到的评价，大多数人对它都是持怀疑态度，毕竟这对于许多 JavaScript 开发者来说，试图将静态类型引入 JavaScript 是一个笑话 —— 或是邪恶的阴谋。反对者则直言这是十分愚蠢的想法，他们认为当时已存在可以编译为 JavaScript 的强类型语言，例如 C#、Java 和 C++。他们还吐槽主导 TypeScript 开发的 Anders Hejlsberg 对静态类型有 “迷之执着”。\n当时微软意识到 JavaScript 未来将会被应用到无数场景，而且他们公司内部团队在处理复杂的 JavaScript 代码库时面临着巨大的挑战，所以他们觉得有必要创造强大的工具来帮助编写 JavaScript—— 尤其是针对大型 JavaScript 项目。基于此需求，TypeScript 也确定了自己的定位和特性，它是 JavaScript 的超集，将类型检查和静态分析、显式接口和最佳实践结合到单一语言和编译器中。通过在 JavaScript 上构建，TypeScript 能够更接近目标运行时，同时仅添加支持大型应用程序和大型团队所需的语法糖。\n团队还坚持 TypeScript 要能够与现有的 JavaScript 无缝交互，与 JavaScript 共同进化，并且看上去也和 JavaScript 类似。\nTypeScript 诞生之初的部分设计目标：\n不会对已有的程序增加运行时开销与当前和未来的 ECMAScript 提案保持一致保留所有 JavaScript 代码的运行时行为避免添加表达式类型的语法 (expression-level syntax)使用一致、完全可擦除的结构化类型系统……\n这些目标指导着 TypeScript 的发展方向：关注类型系统，成为 JavaScript 的类型检查器，只添加类型检查所需的语法，避免添加新的运行时语法和行为。\n微软提到，TypeScript 拥有如今的繁荣生态离不开一个重要属性：开源。TypeScript 一开始就是免费且开源 —— 语言规范和编译器都是开源项目，并且以真正开放的方式来运作。事实上，微软当时对外展现出的姿态并不是现在的 “拥抱开源”，所以他们内部并没真正认识到 TypeScript 的开源是如何帮助它走向成功。因此有人认为，TypeScript 在很大程度上引导微软开始更多地转向开源。\n现在，TypeScript 仍在积极发展和迭代改进，并被全球数百万开发者使用。在诸多编程语言排名、指数或开发者调查中，TypeScript 一直位居前列，也是最受欢迎和最常用的编程语言。','<p style=\"text-indent: 0px; text-align: start;\">TypeScript&nbsp;已经诞生&nbsp;10&nbsp;年了。10&nbsp;年前&nbsp;——2012&nbsp;年&nbsp;10&nbsp;月&nbsp;1&nbsp;日，TypeScript&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fweb.archive.org%2Fweb%2F20121003001910%2Fhttps%3A%2F%2Fblogs.msdn.com%2Fb%2Fsomasegar%2Farchive%2F2012%2F10%2F01%2Ftypescript-javascript-development-at-application-scale.aspx\" target=\"_blank\"><strong>首次公开亮相</strong></a>。当时主导&nbsp;TypeScript&nbsp;开发的&nbsp;Anders&nbsp;Hejlsberg&nbsp;这样描述&nbsp;TypeScript：</p><blockquote style=\"text-indent: 0px; text-align: left;\">它是&nbsp;JavaScript&nbsp;的类型化超集，可被编译成常用的&nbsp;JavaScript。TypeScript&nbsp;还可以通过启用丰富的工具体验来极大地帮助提升生产力，与此同时开发者保持不变维护现有的代码，并继续使用喜爱的&nbsp;JavaScript&nbsp;库。TypeScript&nbsp;is&nbsp;a&nbsp;typed&nbsp;superset&nbsp;of&nbsp;JavaScript&nbsp;that&nbsp;compiles&nbsp;to&nbsp;idiomatic&nbsp;(normal)&nbsp;JavaScript,&nbsp;can&nbsp;dramatically&nbsp;improve&nbsp;your&nbsp;productivity&nbsp;by&nbsp;enabling&nbsp;rich&nbsp;tooling&nbsp;experiences,&nbsp;all&nbsp;while&nbsp;maintaining&nbsp;your&nbsp;existing&nbsp;code&nbsp;and&nbsp;continuing&nbsp;to&nbsp;use&nbsp;the&nbsp;same&nbsp;JavaScript&nbsp;libraries&nbsp;you&nbsp;already&nbsp;love.</blockquote><p style=\"text-indent: 0px; text-align: left;\">微软在博客中回顾了&nbsp;TypeScript&nbsp;刚亮相时受到的评价，大多数人对它都是持怀疑态度，毕竟这对于许多&nbsp;JavaScript&nbsp;开发者来说，试图将静态类型引入&nbsp;JavaScript&nbsp;是一个笑话&nbsp;——&nbsp;或是邪恶的阴谋。反对者则直言这是十分愚蠢的想法，他们认为当时已存在可以编译为&nbsp;JavaScript&nbsp;的强类型语言，例如&nbsp;C#、Java&nbsp;和&nbsp;C++。他们还吐槽主导&nbsp;TypeScript&nbsp;开发的&nbsp;Anders&nbsp;Hejlsberg&nbsp;对静态类型有&nbsp;“迷之执着”。</p><p style=\"text-indent: 0px; text-align: start;\">当时微软意识到&nbsp;JavaScript&nbsp;未来将会被应用到无数场景，而且他们公司内部团队在处理复杂的&nbsp;JavaScript&nbsp;代码库时面临着巨大的挑战，所以他们觉得有必要创造强大的工具来帮助编写&nbsp;JavaScript——&nbsp;尤其是针对大型&nbsp;JavaScript&nbsp;项目。基于此需求，TypeScript&nbsp;也确定了自己的定位和特性，它是&nbsp;JavaScript&nbsp;的超集，将类型检查和静态分析、显式接口和最佳实践结合到单一语言和编译器中。通过在&nbsp;JavaScript&nbsp;上构建，TypeScript&nbsp;能够更接近目标运行时，同时仅添加支持大型应用程序和大型团队所需的语法糖。</p><p style=\"text-indent: 0px; text-align: start;\">团队还坚持&nbsp;TypeScript&nbsp;要能够与现有的&nbsp;JavaScript&nbsp;无缝交互，与&nbsp;JavaScript&nbsp;共同进化，并且看上去也和&nbsp;JavaScript&nbsp;类似。</p><p style=\"text-indent: 0px; text-align: start;\">TypeScript&nbsp;诞生之初的部分<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fmicrosoft%2FTypeScript%2Fwiki%2FTypeScript-Design-Goals%2F53ffa9b1802cd8e18dfe4b2cd4e9ef5d4182df10\" target=\"_blank\"><strong>设计目标</strong></a>：</p><ul style=\"text-indent: 0px; text-align: left;\"><li>不会对已有的程序增加运行时开销</li><li>与当前和未来的&nbsp;ECMAScript&nbsp;提案保持一致</li><li>保留所有&nbsp;JavaScript&nbsp;代码的运行时行为</li><li>避免添加表达式类型的语法&nbsp;(expression-level&nbsp;syntax)</li><li>使用一致、完全可擦除的结构化类型系统</li><li>……</li></ul><p style=\"text-indent: 0px; text-align: start;\">这些目标指导着&nbsp;TypeScript&nbsp;的发展方向：关注类型系统，成为&nbsp;JavaScript&nbsp;的类型检查器，只添加类型检查所需的语法，避免添加新的运行时语法和行为。</p><p style=\"text-indent: 0px; text-align: start;\">微软提到，TypeScript&nbsp;拥有如今的繁荣生态离不开一个重要属性：<strong>开源</strong>。TypeScript&nbsp;一开始就是免费且开源&nbsp;——<span style=\"color: rgb(51, 51, 51);\">&nbsp;语言规范和编译器都是开源项目，</span>并且以真正开放的方式来运作。事实上，微软当时对外展现出的姿态并不是现在的&nbsp;“拥抱开源”，所以他们内部并没真正认识到&nbsp;TypeScript&nbsp;的开源是如何帮助它走向成功。因此有人认为，TypeScript&nbsp;在很大程度上引导微软开始更多地转向开源。</p><p style=\"text-indent: 0px; text-align: start;\">现在，TypeScript&nbsp;仍在积极发展和迭代改进，并被全球数百万开发者使用。在诸多编程语言排名、指数或开发者调查中，TypeScript&nbsp;一直位居前列，也是最受欢迎和最常用的编程语言。</p>','',0,0,'开源中国','开云',NULL,0,1,'2024-01-08 19:02:12','2022-10-22 14:34:56'),(54,1,'JetBrains Fleet 公测，下一代 IDE',1,0,'2024-01-01 20:22:23','JetBrains 宣布首次公共预览 Fleet，所有人都可以使用。Fleet 是由 JetBrains 打造的下一代 IDE，于 2021 年首次正式推出。它是一个新的分布式多语言编辑器和 IDE，基于 JetBrains 在后端的 IntelliJ 平台，采用了全新的用户界面和分布式架构从头开始构建。\n下载 Fleet：https://www.jetbrains.com.cn/fleet/download/\n\n公告表示，自从最初宣布 Fleet 以来，有超过 137,000 人报名参加私人预览；官方最初之所以决定从封闭式预览开始，是为了能够以渐进的方式处理反馈。现如今，JetBrains Fleet 仍处于起步阶段，还有大量的工作要做。其向公众开放预览的原因有两个方面：“首先，我们认为让所有注册者再等下去是不对的，但单独邀请这么多人对我们来说也缺乏意义。面向公众开放预览对我们来说更容易。第二，也是最重要的，我们一直是一家以开放态度打造产品的公司。我们不希望 Fleet 在这方面有任何不同。”\nJetBrains 方面提供了一个图表，以显示 Fleet 目前提供支持的语言和技术，以及每个技术的状态。但值得注意的是，Fleet 仍处于早期阶段，有些事情可能无法按预期工作；所以即使有些东西被列为受支持的，也有可能存在问题。\n同时 JetBrains 也强调称，他们并不打算取代其现有的 IDE。\n因此，请不要期望在 Fleet 中看到与我们的 IDE（如 IntelliJ IDEA）完全相同的功能。尽管我们会继续开发 Fleet，我们 IDE 的所有功能也不会出现在其中。Fleet 是我们为开发者提供不同用户体验的一个机会。话虽如此，我们确实希望听到你认为 Fleet 还缺少什么功能的反馈，例如特定的重构选项、工具集成等。我们现有的 IDE 将继续发展。我们对其有很多计划，包括性能改进、新的用户界面、远程开发等等。最后，Fleet 还在底层采用了我们现有工具的智慧，所以这些工具都不会消失。\nJetBrains 透露，在未来几个月他们将致力于稳定 Fleet，并尽可能地解决得到的反馈。同时，将在以下领域开展工作：\n为插件作者提供 API 支持和 SDK–鉴于 Fleet 有一个分布式架构，我们需要努力为插件作者简化工作。 虽然我们保证会为扩展 Fleet 提供一个平台，但也请求大家在这方面多一点耐心。 性能 – 我们希望 Fleet 不仅在内存占用方面，而且在响应时间方面都能表现出色。 有很多地方我们仍然可以提高性能，我们将在这些方面努力。 主题和键盘地图 – 我们知道许多开发者已经习惯了他们现有的编辑器和 IDE，当他们转移到新的 IDE 时，往往会想念他们以前的键盘绑定和主题。 我们将致力于增加对更多主题和键盘映射的支持。 我们当然也会致力于 Vim 的模拟。\n更多详情可查看官方博客。','<p style=\"text-indent: 0px; text-align: left;\">JetBrains&nbsp;<a href=\"https://my.oschina.net/u/5494143/blog/5584325\" target=\"\">宣布</a>首次公共预览&nbsp;<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.jetbrains.com.cn%2Ffleet%2F\" target=\"_blank\">Fleet</a>，所有人都可以使用。Fleet&nbsp;是由&nbsp;JetBrains&nbsp;打造的下一代&nbsp;IDE，于&nbsp;2021&nbsp;年首次正式<a href=\"https://my.oschina.net/u/5494143/blog/5332934\" target=\"\">推出</a>。它是一个新的分布式多语言编辑器和&nbsp;IDE，基于&nbsp;JetBrains&nbsp;在后端的&nbsp;IntelliJ&nbsp;平台，采用了全新的用户界面和分布式架构从头开始构建。</p><p style=\"text-indent: 0px; text-align: left;\"><strong>下载&nbsp;Fleet：</strong><a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.jetbrains.com.cn%2Ffleet%2Fdownload%2F\" target=\"_blank\">https://www.jetbrains.com.cn/fleet/download/</a></p><p style=\"text-indent: 0px; text-align: left;\"><br></p><p style=\"text-indent: 0px; text-align: left;\">公告表示，自从最初宣布&nbsp;Fleet&nbsp;以来，有超过&nbsp;137,000&nbsp;人报名参加私人预览；官方最初之所以决定从封闭式预览开始，是为了能够以渐进的方式处理反馈。现如今，JetBrains&nbsp;Fleet&nbsp;仍处于起步阶段，还有大量的工作要做。其向公众开放预览的原因有两个方面：“首先，我们认为让所有注册者再等下去是不对的，但单独邀请这么多人对我们来说也缺乏意义。面向公众开放预览对我们来说更容易。第二，也是最重要的，我们一直是一家以开放态度打造产品的公司。我们不希望&nbsp;Fleet&nbsp;在这方面有任何不同。”</p><p style=\"text-indent: 0px; text-align: left;\">JetBrains&nbsp;方面提供了一个<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fjb.gg%2Ffleet-feature-matrix\" target=\"_blank\">图表</a>，以显示&nbsp;Fleet&nbsp;目前提供支持的语言和技术，以及每个技术的状态。但值得注意的是，Fleet&nbsp;仍处于早期阶段，有些事情可能无法按预期工作；所以即使有些东西被列为受支持的，也有可能存在问题。</p><p style=\"text-indent: 0px; text-align: left;\">同时&nbsp;JetBrains&nbsp;也强调称，他们并不打算取代其现有的&nbsp;IDE。</p><blockquote style=\"text-indent: 0px; text-align: left;\">因此，请不要期望在&nbsp;Fleet&nbsp;中看到与我们的&nbsp;IDE（如&nbsp;IntelliJ&nbsp;IDEA）完全相同的功能。尽管我们会继续开发&nbsp;Fleet，我们&nbsp;IDE&nbsp;的所有功能也不会出现在其中。Fleet&nbsp;是我们为开发者提供不同用户体验的一个机会。话虽如此，我们确实希望听到你认为&nbsp;Fleet&nbsp;还缺少什么功能的反馈，例如特定的重构选项、工具集成等。我们现有的&nbsp;IDE&nbsp;将继续发展。我们对其有很多计划，包括性能改进、新的用户界面、远程开发等等。最后，Fleet&nbsp;还在底层采用了我们现有工具的智慧，所以这些工具都不会消失。</blockquote><p style=\"text-indent: 0px; text-align: start;\">JetBrains&nbsp;透露，在未来几个月他们将致力于稳定&nbsp;Fleet，并尽可能地解决得到的反馈。同时，将在以下领域开展工作：</p><ul style=\"text-indent: 0px; text-align: left;\"><li><strong>为插件作者提供&nbsp;API&nbsp;支持和&nbsp;SDK</strong>–鉴于&nbsp;Fleet&nbsp;有一个<a href=\"https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fblog.jetbrains.com%2Fzh-hans%2Ffleet%2F2022%2F01%2Ffleet-below-deck-part-i-architecture-overview%2F\" target=\"_blank\">分布式架构</a>，我们需要努力为插件作者简化工作。&nbsp;虽然我们保证会为扩展&nbsp;Fleet&nbsp;提供一个平台，但也请求大家在这方面多一点耐心。&nbsp;</li><li><strong>性能</strong>&nbsp;–&nbsp;我们希望&nbsp;Fleet&nbsp;不仅在内存占用方面，而且在响应时间方面都能表现出色。&nbsp;有很多地方我们仍然可以提高性能，我们将在这些方面努力。&nbsp;</li><li><strong>主题和键盘地图</strong>&nbsp;–&nbsp;我们知道许多开发者已经习惯了他们现有的编辑器和&nbsp;IDE，当他们转移到新的&nbsp;IDE&nbsp;时，往往会想念他们以前的键盘绑定和主题。&nbsp;我们将致力于增加对更多主题和键盘映射的支持。&nbsp;我们当然也会致力于&nbsp;Vim&nbsp;的模拟。</li></ul><p style=\"text-indent: 0px; text-align: left;\">更多详情可<a href=\"https://my.oschina.net/u/5494143/blog/5584325\" target=\"\">查看官方博客</a>。</p>','',0,0,'CSDN','开云',NULL,0,1,'2024-01-08 19:02:12','2022-10-22 14:36:10'),(55,2,'1024创新实验室 十一放假通知',1,0,'2024-01-01 20:22:23','国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：10月1日至7日放假调休，共7天。\n衷心预祝\n国庆快乐，阖家幸福！','<p style=\"text-indent: 0px; text-align: justify;\">国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：<strong>10月1日至7日放假调休</strong>，共7天。</p><p style=\"text-indent: 0px; text-align: justify;\"><strong>衷心预祝</strong></p><p style=\"text-indent: 0px; text-align: justify;\"><strong>国庆快乐，阖家幸福！</strong></p>','',0,0,'人力行政部','卓大','1024创新实验室发〔2022〕字第36号',0,1,'2024-01-08 19:02:12','2022-10-22 14:37:57'),(56,2,'十月份技术分享会议',1,0,'2024-01-01 20:22:23','尊敬的各位技术大佬：\n1024创新实验室技术分享即将隆重举行\n现将有关会议事宜通知如下：\n一、会议内容\n1、研究探讨SmartAdmin的技术体系\n二、会议形式\n大会专题小会分组讨论;\n三、会议时间及地点\n会议报到时间：xxx1年6月14日\n会议报到地点：洛阳市','<p style=\"text-indent: 0px; text-align: start;\">尊敬的各位技术大佬：</p><p style=\"text-indent: 0px; text-align: start;\">1024创新实验室技术分享即将隆重举行</p><p style=\"text-indent: 0px; text-align: start;\">现将有关会议事宜通知如下：</p><p style=\"text-indent: 0px; text-align: start;\"><strong>一、会议内容</strong></p><p style=\"text-indent: 0px; text-align: start;\">1、研究探讨SmartAdmin的技术体系</p><p style=\"text-indent: 0px; text-align: start;\"><strong>二、会议形式</strong></p><p style=\"text-indent: 0px; text-align: start;\">大会专题小会分组讨论;</p><p style=\"text-indent: 0px; text-align: start;\"><strong>三、会议时间及地点</strong></p><p style=\"text-indent: 0px; text-align: start;\">会议报到时间：xxx1年6月14日</p><p style=\"text-indent: 0px; text-align: start;\">会议报到地点：洛阳市</p>','',0,0,'技术部','开云','1024创新实验室发〔2022〕字第33号',0,1,'2024-01-08 19:02:12','2022-10-22 14:40:45'),(57,2,'关于疫情防控上班通知',1,0,'2024-01-01 20:22:23','近期，国内部分地区疫情频发，多地疫情出现外溢，为有效降低我市疫情输入和传播风险，洛阳市疾病预防控制中心发布疫情防控公众提示：\n一、所有入（返）洛阳人员均需提前3天向目的地社区（村居）、酒店宾馆、接待单位等所属网格进行报备，或通过“洛阳即时通系统”进行自主报备，配合做好健康码和行程码查验、核酸检测、隔离观察和健康监测等相关疫情防控措施。\n二、倡导广大群众减少跨地市出行，避免人群大范围流动引发的疫情传播扩散风险。\n三、对7天内有高风险区旅居史的人员，采取7天集中隔离医学观察；对7天内有中风险区旅居史的人员，采取7天居家隔离医学观察，如不具备居家隔离医学观察条件的，采取集中隔离医学观察。\n四、对疫情发生地出现一定范围社区传播或已实施大范围社区管控措施，基于对疫情输入风险研判结果，对近7天内来自疫情发生地所在县（市、区）的流入人员，参照中风险区旅居史人员的防控要求采取相应措施。\n五、对所有省外入（返）洛阳人员，须持有48小时内核酸检测阴性证明，抵达后进行“5天3检”，每次检测间隔24小时。推广“落地检”，按照“自愿免费即采即走，不限制流动”的原则，抵达我市后，立即进行1次核酸检测。\n六、加强重点机构场所疫情防控，坚持非必要不举办，对确需举办的培训、会展、文艺演出等大型聚集性活动，查验48小时内核酸检测阴性证明；建筑工地等人员密集型单位，查验外省（区、市）返岗人员48小时内核酸检测阴性证明；养老机构、儿童福利机构等查验探访人员48小时内核酸检测阴性证明；对进入宾馆、酒店和旅游景区等人流密集场所时，查验48小时内核酸检测阴性证明。\n七、近期有外出旅行史的人员，请密切关注疫情发生地区公布的病例和无症状感染者流调轨迹信息和中高风险区信息。有涉疫风险的人员要立即向社区（村）、住宿宾馆和单位报告，配合落实隔离医学观察。\n八、发热病人、健康码“黄码”等人员要履行个人防护责任，主动配合健康监测和核酸检测，在未排除感染风险前不出行。\n','<p style=\"text-indent: 0px; text-align: justify;\">近期，国内部分地区疫情频发，多地疫情出现外溢，为有效降低我市疫情输入和传播风险，洛阳市疾病预防控制中心发布疫情防控公众提示：</p><p style=\"text-indent: 0px; text-align: justify;\">一、所有入（返）洛阳人员均需提前3天向目的地社区（村居）、酒店宾馆、接待单位等所属网格进行报备，或通过“洛阳即时通系统”进行自主报备，配合做好健康码和行程码查验、核酸检测、隔离观察和健康监测等相关疫情防控措施。</p><p style=\"text-indent: 0px; text-align: justify;\">二、倡导广大群众减少跨地市出行，避免人群大范围流动引发的疫情传播扩散风险。</p><p style=\"text-indent: 0px; text-align: justify;\">三、对7天内有高风险区旅居史的人员，采取7天集中隔离医学观察；对7天内有中风险区旅居史的人员，采取7天居家隔离医学观察，如不具备居家隔离医学观察条件的，采取集中隔离医学观察。</p><p style=\"text-indent: 0px; text-align: justify;\">四、对疫情发生地出现一定范围社区传播或已实施大范围社区管控措施，基于对疫情输入风险研判结果，对近7天内来自疫情发生地所在县（市、区）的流入人员，参照中风险区旅居史人员的防控要求采取相应措施。</p><p style=\"text-indent: 0px; text-align: justify;\">五、对所有省外入（返）洛阳人员，须持有48小时内核酸检测阴性证明，抵达后进行“5天3检”，每次检测间隔24小时。推广“落地检”，按照“自愿免费即采即走，不限制流动”的原则，抵达我市后，立即进行1次核酸检测。</p><p style=\"text-indent: 0px; text-align: justify;\">六、加强重点机构场所疫情防控，坚持非必要不举办，对确需举办的培训、会展、文艺演出等大型聚集性活动，查验48小时内核酸检测阴性证明；建筑工地等人员密集型单位，查验外省（区、市）返岗人员48小时内核酸检测阴性证明；养老机构、儿童福利机构等查验探访人员48小时内核酸检测阴性证明；对进入宾馆、酒店和旅游景区等人流密集场所时，查验48小时内核酸检测阴性证明。</p><p style=\"text-indent: 0px; text-align: justify;\">七、近期有外出旅行史的人员，请密切关注疫情发生地区公布的病例和无症状感染者流调轨迹信息和中高风险区信息。有涉疫风险的人员要立即向社区（村）、住宿宾馆和单位报告，配合落实隔离医学观察。</p><p style=\"text-indent: 0px; text-align: justify;\">八、发热病人、健康码“黄码”等人员要履行个人防护责任，主动配合健康监测和核酸检测，在未排除感染风险前不出行。</p><p style=\"text-indent: 0px; text-align: justify;\"><br></p>','',0,0,'行政部','卓大','1024创新实验室发〔2022〕字第40号',0,1,'2024-01-08 19:02:12','2022-10-22 14:46:00'),(58,2,'办公室消杀关键位置通知',1,0,'2024-01-01 20:22:23','开展消毒消杀是杀灭病源、切断疫情传播的有效手段，是防控疫情的重要措施。为了切实将新型冠状病毒肺炎疫情防控工作落到实处，守护好辖区居民及工作人员的身体健康和生命安全，青山镇高度重视新型冠状病毒肺炎的消杀工作，将采购的防护服，防护面罩，一次性手套，口罩，84消毒液，酒精消毒液以及喷雾工具等消毒消杀物资，分发到镇级各站所各村（社区），全镇开展消杀工作。','<p><span style=\"color: rgb(93, 93, 93); background-color: rgb(247, 247, 247);\">开展消毒消杀是杀灭病源、切断疫情传播的有效手段，是防控疫情的重要措施。为了切实将新型冠状病毒肺炎疫情防控工作落到实处，守护好辖区居民及工作人员的身体健康和生命安全，青山镇高度重视新型冠状病毒肺炎的消杀工作，将采购的防护服，防护面罩，一次性手套，口罩，84消毒液，酒精消毒液以及喷雾工具等消毒消杀物资，分发到镇级各站所各村（社区），全镇开展消杀工作。</span></p>','',0,0,'行政部','卓大','1024创新实验室发〔2022〕字第26号',0,1,'2024-01-08 19:02:12','2022-10-22 14:47:12'),(59,2,'十月份人事任命通知',1,0,'2024-01-01 20:22:23','1024创新实验室发〔2022〕字第36号\n1024创新实验室发〔2022〕字第36号\n1024创新实验室发〔2022〕字第36号\n1024创新实验室发〔2022〕字第36号\n1024创新实验室发〔2022〕字第36号\n1024创新实验室发〔2022〕字第36号','<p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p>','',0,0,'销售部','卓大','1024创新实验室发〔2022〕字第30号',0,1,'2024-01-08 19:02:12','2022-10-22 14:50:11'),(60,2,'1024创新实验室 春节放假通知',1,0,'2024-01-01 20:22:23','春节假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：10月1日至7日放假调休，共7天。\n衷心预祝\n国庆快乐，阖家幸福！','<p style=\"text-indent: 0px; text-align: justify;\">国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：<strong>10月1日至7日放假调休</strong>，共7天。</p><p style=\"text-indent: 0px; text-align: justify;\"><strong>衷心预祝</strong></p><p style=\"text-indent: 0px; text-align: justify;\"><strong>国庆快乐，阖家幸福！</strong></p>','',0,0,'人力行政部','卓大','1024创新实验室发〔2022〕字第36号',0,1,'2024-01-08 19:02:12','2022-10-22 14:37:57');
/*!40000 ALTER TABLE `t_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_notice_type`
--

DROP TABLE IF EXISTS `t_notice_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_notice_type` (
  `notice_type_id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知类型',
  `notice_type_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`notice_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_notice_type`
--

LOCK TABLES `t_notice_type` WRITE;
/*!40000 ALTER TABLE `t_notice_type` DISABLE KEYS */;
INSERT INTO `t_notice_type` VALUES (1,'新闻','2022-08-16 20:29:15','2024-09-03 21:44:42'),(2,'通知','2022-08-16 20:29:20','2022-08-16 20:29:20');
/*!40000 ALTER TABLE `t_notice_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_notice_view_record`
--

DROP TABLE IF EXISTS `t_notice_view_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_notice_view_record` (
  `notice_id` bigint NOT NULL COMMENT '通知公告id',
  `employee_id` bigint NOT NULL COMMENT '员工id',
  `page_view_count` int DEFAULT '0' COMMENT '查看次数',
  `first_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首次ip',
  `first_user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首次用户设备等标识',
  `last_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后一次ip',
  `last_user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后一次用户设备等标识',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`notice_id`,`employee_id`) USING BTREE,
  UNIQUE KEY `uk_notice_employee` (`notice_id`,`employee_id`) USING BTREE COMMENT '资讯员工'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知查看记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_notice_view_record`
--

LOCK TABLES `t_notice_view_record` WRITE;
/*!40000 ALTER TABLE `t_notice_view_record` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_notice_view_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_notice_visible_range`
--

DROP TABLE IF EXISTS `t_notice_visible_range`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_notice_visible_range` (
  `notice_id` bigint NOT NULL COMMENT '资讯id',
  `data_type` tinyint NOT NULL COMMENT '数据类型1员工 2部门',
  `data_id` bigint NOT NULL COMMENT '员工or部门id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_notice_data` (`notice_id`,`data_type`,`data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知可见范围';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_notice_visible_range`
--

LOCK TABLES `t_notice_visible_range` WRITE;
/*!40000 ALTER TABLE `t_notice_visible_range` DISABLE KEYS */;
INSERT INTO `t_notice_visible_range` VALUES (63,1,63,'2024-08-09 10:40:32');
/*!40000 ALTER TABLE `t_notice_visible_range` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_oa_bank`
--

DROP TABLE IF EXISTS `t_oa_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_oa_bank` (
  `bank_id` bigint NOT NULL AUTO_INCREMENT COMMENT '银行信息ID',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开户银行',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户名称',
  `account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `business_flag` tinyint(1) NOT NULL COMMENT '是否对公',
  `enterprise_id` bigint NOT NULL COMMENT '企业ID',
  `disabled_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bank_id`) USING BTREE,
  KEY `idx_enterprise_id` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='OA银行信息\n';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_oa_bank`
--

LOCK TABLES `t_oa_bank` WRITE;
/*!40000 ALTER TABLE `t_oa_bank` DISABLE KEYS */;
INSERT INTO `t_oa_bank` VALUES (26,'工商银行','1024创新实验室','1024','基本户',1,2,0,0,1,'管理员','2022-10-22 17:58:43','2022-10-22 17:58:43'),(27,'建设银行','1024创新实验室','10241','其他户',0,2,0,0,1,'管理员','2022-10-22 17:59:19','2022-10-22 17:59:19');
/*!40000 ALTER TABLE `t_oa_bank` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_oa_enterprise`
--

DROP TABLE IF EXISTS `t_oa_enterprise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_oa_enterprise` (
  `enterprise_id` bigint NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `enterprise_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `enterprise_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业logo',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型（1:有限公司;2:合伙公司）',
  `unified_social_credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统一社会信用代码',
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人',
  `contact_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份',
  `province_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '省份名称',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '市',
  `city_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市名称',
  `district` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区县',
  `district_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区县名称',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '详细地址',
  `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '营业执照',
  `disabled_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`enterprise_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='OA企业模块\r\n';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_oa_enterprise`
--

LOCK TABLES `t_oa_enterprise` WRITE;
/*!40000 ALTER TABLE `t_oa_enterprise` DISABLE KEYS */;
INSERT INTO `t_oa_enterprise` VALUES (1,'1024创新区块链实验室','public/common/34f5ac0fc097402294aea75352c128f0_20240306112435.png',1,'1024lab_block','开云','18637925892',NULL,'410000','河南省','410300','洛阳市','410311','洛龙区','区块链大楼','public/common/1d89055e5680426280446aff1e7e627c_20240306112451.jpeg',0,1,1,'管理员','2021-10-22 17:03:35','2025-06-28 11:17:13'),(2,'西大实验室','',2,'88888888','大大','1*********8','<EMAIL>','810000','香港','810103','九龙城区','410311','','加和大楼','',0,0,44,'卓大','2022-10-22 14:57:36','2022-10-22 17:03:57');
/*!40000 ALTER TABLE `t_oa_enterprise` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_oa_enterprise_employee`
--

DROP TABLE IF EXISTS `t_oa_enterprise_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_oa_enterprise_employee` (
  `enterprise_employee_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enterprise_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `employee_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '货物名称',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`enterprise_employee_id`) USING BTREE,
  UNIQUE KEY `uk_enterprise_employee` (`enterprise_id`,`employee_id`) USING BTREE,
  KEY `idx_employee_id` (`employee_id`) USING BTREE,
  KEY `idx_enterprise_id` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业关联的员工';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_oa_enterprise_employee`
--

LOCK TABLES `t_oa_enterprise_employee` WRITE;
/*!40000 ALTER TABLE `t_oa_enterprise_employee` DISABLE KEYS */;
INSERT INTO `t_oa_enterprise_employee` VALUES (154,'2','2','2022-10-22 17:57:50','2022-10-22 17:57:50'),(155,'2','44','2022-10-22 17:57:50','2022-10-22 17:57:50');
/*!40000 ALTER TABLE `t_oa_enterprise_employee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_oa_invoice`
--

DROP TABLE IF EXISTS `t_oa_invoice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_oa_invoice` (
  `invoice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '发票信息ID',
  `invoice_heads` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开票抬头',
  `taxpayer_identification_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纳税人识别号',
  `account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行账户',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '开户行',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `enterprise_id` bigint NOT NULL COMMENT '企业ID',
  `disabled_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '禁用状态',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`invoice_id`) USING BTREE,
  KEY `idx_enterprise_id` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='OA发票信息\n';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_oa_invoice`
--

LOCK TABLES `t_oa_invoice` WRITE;
/*!40000 ALTER TABLE `t_oa_invoice` DISABLE KEYS */;
INSERT INTO `t_oa_invoice` VALUES (15,'1024创新实验室','1024lab','1024lab','中国银行','123',2,0,0,1,'管理员','2022-10-22 17:59:35','2023-09-27 16:26:07');
/*!40000 ALTER TABLE `t_oa_invoice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_operate_log`
--

DROP TABLE IF EXISTS `t_operate_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_operate_log` (
  `operate_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `operate_user_id` bigint NOT NULL COMMENT '用户id',
  `operate_user_type` int NOT NULL COMMENT '用户类型',
  `operate_user_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `module` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作模块',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '操作内容',
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求路径',
  `method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求方法',
  `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数',
  `ip` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求ip',
  `ip_region` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求ip地区',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求user-agent',
  `success_flag` tinyint DEFAULT NULL COMMENT '请求结果 0失败 1成功',
  `fail_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败原因',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`operate_log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4882 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_operate_log`
--

LOCK TABLES `t_operate_log` WRITE;
/*!40000 ALTER TABLE `t_operate_log` DISABLE KEYS */;
INSERT INTO `t_operate_log` VALUES (4765,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:25:57','2025-07-01 15:25:57'),(4766,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:25:57','2025-07-01 15:25:57'),(4767,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:38:38','2025-07-01 15:38:38'),(4768,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:38:38','2025-07-01 15:38:38'),(4769,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:45:18','2025-07-01 15:45:18'),(4770,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 15:45:18','2025-07-01 15:45:18'),(4771,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 16:19:18','2025-07-01 16:19:18'),(4772,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 16:19:18','2025-07-01 16:19:18'),(4773,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 16:27:18','2025-07-01 16:27:18'),(4774,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-01 16:27:18','2025-07-01 16:27:18'),(4775,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-02 09:06:31','2025-07-02 09:06:31'),(4776,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-02 09:06:31','2025-07-02 09:06:31'),(4777,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-02 20:30:33','2025-07-02 20:30:33'),(4778,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-02 20:30:33','2025-07-02 20:30:33'),(4779,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:05:28','2025-07-03 11:05:28'),(4780,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:05:28','2025-07-03 11:05:28'),(4781,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:07:43','2025-07-03 11:07:43'),(4782,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:07:43','2025-07-03 11:07:43'),(4783,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:35:36','2025-07-03 11:35:36'),(4784,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 11:35:36','2025-07-03 11:35:36'),(4785,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 13:33:14','2025-07-03 13:33:14'),(4786,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 13:33:14','2025-07-03 13:33:14'),(4787,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 14:52:09','2025-07-03 14:52:09'),(4788,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 14:52:09','2025-07-03 14:52:09'),(4789,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 15:41:57','2025-07-03 15:41:57'),(4790,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 15:41:57','2025-07-03 15:41:57'),(4791,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 18:18:37','2025-07-03 18:18:37'),(4792,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 18:18:37','2025-07-03 18:18:37'),(4793,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 19:23:17','2025-07-03 19:23:17'),(4794,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 19:23:17','2025-07-03 19:23:17'),(4795,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 19:40:28','2025-07-03 19:40:28'),(4796,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-03 19:40:28','2025-07-03 19:40:28'),(4797,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 23:08:41','2025-07-03 23:08:41'),(4798,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,NULL,'2025-07-03 23:08:41','2025-07-03 23:08:41'),(4799,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 14:08:36','2025-07-04 14:08:36'),(4800,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 14:08:36','2025-07-04 14:08:36'),(4801,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 14:48:04','2025-07-04 14:48:04'),(4802,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 14:48:04','2025-07-04 14:48:04'),(4803,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 20:10:33','2025-07-04 20:10:33'),(4804,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 20:10:33','2025-07-04 20:10:33'),(4805,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 21:22:27','2025-07-04 21:22:27'),(4806,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-04 21:22:27','2025-07-04 21:22:27'),(4807,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 08:30:42','2025-07-05 08:30:42'),(4808,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 08:30:42','2025-07-05 08:30:42'),(4809,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 09:53:33','2025-07-05 09:53:33'),(4810,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 09:53:33','2025-07-05 09:53:33'),(4811,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 09:53:34','2025-07-05 09:53:34'),(4812,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 09:53:34','2025-07-05 09:53:34'),(4813,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:09:12','2025-07-05 10:09:12'),(4814,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:09:12','2025-07-05 10:09:12'),(4815,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:18:23','2025-07-05 10:18:23'),(4816,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:18:23','2025-07-05 10:18:23'),(4817,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:30:23','2025-07-05 10:30:23'),(4818,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 10:30:23','2025-07-05 10:30:23'),(4819,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 13:19:27','2025-07-05 13:19:27'),(4820,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 13:19:27','2025-07-05 13:19:27'),(4821,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 18:34:06','2025-07-05 18:34:06'),(4822,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 18:34:06','2025-07-05 18:34:06'),(4823,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 21:28:26','2025-07-05 21:28:26'),(4824,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 21:28:26','2025-07-05 21:28:26'),(4825,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 23:54:01','2025-07-05 23:54:01'),(4826,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-05 23:54:01','2025-07-05 23:54:01'),(4827,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-06 12:00:27','2025-07-06 12:00:27'),(4828,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-06 12:00:27','2025-07-06 12:00:27'),(4829,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-07 21:30:57','2025-07-07 21:30:57'),(4830,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-07 21:30:57','2025-07-07 21:30:57'),(4831,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:00:05','2025-07-08 11:00:05'),(4832,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:00:05','2025-07-08 11:00:05'),(4833,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:05:20','2025-07-08 11:05:20'),(4834,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:05:20','2025-07-08 11:05:20'),(4835,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:07:33','2025-07-08 11:07:33'),(4836,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 11:07:33','2025-07-08 11:07:33'),(4837,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 15:28:04','2025-07-08 15:28:04'),(4838,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 15:28:04','2025-07-08 15:28:04'),(4839,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 20:01:43','2025-07-08 20:01:43'),(4840,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 20:01:43','2025-07-08 20:01:43'),(4841,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 21:17:58','2025-07-08 21:17:58'),(4842,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 21:17:58','2025-07-08 21:17:58'),(4843,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 21:20:56','2025-07-08 21:20:56'),(4844,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 21:20:56','2025-07-08 21:20:56'),(4845,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 22:19:28','2025-07-08 22:19:28'),(4846,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 22:19:28','2025-07-08 22:19:28'),(4847,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 22:19:33','2025-07-08 22:19:33'),(4848,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-08 22:19:33','2025-07-08 22:19:33'),(4849,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 09:35:58','2025-07-09 09:35:58'),(4850,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 09:35:58','2025-07-09 09:35:58'),(4851,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 16:11:57','2025-07-09 16:11:57'),(4852,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 16:11:57','2025-07-09 16:11:57'),(4853,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 19:57:08','2025-07-09 19:57:08'),(4854,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-09 19:57:08','2025-07-09 19:57:08'),(4855,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-14 21:47:58','2025-07-14 21:47:58'),(4856,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-14 21:47:58','2025-07-14 21:47:58'),(4857,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-17 22:38:34','2025-07-17 22:38:34'),(4858,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-17 22:38:34','2025-07-17 22:38:34'),(4859,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-18 16:15:23','2025-07-18 16:15:23'),(4860,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-18 16:15:23','2025-07-18 16:15:23'),(4861,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 22:18:30','2025-07-21 22:18:30'),(4862,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 22:18:30','2025-07-21 22:18:30'),(4863,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:02:07','2025-07-21 23:02:07'),(4864,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:02:07','2025-07-21 23:02:07'),(4865,1,1,'管理员','OA办公-企业','分页查询企业模块 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:03:11','2025-07-21 23:03:11'),(4866,1,1,'管理员','OA办公-通知公告','通知公告类型-获取全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:03:13','2025-07-21 23:03:13'),(4867,1,1,'管理员','OA办公-通知公告','【管理】通知公告-分页查询 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:03:13','2025-07-21 23:03:13'),(4868,1,1,'管理员','OA办公-企业','分页查询企业模块 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:03:45','2025-07-21 23:03:45'),(4869,1,1,'管理员','OA办公-企业','分页查询企业模块 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-21 23:09:06','2025-07-21 23:09:06'),(4870,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-22 09:01:26','2025-07-22 09:01:26'),(4871,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-22 09:01:26','2025-07-22 09:01:26'),(4872,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-22 21:54:30','2025-07-22 21:54:30'),(4873,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-22 21:54:30','2025-07-22 21:54:30'),(4874,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 00:48:32','2025-07-23 00:48:32'),(4875,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 00:48:32','2025-07-23 00:48:32'),(4876,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 09:23:55','2025-07-23 09:23:55'),(4877,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 09:23:55','2025-07-23 09:23:55'),(4878,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 13:51:50','2025-07-23 13:51:50'),(4879,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 13:51:50','2025-07-23 13:51:50'),(4880,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 14:31:24','2025-07-23 14:31:24'),(4881,1,1,'管理员','OA办公-通知公告','【员工】通知公告-查询全部 <AUTHOR> (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',1,NULL,'2025-07-23 14:31:24','2025-07-23 14:31:24');
/*!40000 ALTER TABLE `t_operate_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_order_logistics`
--

DROP TABLE IF EXISTS `t_order_logistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_order_logistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `tracking_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递单号',
  `courier_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递公司',
  `status` enum('pending','shipped','in_transit','delivered','returned') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'pending' COMMENT '物流状态',
  `tracking_info` json DEFAULT NULL COMMENT '物流跟踪信息',
  `shipped_time` datetime DEFAULT NULL COMMENT '发货时间',
  `deliver_time` datetime DEFAULT NULL COMMENT '签收时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_order_id` (`order_id`) USING BTREE,
  KEY `idx_tracking_number` (`tracking_number`) USING BTREE,
  CONSTRAINT `fk_order_logistics` FOREIGN KEY (`order_id`) REFERENCES `t_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='订单物流表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_order_logistics`
--

LOCK TABLES `t_order_logistics` WRITE;
/*!40000 ALTER TABLE `t_order_logistics` DISABLE KEYS */;
INSERT INTO `t_order_logistics` VALUES (1,1,'*********','*********','delivered','{\"msg\": \"ok\", \"list\": [0, 1, 2, 3]}','2025-06-30 08:26:03','2025-06-30 08:26:14','2025-06-29 23:25:17');
/*!40000 ALTER TABLE `t_order_logistics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_orders`
--

DROP TABLE IF EXISTS `t_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_orders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `activity_id` bigint unsigned NOT NULL COMMENT '活动ID',
  `status` enum('PENDING_PAYMENT','WON_PENDING_ACTION','WON','LOST','COMPLETED','CANCELLED','EXPIRED') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'PENDING_PAYMENT' COMMENT '订单状态',
  `draw_result` tinyint unsigned DEFAULT NULL COMMENT '开奖结果 1 赢, 0 输',
  `goods_id` bigint DEFAULT NULL COMMENT '商品ID',
  `sku_id` bigint DEFAULT NULL COMMENT 'SKUID',
  `win_option` enum('TAKE_GOODS','CASH_OUT','PAY_REMAINDER','FORFEIT_DEPOSIT') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '中签后选择',
  `amount_paid` decimal(10,2) NOT NULL COMMENT '用户实付金额',
  `experience_paid` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '使用体验金',
  `subsidy_paid` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '补贴金额',
  `points_paid` int DEFAULT NULL COMMENT '使用积分',
  `payable_amount` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '应付款金额',
  `shipping_address_id` bigint DEFAULT NULL COMMENT '收货地址ID',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付方式 0 余额, 1 体验金, 2 积分, 其它',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `draw_time` datetime DEFAULT NULL COMMENT '开奖时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `settle_flag` tinyint unsigned DEFAULT '0' COMMENT '结算标记',
  `deleted_flag` tinyint unsigned DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_order_sn` (`order_sn`) USING BTREE,
  KEY `idx_user_create` (`user_id`,`create_time`) USING BTREE,
  KEY `idx_activity_id` (`activity_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_draw_result` (`draw_result`) USING BTREE,
  KEY `fk_orders_address` (`shipping_address_id`) USING BTREE,
  CONSTRAINT `fk_orders_activity` FOREIGN KEY (`activity_id`) REFERENCES `t_activities` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_orders_address` FOREIGN KEY (`shipping_address_id`) REFERENCES `t_user_address` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_orders`
--

LOCK TABLES `t_orders` WRITE;
/*!40000 ALTER TABLE `t_orders` DISABLE KEYS */;
INSERT INTO `t_orders` VALUES (1,'20250629100166',1,3,'LOST',0,1,17,NULL,10.00,0.00,0.00,0,0.00,1,'0','2025-06-29 18:58:41','2025-06-29 18:58:44','2025-07-01 11:32:42','2025-06-29 18:58:53','2025-07-01 11:34:40',1,0),(8,'DK20250707100013',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 15:55:40','2025-07-07 15:55:40','2025-07-07 15:55:40','2025-07-07 15:55:40','2025-07-07 15:55:40',0,0),(9,'DK20250707100014',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 16:24:20','2025-07-07 16:24:20','2025-07-07 16:24:20','2025-07-07 16:24:20','2025-07-07 16:24:20',0,0),(10,'DK20250707100021',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 20:43:41','2025-07-07 20:43:41','2025-07-07 20:43:41','2025-07-07 20:43:41','2025-07-07 20:43:41',0,0),(11,'DK20250707100025',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 21:35:09','2025-07-07 21:35:09','2025-07-07 21:35:09','2025-07-07 21:35:09','2025-07-07 21:35:09',0,0),(12,'DK20250707100032',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 21:36:41','2025-07-07 21:36:41','2025-07-07 21:36:41','2025-07-07 21:36:41','2025-07-07 21:36:41',0,0),(13,'DK20250707100034',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 21:45:56','2025-07-07 21:45:56','2025-07-07 21:45:56','2025-07-07 21:45:56','2025-07-07 21:45:56',0,0),(14,'DK20250707100042',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 22:37:37','2025-07-07 22:37:37','2025-07-07 22:37:37','2025-07-07 22:37:37','2025-07-07 22:37:37',0,0),(15,'DK20250707100045',73,5,'COMPLETED',0,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-07 22:45:22','2025-07-07 22:45:22','2025-07-07 22:45:22','2025-07-07 22:45:22','2025-07-07 22:45:22',0,0),(16,'DK20250708100006',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-08 15:29:53','2025-07-08 15:29:53','2025-07-08 15:29:53','2025-07-08 15:29:53','2025-07-08 15:29:53',0,0),(17,'DK20250709100009',79,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,7,'0','2025-07-09 21:10:22','2025-07-09 21:10:22','2025-07-09 21:10:22','2025-07-09 21:10:22','2025-07-09 21:10:22',1,0),(18,'DK20250709100016',79,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,7,'0','2025-07-09 21:12:06','2025-07-09 21:12:06','2025-07-09 21:12:06','2025-07-09 21:12:06','2025-07-09 21:12:06',1,0),(19,'DK20250711100004',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 19:55:39','2025-07-11 19:55:39','2025-07-11 19:55:39','2025-07-11 19:55:39','2025-07-11 19:55:39',1,0),(20,'DK20250711100009',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 19:57:22','2025-07-11 19:57:22','2025-07-11 19:57:22','2025-07-11 19:57:22','2025-07-11 19:57:22',1,0),(21,'DK20250711100018',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 19:58:06','2025-07-11 19:58:06','2025-07-11 19:58:06','2025-07-11 19:58:06','2025-07-11 19:58:06',1,0),(22,'DK20250711100025',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 20:01:52','2025-07-11 20:01:52','2025-07-11 20:01:52','2025-07-11 20:01:52','2025-07-11 20:01:52',1,0),(23,'DK20250711100026',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 20:10:47','2025-07-11 20:10:47','2025-07-11 20:10:47','2025-07-11 20:10:47','2025-07-11 20:10:47',1,0),(24,'DK20250711100036',81,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,8,'0','2025-07-11 20:11:12','2025-07-11 20:11:12','2025-07-11 20:11:12','2025-07-11 20:11:12','2025-07-11 20:11:12',1,0),(25,'DK20250714100002',83,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,9,'0','2025-07-14 14:25:46','2025-07-14 14:25:46','2025-07-14 14:25:46','2025-07-14 14:25:46','2025-07-14 14:25:46',1,0),(26,'DK20250714100007',83,4,'COMPLETED',1,34,49,NULL,99.00,0.00,0.00,NULL,99.00,9,'0','2025-07-14 14:33:41','2025-07-14 14:33:41','2025-07-14 14:33:41','2025-07-14 14:33:41','2025-07-14 14:33:41',1,0),(27,'DK20250718100004',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 12:49:30','2025-07-18 12:49:30','2025-07-18 12:49:30','2025-07-18 12:49:30','2025-07-18 12:49:30',1,0),(28,'DK20250718100008',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 12:55:19','2025-07-18 12:55:19','2025-07-18 12:55:19','2025-07-18 12:55:19','2025-07-18 12:55:19',1,0),(29,'DK20250718100017',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 13:02:14','2025-07-18 13:02:14','2025-07-18 13:02:14','2025-07-18 13:02:14','2025-07-18 13:02:14',1,0),(34,'DK20250718100048',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 13:13:09','2025-07-18 13:13:09','2025-07-18 13:13:09','2025-07-18 13:13:09','2025-07-18 13:13:09',1,0),(36,'DK20250718100055',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 15:58:58','2025-07-18 15:58:58','2025-07-18 15:58:58','2025-07-18 15:58:58','2025-07-18 15:58:58',1,0),(38,'DK20250718100069',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-18 16:09:58','2025-07-18 16:09:58','2025-07-18 16:09:58','2025-07-18 16:09:58','2025-07-18 16:09:58',1,0),(41,'DK20250718100091',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 16:22:28','2025-07-18 16:22:28','2025-07-18 16:22:28','2025-07-18 16:22:28','2025-07-18 16:22:28',1,0),(42,'DK20250718100096',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 16:26:35','2025-07-18 16:26:35','2025-07-18 16:26:35','2025-07-18 16:26:35','2025-07-18 16:26:35',1,0),(43,'DK20250718100106',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 16:41:04','2025-07-18 16:41:04','2025-07-18 16:41:04','2025-07-18 16:41:04','2025-07-18 16:41:04',1,0),(44,'DK20250718100109',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 16:47:21','2025-07-18 16:47:21','2025-07-18 16:47:21','2025-07-18 16:47:21','2025-07-18 16:47:21',1,0),(45,'DK20250718100114',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:04:49','2025-07-18 20:04:49','2025-07-18 20:04:49','2025-07-18 20:04:49','2025-07-18 20:04:49',1,0),(46,'DK20250718100119',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:11:40','2025-07-18 20:11:40','2025-07-18 20:11:40','2025-07-18 20:11:40','2025-07-18 20:11:40',1,0),(47,'DK20250718100120',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:16:38','2025-07-18 20:16:38','2025-07-18 20:16:38','2025-07-18 20:16:38','2025-07-18 20:16:38',1,0),(48,'DK20250718100122',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:20:43','2025-07-18 20:20:43','2025-07-18 20:20:43','2025-07-18 20:20:43','2025-07-18 20:20:43',1,0),(49,'DK20250718100123',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:24:31','2025-07-18 20:24:31','2025-07-18 20:24:31','2025-07-18 20:24:31','2025-07-18 20:24:31',1,0),(50,'DK20250718100128',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:28:43','2025-07-18 20:28:43','2025-07-18 20:28:43','2025-07-18 20:28:43','2025-07-18 20:28:43',1,0),(51,'DK20250718100131',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-18 20:47:31','2025-07-18 20:47:31','2025-07-18 20:47:31','2025-07-18 20:47:31','2025-07-18 20:47:31',1,0),(52,'DK20250721100001',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 11:11:31','2025-07-21 11:11:31','2025-07-21 11:11:31','2025-07-21 11:11:31','2025-07-21 11:11:31',1,0),(53,'DK20250721100006',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 16:02:55','2025-07-21 16:02:55','2025-07-21 16:02:55','2025-07-21 16:02:55','2025-07-21 16:02:55',1,0),(54,'DK20250721100014',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 16:13:14','2025-07-21 16:13:14','2025-07-21 16:13:14','2025-07-21 16:13:14','2025-07-21 16:13:14',1,0),(55,'DK20250721100022',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-21 18:13:36','2025-07-21 18:13:36','2025-07-21 18:13:36','2025-07-21 18:13:36','2025-07-21 18:13:36',1,0),(56,'DK20250721100030',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 19:30:08','2025-07-21 19:30:08','2025-07-21 19:30:08','2025-07-21 19:30:08','2025-07-21 19:30:08',1,0),(57,'DK20250721100038',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 22:05:15','2025-07-21 22:05:15','2025-07-21 22:05:15','2025-07-21 22:05:15','2025-07-21 22:05:15',1,0),(58,'DK20250721100045',73,3,'COMPLETED',1,8,54,NULL,3999.00,0.00,0.00,NULL,3999.00,5,'0','2025-07-21 22:06:14','2025-07-21 22:06:14','2025-07-21 22:06:14','2025-07-21 22:06:14','2025-07-21 22:06:14',1,0),(59,'DK20250721100052',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-21 22:07:06','2025-07-21 22:07:06','2025-07-21 22:07:06','2025-07-21 22:07:06','2025-07-21 22:07:06',1,0),(60,'DK20250721100053',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-21 22:35:32','2025-07-21 22:35:32','2025-07-21 22:35:32','2025-07-21 22:35:32','2025-07-21 22:35:32',1,0),(61,'DK20250721100056',73,4,'COMPLETED',1,7,52,NULL,5999.00,0.00,0.00,NULL,5999.00,5,'0','2025-07-21 22:36:01','2025-07-21 22:36:01','2025-07-21 22:36:01','2025-07-21 22:36:01','2025-07-21 22:36:01',1,0),(62,'DK20250721100062',83,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,9,'0','2025-07-21 22:38:07','2025-07-21 22:38:07','2025-07-21 22:38:07','2025-07-21 22:38:07','2025-07-21 22:38:07',1,0),(63,'DK20250721100071',83,4,'COMPLETED',1,7,52,NULL,5999.00,0.00,0.00,NULL,5999.00,9,'0','2025-07-21 22:39:07','2025-07-21 22:39:07','2025-07-21 22:39:07','2025-07-21 22:39:07','2025-07-21 22:39:07',1,0),(64,'DK20250722100002',73,5,'COMPLETED',1,1,46,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-22 17:10:21','2025-07-22 17:10:21','2025-07-22 17:10:21','2025-07-22 17:10:21','2025-07-22 17:10:21',1,0),(65,'DK20250722100005',73,5,'COMPLETED',1,1,58,NULL,6999.00,0.00,0.00,NULL,6999.00,5,'0','2025-07-22 23:34:15','2025-07-22 23:34:15','2025-07-22 23:34:15','2025-07-22 23:34:15','2025-07-22 23:34:15',1,0);
/*!40000 ALTER TABLE `t_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_password_log`
--

DROP TABLE IF EXISTS `t_password_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_password_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `old_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧密码',
  `new_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '新密码',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_and_type_index` (`user_id`,`user_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='密码修改记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_password_log`
--

LOCK TABLES `t_password_log` WRITE;
/*!40000 ALTER TABLE `t_password_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_password_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_popups`
--

DROP TABLE IF EXISTS `t_popups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_popups` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '弹窗ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '弹窗标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '图片URL',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '跳转链接',
  `trigger_rules` json NOT NULL COMMENT '触发规则',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态 1 开, 0 关',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_time_range` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='弹窗管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_popups`
--

LOCK TABLES `t_popups` WRITE;
/*!40000 ALTER TABLE `t_popups` DISABLE KEYS */;
INSERT INTO `t_popups` VALUES (1,'test','public/common/5305738fce6a4c2b8d1bf7545d04c8b8_20250701134231.jpg',NULL,'{\"msg\": \"ok\"}',1,'2025-07-01 13:42:52','2025-09-30 13:42:54','2025-07-01 13:42:58');
/*!40000 ALTER TABLE `t_popups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_position`
--

DROP TABLE IF EXISTS `t_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_position` (
  `position_id` bigint NOT NULL AUTO_INCREMENT COMMENT '职务ID',
  `position_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职务名称',
  `position_level` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职级',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint(1) DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`position_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_position`
--

LOCK TABLES `t_position` WRITE;
/*!40000 ALTER TABLE `t_position` DISABLE KEYS */;
INSERT INTO `t_position` VALUES (3,'默认','L1',3,'',0,'2024-06-29 15:57:07','2025-06-28 10:58:33'),(4,'一般','L2',1,NULL,0,'2024-07-15 23:34:14','2025-06-28 10:58:47'),(5,'普通','L1',5,NULL,0,'2024-07-15 23:34:48','2025-06-28 10:58:18'),(6,'高级','L2',4,NULL,0,'2024-07-15 23:35:00','2025-06-28 10:58:54');
/*!40000 ALTER TABLE `t_position` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_reload_item`
--

DROP TABLE IF EXISTS `t_reload_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_reload_item` (
  `tag` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '项名称',
  `args` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数 可选',
  `identification` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '运行标识',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`tag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='reload项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_reload_item`
--

LOCK TABLES `t_reload_item` WRITE;
/*!40000 ALTER TABLE `t_reload_item` DISABLE KEYS */;
INSERT INTO `t_reload_item` VALUES ('system_config','4','234','2024-08-13 14:14:30','2019-04-18 11:48:27');
/*!40000 ALTER TABLE `t_reload_item` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_reload_result`
--

DROP TABLE IF EXISTS `t_reload_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_reload_result` (
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `identification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行标识',
  `args` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `result` tinyint unsigned NOT NULL COMMENT '是否成功 ',
  `exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='reload结果';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_reload_result`
--

LOCK TABLES `t_reload_result` WRITE;
/*!40000 ALTER TABLE `t_reload_result` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_reload_result` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_role`
--

DROP TABLE IF EXISTS `t_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色名称',
  `role_code` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色编码',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色描述',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`role_id`) USING BTREE,
  UNIQUE KEY `role_code_uni` (`role_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb3 COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_role`
--

LOCK TABLES `t_role` WRITE;
/*!40000 ALTER TABLE `t_role` DISABLE KEYS */;
INSERT INTO `t_role` VALUES (1,'技术总监',NULL,'','2022-10-19 20:24:09','2019-06-21 12:09:34'),(34,'销售总监','cto','','2023-09-06 19:10:34','2019-08-30 09:30:50'),(35,'总经理',NULL,'','2019-08-30 09:31:05','2019-08-30 09:31:05'),(36,'董事长',NULL,'','2019-08-30 09:31:11','2019-08-30 09:31:11'),(37,'财务',NULL,'','2019-08-30 09:31:16','2019-08-30 09:31:16'),(59,'客户','KeHu',NULL,'2025-06-27 11:40:51','2025-06-27 11:40:51');
/*!40000 ALTER TABLE `t_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_role_data_scope`
--

DROP TABLE IF EXISTS `t_role_data_scope`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role_data_scope` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `data_scope_type` int NOT NULL COMMENT '数据范围类型',
  `view_type` int NOT NULL COMMENT '数据可见范围类型',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色的数据范围';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_role_data_scope`
--

LOCK TABLES `t_role_data_scope` WRITE;
/*!40000 ALTER TABLE `t_role_data_scope` DISABLE KEYS */;
INSERT INTO `t_role_data_scope` VALUES (67,1,2,1,'2024-03-18 20:41:00','2024-03-18 20:41:00');
/*!40000 ALTER TABLE `t_role_data_scope` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_role_employee`
--

DROP TABLE IF EXISTS `t_role_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role_employee` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL COMMENT '角色id',
  `employee_id` bigint NOT NULL COMMENT '员工id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_role_employee` (`role_id`,`employee_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=344 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色员工功能表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_role_employee`
--

LOCK TABLES `t_role_employee` WRITE;
/*!40000 ALTER TABLE `t_role_employee` DISABLE KEYS */;
INSERT INTO `t_role_employee` VALUES (325,36,63,'2022-10-19 20:25:26','2022-10-19 20:25:26'),(329,34,72,'2022-11-05 10:56:54','2022-11-05 10:56:54'),(330,36,72,'2022-11-05 10:56:54','2022-11-05 10:56:54'),(333,1,44,'2023-10-07 18:53:29','2023-10-07 18:53:29'),(334,1,47,'2023-10-07 18:55:00','2023-10-07 18:55:00'),(341,1,48,'2024-09-02 23:03:28','2024-09-02 23:03:28'),(343,59,73,'2025-07-09 09:36:36','2025-07-09 09:36:36');
/*!40000 ALTER TABLE `t_role_employee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_role_menu`
--

DROP TABLE IF EXISTS `t_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role_menu` (
  `role_menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `menu_id` bigint NOT NULL COMMENT '菜单id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`role_menu_id`) USING BTREE,
  KEY `idx_role_id` (`role_id`) USING BTREE,
  KEY `idx_menu_id` (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=941 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色-菜单\n';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_role_menu`
--

LOCK TABLES `t_role_menu` WRITE;
/*!40000 ALTER TABLE `t_role_menu` DISABLE KEYS */;
INSERT INTO `t_role_menu` VALUES (820,1,138,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(821,1,132,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(822,1,142,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(823,1,149,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(824,1,150,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(825,1,185,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(826,1,186,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(827,1,187,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(828,1,188,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(829,1,145,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(830,1,196,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(831,1,144,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(832,1,181,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(833,1,183,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(834,1,184,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(835,1,165,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(836,1,47,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(837,1,48,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(838,1,137,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(839,1,166,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(840,1,194,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(841,1,78,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(842,1,173,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(843,1,174,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(844,1,175,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(845,1,176,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(846,1,50,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(847,1,26,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(848,1,40,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(849,1,105,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(850,1,106,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(851,1,109,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(852,1,163,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(853,1,164,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(854,1,199,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(855,1,110,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(856,1,159,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(857,1,160,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(858,1,161,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(859,1,162,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(860,1,130,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(861,1,157,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(862,1,158,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(863,1,133,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(864,1,117,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(865,1,156,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(866,1,193,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(867,1,200,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(868,1,220,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(869,1,45,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(870,1,219,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(871,1,46,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(872,1,91,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(873,1,92,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(874,1,93,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(875,1,94,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(876,1,95,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(877,1,96,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(878,1,86,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(879,1,87,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(880,1,88,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(881,1,76,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(882,1,97,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(883,1,98,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(884,1,99,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(885,1,100,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(886,1,101,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(887,1,102,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(888,1,103,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(889,1,104,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(890,1,213,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(891,1,214,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(892,1,143,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(893,1,203,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(894,1,215,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(895,1,218,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(896,1,147,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(897,1,170,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(898,1,171,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(899,1,168,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(900,1,169,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(901,1,202,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(902,1,201,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(903,1,148,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(904,1,152,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(905,1,190,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(906,1,191,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(907,1,192,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(908,1,198,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(909,1,207,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(910,1,111,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(911,1,206,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(912,1,81,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(913,1,204,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(914,1,205,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(915,1,122,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(916,1,301,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(917,1,167,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(918,1,195,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(919,1,216,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(920,1,217,'2025-06-26 00:18:07','2025-06-26 00:18:07'),(940,59,308,'2025-06-28 16:36:38','2025-06-28 16:36:38');
/*!40000 ALTER TABLE `t_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_serial_number`
--

DROP TABLE IF EXISTS `t_serial_number`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_serial_number` (
  `serial_number_id` int NOT NULL,
  `business_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务名称',
  `format` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '格式[yyyy]表示年,[mm]标识月,[dd]表示日,[nnn]表示三位数字',
  `rule_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则格式。none没有周期, year 年周期, month月周期, day日周期',
  `init_number` int unsigned NOT NULL COMMENT '初始值',
  `step_random_range` int unsigned NOT NULL COMMENT '步长随机数',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `last_number` bigint DEFAULT NULL COMMENT '上次产生的单号, 默认为空',
  `last_time` datetime DEFAULT NULL COMMENT '上次产生的单号时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`serial_number_id`) USING BTREE,
  UNIQUE KEY `key_name` (`business_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='单号生成器定义表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_serial_number`
--

LOCK TABLES `t_serial_number` WRITE;
/*!40000 ALTER TABLE `t_serial_number` DISABLE KEYS */;
INSERT INTO `t_serial_number` VALUES (1,'订单编号','DK[yyyy][mm][dd]NO[nnnnn]','day',1000,10,'DK20201101NO321',1088,'2025-06-29 18:45:34','2025-06-29 18:45:33','2021-02-19 14:37:50'),(2,'合同编号','HT[yyyy][mm][dd][nnnnn]-CX','none',1,1,'HT2025062900010-CX',10,'2025-06-29 18:52:18','2025-06-29 18:52:21','2021-08-12 20:40:37'),(3,'订单编号-2','DK[yyyy][mm][dd][nnnnnn]','day',100000,10,'DK20250629100166',100005,'2025-07-22 23:34:15','2025-07-22 23:34:15','2025-06-29 18:44:55');
/*!40000 ALTER TABLE `t_serial_number` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_serial_number_record`
--

DROP TABLE IF EXISTS `t_serial_number_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_serial_number_record` (
  `serial_number_id` int NOT NULL,
  `record_date` date NOT NULL COMMENT '记录日期',
  `last_number` bigint NOT NULL DEFAULT '0' COMMENT '最后更新值',
  `last_time` datetime NOT NULL COMMENT '最后更新时间',
  `count` bigint NOT NULL DEFAULT '0' COMMENT '更新次数',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  KEY `uk_generator` (`serial_number_id`,`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='serial_number记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_serial_number_record`
--

LOCK TABLES `t_serial_number_record` WRITE;
/*!40000 ALTER TABLE `t_serial_number_record` DISABLE KEYS */;
INSERT INTO `t_serial_number_record` VALUES (1,'2025-06-29',1088,'2025-06-29 18:33:23',17,'2025-06-29 18:45:33','2025-06-29 18:33:22'),(2,'2025-06-29',10,'2025-06-29 18:33:44',2,'2025-06-29 18:52:18','2025-06-29 18:33:44'),(3,'2025-06-29',100167,'2025-06-29 18:47:07',31,'2025-06-29 18:52:01','2025-06-29 18:47:06'),(3,'2025-07-06',100019,'2025-07-06 22:19:33',4,'2025-07-06 22:41:16','2025-07-06 22:19:33'),(3,'2025-07-07',100045,'2025-07-07 09:51:13',10,'2025-07-07 22:45:22','2025-07-07 09:51:12'),(3,'2025-07-08',100006,'2025-07-08 15:29:53',1,'2025-07-08 15:29:52','2025-07-08 15:29:52'),(3,'2025-07-09',100016,'2025-07-09 21:10:22',2,'2025-07-09 21:12:05','2025-07-09 21:10:22'),(3,'2025-07-11',100036,'2025-07-11 19:55:39',6,'2025-07-11 20:11:12','2025-07-11 19:55:39'),(3,'2025-07-14',100007,'2025-07-14 14:25:46',2,'2025-07-14 14:33:41','2025-07-14 14:25:45'),(3,'2025-07-18',100131,'2025-07-18 12:49:30',25,'2025-07-18 20:47:30','2025-07-18 12:49:30'),(3,'2025-07-21',100071,'2025-07-21 11:11:31',12,'2025-07-21 22:39:06','2025-07-21 11:11:30'),(3,'2025-07-22',100005,'2025-07-22 17:10:21',2,'2025-07-22 23:34:15','2025-07-22 17:10:20');
/*!40000 ALTER TABLE `t_serial_number_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_smart_job`
--

DROP TABLE IF EXISTS `t_smart_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_smart_job` (
  `job_id` int NOT NULL AUTO_INCREMENT COMMENT '任务id',
  `job_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_class` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务执行类',
  `trigger_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发类型',
  `trigger_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发配置',
  `enabled_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启',
  `param` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数',
  `last_execute_time` datetime DEFAULT NULL COMMENT '最后一次执行时间',
  `last_execute_log_id` int DEFAULT NULL COMMENT '最后一次执行记录id',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态',
  `update_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`job_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务配置 @listen';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_smart_job`
--

LOCK TABLES `t_smart_job` WRITE;
/*!40000 ALTER TABLE `t_smart_job` DISABLE KEYS */;
INSERT INTO `t_smart_job` VALUES (1,'示例任务1','net.lab1024.sa.base.module.support.job.sample.SmartJobSample1','cron','10 15 0/1 * * *',0,'执行示例任务1','2025-06-25 16:15:10',8059,1,'执行示例任务1',0,'管理员','2024-06-17 20:00:46','2025-06-25 16:28:48'),(2,'示例任务2','net.lab1024.sa.base.module.support.job.sample.SmartJobSample2','fixed_delay','120',0,'执行示例任务2','2025-06-25 16:28:03',8065,2,'执行示例任务2',0,'管理员','2024-06-18 20:45:35','2025-06-25 16:28:46'),(4,'结算订单','net.lab1024.sa.admin.job.SettleOrderJob','fixed_delay','5',0,'','2025-07-08 21:18:03',99780,0,'结算订单',0,'管理员','2025-06-30 13:36:29','2025-07-08 21:18:04');
/*!40000 ALTER TABLE `t_smart_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_smart_job_log`
--

DROP TABLE IF EXISTS `t_smart_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_smart_job_log` (
  `log_id` int NOT NULL AUTO_INCREMENT,
  `job_id` int NOT NULL COMMENT '任务id',
  `job_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行参数',
  `success_flag` tinyint(1) NOT NULL COMMENT '是否成功',
  `execute_start_time` datetime NOT NULL COMMENT '执行开始时间',
  `execute_time_millis` int DEFAULT NULL COMMENT '执行时长',
  `execute_end_time` datetime DEFAULT NULL COMMENT '执行结束时间',
  `execute_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ip',
  `process_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '进程id',
  `program_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '程序目录',
  `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=99781 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务-执行记录 @listen';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_smart_job_log`
--

LOCK TABLES `t_smart_job_log` WRITE;
/*!40000 ALTER TABLE `t_smart_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_smart_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_table_column`
--

DROP TABLE IF EXISTS `t_table_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_table_column` (
  `table_column_id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_type` int NOT NULL COMMENT '用户类型',
  `table_id` int NOT NULL COMMENT '表格id',
  `columns` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '具体的表格列，存入的json',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_column_id`) USING BTREE,
  UNIQUE KEY `uni_employee_table` (`user_id`,`table_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='表格的自定义列存储';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_table_column`
--

LOCK TABLES `t_table_column` WRITE;
/*!40000 ALTER TABLE `t_table_column` DISABLE KEYS */;
INSERT INTO `t_table_column` VALUES (6,1,1,40001,'[{\"columnKey\":\"categoryName\",\"showFlag\":true,\"sort\":1,\"width\":150},{\"columnKey\":\"goodsName\",\"showFlag\":true,\"sort\":2,\"width\":150},{\"columnKey\":\"goodsStatus\",\"showFlag\":true,\"sort\":3,\"width\":150},{\"columnKey\":\"place\",\"showFlag\":false,\"sort\":4,\"width\":150},{\"columnKey\":\"price\",\"showFlag\":true,\"sort\":5,\"width\":100},{\"columnKey\":\"shelvesFlag\",\"showFlag\":true,\"sort\":6,\"width\":80},{\"columnKey\":\"remark\",\"showFlag\":true,\"sort\":7,\"width\":150},{\"columnKey\":\"createTime\",\"showFlag\":true,\"sort\":8,\"width\":150},{\"columnKey\":\"action\",\"showFlag\":true,\"sort\":9,\"width\":100}]','2025-07-04 14:49:14','2025-07-04 14:49:14');
/*!40000 ALTER TABLE `t_table_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_team_rewards`
--

DROP TABLE IF EXISTS `t_team_rewards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_team_rewards` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '奖励ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `reward_date` date NOT NULL COMMENT '奖励日期',
  `qualified_count` int NOT NULL COMMENT '达标人数',
  `reward_amount` decimal(10,2) NOT NULL COMMENT '奖励金额',
  `status` enum('pending','paid') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'pending' COMMENT '发放状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_date` (`user_id`,`reward_date`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  CONSTRAINT `fk_team_rewards_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='团队奖励表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_team_rewards`
--

LOCK TABLES `t_team_rewards` WRITE;
/*!40000 ALTER TABLE `t_team_rewards` DISABLE KEYS */;
INSERT INTO `t_team_rewards` VALUES (1,1,'2025-07-01',1,20.00,'pending','2025-07-01 09:16:07');
/*!40000 ALTER TABLE `t_team_rewards` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_address`
--

DROP TABLE IF EXISTS `t_user_address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_address` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `recipient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收件人姓名',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收件人电话',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `address_line` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  CONSTRAINT `fk_user_addresses_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户收货地址表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_address`
--

LOCK TABLES `t_user_address` WRITE;
/*!40000 ALTER TABLE `t_user_address` DISABLE KEYS */;
INSERT INTO `t_user_address` VALUES (1,1,'张学友','15977521663','广西','南宁','青秀','铜鼓岭59号',0,'2025-06-28 15:38:33'),(2,2,'张学友','15977521663','广西','南宁','青秀','铜鼓岭59号',1,'2025-06-28 15:45:44'),(3,1,'黎明','15977521664','广西','南宁','青秀','铜鼓岭60号',1,'2025-06-28 16:07:23'),(4,1,'刘德华','15977521665','广西','南宁','青秀','铜鼓岭61号',0,'2025-06-28 16:08:03'),(5,73,'张九','13800138000','广西区','南宁市','青秀区','测试街道123号',1,'2025-07-06 18:17:47'),(6,73,'李四','13800139000','广东','广州','越秀','测试地址123号',0,'2025-07-06 18:22:02'),(7,79,'111111','11111111','111','111','111','1111',1,'2025-07-09 21:10:17'),(8,81,'00','000','00','00','00','00',0,'2025-07-11 19:55:33'),(9,83,'啊啊啊啊啊啊啊','*********88','好👌','擦边','爸爸','爸爸啦啦啦啦啊啦啦',0,'2025-07-14 14:25:42');
/*!40000 ALTER TABLE `t_user_address` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_wallet_transactions`
--

DROP TABLE IF EXISTS `t_wallet_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_wallet_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '流水ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额(正数收入，负数支出)',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后可用余额',
  `experience_balance_after` decimal(15,2) DEFAULT NULL COMMENT '变动后不可用余额',
  `points_after` bigint DEFAULT NULL COMMENT '变动后积分',
  `mode` tinyint unsigned DEFAULT NULL COMMENT '操作类型: 0 可用余额,1 不可用余额, 2 积分',
  `type` enum('RECHARGE','WITHDRAWAL','PAYMENT','REFUND_WIN','REFUND_LOSS','SUBSIDY','COMMISSION','FIRST_REWARD','TEAM_REWARD','MANUAL_REDUCE','MANUAL_ADD','EXPERIENCE_GIFT','FREE_NOVICE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易类型',
  `related_id` bigint DEFAULT NULL COMMENT '关联业务ID',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_created` (`user_id`,`create_time`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `idx_related_id` (`related_id`) USING BTREE,
  CONSTRAINT `fk_wallet_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='钱包流水表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_wallet_transactions`
--

LOCK TABLES `t_wallet_transactions` WRITE;
/*!40000 ALTER TABLE `t_wallet_transactions` DISABLE KEYS */;
INSERT INTO `t_wallet_transactions` VALUES (1,1,200.00,10300.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 12:38:05'),(4,2,-200.00,600.00,0.00,10000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 12:44:06'),(5,1,10.00,10310.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:09:43'),(6,1,200.00,10510.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:09:47'),(7,1,90.00,10600.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:09:52'),(8,1,800.00,11400.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:09:59'),(9,1,-600.00,10800.00,5000.00,60000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 13:10:12'),(10,1,-300.00,10500.00,5000.00,60000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 13:10:18'),(11,1,-150.00,10350.00,5000.00,60000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 13:10:23'),(12,1,-10000.00,350.00,5000.00,60000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 13:10:28'),(13,1,50000.00,50350.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:10:58'),(14,1,-350.00,50000.00,5000.00,60000,0,'WITHDRAWAL',NULL,'后台提现','2025-06-28 13:11:18'),(15,1,1000.00,51000.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 13:11:25'),(16,1,4000.00,55000.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-28 14:00:21'),(17,2,200.00,800.00,0.00,10000,0,'RECHARGE',NULL,'后台充值','2025-06-28 14:00:56'),(18,2,300.00,1100.00,0.00,10000,0,'RECHARGE',NULL,'后台充值','2025-06-28 14:02:20'),(19,1,500.00,55500.00,5000.00,60000,0,'RECHARGE',NULL,'后台充值','2025-06-29 17:12:12'),(20,1,0.30,55500.30,5000.00,60000,0,'REFUND_LOSS',1,'未中奖励','2025-07-01 11:16:41'),(21,1,0.30,55500.60,5000.00,60000,0,'REFUND_LOSS',1,'未中奖励','2025-07-01 11:18:26'),(22,1,0.30,55500.90,5000.00,60000,0,'REFUND_WIN',1,'未中奖励','2025-07-01 11:32:42'),(23,1,10.00,55510.90,5000.00,60000,0,'REFUND_LOSS',1,'未中返还','2025-07-01 11:32:42'),(24,73,1000000.00,1000000.00,0.00,0,0,'RECHARGE',NULL,'后台充值','2025-07-06 12:01:02'),(25,73,88888888.00,1000000.00,0.00,88888888,2,'RECHARGE',NULL,'后台充值','2025-07-06 12:19:29'),(32,73,-6999.00,993001.00,0.00,88888888,0,'PAYMENT',8,'购物支付','2025-07-07 15:55:40'),(33,73,-6999.00,986002.00,0.00,88888888,0,'PAYMENT',9,'购物支付','2025-07-07 16:24:20'),(34,73,-6999.00,979003.00,0.00,88888888,0,'PAYMENT',10,'购物支付','2025-07-07 20:43:41'),(35,73,-6999.00,972004.00,0.00,88888888,0,'PAYMENT',11,'购物支付','2025-07-07 21:35:09'),(36,73,-6999.00,965005.00,0.00,88888888,0,'PAYMENT',12,'购物支付','2025-07-07 21:36:41'),(37,73,-6999.00,958006.00,0.00,88888888,0,'PAYMENT',13,'购物支付','2025-07-07 21:45:56'),(38,73,-6999.00,951007.00,0.00,88888888,0,'PAYMENT',14,'购物支付','2025-07-07 22:37:37'),(39,73,-6999.00,944008.00,0.00,88888888,0,'PAYMENT',15,'购物支付','2025-07-07 22:45:22'),(40,73,-6999.00,937009.00,0.00,88888888,0,'PAYMENT',16,'购物支付','2025-07-08 15:29:53'),(41,73,1000000.00,1937009.00,0.00,88888888,0,'RECHARGE',NULL,'后台充值','2025-07-09 09:36:29'),(42,77,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-09 13:12:11'),(43,73,-200000.00,1737009.00,0.00,88888888,0,'WITHDRAWAL',3,'提现通过','2025-07-09 16:12:16'),(44,78,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-09 19:46:02'),(45,79,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-09 21:08:45'),(46,79,-99.00,901.00,0.00,0,0,'PAYMENT',17,'购物支付','2025-07-09 21:10:22'),(47,79,-99.00,802.00,0.00,0,0,'PAYMENT',18,'购物支付','2025-07-09 21:12:06'),(48,80,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-09 21:27:43'),(49,81,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-11 19:52:16'),(50,81,-99.00,901.00,0.00,0,0,'PAYMENT',19,'购物支付','2025-07-11 19:55:39'),(51,81,-99.00,802.00,0.00,0,0,'PAYMENT',20,'购物支付','2025-07-11 19:57:22'),(52,81,-99.00,703.00,0.00,0,0,'PAYMENT',21,'购物支付','2025-07-11 19:58:06'),(53,81,-99.00,604.00,0.00,0,0,'PAYMENT',22,'购物支付','2025-07-11 20:01:52'),(54,81,-99.00,505.00,0.00,0,0,'PAYMENT',23,'购物支付','2025-07-11 20:10:47'),(55,81,-99.00,406.00,0.00,0,0,'PAYMENT',24,'购物支付','2025-07-11 20:11:12'),(56,82,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-12 14:57:12'),(57,83,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-14 14:22:50'),(58,83,-99.00,901.00,0.00,0,0,'PAYMENT',25,'购物支付','2025-07-14 14:25:46'),(59,83,-99.00,802.00,0.00,0,0,'PAYMENT',26,'购物支付','2025-07-14 14:33:41'),(60,84,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-17 09:56:37'),(61,73,-6999.00,1730010.00,0.00,88888888,0,'PAYMENT',27,'购物支付','2025-07-18 12:49:30'),(62,73,-6999.00,1723011.00,0.00,88888888,0,'PAYMENT',28,'购物支付','2025-07-18 12:55:19'),(63,73,-6999.00,1716012.00,0.00,88888888,0,'PAYMENT',29,'购物支付','2025-07-18 13:02:14'),(68,73,-6999.00,1709013.00,0.00,88888888,0,'PAYMENT',34,'购物支付','2025-07-18 13:13:09'),(70,73,-6999.00,1702014.00,0.00,88888888,0,'PAYMENT',36,'购物支付','2025-07-18 15:58:58'),(72,73,-6999.00,1695015.00,0.00,88888888,0,'PAYMENT',38,'购物支付','2025-07-18 16:09:58'),(75,73,-3999.00,1691016.00,0.00,88888888,0,'PAYMENT',41,'购物支付','2025-07-18 16:22:28'),(76,73,-3999.00,1687017.00,0.00,88888888,0,'PAYMENT',42,'购物支付','2025-07-18 16:26:35'),(77,73,-3999.00,1683018.00,0.00,88888888,0,'PAYMENT',43,'购物支付','2025-07-18 16:41:04'),(78,73,-3999.00,1679019.00,0.00,88888888,0,'PAYMENT',44,'购物支付','2025-07-18 16:47:21'),(79,73,-3999.00,1675020.00,0.00,88888888,0,'PAYMENT',45,'购物支付','2025-07-18 20:04:49'),(80,73,-3999.00,1671021.00,0.00,88888888,0,'PAYMENT',46,'购物支付','2025-07-18 20:11:40'),(81,73,-3999.00,1667022.00,0.00,88888888,0,'PAYMENT',47,'购物支付','2025-07-18 20:16:38'),(82,73,-3999.00,1663023.00,0.00,88888888,0,'PAYMENT',48,'购物支付','2025-07-18 20:20:43'),(83,73,-3999.00,1659024.00,0.00,88888888,0,'PAYMENT',49,'购物支付','2025-07-18 20:24:31'),(84,73,-3999.00,1655025.00,0.00,88888888,0,'PAYMENT',50,'购物支付','2025-07-18 20:28:43'),(85,73,-3999.00,1651026.00,0.00,88888888,0,'PAYMENT',51,'购物支付','2025-07-18 20:47:31'),(86,85,1000.00,1000.00,0.00,0,0,'FREE_NOVICE',NULL,'赠送注册现金','2025-07-18 23:10:44'),(87,73,-3999.00,1647027.00,0.00,88888888,0,'PAYMENT',52,'购物支付','2025-07-21 11:11:31'),(88,73,-3999.00,1643028.00,0.00,88888888,0,'PAYMENT',53,'购物支付','2025-07-21 16:02:55'),(89,73,-3999.00,1639029.00,0.00,88888888,0,'PAYMENT',54,'购物支付','2025-07-21 16:13:14'),(90,73,-6999.00,1632030.00,0.00,88888888,0,'PAYMENT',55,'购物支付','2025-07-21 18:13:36'),(91,73,-3999.00,1628031.00,0.00,88888888,0,'PAYMENT',56,'购物支付','2025-07-21 19:30:08'),(92,73,-3999.00,1624032.00,0.00,88888888,0,'PAYMENT',57,'购物支付','2025-07-21 22:05:15'),(93,73,-3999.00,1620033.00,0.00,88888888,0,'PAYMENT',58,'购物支付','2025-07-21 22:06:14'),(94,73,-6999.00,1613034.00,0.00,88888888,0,'PAYMENT',59,'购物支付','2025-07-21 22:07:06'),(95,83,100000000.00,100000802.00,0.00,0,0,'RECHARGE',NULL,'后台充值','2025-07-21 22:19:12'),(96,73,100000000.00,101613034.00,0.00,88888888,0,'RECHARGE',NULL,'后台充值','2025-07-21 22:19:36'),(97,73,10000000.00,101613034.00,0.00,98888888,2,'RECHARGE',NULL,'后台充值','2025-07-21 22:19:47'),(98,73,-6999.00,101606035.00,0.00,98888888,0,'PAYMENT',60,'购物支付','2025-07-21 22:35:32'),(99,73,-5999.00,101600036.00,0.00,98888888,0,'PAYMENT',61,'购物支付','2025-07-21 22:36:01'),(100,83,-6999.00,99993803.00,0.00,0,0,'PAYMENT',62,'购物支付','2025-07-21 22:38:07'),(101,83,-5999.00,99987804.00,0.00,0,0,'PAYMENT',63,'购物支付','2025-07-21 22:39:07'),(102,73,-6999.00,101593037.00,0.00,98888888,0,'PAYMENT',64,'购物支付','2025-07-22 17:10:21'),(103,73,-6999.00,101586038.00,0.00,98888888,0,'PAYMENT',65,'购物支付','2025-07-22 23:34:15');
/*!40000 ALTER TABLE `t_wallet_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_wallets`
--

DROP TABLE IF EXISTS `t_wallets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_wallets` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额(可提现)',
  `experience_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '体验金(不可提现)',
  `points` bigint NOT NULL DEFAULT '0' COMMENT '用户积分',
  `total_recharge` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_withdraw` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现金额',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '钱包状态: 1 可用, 其它不可用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  CONSTRAINT `fk_wallets_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户钱包表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_wallets`
--

LOCK TABLES `t_wallets` WRITE;
/*!40000 ALTER TABLE `t_wallets` DISABLE KEYS */;
INSERT INTO `t_wallets` VALUES (1,55510.90,5000.00,60000,56910.90,11400.00,1,'2025-06-27 19:18:30','2025-07-01 11:32:41'),(2,1100.00,0.00,10000,800.00,700.00,1,'2025-06-27 19:41:51','2025-06-28 14:02:19'),(44,0.00,0.00,0,0.00,0.00,1,'2025-07-01 16:24:14',NULL),(48,0.00,0.00,0,0.00,0.00,1,'2025-06-28 10:51:01','2025-06-28 14:02:33'),(64,0.00,0.00,0,0.00,0.00,1,'2025-06-28 11:08:48',NULL),(65,0.00,0.00,0,0.00,0.00,1,'2025-06-28 10:52:27',NULL),(68,0.00,0.00,0,0.00,0.00,1,'2025-06-28 11:10:53',NULL),(73,101586038.00,0.00,98888888,102000000.00,413962.00,1,'2025-06-28 10:53:15','2025-07-22 23:34:15'),(74,0.00,0.00,0,0.00,0.00,1,'2025-06-28 11:08:41',NULL),(77,1000.00,0.00,0,1000.00,0.00,1,'2025-07-09 13:12:11','2025-07-09 13:12:10'),(78,1000.00,0.00,0,1000.00,0.00,1,'2025-07-09 19:46:02','2025-07-09 19:46:01'),(79,802.00,0.00,0,1000.00,198.00,1,'2025-07-09 21:08:45','2025-07-09 21:12:05'),(80,1000.00,0.00,0,1000.00,0.00,1,'2025-07-09 21:27:43','2025-07-09 21:27:42'),(81,406.00,0.00,0,1000.00,594.00,1,'2025-07-11 19:52:16','2025-07-11 20:11:12'),(82,1000.00,0.00,0,1000.00,0.00,1,'2025-07-12 14:57:12','2025-07-12 14:57:12'),(83,99987804.00,0.00,0,100001000.00,13196.00,1,'2025-07-14 14:22:50','2025-07-21 22:39:06'),(84,1000.00,0.00,0,1000.00,0.00,1,'2025-07-17 09:56:37','2025-07-17 09:56:36'),(85,1000.00,0.00,0,1000.00,0.00,1,'2025-07-18 23:10:44','2025-07-18 23:10:43');
/*!40000 ALTER TABLE `t_wallets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_withdrawals`
--

DROP TABLE IF EXISTS `t_withdrawals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_withdrawals` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '申请金额',
  `fee` decimal(15,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '实际到账',
  `status` enum('pending','approved','rejected','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行名称',
  `bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行账号',
  `account_holder` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '账户姓名',
  `rejection_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '驳回原因',
  `processed_by` bigint DEFAULT NULL COMMENT '处理人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_created` (`user_id`,`create_time`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_processed_by` (`processed_by`) USING BTREE,
  CONSTRAINT `fk_withdrawals_user` FOREIGN KEY (`user_id`) REFERENCES `t_employee` (`employee_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='提现申请表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_withdrawals`
--

LOCK TABLES `t_withdrawals` WRITE;
/*!40000 ALTER TABLE `t_withdrawals` DISABLE KEYS */;
INSERT INTO `t_withdrawals` VALUES (1,1,10.00,0.00,20.00,'rejected','轱辘','123','123','银行账号错误',1,'2025-07-01 08:40:26','2025-07-01 10:45:20'),(2,73,200000.00,0.00,0.00,'pending','越南银行','**********','布洛林',NULL,NULL,'2025-07-09 16:10:30',NULL),(3,73,200000.00,0.00,200000.00,'approved','Vietcombank','*********','321321',NULL,1,'2025-07-09 16:11:32','2025-07-09 16:12:16');
/*!40000 ALTER TABLE `t_withdrawals` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-23 15:49:12
