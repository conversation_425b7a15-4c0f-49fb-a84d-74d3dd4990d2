# 地址接口测试功能说明

## 📍 地址管理API测试工具

已根据 `地址接口.txt` 文档完整实现了地址管理相关的API测试功能，包含以下四个核心接口的测试：

### 🎯 支持的接口

#### 1. 地址列表接口
- **接口路径**: `GET /api/v1/address`
- **功能**: 获取用户所有收货地址
- **认证**: 需要登录
- **返回数据**: 分页地址列表，包含默认地址标识

#### 2. 添加地址接口
- **接口路径**: `POST /api/v1/address/add`
- **功能**: 添加新的收货地址
- **认证**: 需要登录
- **必填参数**:
  - `recipientName`: 收货人姓名
  - `phoneNumber`: 手机号码
  - `addressLine`: 详细地址
  - `isDefault`: 是否默认地址（1是0否）
- **可选参数**:
  - `province`: 省份
  - `city`: 城市
  - `district`: 区县

#### 3. 修改地址接口
- **接口路径**: `POST /api/v1/address/update`
- **功能**: 修改现有收货地址
- **认证**: 需要登录
- **必填参数**:
  - `id`: 地址ID
  - `recipientName`: 收货人姓名
  - `phoneNumber`: 手机号码
  - `addressLine`: 详细地址
  - `isDefault`: 是否默认地址

#### 4. 删除地址接口
- **接口路径**: `GET /api/v1/address/delete/{id}`
- **功能**: 删除指定收货地址
- **认证**: 需要登录
- **参数**: 地址ID（URL路径参数）

### 🛠️ 测试功能特点

#### 1. 完整的表单验证
- ✅ 必填参数检查
- ✅ 数据格式验证
- ✅ 友好的错误提示

#### 2. 预设测试数据
- ✅ 默认填充测试数据
- ✅ 符合中国地址格式
- ✅ 便于快速测试

#### 3. 安全操作提示
- ✅ 删除操作警告提示
- ✅ 批量测试排除删除操作
- ✅ 防止误操作

#### 4. 批量测试支持
- ✅ 一键测试所有地址接口
- ✅ 智能跳过危险操作
- ✅ 详细的测试日志

### 📋 使用方法

#### 1. 单个接口测试
1. 确保已登录（获取认证Token）
2. 选择要测试的地址接口卡片
3. 填写相应的测试参数
4. 点击测试按钮查看结果

#### 2. 批量测试
1. 确保已登录
2. 点击"📍 测试地址管理"按钮
3. 系统会依次测试：地址列表 → 添加地址 → 修改地址
4. 查看各接口的测试结果

#### 3. 测试建议流程
1. **先测试地址列表**：查看现有地址
2. **测试添加地址**：添加新的测试地址
3. **再次查看列表**：确认添加成功
4. **测试修改地址**：修改刚添加的地址
5. **最后测试删除**：清理测试数据

### 🔧 测试参数说明

#### 默认测试数据
```json
{
  "recipientName": "张三",
  "phoneNumber": "13800138000", 
  "province": "广西区",
  "city": "南宁市",
  "district": "青秀区",
  "addressLine": "测试街道123号",
  "isDefault": 0
}
```

#### 修改测试数据
```json
{
  "id": 8,
  "recipientName": "李四",
  "phoneNumber": "13900139000",
  "addressLine": "修改后的地址456号",
  "isDefault": 1
}
```

### 📊 接口响应格式

#### 成功响应
```json
{
  "code": 0,
  "msg": "success", 
  "ok": true,
  "data": "地址ID或地址列表",
  "dataType": 1
}
```

#### 错误响应
```json
{
  "code": 非0值,
  "msg": "错误信息",
  "ok": false
}
```

### ⚠️ 注意事项

#### 1. 认证要求
- 所有地址接口都需要用户登录
- 使用Bearer Token认证
- Token失效时会返回30007/401/403错误

#### 2. 数据安全
- 删除操作不可恢复
- 建议在测试环境进行
- 批量测试不包含删除操作

#### 3. 测试建议
- 先查看现有地址再进行操作
- 使用不重要的测试数据
- 及时清理测试产生的数据

#### 4. 错误处理
- 参数验证失败会提示具体错误
- 网络异常会显示连接状态
- 认证失败会引导重新登录

### 🚀 扩展功能

#### 1. 自定义测试数据
- 可修改默认的测试参数
- 支持各种地址格式测试
- 灵活的参数组合

#### 2. 测试结果分析
- 详细的响应数据展示
- 请求时间统计
- 成功率统计

#### 3. 批量操作
- 支持批量测试所有接口
- 智能跳过危险操作
- 自动间隔控制

### 📈 测试统计

测试工具会自动统计：
- 总测试次数
- 成功/失败次数  
- 平均响应时间
- 最后测试时间
- 成功率百分比

### 🔮 后续优化

1. **参数模板**: 保存常用的测试参数组合
2. **数据导入**: 支持从文件导入测试数据
3. **自动化测试**: 支持测试用例的自动执行
4. **结果导出**: 支持测试结果的导出功能
5. **性能测试**: 支持并发和压力测试

---

## 使用示例

### 完整测试流程
1. 打开测试工具页面
2. 配置API服务器地址
3. 登录获取认证Token
4. 点击"📍 测试地址管理"进行批量测试
5. 查看各接口的测试结果
6. 根据需要进行单个接口的详细测试

### 快速开始
```bash
# 1. 打开浏览器访问测试工具
# 2. 登录系统
手机号: 18906662339
密码: IGG50em@

# 3. 批量测试地址接口
点击 "📍 测试地址管理" 按钮

# 4. 查看测试结果
所有接口测试完成后查看响应数据
``` 