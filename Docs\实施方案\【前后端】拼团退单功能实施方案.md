# 拼团退单功能实施方案

## 📋 需求分析

**核心需求**：在拼团购买且拼中时，用户可以选择不收货并申请退单，系统扣除违约金后退还剩余款项。

## 🏗️ 整体实施方案

### 1. 数据库设计

#### 1.1 系统配置表扩展
**表名**: `t_config` 
```sql
-- 添加违约金比例配置
INSERT INTO t_config (config_key, config_value, config_name, config_description) 
VALUES ('BREACH_FEE_RATE', '0.10', '违约金比例', '拼团退单违约金比例，0.10表示10%');
```

#### 1.2 订单表扩展
**表名**: `t_orders`
```sql
-- 添加退单相关字段
ALTER TABLE t_orders ADD COLUMN breach_fee_rate DECIMAL(5,4) DEFAULT 0 COMMENT '违约金比例';
ALTER TABLE t_orders ADD COLUMN breach_fee_amount DECIMAL(10,2) DEFAULT 0 COMMENT '违约金金额';
ALTER TABLE t_orders ADD COLUMN refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '实际退款金额';
ALTER TABLE t_orders ADD COLUMN cancel_reason VARCHAR(200) DEFAULT NULL COMMENT '退单原因';
ALTER TABLE t_orders ADD COLUMN cancel_time DATETIME DEFAULT NULL COMMENT '退单时间';
```

#### 1.3 钱包表扩展
**表名**: `t_wallets`
```sql
-- 添加累计违约金字段
ALTER TABLE t_wallets ADD COLUMN total_breach_fee DECIMAL(10,2) DEFAULT 0 COMMENT '累计违约金';
```

#### 1.4 交易类型常量扩展
```java
// 新增交易类型
public static final String BREACH_FEE = "BREACH_FEE"; // 违约金扣除
public static final String BREACH_REFUND = "BREACH_REFUND"; // 违约退款
```

### 2. 后端接口设计

#### 2.1 订单退单接口
**接口路径**: `POST /app/v1/orders/{orderId}/cancel`

**请求参数**:
```json
{
  "reason": "不想要了", // 退单原因
  "confirmBreach": true // 确认接受违约金扣除
}
```

**响应参数**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "orderId": 12345,
    "originalAmount": 100000, // 原支付金额
    "breachFeeRate": 0.10, // 违约金比例
    "breachFeeAmount": 10000, // 违约金金额
    "refundAmount": 90000, // 实际退款金额
    "processTime": "2025-08-01 12:30:00"
  }
}
```

#### 2.2 违约金配置接口
**接口路径**: `GET /app/v1/config/breach-fee-rate`

**响应参数**:
```json
{
  "code": 0,
  "data": {
    "breachFeeRate": 0.10,
    "description": "退单需扣除订单金额的10%作为违约金"
  }
}
```

#### 2.3 钱包信息扩展
**接口路径**: `GET /app/v1/wallet` (现有接口扩展)

**响应参数扩展**:
```json
{
  "code": 0,
  "data": {
    "balance": {
      // ... 现有字段
      "totalBreachFee": 50000 // 累计违约金
    }
  }
}
```

### 3. 业务流程设计

#### 3.1 退单前置条件检查
1. 订单状态必须是 `PAID`（已支付）或 `SHIPPING`（待发货）
2. 订单类型必须是拼团订单
3. 拼团状态必须是成功（拼中状态）
4. 不能是已退单或已完成的订单

#### 3.2 退单处理流程
```
1. 验证退单条件
2. 获取违约金比例配置
3. 计算违约金和退款金额
4. 创建违约金扣除交易记录
5. 创建退款交易记录
6. 更新订单状态为已退单
7. 更新钱包累计违约金
8. 返回处理结果
```

#### 3.3 交易记录处理
- **违约金记录**: 类型 `BREACH_FEE`，金额为负数，描述为"拼团退单违约金"
- **退款记录**: 类型 `BREACH_REFUND`，金额为正数，描述为"拼团退单退款"

### 4. 前端界面设计

#### 4.1 结算成功页面改造
**位置**: `SettlementSuccessPage.vue`

**新增内容**:
- 收货确认按钮（原有逻辑）
- 申请退单按钮（新增）
- 退单说明文字（违约金比例说明）

#### 4.2 退单确认弹窗
**功能**:
- 显示违约金比例和金额
- 显示实际退款金额
- 退单原因输入
- 确认和取消按钮

#### 4.3 钱包页面扩展
**位置**: `ProfilePage.vue`

**新增内容**:
- 在钱包信息中显示累计违约金
- 在交易记录中显示违约金和退款记录

### 5. 核心代码结构

#### 5.1 后端服务层
```java
@Service
public class OrderCancelService {
    
    /**
     * 处理拼团退单
     */
    @Transactional
    public OrderCancelResultVO cancelGroupOrder(Long orderId, String reason) {
        // 1. 验证订单状态
        // 2. 获取违约金配置
        // 3. 计算费用
        // 4. 处理退款
        // 5. 更新订单和钱包
        // 6. 记录交易流水
    }
    
    /**
     * 获取违约金配置
     */
    public BigDecimal getBreachFeeRate() {
        // 从配置表获取违约金比例
    }
}
```

#### 5.2 前端API服务
```javascript
// 订单退单API
export const cancelGroupOrder = (orderId, reason) => {
  return request({
    url: `/orders/${orderId}/cancel`,
    method: 'post',
    data: { reason, confirmBreach: true }
  })
}

// 获取违约金配置
export const getBreachFeeRate = () => {
  return request({
    url: '/config/breach-fee-rate',
    method: 'get'
  })
}
```

### 6. 实施步骤

1. **数据库改造**（1天）
   - 修改表结构
   - 添加配置数据
   - 数据迁移脚本

2. **后端开发**（2-3天）
   - 退单服务开发
   - API接口开发
   - 业务逻辑测试

3. **前端开发**（2天）
   - 界面改造
   - 交互逻辑
   - API集成

4. **联调测试**（1天）
   - 功能测试
   - 边界测试
   - 性能测试

5. **部署上线**（0.5天）
   - 生产环境部署
   - 数据验证
   - 功能验证

### 7. 风险控制

- **数据一致性**: 使用事务确保退单过程的原子性
- **重复提交**: 添加订单状态检查防止重复退单
- **配置安全**: 违约金比例可在后台动态配置
- **审计追踪**: 完整记录退单操作和交易流水

### 8. 技术要点

#### 8.1 订单状态管理
```java
// 新增订单状态
public enum OrderStatusEnum {
    // ... 现有状态
    CANCELLED_BREACH("CANCELLED_BREACH", "违约退单"),
    // ...
}
```

#### 8.2 违约金计算逻辑
```java
public class BreachFeeCalculator {
    
    public BreachFeeResult calculateBreachFee(OrderEntity order, BigDecimal feeRate) {
        BigDecimal originalAmount = order.getTotalAmount();
        BigDecimal breachFeeAmount = originalAmount.multiply(feeRate);
        BigDecimal refundAmount = originalAmount.subtract(breachFeeAmount);
        
        return BreachFeeResult.builder()
            .originalAmount(originalAmount)
            .breachFeeRate(feeRate)
            .breachFeeAmount(breachFeeAmount)
            .refundAmount(refundAmount)
            .build();
    }
}
```

#### 8.3 前端状态管理
```javascript
// 退单状态管理
const cancelOrderState = reactive({
  loading: false,
  breachFeeRate: 0,
  showConfirmDialog: false,
  cancelReason: '',
  calculatedAmounts: null
})
```

### 9. 测试用例

#### 9.1 正常流程测试
1. 拼团成功后选择退单
2. 确认违约金扣除
3. 验证退款金额计算
4. 验证交易记录生成

#### 9.2 异常流程测试
1. 重复退单请求
2. 订单状态不符合条件
3. 网络异常处理
4. 数据库异常回滚

#### 9.3 边界条件测试
1. 违约金比例为0的情况
2. 违约金比例为100%的情况
3. 订单金额为0的情况

---

**总体评估**: 
- **开发工期**: 5-6天
- **技术难度**: 中等
- **业务风险**: 低（有完整的数据追踪和验证）
- **维护成本**: 低（配置化管理，逻辑清晰）

## 附录

### A. 数据库脚本汇总

```sql
-- 1. 订单表扩展
ALTER TABLE t_orders ADD COLUMN breach_fee_rate DECIMAL(5,4) DEFAULT 0 COMMENT '违约金比例';
ALTER TABLE t_orders ADD COLUMN breach_fee_amount DECIMAL(10,2) DEFAULT 0 COMMENT '违约金金额';
ALTER TABLE t_orders ADD COLUMN refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '实际退款金额';
ALTER TABLE t_orders ADD COLUMN cancel_reason VARCHAR(200) DEFAULT NULL COMMENT '退单原因';
ALTER TABLE t_orders ADD COLUMN cancel_time DATETIME DEFAULT NULL COMMENT '退单时间';

-- 2. 钱包表扩展
ALTER TABLE t_wallets ADD COLUMN total_breach_fee DECIMAL(10,2) DEFAULT 0 COMMENT '累计违约金';

-- 3. 系统配置
INSERT INTO t_config (config_key, config_value, config_name, config_description) 
VALUES ('BREACH_FEE_RATE', '0.10', '违约金比例', '拼团退单违约金比例，0.10表示10%');
```

### B. API接口汇总

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 订单退单 | POST | `/app/v1/orders/{orderId}/cancel` | 处理拼团退单 |
| 违约金配置 | GET | `/app/v1/config/breach-fee-rate` | 获取违约金比例 |
| 钱包信息 | GET | `/app/v1/wallet` | 扩展累计违约金字段 |

### C. 前端页面改动清单

| 页面 | 文件路径 | 改动说明 |
|------|----------|----------|
| 结算成功页 | `/views/settlement/SettlementSuccessPage.vue` | 增加退单选项和确认弹窗 |
| 个人中心 | `/views/user/ProfilePage.vue` | 钱包信息显示累计违约金 |
| 交易记录 | `/views/user/WalletPage.vue` | 支持违约金相关交易类型 |

---

**文档版本**: v1.0  
**创建时间**: 2025-08-01  
**最后更新**: 2025-08-01  
**负责人**: Claude Code Assistant