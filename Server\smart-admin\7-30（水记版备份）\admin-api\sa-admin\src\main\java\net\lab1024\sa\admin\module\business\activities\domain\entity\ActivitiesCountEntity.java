package net.lab1024.sa.admin.module.business.activities.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;


@Data
@TableName(value = "t_activities_count", autoResultMap = true)
public class ActivitiesCountEntity {

    /**
     * 用户ID
     */
    @TableId
    private Long userId;

    /**
     * 辩手团计数
     */
    private Integer noviceCount;
    /**
     * 辩手团计数
     */
    private BigDecimal novice;

    /**
     * 低价团计数
     */
    private Integer lowPriceCount;
    /**
     * 低价团计数
     */
    private BigDecimal lowPrice;

    /**
     * 高价团计数
     */
    private Integer highPriceCount;
    /**
     * 高价团计数
     */
    private BigDecimal highPrice;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
