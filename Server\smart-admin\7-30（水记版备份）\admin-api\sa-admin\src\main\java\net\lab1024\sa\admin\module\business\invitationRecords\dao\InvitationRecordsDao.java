package net.lab1024.sa.admin.module.business.invitationRecords.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.entity.InvitationRecordsEntity;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.form.InvitationRecordsQueryForm;
import net.lab1024.sa.admin.module.business.invitationRecords.domain.vo.InvitationRecordsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


/**
 * 邀请记录表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-30 21:43:36
 * @Copyright -
 */

@Mapper
public interface InvitationRecordsDao extends BaseMapper<InvitationRecordsEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<InvitationRecordsVO> queryPage(Page page, @Param("queryForm") InvitationRecordsQueryForm queryForm);

    @Select("SELECT * FROM t_invitation_records WHERE invitee_id=#{userId} LIMIT 1")
    InvitationRecordsEntity selectByInviteeId(@Param("userId") Long userId);
}
