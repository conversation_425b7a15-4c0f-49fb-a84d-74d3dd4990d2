#!/bin/bash

# 健壮的备份脚本 - 带超时和进度监控
# 使用方法: ./robust_backup.sh [数据库名]

SERVER_IP="*************"
DATABASE_NAME=${1:-"tgw_pp"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_${TIMESTAMP}.sql"
LOCAL_BACKUP_DIR="DataBackup"

echo "=== MySQL 数据库备份脚本 (增强版) ==="
echo "数据库: $DATABASE_NAME"
echo "服务器: $SERVER_IP"
echo "备份文件: $BACKUP_FILENAME"
echo ""

# 创建本地备份目录
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "创建备份目录: $LOCAL_BACKUP_DIR"
    mkdir -p "$LOCAL_BACKUP_DIR"
fi

echo "📋 密码说明:"
echo "   SSH密码: 服务器登录密码"
echo "   MySQL密码: 数据库密码"
echo ""

echo "🔄 开始备份流程..."
echo ">>> 请输入SSH密码 (服务器登录密码):"

# 使用超时和进度监控的备份
ssh -o ServerAliveInterval=30 -o ServerAliveCountMax=3 root@$SERVER_IP "
echo '✅ 已连接到服务器'
echo ''

# 检查MySQL服务
echo '🔍 检查MySQL服务状态...'
if ! systemctl is-active --quiet mysql; then
    echo '❌ MySQL服务未运行，尝试启动...'
    systemctl start mysql
    if [ \$? -ne 0 ]; then
        echo '❌ 无法启动MySQL服务'
        exit 1
    fi
fi
echo '✅ MySQL服务正常'
echo ''

# 检查数据库大小
echo '📊 检查数据库信息...'
echo '>>> 请输入MySQL密码:'
DB_SIZE=\$(mysql -u root -p -e \"
SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
FROM information_schema.tables 
WHERE table_schema = '$DATABASE_NAME';\" 2>/dev/null | tail -n 1)

if [ -z \"\$DB_SIZE\" ] || [ \"\$DB_SIZE\" = \"NULL\" ]; then
    echo '❌ 数据库 \"$DATABASE_NAME\" 不存在或为空'
    echo '📋 可用的数据库:'
    mysql -u root -p -e 'SHOW DATABASES;' 2>/dev/null
    exit 1
fi

echo \"✅ 数据库大小: \${DB_SIZE} MB\"

# 估算备份时间
if (( \$(echo \"\$DB_SIZE > 100\" | bc -l) )); then
    echo '⏰ 数据库较大，备份可能需要几分钟时间'
elif (( \$(echo \"\$DB_SIZE > 10\" | bc -l) )); then
    echo '⏰ 预计备份时间: 1-2分钟'
else
    echo '⏰ 预计备份时间: 30秒内'
fi
echo ''

# 创建带进度监控的备份脚本
cat > /tmp/backup_with_progress.sh << 'BACKUP_EOF'
#!/bin/bash
DB_NAME=\"$DATABASE_NAME\"
BACKUP_FILE=\"/root/$BACKUP_FILENAME\"

echo '💾 开始备份数据库...'
echo '>>> 请输入MySQL密码:'

# 后台执行备份
mysqldump -u root -p --single-transaction --routines --triggers --set-gtid-purged=OFF \"\$DB_NAME\" > \"\$BACKUP_FILE\" 2>/tmp/backup_error.log &
BACKUP_PID=\$!

# 监控备份进程
echo '📈 备份进行中...'
COUNTER=0
while kill -0 \$BACKUP_PID 2>/dev/null; do
    COUNTER=\$((COUNTER + 1))
    if [ -f \"\$BACKUP_FILE\" ]; then
        CURRENT_SIZE=\$(stat -c%s \"\$BACKUP_FILE\" 2>/dev/null || echo 0)
        CURRENT_SIZE_MB=\$(echo \"scale=2; \$CURRENT_SIZE / 1024 / 1024\" | bc -l 2>/dev/null || echo \"0\")
        printf \"\\r⏳ 备份中... 已生成: \${CURRENT_SIZE_MB} MB (耗时: \${COUNTER}0秒)\"
    else
        printf \"\\r⏳ 备份中... (耗时: \${COUNTER}0秒)\"
    fi
    sleep 10
done

echo \"\"
echo \"\"

# 检查备份结果
wait \$BACKUP_PID
BACKUP_RESULT=\$?

if [ \$BACKUP_RESULT -eq 0 ]; then
    if [ -f \"\$BACKUP_FILE\" ]; then
        FILE_SIZE=\$(stat -c%s \"\$BACKUP_FILE\")
        if [ \$FILE_SIZE -gt 0 ]; then
            echo \"✅ 备份成功! 文件大小: \$(ls -lh \"\$BACKUP_FILE\" | awk '{print \$5}')\"
            echo \"📁 备份文件: \$BACKUP_FILE\"
            
            # 验证备份文件内容
            if head -5 \"\$BACKUP_FILE\" | grep -q \"MySQL dump\"; then
                echo \"✅ 备份文件格式正确\"
            else
                echo \"⚠️  备份文件格式可能有问题\"
            fi
        else
            echo \"❌ 备份文件为空\"
            cat /tmp/backup_error.log
            exit 1
        fi
    else
        echo \"❌ 备份文件未生成\"
        cat /tmp/backup_error.log
        exit 1
    fi
else
    echo \"❌ 备份失败\"
    cat /tmp/backup_error.log
    exit 1
fi
BACKUP_EOF

chmod +x /tmp/backup_with_progress.sh
/tmp/backup_with_progress.sh

# 清理临时文件
rm -f /tmp/backup_with_progress.sh /tmp/backup_error.log
"

# 检查SSH命令执行结果
if [ $? -ne 0 ]; then
    echo "❌ 服务器端操作失败"
    exit 1
fi

echo ""
echo "📥 下载备份文件到本地..."
echo ">>> 请输入SSH密码 (下载文件):"

# 使用压缩传输加速下载
scp -C root@$SERVER_IP:/root/$BACKUP_FILENAME "$LOCAL_BACKUP_DIR/"

if [ $? -ne 0 ]; then
    echo "❌ 文件下载失败"
    exit 1
fi

# 验证本地文件
LOCAL_FILE_PATH="$LOCAL_BACKUP_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    LOCAL_FILE_SIZE=$(stat -c%s "$LOCAL_FILE_PATH" 2>/dev/null || stat -f%z "$LOCAL_FILE_PATH" 2>/dev/null)
    if [ "$LOCAL_FILE_SIZE" -gt 0 ]; then
        echo "✅ 文件下载成功!"
        echo "📁 本地文件: $LOCAL_FILE_PATH"
        echo "📊 文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"
        
        # 验证本地文件内容
        if head -5 "$LOCAL_FILE_PATH" | grep -q "MySQL dump"; then
            echo "✅ 本地文件验证通过"
        else
            echo "⚠️  本地文件可能有问题"
        fi
    else
        echo "❌ 本地文件为空"
        exit 1
    fi
else
    echo "❌ 本地文件不存在"
    exit 1
fi

# 询问是否清理远程文件
echo ""
echo "🗑️  清理选项:"
read -p "是否删除服务器上的备份文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ">>> 请输入SSH密码 (删除远程文件):"
    ssh root@$SERVER_IP "rm -f /root/$BACKUP_FILENAME && echo '✅ 远程文件已删除'"
else
    echo "📁 远程文件保留在: /root/$BACKUP_FILENAME"
fi

echo ""
echo "🎉 备份完成!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📁 本地文件: $LOCAL_FILE_PATH"
echo "📊 文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"
echo "🕐 备份时间: $(date)"

echo ""
echo "💡 提示:"
echo "   - 如果备份经常卡住，可能是数据库过大或有锁定"
echo "   - 建议在数据库使用较少的时间进行备份"
echo "   - 可以考虑配置SSH密钥避免重复输入密码"
