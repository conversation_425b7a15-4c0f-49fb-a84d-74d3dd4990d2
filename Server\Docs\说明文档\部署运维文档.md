# 团购网部署运维文档

## 1. 部署架构概览

### 1.1 整体部署架构
```
                    ┌─────────────────┐
                    │   用户终端      │
                    │ (Mobile/Web)    │
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │   CDN分发       │
                    │  (静态资源)     │
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │   负载均衡      │
                    │    (Nginx)      │
                    └─────────────────┘
                             │
            ┌────────────────┼────────────────┐
            │                │                │
   ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
   │   应用服务器1   │ │   应用服务器2   │ │   应用服务器N   │
   │  (Spring Boot)  │ │  (Spring Boot)  │ │  (Spring Boot)  │
   └─────────────────┘ └─────────────────┘ └─────────────────┘
            │                │                │
            └────────────────┼────────────────┘
                             │
            ┌────────────────┼────────────────┐
            │                │                │
   ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
   │   MySQL主库     │ │   MySQL从库     │ │   Redis集群     │
   │   (写操作)      │ │   (读操作)      │ │   (缓存)        │
   └─────────────────┘ └─────────────────┘ └─────────────────┘
```

### 1.2 部署环境规划
| 环境 | 用途 | 配置要求 | 部署内容 |
|------|------|----------|----------|
| 开发环境 | 开发调试 | 2C4G | 单机部署所有组件 |
| 测试环境 | 功能测试 | 4C8G | 模拟生产环境配置 |
| 预发环境 | 上线验证 | 8C16G | 生产环境完整配置 |
| 生产环境 | 正式服务 | 16C32G+ | 高可用集群部署 |

### 1.3 服务器资源规划
#### 生产环境推荐配置
```
负载均衡服务器 (×2)
├── CPU: 4核
├── 内存: 8GB
├── 存储: 100GB SSD
└── 网络: 10Mbps

应用服务器 (×3)
├── CPU: 8核
├── 内存: 16GB
├── 存储: 200GB SSD
└── 网络: 100Mbps

数据库服务器 (×2 主从)
├── CPU: 16核
├── 内存: 32GB
├── 存储: 1TB SSD + 2TB HDD
└── 网络: 1000Mbps

缓存服务器 (×3 Redis集群)
├── CPU: 4核
├── 内存: 16GB
├── 存储: 200GB SSD
└── 网络: 100Mbps
```

## 2. 环境准备

### 2.1 操作系统要求
```bash
# 推荐使用 CentOS 7/8 或 Ubuntu 18.04/20.04
# 检查系统版本
cat /etc/os-release

# 系统更新
yum update -y  # CentOS
apt update && apt upgrade -y  # Ubuntu
```

### 2.2 基础软件安装

#### Java环境安装
```bash
# 安装OpenJDK 17
yum install java-17-openjdk java-17-openjdk-devel -y

# 配置JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk' >> /etc/profile
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/profile
source /etc/profile

# 验证安装
java -version
```

#### MySQL 8.0安装
```bash
# 下载MySQL Yum Repository
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL
yum install mysql-server -y

# 启动MySQL服务
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码
grep 'temporary password' /var/log/mysqld.log

# 安全配置
mysql_secure_installation
```

#### Redis安装
```bash
# 编译安装Redis 7.0
wget https://download.redis.io/redis-stable.tar.gz
tar xzf redis-stable.tar.gz
cd redis-stable
make && make install

# 创建配置目录
mkdir -p /etc/redis /var/lib/redis /var/log/redis

# 创建Redis用户
useradd -r -s /bin/false redis

# 配置文件
cp redis.conf /etc/redis/redis.conf
```

#### Nginx安装
```bash
# 安装Nginx
yum install nginx -y  # CentOS
apt install nginx -y  # Ubuntu

# 启动Nginx
systemctl start nginx
systemctl enable nginx

# 检查状态
systemctl status nginx
```

#### Node.js安装
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install nodejs -y

# 验证安装
node --version
npm --version
```

### 2.3 防火墙配置
```bash
# CentOS防火墙配置
firewall-cmd --permanent --zone=public --add-port=80/tcp
firewall-cmd --permanent --zone=public --add-port=443/tcp
firewall-cmd --permanent --zone=public --add-port=8080/tcp
firewall-cmd --reload

# Ubuntu防火墙配置
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8080/tcp
ufw enable
```

## 3. 数据库部署

### 3.1 MySQL主从复制配置

#### 主库配置 (master)
```ini
# /etc/my.cnf
[mysqld]
# 服务器ID，主从不能相同
server-id = 1

# 开启二进制日志
log-bin = mysql-bin
binlog-format = ROW

# 需要同步的数据库
binlog-do-db = smart_admin

# 不需要同步的数据库
binlog-ignore-db = mysql,information_schema,performance_schema,sys

# InnoDB配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 1
innodb_sync_binlog = 1

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接数配置
max_connections = 1000
max_connect_errors = 10000

# 查询缓存
query_cache_size = 0
query_cache_type = 0

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 2
```

#### 从库配置 (slave)
```ini
# /etc/my.cnf
[mysqld]
# 服务器ID，必须唯一
server-id = 2

# 开启中继日志
relay-log = mysql-relay-bin
log-slave-updates = 1

# 只读模式
read-only = 1

# 其他配置与主库相同
innodb_buffer_pool_size = 1G
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
max_connections = 1000
```

#### 主从同步设置
```sql
-- 在主库创建复制用户
CREATE USER 'replication'@'%' IDENTIFIED BY 'ReplicationPassword123!';
GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';
FLUSH PRIVILEGES;

-- 查看主库状态
SHOW MASTER STATUS;

-- 在从库配置主从关系
CHANGE MASTER TO
  MASTER_HOST='主库IP',
  MASTER_USER='replication',
  MASTER_PASSWORD='ReplicationPassword123!',
  MASTER_LOG_FILE='mysql-bin.000001',
  MASTER_LOG_POS=154;

-- 启动从库同步
START SLAVE;

-- 检查从库状态
SHOW SLAVE STATUS\G;
```

### 3.2 数据库初始化
```sql
-- 创建业务数据库
CREATE DATABASE smart_admin DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'smart_admin'@'%' IDENTIFIED BY 'SmartAdminPassword123!';
GRANT SELECT, INSERT, UPDATE, DELETE ON smart_admin.* TO 'smart_admin'@'%';
FLUSH PRIVILEGES;

-- 导入表结构
USE smart_admin;
SOURCE /path/to/schema.sql;

-- 导入初始数据
SOURCE /path/to/init_data.sql;
```

### 3.3 数据库性能优化
```sql
-- 创建索引优化查询性能
-- 用户表索引
CREATE INDEX idx_phone ON t_user(phone);
CREATE INDEX idx_invite_code ON t_user(invite_code);
CREATE INDEX idx_register_time ON t_user(register_time);

-- 订单表索引
CREATE INDEX idx_user_id ON t_orders(user_id);
CREATE INDEX idx_status ON t_orders(status);
CREATE INDEX idx_order_sn ON t_orders(order_sn);
CREATE INDEX idx_create_time ON t_orders(create_time);
CREATE INDEX idx_user_status_time ON t_orders(user_id, status, create_time);

-- 商品表索引
CREATE INDEX idx_category_id ON t_goods(category_id);
CREATE INDEX idx_status ON t_goods(status);
CREATE INDEX idx_sort_order ON t_goods(sort_order);

-- 钱包交易流水表索引
CREATE INDEX idx_user_id ON t_wallet_transactions(user_id);
CREATE INDEX idx_transaction_type ON t_wallet_transactions(transaction_type);
CREATE INDEX idx_create_time ON t_wallet_transactions(create_time);
CREATE INDEX idx_user_type_time ON t_wallet_transactions(user_id, transaction_type, create_time);
```

## 4. Redis集群部署

### 4.1 Redis集群配置

#### 创建Redis集群配置文件
```bash
# 创建集群目录
mkdir -p /etc/redis/cluster/{7000,7001,7002,7003,7004,7005}

# 为每个节点创建配置文件
for port in {7000..7005}; do
cat > /etc/redis/cluster/$port/redis.conf << EOF
port $port
cluster-enabled yes
cluster-config-file nodes-$port.conf
cluster-node-timeout 15000
appendonly yes
appendfilename "appendonly-$port.aof"
dbfilename "dump-$port.rdb"
logfile "/var/log/redis/redis-$port.log"
daemonize yes
protected-mode no
bind 0.0.0.0
requirepass "RedisPassword123!"
masterauth "RedisPassword123!"

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0
EOF
done
```

#### 启动Redis集群节点
```bash
# 启动所有节点
for port in {7000..7005}; do
    redis-server /etc/redis/cluster/$port/redis.conf
done

# 检查节点状态
ps aux | grep redis-server

# 创建集群
redis-cli --cluster create \
  127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 \
  127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005 \
  --cluster-replicas 1 \
  -a "RedisPassword123!"
```

#### Redis集群监控脚本
```bash
#!/bin/bash
# redis_cluster_monitor.sh

REDIS_PASSWORD="RedisPassword123!"
NODES=(7000 7001 7002 7003 7004 7005)

echo "Redis Cluster Status Check - $(date)"
echo "=================================="

for port in "${NODES[@]}"; do
    echo "Checking Redis Node: $port"
    redis-cli -p $port -a $REDIS_PASSWORD ping
    
    if [ $? -eq 0 ]; then
        echo "✓ Node $port is running"
        # 检查集群状态
        redis-cli -p $port -a $REDIS_PASSWORD cluster nodes | grep $(redis-cli -p $port -a $REDIS_PASSWORD cluster myid)
    else
        echo "✗ Node $port is down"
    fi
    echo ""
done

# 检查集群整体状态
echo "Cluster Info:"
redis-cli -p 7000 -a $REDIS_PASSWORD cluster info
```

### 4.2 Redis Sentinel高可用配置
```bash
# Sentinel配置文件
cat > /etc/redis/sentinel.conf << EOF
port 26379
sentinel monitor smart-admin-master 127.0.0.1 6379 2
sentinel auth-pass smart-admin-master RedisPassword123!
sentinel down-after-milliseconds smart-admin-master 30000
sentinel parallel-syncs smart-admin-master 1
sentinel failover-timeout smart-admin-master 180000
sentinel deny-scripts-reconfig yes
daemonize yes
logfile "/var/log/redis/sentinel.log"
EOF

# 启动Sentinel
redis-sentinel /etc/redis/sentinel.conf
```

## 5. 应用服务部署

### 5.1 后端应用部署

#### 创建应用用户和目录
```bash
# 创建应用用户
useradd -r -s /bin/false smartadmin

# 创建应用目录
mkdir -p /opt/smart-admin/{bin,config,logs,data}
chown -R smartadmin:smartadmin /opt/smart-admin
```

#### 生产环境配置文件
```yaml
# /opt/smart-admin/config/application-prod.yml
server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100

spring:
  profiles:
    active: prod
    
  datasource:
    # 主数据源（写）
    master:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ************************************************************************************************************************
      username: smart_admin
      password: ${DB_PASSWORD}
      hikari:
        minimum-idle: 10
        maximum-pool-size: 50
        idle-timeout: 600000
        max-lifetime: 1800000
        connection-timeout: 30000
        
    # 从数据源（读）
    slave:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************************
      username: smart_admin
      password: ${DB_PASSWORD}
      hikari:
        minimum-idle: 5
        maximum-pool-size: 30
        idle-timeout: 600000
        max-lifetime: 1800000
        connection-timeout: 30000
        
  redis:
    cluster:
      nodes:
        - redis-1:7000
        - redis-1:7001
        - redis-2:7002
        - redis-2:7003
        - redis-3:7004
        - redis-3:7005
      max-redirects: 3
    password: ${REDIS_PASSWORD}
    timeout: 5000
    lettuce:
      pool:
        max-active: 50
        max-wait: -1
        max-idle: 20
        min-idle: 5

# SA-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  active-timeout: -1
  is-concurrent: false
  is-share: false
  is-log: false

# 日志配置
logging:
  level:
    root: INFO
    net.lab1024.sa.admin: INFO
    org.springframework.security: WARN
  file:
    name: /opt/smart-admin/logs/smart-admin.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 应用启动脚本
```bash
#!/bin/bash
# /opt/smart-admin/bin/start.sh

APP_NAME="smart-admin"
APP_HOME="/opt/smart-admin"
APP_JAR="$APP_HOME/bin/smart-admin.jar"
APP_CONFIG="$APP_HOME/config/application-prod.yml"
APP_LOG="$APP_HOME/logs/smart-admin.log"
APP_PID="$APP_HOME/smart-admin.pid"

# JVM参数
JVM_OPTS="-server -Xms2g -Xmx4g -Xmn1g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$APP_HOME/logs/"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:$APP_HOME/logs/gc.log"
JVM_OPTS="$JVM_OPTS -Djava.security.egd=file:/dev/./urandom"

# 环境变量
export DB_PASSWORD="SmartAdminPassword123!"
export REDIS_PASSWORD="RedisPassword123!"

# 函数定义
start() {
    if [ -f "$APP_PID" ]; then
        echo "$APP_NAME is already running (PID: $(cat $APP_PID))"
        return 1
    fi
    
    echo "Starting $APP_NAME..."
    nohup java $JVM_OPTS -jar $APP_JAR --spring.config.location=$APP_CONFIG > $APP_LOG 2>&1 &
    echo $! > $APP_PID
    
    sleep 3
    if [ -f "$APP_PID" ] && kill -0 $(cat $APP_PID) 2>/dev/null; then
        echo "$APP_NAME started successfully (PID: $(cat $APP_PID))"
    else
        echo "$APP_NAME failed to start"
        return 1
    fi
}

stop() {
    if [ ! -f "$APP_PID" ]; then
        echo "$APP_NAME is not running"
        return 1
    fi
    
    echo "Stopping $APP_NAME..."
    kill $(cat $APP_PID)
    
    # 等待进程结束
    for i in {1..30}; do
        if ! kill -0 $(cat $APP_PID) 2>/dev/null; then
            rm -f $APP_PID
            echo "$APP_NAME stopped successfully"
            return 0
        fi
        sleep 1
    done
    
    # 强制结束
    echo "Force killing $APP_NAME..."
    kill -9 $(cat $APP_PID) 2>/dev/null
    rm -f $APP_PID
    echo "$APP_NAME force stopped"
}

restart() {
    stop
    sleep 2
    start
}

status() {
    if [ -f "$APP_PID" ] && kill -0 $(cat $APP_PID) 2>/dev/null; then
        echo "$APP_NAME is running (PID: $(cat $APP_PID))"
    else
        echo "$APP_NAME is not running"
        return 1
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
```

#### 系统服务配置
```ini
# /etc/systemd/system/smart-admin.service
[Unit]
Description=Smart Admin Application
After=network.target mysql.service redis.service

[Service]
Type=forking
User=smartadmin
Group=smartadmin
ExecStart=/opt/smart-admin/bin/start.sh start
ExecStop=/opt/smart-admin/bin/start.sh stop
ExecReload=/opt/smart-admin/bin/start.sh restart
PIDFile=/opt/smart-admin/smart-admin.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
systemctl daemon-reload
systemctl enable smart-admin
systemctl start smart-admin
systemctl status smart-admin
```

### 5.2 前端应用部署

#### 构建前端应用
```bash
# 移动端APP构建
cd /path/to/APP
npm run build
tar -czf app-dist.tar.gz dist/

# 管理后台构建
cd /path/to/admin-web
npm run build:prod
tar -czf admin-dist.tar.gz dist/
```

#### Nginx配置
```nginx
# /etc/nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';
    
    access_log /var/log/nginx/access.log main;
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 上游服务器配置
    upstream smart_admin_backend {
        least_conn;
        server ************:8080 weight=3 max_fails=3 fail_timeout=30s;
        server ************:8080 weight=3 max_fails=3 fail_timeout=30s;
        server ************:8080 weight=3 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_conn_zone $binary_remote_addr zone=conn:10m;
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
```

#### 站点配置文件
```nginx
# /etc/nginx/conf.d/smart-admin.conf
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 移动端APP
    location / {
        root /var/www/app/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存策略
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 管理后台
    location /admin {
        alias /var/www/admin/dist;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        
        # 管理后台IP白名单
        allow ***********/24;
        allow 10.0.0.0/8;
        deny all;
    }
    
    # API代理
    location /api {
        # 限流和限连
        limit_req zone=api burst=20 nodelay;
        limit_conn conn 10;
        
        # 代理设置
        proxy_pass http://smart_admin_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }
    
    # 静态资源
    location /static {
        root /var/www;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

## 6. 监控和日志

### 6.1 系统监控

#### Prometheus配置
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "smart_admin_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Smart Admin应用监控
  - job_name: 'smart-admin'
    static_configs:
      - targets:
        - '************:8080'
        - '************:8080'
        - '************:8080'
    metrics_path: '/api/actuator/prometheus'
    scrape_interval: 10s
    
  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets:
        - '192.168.1.20:9104'
        - '192.168.1.21:9104'
        
  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets:
        - '192.168.1.30:9121'
        - '192.168.1.31:9121'
        - '192.168.1.32:9121'
        
  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets:
        - '192.168.1.5:9113'
        - '192.168.1.6:9113'
        
  # 系统监控
  - job_name: 'node'
    static_configs:
      - targets:
        - '************:9100'
        - '************:9100'
        - '************:9100'
```

#### 告警规则配置
```yaml
# /etc/prometheus/smart_admin_rules.yml
groups:
- name: smart_admin_alerts
  rules:
  # 应用服务告警
  - alert: SmartAdminDown
    expr: up{job="smart-admin"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Smart Admin instance is down"
      description: "Smart Admin instance {{ $labels.instance }} has been down for more than 1 minute"
      
  - alert: SmartAdminHighMemoryUsage
    expr: (jvm_memory_used_bytes{job="smart-admin"} / jvm_memory_max_bytes{job="smart-admin"}) * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on Smart Admin"
      description: "Memory usage is above 80% on {{ $labels.instance }}"
      
  - alert: SmartAdminHighCpuUsage
    expr: system_cpu_usage{job="smart-admin"} * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on Smart Admin"
      description: "CPU usage is above 80% on {{ $labels.instance }}"
      
  # 数据库告警
  - alert: MySQLDown
    expr: up{job="mysql"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "MySQL instance is down"
      description: "MySQL instance {{ $labels.instance }} has been down for more than 1 minute"
      
  - alert: MySQLSlowQueries
    expr: rate(mysql_global_status_slow_queries[5m]) > 10
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "MySQL slow queries detected"
      description: "MySQL has more than 10 slow queries per second on {{ $labels.instance }}"
      
  # Redis告警
  - alert: RedisDown
    expr: up{job="redis"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Redis instance is down"
      description: "Redis instance {{ $labels.instance }} has been down for more than 1 minute"
      
  - alert: RedisHighMemoryUsage
    expr: (redis_memory_used_bytes / redis_memory_max_bytes) * 100 > 90
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Redis high memory usage"
      description: "Redis memory usage is above 90% on {{ $labels.instance }}"
```

#### Grafana仪表盘配置
```json
{
  "dashboard": {
    "title": "Smart Admin监控仪表盘",
    "panels": [
      {
        "title": "应用实例状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"smart-admin\"}",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "JVM内存使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "(jvm_memory_used_bytes{job=\"smart-admin\"} / jvm_memory_max_bytes{job=\"smart-admin\"}) * 100",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "HTTP请求QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_server_requests_seconds_count{job=\"smart-admin\"}[5m])",
            "legendFormat": "{{instance}} - {{uri}}"
          }
        ]
      },
      {
        "title": "数据库连接池",
        "type": "graph",
        "targets": [
          {
            "expr": "hikaricp_connections_active{job=\"smart-admin\"}",
            "legendFormat": "活跃连接 - {{instance}}"
          },
          {
            "expr": "hikaricp_connections_idle{job=\"smart-admin\"}",
            "legendFormat": "空闲连接 - {{instance}}"
          }
        ]
      }
    ]
  }
}
```

### 6.2 日志管理

#### ELK Stack部署

##### Elasticsearch配置
```yaml
# /etc/elasticsearch/elasticsearch.yml
cluster.name: smart-admin-logs
node.name: es-node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 0.0.0.0
http.port: 9200
discovery.seed_hosts: ["es-node-1", "es-node-2", "es-node-3"]
cluster.initial_master_nodes: ["es-node-1", "es-node-2", "es-node-3"]

# 内存设置
bootstrap.memory_lock: true

# 安全配置
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
```

##### Logstash配置
```ruby
# /etc/logstash/conf.d/smart-admin.conf
input {
  # 应用日志
  beats {
    port => 5044
  }
  
  # Nginx访问日志
  file {
    path => "/var/log/nginx/access.log"
    start_position => "beginning"
    type => "nginx-access"
  }
  
  # MySQL慢查询日志
  file {
    path => "/var/log/mysql/mysql-slow.log"
    start_position => "beginning"
    type => "mysql-slow"
    multiline {
      pattern => "^# Time:"
      negate => true
      what => "previous"
    }
  }
}

filter {
  if [type] == "nginx-access" {
    grok {
      match => { 
        "message" => "%{NGINXACCESS}" 
      }
    }
    
    date {
      match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }
    
    mutate {
      convert => { 
        "response" => "integer"
        "response_time" => "float"
        "bytes" => "integer"
      }
    }
  }
  
  if [type] == "smart-admin" {
    grok {
      match => { 
        "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{LOGLEVEL:level} \[%{DATA:thread}\] %{DATA:logger} - %{GREEDYDATA:msg}" 
      }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["es-node-1:9200", "es-node-2:9200", "es-node-3:9200"]
    index => "smart-admin-logs-%{+YYYY.MM.dd}"
    user => "elastic"
    password => "password"
  }
  
  # 输出到控制台用于调试
  stdout { 
    codec => rubydebug 
  }
}
```

##### Filebeat配置
```yaml
# /etc/filebeat/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /opt/smart-admin/logs/*.log
  fields:
    service: smart-admin
    environment: production
  fields_under_root: true
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.logstash:
  hosts: ["logstash:5044"]

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
```

### 6.3 健康检查脚本

#### 应用健康检查
```bash
#!/bin/bash
# /opt/smart-admin/bin/health_check.sh

HEALTH_URL="http://localhost:8080/api/actuator/health"
TIMEOUT=10
MAX_RETRIES=3

check_health() {
    local retry=0
    
    while [ $retry -lt $MAX_RETRIES ]; do
        response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $TIMEOUT $HEALTH_URL)
        
        if [ "$response" = "200" ]; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - Health check passed"
            return 0
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - Health check failed (HTTP $response), retry $((retry + 1))/$MAX_RETRIES"
            retry=$((retry + 1))
            sleep 5
        fi
    done
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Health check failed after $MAX_RETRIES retries"
    return 1
}

# 执行健康检查
if check_health; then
    exit 0
else
    # 发送告警
    /opt/smart-admin/bin/send_alert.sh "Smart Admin health check failed"
    exit 1
fi
```

#### 数据库连接检查
```bash
#!/bin/bash
# /opt/smart-admin/bin/db_check.sh

DB_HOST="mysql-master"
DB_PORT="3306"
DB_USER="smart_admin"
DB_PASSWORD="SmartAdminPassword123!"
DB_NAME="smart_admin"

check_db_connection() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "SELECT 1" $DB_NAME >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Database connection successful"
        return 0
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Database connection failed"
        return 1
    fi
}

check_db_replication() {
    local slave_status=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running\|Slave_SQL_Running")
    
    if echo "$slave_status" | grep -q "Yes"; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Database replication is running"
        return 0
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Database replication issue detected"
        return 1
    fi
}

# 执行检查
if check_db_connection && check_db_replication; then
    exit 0
else
    /opt/smart-admin/bin/send_alert.sh "Database connectivity issue"
    exit 1
fi
```

## 7. 备份和恢复

### 7.1 数据库备份

#### 自动备份脚本
```bash
#!/bin/bash
# /opt/backup/mysql_backup.sh

# 备份配置
BACKUP_DIR="/opt/backup/mysql"
DB_HOST="localhost"
DB_USER="backup_user"
DB_PASSWORD="BackupPassword123!"
DB_NAME="smart_admin"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR

# 生成备份文件名
BACKUP_FILE="smart_admin_$(date +%Y%m%d_%H%M%S).sql"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

# 执行备份
echo "$(date '+%Y-%m-%d %H:%M:%S') - Starting MySQL backup..."

mysqldump -h$DB_HOST -u$DB_USER -p$DB_PASSWORD \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --set-gtid-purged=OFF \
    $DB_NAME > $BACKUP_PATH

if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Backup completed: $BACKUP_PATH"
    
    # 压缩备份文件
    gzip $BACKUP_PATH
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Backup compressed: $BACKUP_PATH.gz"
    
    # 上传到云存储（可选）
    # aws s3 cp $BACKUP_PATH.gz s3://your-backup-bucket/mysql/
    
    # 清理过期备份
    find $BACKUP_DIR -name "*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Old backups cleaned up"
    
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Backup failed"
    /opt/smart-admin/bin/send_alert.sh "MySQL backup failed"
    exit 1
fi
```

#### 定时备份任务
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点执行备份
0 2 * * * /opt/backup/mysql_backup.sh >> /var/log/mysql_backup.log 2>&1

# 每周日凌晨执行完整备份
0 3 * * 0 /opt/backup/mysql_full_backup.sh >> /var/log/mysql_backup.log 2>&1
```

### 7.2 Redis备份

#### Redis备份脚本
```bash
#!/bin/bash
# /opt/backup/redis_backup.sh

BACKUP_DIR="/opt/backup/redis"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD="RedisPassword123!"
RETENTION_DAYS=7

mkdir -p $BACKUP_DIR

# 生成备份文件名
BACKUP_FILE="redis_$(date +%Y%m%d_%H%M%S).rdb"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

echo "$(date '+%Y-%m-%d %H:%M:%S') - Starting Redis backup..."

# 执行BGSAVE命令
redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD BGSAVE

# 等待备份完成
while [ $(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD LASTSAVE) -le $(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD LASTSAVE) ]; do
    sleep 1
done

# 复制RDB文件
cp /var/lib/redis/dump.rdb $BACKUP_PATH

if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Redis backup completed: $BACKUP_PATH"
    
    # 压缩备份文件
    gzip $BACKUP_PATH
    
    # 清理过期备份
    find $BACKUP_DIR -name "*.rdb.gz" -type f -mtime +$RETENTION_DAYS -delete
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Redis backup failed"
    exit 1
fi
```

### 7.3 应用数据恢复

#### 数据库恢复脚本
```bash
#!/bin/bash
# /opt/backup/mysql_restore.sh

if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup_file.sql.gz>"
    exit 1
fi

BACKUP_FILE=$1
DB_HOST="localhost"
DB_USER="root"
DB_PASSWORD="RootPassword123!"
DB_NAME="smart_admin"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - Starting database restore from $BACKUP_FILE"

# 停止应用服务
systemctl stop smart-admin
echo "Application stopped"

# 解压备份文件
if [[ $BACKUP_FILE == *.gz ]]; then
    SQL_FILE="${BACKUP_FILE%.gz}"
    gunzip -c $BACKUP_FILE > $SQL_FILE
else
    SQL_FILE=$BACKUP_FILE
fi

# 创建数据库备份（恢复前）
mysqldump -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME > "${DB_NAME}_before_restore_$(date +%Y%m%d_%H%M%S).sql"

# 执行恢复
mysql -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME < $SQL_FILE

if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Database restore completed successfully"
    
    # 启动应用服务
    systemctl start smart-admin
    echo "Application started"
    
    # 验证服务状态
    sleep 10
    if systemctl is-active --quiet smart-admin; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Service is running normally"
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Service failed to start after restore"
        exit 1
    fi
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Database restore failed"
    exit 1
fi

# 清理临时文件
if [[ $BACKUP_FILE == *.gz ]]; then
    rm -f $SQL_FILE
fi
```

## 8. 灾难恢复

### 8.1 故障场景和应对方案

#### 应用服务故障
```bash
#!/bin/bash
# /opt/smart-admin/bin/disaster_recovery.sh

SERVICE_NAME="smart-admin"
BACKUP_SERVER="backup-server"

check_service_health() {
    if systemctl is-active --quiet $SERVICE_NAME; then
        if curl -s -f http://localhost:8080/api/actuator/health >/dev/null; then
            return 0
        fi
    fi
    return 1
}

failover_to_backup() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Initiating failover to backup server"
    
    # 更新DNS或负载均衡器配置
    # 这里需要根据实际环境配置
    
    # 通知运维团队
    /opt/smart-admin/bin/send_alert.sh "Service failover initiated"
}

auto_recovery() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Attempting automatic recovery"
    
    # 重启服务
    systemctl restart $SERVICE_NAME
    sleep 30
    
    # 检查恢复状态
    if check_service_health; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Service recovered successfully"
        return 0
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Automatic recovery failed"
        return 1
    fi
}

# 主逻辑
if ! check_service_health; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Service health check failed"
    
    if ! auto_recovery; then
        failover_to_backup
    fi
fi
```

#### 数据库故障恢复
```bash
#!/bin/bash
# /opt/backup/db_disaster_recovery.sh

MASTER_HOST="mysql-master"
SLAVE_HOST="mysql-slave"
VIP="*************"

check_mysql_health() {
    local host=$1
    mysql -h$host -u$DB_USER -p$DB_PASSWORD -e "SELECT 1" >/dev/null 2>&1
    return $?
}

promote_slave_to_master() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Promoting slave to master"
    
    # 停止从库复制
    mysql -h$SLAVE_HOST -u$DB_USER -p$DB_PASSWORD -e "STOP SLAVE;"
    mysql -h$SLAVE_HOST -u$DB_USER -p$DB_PASSWORD -e "RESET SLAVE ALL;"
    
    # 设置从库为可写
    mysql -h$SLAVE_HOST -u$DB_USER -p$DB_PASSWORD -e "SET GLOBAL read_only = OFF;"
    
    # 更新VIP到从库
    # 这里需要根据实际网络环境配置
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Slave promoted to master successfully"
}

# 检查主库状态
if ! check_mysql_health $MASTER_HOST; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Master database is down"
    
    # 检查从库状态
    if check_mysql_health $SLAVE_HOST; then
        promote_slave_to_master
        /opt/smart-admin/bin/send_alert.sh "Database failover completed - slave promoted to master"
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Both master and slave are down!"
        /opt/smart-admin/bin/send_alert.sh "CRITICAL: Both database servers are down"
        exit 1
    fi
fi
```

### 8.2 数据恢复验证

#### 数据一致性检查
```bash
#!/bin/bash
# /opt/backup/data_integrity_check.sh

DB_HOST="localhost"
DB_USER="smart_admin"
DB_PASSWORD="SmartAdminPassword123!"
DB_NAME="smart_admin"

# 核心表数据量检查
check_table_counts() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Checking table row counts"
    
    tables=("t_user" "t_orders" "t_goods" "t_wallet_transactions")
    
    for table in "${tables[@]}"; do
        count=$(mysql -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT COUNT(*) FROM $table" -s -N)
        echo "$table: $count rows"
        
        # 记录到日志文件
        echo "$(date '+%Y-%m-%d %H:%M:%S') - $table: $count rows" >> /var/log/data_integrity.log
    done
}

# 数据完整性检查
check_data_integrity() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Checking data integrity"
    
    # 检查外键约束
    mysql -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "
        SELECT 
            COUNT(*) as orphaned_orders
        FROM t_orders o 
        LEFT JOIN t_user u ON o.user_id = u.id 
        WHERE u.id IS NULL
    " -s -N
    
    # 检查数据一致性
    mysql -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "
        SELECT 
            COUNT(*) as inconsistent_wallets
        FROM t_wallets w
        LEFT JOIN t_user u ON w.user_id = u.id
        WHERE u.id IS NULL
    " -s -N
}

# 执行检查
check_table_counts
check_data_integrity

echo "$(date '+%Y-%m-%d %H:%M:%S') - Data integrity check completed"
```

## 9. 安全加固

### 9.1 系统安全配置

#### 防火墙配置
```bash
#!/bin/bash
# /opt/security/firewall_setup.sh

# 清除现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# SSH访问（限制IP）
iptables -A INPUT -p tcp --dport 22 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -s 10.0.0.0/8 -j ACCEPT

# HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 应用端口（限制内网访问）
iptables -A INPUT -p tcp --dport 8080 -s ***********/24 -j ACCEPT

# 数据库端口（仅允许应用服务器访问）
iptables -A INPUT -p tcp --dport 3306 -s ************ -j ACCEPT
iptables -A INPUT -p tcp --dport 3306 -s ************ -j ACCEPT
iptables -A INPUT -p tcp --dport 3306 -s ************ -j ACCEPT

# Redis端口（仅允许应用服务器访问）
iptables -A INPUT -p tcp --dport 6379 -s ************ -j ACCEPT
iptables -A INPUT -p tcp --dport 6379 -s ************ -j ACCEPT
iptables -A INPUT -p tcp --dport 6379 -s ************ -j ACCEPT

# 禁ping（可选）
iptables -A INPUT -p icmp --icmp-type echo-request -j DROP

# 保存规则
service iptables save
```

#### SSL证书配置
```bash
#!/bin/bash
# /opt/security/ssl_setup.sh

DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# 使用Let's Encrypt获取免费SSL证书
certbot --nginx -d $DOMAIN -d www.$DOMAIN --email $EMAIL --agree-tos --no-eff-email

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

# 测试续期
certbot renew --dry-run
```

### 9.2 应用安全配置

#### 安全头配置
```java
// SecurityConfig.java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .headers(headers -> headers
                .frameOptions().deny()
                .contentTypeOptions().and()
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)
                    .includeSubdomains(true)
                )
                .and()
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            );
            
        return http.build();
    }
}
```

#### 敏感数据加密
```yaml
# application-prod.yml
# 使用Jasypt加密敏感配置
spring:
  datasource:
    password: ENC(encrypted_password_here)
  redis:
    password: ENC(encrypted_redis_password_here)

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
```

## 10. 运维脚本

### 10.1 日常运维脚本

#### 系统状态检查脚本
```bash
#!/bin/bash
# /opt/scripts/system_status.sh

echo "=================================="
echo "系统状态检查报告 - $(date)"
echo "=================================="

# 系统基本信息
echo "1. 系统信息:"
echo "   主机名: $(hostname)"
echo "   系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d '"' -f 2)"
echo "   内核版本: $(uname -r)"
echo "   运行时间: $(uptime -p)"
echo ""

# CPU和内存使用情况
echo "2. 资源使用情况:"
echo "   CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d '%' -f 1)%"
echo "   内存使用: $(free -h | grep Mem | awk '{printf "%.1f%% (%s/%s)", ($3/$2)*100, $3, $2}')"
echo "   磁盘使用: $(df -h / | tail -1 | awk '{print $5 " (" $3 "/" $2 ")"}')"
echo ""

# 服务状态
echo "3. 服务状态:"
services=("smart-admin" "nginx" "mysql" "redis")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        status="✓ 运行中"
    else
        status="✗ 已停止"
    fi
    echo "   $service: $status"
done
echo ""

# 网络连接
echo "4. 网络连接:"
echo "   监听端口:"
netstat -tlnp | grep -E ':80|:443|:8080|:3306|:6379' | while read line; do
    echo "   $line"
done
echo ""

# 最近的错误日志
echo "5. 最近错误日志:"
echo "   应用错误 (最近10条):"
tail -10 /opt/smart-admin/logs/smart-admin.log | grep -i error | tail -5
echo "   系统错误 (最近5条):"
tail -5 /var/log/messages | grep -i error | tail -3
```

#### 性能监控脚本
```bash
#!/bin/bash
# /opt/scripts/performance_monitor.sh

LOGFILE="/var/log/performance_monitor.log"
THRESHOLD_CPU=80
THRESHOLD_MEM=85
THRESHOLD_DISK=90

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOGFILE
}

check_cpu() {
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d '%' -f 1 | cut -d '.' -f 1)
    
    if [ $cpu_usage -gt $THRESHOLD_CPU ]; then
        log_message "ALERT: High CPU usage: ${cpu_usage}%"
        /opt/smart-admin/bin/send_alert.sh "High CPU usage: ${cpu_usage}%"
    else
        log_message "INFO: CPU usage normal: ${cpu_usage}%"
    fi
}

check_memory() {
    mem_usage=$(free | grep Mem | awk '{printf "%.0f", ($3/$2)*100}')
    
    if [ $mem_usage -gt $THRESHOLD_MEM ]; then
        log_message "ALERT: High memory usage: ${mem_usage}%"
        /opt/smart-admin/bin/send_alert.sh "High memory usage: ${mem_usage}%"
    else
        log_message "INFO: Memory usage normal: ${mem_usage}%"
    fi
}

check_disk() {
    disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d '%' -f 1)
    
    if [ $disk_usage -gt $THRESHOLD_DISK ]; then
        log_message "ALERT: High disk usage: ${disk_usage}%"
        /opt/smart-admin/bin/send_alert.sh "High disk usage: ${disk_usage}%"
    else
        log_message "INFO: Disk usage normal: ${disk_usage}%"
    fi
}

# 执行监控检查
check_cpu
check_memory
check_disk

log_message "Performance check completed"
```

### 10.2 告警通知脚本

#### 邮件告警脚本
```bash
#!/bin/bash
# /opt/smart-admin/bin/send_alert.sh

ALERT_MESSAGE="$1"
EMAIL_TO="<EMAIL>"
EMAIL_FROM="<EMAIL>"
HOSTNAME=$(hostname)

if [ -z "$ALERT_MESSAGE" ]; then
    echo "Usage: $0 <alert_message>"
    exit 1
fi

# 发送邮件
cat << EOF | sendmail $EMAIL_TO
From: $EMAIL_FROM
To: $EMAIL_TO
Subject: [ALERT] Smart Admin - $HOSTNAME

时间: $(date)
主机: $HOSTNAME
告警: $ALERT_MESSAGE

请及时处理此告警。

--
Smart Admin 监控系统
EOF

# 记录告警日志
echo "$(date '+%Y-%m-%d %H:%M:%S') - Alert sent: $ALERT_MESSAGE" >> /var/log/alerts.log

# 发送到钉钉（可选）
WEBHOOK_URL="https://oapi.dingtalk.com/robot/send?access_token=your_token"
curl -H "Content-Type: application/json" -X POST -d "{
    \"msgtype\": \"text\",
    \"text\": {
        \"content\": \"【告警】$HOSTNAME: $ALERT_MESSAGE\"
    }
}" $WEBHOOK_URL
```

### 10.3 定时任务配置

#### Crontab配置
```bash
# 编辑crontab
crontab -e

# 系统监控任务
*/5 * * * * /opt/scripts/performance_monitor.sh
*/10 * * * * /opt/smart-admin/bin/health_check.sh
*/15 * * * * /opt/smart-admin/bin/db_check.sh

# 备份任务
0 2 * * * /opt/backup/mysql_backup.sh >> /var/log/mysql_backup.log 2>&1
0 3 * * 0 /opt/backup/redis_backup.sh >> /var/log/redis_backup.log 2>&1

# 日志清理任务
0 1 * * * find /opt/smart-admin/logs -name "*.log" -type f -mtime +7 -delete
0 1 * * * find /var/log/nginx -name "*.log" -type f -mtime +30 -delete

# 系统状态报告
0 9 * * 1 /opt/scripts/system_status.sh | mail -s "Weekly System Status" <EMAIL>

# SSL证书续期检查
0 12 * * * /usr/bin/certbot renew --quiet

# 临时文件清理
0 0 * * * find /tmp -type f -mtime +3 -delete
```

## 11. 总结

本部署运维文档涵盖了团购网系统从环境准备到生产运维的完整流程，包括：

### 11.1 部署要点
- **高可用架构**: 负载均衡、数据库主从、Redis集群
- **安全配置**: 防火墙、SSL证书、数据加密
- **监控告警**: Prometheus、Grafana、ELK Stack
- **备份恢复**: 自动备份、灾难恢复方案

### 11.2 运维重点
- **性能监控**: CPU、内存、磁盘、网络监控
- **日志管理**: 集中日志收集和分析
- **健康检查**: 应用和基础设施健康监控
- **故障处理**: 自动故障检测和恢复

### 11.3 最佳实践
- **标准化部署**: 使用脚本自动化部署流程
- **版本控制**: 配置文件和脚本版本管理
- **文档更新**: 及时更新运维文档
- **团队协作**: 建立运维团队协作流程

通过遵循本文档的部署和运维规范，可以确保团购网系统的稳定运行和高效维护。

---

**文档版本**: V1.0  
**创建日期**: 2025年1月  
**维护团队**: 运维团队  
**审核状态**: 已审核