# 社交拼团APP产品需求文档 (PRD) - 新版

| 版本   | 日期       | 作者   | 审核人 | 变更描述                                                                 |
| :----- | :--------- | :----- | :----- | :----------------------------------------------------------------------- |
| V2.2.2 | 2025-06-25 | Gemini |        | **优化用户流程**：根据用户反馈，重构核心用户路径和页面跳转逻辑。 |

---

## 1. 引言 (Introduction)

### 1.1. 文档目的

本文档旨在明确"社交拼团APP"的V2.2.2版本产品需求，定义其核心功能、业务逻辑、用户角色及非功能性需求。旨在为产品、设计、开发、测试等团队提供统一的指导和依据，确保产品能够高效、高质量地完成。

### 1.2. 产品愿景

打造一个以**社交分享**和**趣味性抽奖拼团**为核心模式的直营电商平台。我们的愿景是：

* **对用户：** 成为用户通过社交分享发现、参与和购买高性价比商品的首选入口，提供"更低价格、更好服务、更多趣味"的购物体验。
* **对平台：** 构建一个高效、精选、可持续发展的B2C直营商业生态系统，通过创新的玩法实现用户增长和平台盈利。

### 1.3. 目标用户

- **C端用户 (消费者):**
  - **价格敏感型用户：** 追求高性价比，乐于通过拼团和抽奖获取优惠。
  - **社交活跃型用户：** 享受与朋友共同购物、分享裂变的乐趣，乐于在社交网络中传播。
  - **娱乐探索型用户：** 喜欢新颖的购物玩法，享受抽奖模式带来的不确定性和惊喜感。

### 1.4. 名词解释

| 术语                          | 解释                                                                                  |
| :---------------------------- | :------------------------------------------------------------------------------------ |
| **SPU/SKU**             | 标准产品单元 / 库存量单位。                                                           |
| **抽奖拼团**            | 一种新的购物模式，用户支付后参与抽奖，结果分为"中签"或"未中签"。                      |
| **新手团 (3-5人)**      | 专为新用户设计的低门槛抽奖拼团，通常与"体验金"配合使用。                              |
| **低价抽奖团 (<500元)** | 支付全款参与，中签者可选择要货或折价返款；未中签者退回本金并获得补贴。                |
| **高价抽奖团 (>500元)** | 支付定金(如20%)参与，中签者可选择补齐尾款要货或放弃定金；未中签者退回定金并获得补贴。 |
| **中签/未中签**         | 抽奖拼团的结果。中签代表获得购买资格，未中签则代表未获得。                            |
| **体验金**              | 平台赠送给新用户的虚拟资金，可用于抵扣订单金额，但不可提现。                          |
| **账户余额**            | 用户在平台内的虚拟资金账户，用于接收退款、补贴、佣金奖励和支付订单。                  |
| **积分**                | 用户通过消费等行为获得的奖励，未来可用于兑换权益。                                    |
| **裂变体系**            | 通过邀请好友注册并消费，从而获得佣金、现金奖励和抽奖机会的病毒营销机制。              |

---

## 2. 产品功能 (Product Features)

### 2.1. 功能总览 (Feature Overview)

```mermaid
graph TD;
    A["社交拼团APP平台"] --> B["C端用户APP"];
    A --> E["平台管理后台 (Web)"];
    A --> F["通用底层服务"];

    subgraph C端用户APP
        B1["用户中心 (订单, 钱包, 团队)"];
        B2["首页浏览 (搜索, 推荐, 活动专区)"];
        B3["交易流程 (下单, 支付, 物流)"];
        B4["核心玩法 (抽奖拼团)"];
        B5["裂变与分享"];
    end

    subgraph 平台管理后台
        E1["用户管理"];
        E2["商品与库存管理"];
        E3["订单与售后管理"];
        E4["营销与内容配置"];
        E5["数据分析报表"];
        E6["风控与策略中心"];
        E7["裂变体系管理"];
        E8["财务管理"];
    end
    
    subgraph 通用底层服务
        F1["LBS定位服务"];
        F2["支付网关"];
        F3["消息推送"];
        F4["智能推荐引擎"];
        F5["仓储物流接口"];
        F6["客服系统接口"];
    end

    B --> B1; B --> B2; B --> B3; B --> B4; B --> B5;
    E --> E1; E --> E2; E --> E3; E --> E4; E --> E5; E --> E6; E --> E7; E --> E8;
```

### 2.2. 功能详述 (Detailed Features)

#### 2.2.1. C端 - 核心玩法模块 (Core Gameplay)

| 功能ID | 功能名称             | 用户故事                                                                               | 功能描述                                                                                                                                                                                                                                                                                                                                                    | 优先级 |
| :----- | :------------------- | :------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| CG-001 | **新手抽奖团** | 作为新用户，我想用平台送的体验金尝试一下，看看这是怎么玩的。                           | 1.**参与门槛**：仅限新用户或有特定资格的用户参与，后台可设置参与次数上限。`<br>`2. **支付方式**：通常使用注册时赠送的"体验金"进行支付。`<br>`3. **结果策略**：默认策略为"必不中签"，让用户无成本体验流程。                                                                                                                            | 高     |
| CG-002 | **低价抽奖团** | 作为用户，我愿意花一笔小钱参与抽奖，如果中了，我既可以选择要这个商品，也可以选择折现。 | 1.**参与方式**：用户全款支付商品金额后参与抽奖。`<br>`2. **中签结果**：a) **要产品**：平台发货，用户收货后可申请退货（扣除20%手续费和运费）；b) **不要产品**：平台将支付款扣除20%后，返还至用户账户余额。`<br>`3. **未中签结果**：本金全额退还至账户余额，并额外获得平台补贴（订单金额的3-5%，比例由后台配置）。          | 高     |
| CG-003 | **高价抽奖团** | 作为用户，对于很贵的商品，我希望能先付一部分定金参与，中了再决定要不要。               | 1.**参与方式**：用户仅需支付商品标价的20%作为预付款即可参与。`<br>`2. **中签结果**：a) **要产品**：用户需在规定时间内付清剩余80%尾款，平台发货；b) **不要产品**：用户放弃购买，预付的20%定金不予退还。`<br>`3. **未中签结果**：预付款全额退还至账户余额，并额外获得平台补贴（**商品标价**的3-5%，比例由后台配置）。 | 高     |

#### 2.2.2. C端 - 裂变体系模块 (Viral System)

| 功能ID | 功能名称                   | 用户故事                                                           | 功能描述                                                                                                                                                                                                                                    | 优先级 |
| :----- | :------------------------- | :----------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| VS-001 | **新用户注册礼**     | 作为新用户，我希望注册后能立即获得奖励，让我可以免费体验一下。     | 1. 新用户完成注册后，系统自动赠送一笔"体验金"到其钱包。`<br>`2. 体验金金额由后台统一配置，通常设置为刚好可参与一次"新手团"。                                                                                              | 高     |
| VS-002 | **邀请消费返佣**     | 作为推广者，我希望能通过邀请好友来赚钱，他们消费了我应该有提成。   | 1. 用户通过分享链接邀请好友注册。`<br>`2. 当被邀请的好友（下级）成功参与任何类型的**抽奖团**（产生消费行为），邀请人（上级）可获得该笔消费金额一定比例的佣金奖励（如0.5%，后台可配）。`<br>`3. 佣金发放到邀请人的账户余额中。 | 高     |
| VS-003 | **团队人数奖励**     | 作为推广者，如果我邀请的很多人当天都消费了，我希望能有额外的奖励。 | 1. 统计邀请人名下，当天有消费行为的直属下级人数。`<br>`2. 当该人数达到后台设定的阈值时（如5人），触发额外奖励。`<br>`3. 奖励金额为：`达标人数 * N元`（N由后台配置）。奖励发放到邀请人的账户余额中。                   | 中     |
| VS-004 | **邀请有效用户奖励** | 作为推广者，我每成功邀请一个有消费的好友，都希望能有即时激励。     | 1. "有效用户"定义为：完成注册且至少有过一次消费行为的用户。`<br>`2. 每成功邀请一个有效用户，邀请人即可获得一次转盘抽奖机会。`<br>`3. (本期可后置)转盘抽奖功能，奖品可设置为积分、红包、实物等。                         | 低     |

#### 2.2.3. C端 - 用户中心模块 (User Center)

| 功能ID | 功能名称           | 用户故事                                                             | 功能描述                                                                                                                                                                                                                                                                                                                                   | 优先级 |
| :----- | :----------------- | :------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| UC-001 | 注册/登录          | (同V2.1)                                                             | (同V2.1)                                                                                                                                                                                                                                                                                                                                   | 高     |
| UC-002 | 个人资料管理       | (同V2.1)                                                             | (同V2.1)                                                                                                                                                                                                                                                                                                                                   | 高     |
| UC-003 | 我的订单           | 作为用户，我希望能方便地查看我所有抽奖订单的状态。             | 1. 列表展示所有抽奖订单。`<br>`2. 抽奖订单状态包括：**已中签、未中签、已完成、已失效**。`<br>`3. 订单详情展示完整的产品信息、金额、支付方式、物流信息(中签要货后)、中签/未中签结果及处理方式。                                                                                                           | 高     |
| UC-004 | **我的钱包** | 我想管理我的钱，能充值和提现，也想看清楚体验金、余额都是怎么来的怎么花的。 | 1. **余额展示与操作**：清晰展示当前账户可用余额。在余额下方提供**视觉突出的"充值"和"提现"功能按钮**。<br>2. **体验金展示**：在钱包页面分开展示体验金及其使用规则（不可提现）。<br>3. **充值功能**：用户可通过第三方支付向账户余额充值。<br>4. **提现功能**：a) 绑定银行卡/电子钱包；b) 输入提现金额，有最低提现额度限制（后台可配）；c) 扣除手续费后，T+N天到账。<br>5. **账单流水**：记录所有资金变动，类型包括：充值、订单支付、余额退款、中签后退款、未中签补贴、邀请返佣、团队奖励、提现等。 | 高     |
| UC-005 | **我的团队** | 作为推广者，我想看看我邀请了多少人，他们给我带来了多少收益。         | 1.**团队数据总览**：展示直接邀请人数、团队总人数（未来可扩展）、累计邀请收益。`<br>`2. **团队成员列表**：展示我直接邀请的用户列表（昵称、注册时间、是否消费）。                                                                                                                                                              | 高     |
| UC-006 | **我的积分** | 作为用户，我想知道我消费后获得了多少积分。                           | 1. 展示当前总积分。`<br>`2. 积分明细列表，记录积分的获取（如消费赠送）和消耗（暂无）。                                                                                                                                                                                                                                                   | 中     |
| UC-007 | 收藏/足迹          | (同V2.1)                                                             | (同V2.1)                                                                                                                                                                                                                                                                                                                                   | 中     |
| UC-008 | 客服中心           | 当我遇到问题时，我希望能快速联系到客服。                             | 集成第三方在线客服系统（如聊天窗口），或提供客服联系方式。                                                                                                                                                                                                                                                                                 | 高     |

#### 2.2.4. C端 - 交易流程模块 (Transaction)

交易流程需根据新的抽奖模式进行调整。

| 功能ID | 功能名称                     | 描述                                                                                                                                                                                                                                                                                                                                                                                                                     | 优先级 |
| :----- | :--------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- |
| TR-001 | 订单确认流程                 | 1. 页面需清晰展示参与的活动类型（新手团/低价团/高价团）。`<br>`2. 价格明细需清晰展示：商品价格、**支付金额**（全款或定金）、可用优惠、**体验金抵扣**。`<br>`3. 订单支付时，优先使用体验金进行抵扣。                                                                                                                                                                                                      | 高     |
| TR-002 | 支付功能                     | (同V2.1) 支付成功后，跳转到"开奖等待页"。                                                                                                                                                                                                                                                                                                                                                                                | 高     |
| TR-003 | **开奖等待与结果反馈** | 1.**开奖等待页**：展示参与的团信息、开奖倒计时等。`<br>`2. **结果通知**：开奖后通过Push消息和站内信通知用户。`<br>`3. **结果弹窗**：用户进入APP或指定页面时，弹出模态窗口，清晰告知"恭喜您已中签"或"很遗憾未中签"，并提供后续操作指引。`<br>`   - **中签指引**：引导用户选择"要货"或"折现"（根据高/低价团规则）。`<br>`   - **未中签指引**：告知用户本金已退回，并获得了XX元补贴。 | 高     |

#### 2.2.5. 平台管理后台 (Web)

**核心新增模块：风控与策略中心**

| 功能模块                 | 功能点                 | 详细描述                                                                                                                                                                                                                                                     |
| :----------------------- | :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **风控与策略中心** | **全局概率设置** | 1.**低价区总中签率**：设置低价区商品当天整体的中签概率。`<br>`2. **高价区总中签率**：设置高价区商品当天整体的中签概率。                                                                                                                        |
|                          | **用户策略干预** | 1.**指定用户中签率**：可按用户ID或手机号，单独设置其在未来N次参与的中签概率。`<br>`2. **新用户必不中**：开关，开启后，所有新用户的首次参与默认为"未中签"。`<br>`3. **充值后必中**：配置规则，如"累计充值达到X元后，未来Y次内必中Z次"。 |
|                          | **活动策略干预** | 1.**商品必不中开关**：在商品或活动层面设置一个"必不中"开关，开启后所有参与该活动的用户均为"未中签"。主要用于测试或特殊运营活动。                                                                                                                       |
|                          | **防刷单风控**   | (未来规划) 基于用户行为（如IP、设备号、参与频率）识别异常用户，并进行预警或限制。                                                                                                                                                                            |

**其他后台模块调整：**

| 功能模块               | 功能点                 | 详细描述                                                                                                                                                                                                                                                                                                                      |
| :--------------------- | :--------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **营销管理**     | **抽奖活动管理** | 1. 创建抽奖活动，关联商品。`<br>`2. **配置活动类型**：选择低价团/高价团/新手团。`<br>`3. **设置核心参数**：`<br>`   - **低价团**：设置中签后不要货的返款比例（如80%），未中签的补贴比例（如3-5%）。`<br>`   - **高价团**：设置预付款比例（如20%），未中签的补贴比例（基于商品标价，如3-5%）。 |
| **裂变体系管理** | **邀请规则配置** | 1.**体验金**：设置新用户注册赠送的体验金金额。`<br>`2. **新手团限制**：设置每个用户可参与新手团的次数。`<br>`3. **消费返佣比例**：设置邀请消费的返佣百分比。`<br>`4. **团队奖励规则**：设置触发团队奖励的人数阈值和人均奖励金额。                                                               |
| **财务管理**     | **提现管理**     | 1.**提现设置**：配置最低提现金额、提现手续费率。`<br>`2. **提现审核**：处理用户的提现申请。                                                                                                                                                                                                                     |
| **用户管理**     | 用户详情               | 在用户详情页增加展示：**所属上级、团队人数、累计收益、当前积分、风控标签**等信息。                                                                                                                                                                                                                                      |
| **积分管理**     | 积分规则设置           | 1. 设置消费金额与积分的兑换比例（如 1元:10积分）。                                                                                                                                                                                                                                                                            |

---

## 3. 用户流程与页面跳转逻辑

### 3.1. 核心用户路径

基于业务逻辑，用户的核心路径主要分为新手体验和常规抽奖两种。

#### 路径一：新手团流程
此流程主要通过弹窗引导新用户完成首次体验，路径短，转化快。
```
首页 → (触发)拉新弹窗 → 点击"立即体验" → 订单确认页 → 支付页(体验金抵扣) → 开奖等待页 → 结果通知弹窗 → (关闭)分享引导弹窗
```

#### 路径二：低/高价团流程
此流程为平台核心的抽奖购物流程，有多个入口，覆盖用户主动发现和被动营销的场景。
*   **入口1：通过活动专区 (用户主动)**
```
首页 → 点击活动专区入口 → 活动专区页(活动商品列表) → 点击商品 → 商品详情页 → 点击"立即参与" → 订单确认页 → 支付页 → 开奖等待页 → 结果通知弹窗 → (关闭)分享引导弹窗
```
*   **入口2：通过活动弹窗 (平台营销)**
```
首页 → (触发)活动弹窗广告 → 点击参与 → 商品详情页 → 点击"立即参与" → 订单确认页 → 支付页 → 开奖等待页 → 结果通知弹窗 → (关闭)分享引导弹窗
```

### 3.2. 页面跳转规则

| 源页面 | 触发操作 | 目标页面 | 备注 |
| :--- | :--- | :--- | :--- |
| 首页 | (自动触发)拉新弹窗 | 订单确认页 | **新手团流程**，可跳过详情页直接确认订单 |
| 首页 | (自动触发)活动弹窗 | 商品详情页 | **低/高价团流程**，引导至特定商品 |
| 首页 | 点击Banner/活动入口 | 活动专区页 | 用户主动寻找活动 |
| 首页 | 点击商品卡片 | 商品详情页 | 用户浏览信息流 |
| 活动专区页 | 点击商品卡片 | 商品详情页 | - |
| 商品详情页 | 点击"立即参与" | 订单确认页 | - |
| 订单确认页 | 点击"提交订单" | 支付页 | 统一支付入口 |
| 支付页 | 支付成功 | 开奖等待页 | - |
| (任意页面) | 开奖后，收到通知 | 结果通知弹窗 | 开奖结果通过弹窗全局触达 |
| 结果通知弹窗 | 关闭弹窗 | 分享引导弹窗 | 联动触发，鼓励社交裂变 |

---

## 4. 非功能性需求 (Non-Functional Requirements)

| 类别                 | 需求描述                                                                                                                                                                  |
| :------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **性能需求**   | - 开奖逻辑处理需高效，确保在高并发下能快速、准确地计算出所有参与用户的中奖结果。                                                                                          |
| **安全性需求** | -**防作弊**：后台风控策略相关接口需做严格的权限校验，防止被刷。`<br>`- **资金安全**：用户余额、体验金、佣金等资金操作必须有详细的日志记录，便于对账和追溯。 |
| **数据准确性** | - 中签/未中签的概率计算和结果分配必须保证绝对准确，符合后台配置的策略。`<br>`- 佣金和补贴的计算必须准确无误。                                                           |

---

## 5. 未来规划 (Future Roadmap)

- **转盘抽奖功能：** 作为邀请有效用户的核心奖励，提供丰富的奖品池，增加裂变趣味性。
- **积分商城：** 建立积分消耗渠道，用户可使用积分兑换优惠券、红包或实物小礼品。
- **内容社区：** 增加"晒单"板块，鼓励中签用户分享喜悦，营造社群氛围，提高用户信任度和参与度。
- **会员体系：** 建立付费会员体系，提供"中签率加成"、"更高补贴"等特权。

---

**文档结束** 