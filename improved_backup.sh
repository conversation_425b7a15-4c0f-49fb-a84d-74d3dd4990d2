#!/bin/bash

# 改进的备份脚本 - 增强错误检测
# 使用方法: ./improved_backup.sh [数据库名]

SERVER_IP="*************"
DATABASE_NAME=${1:-"tgw_pp"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="${DATABASE_NAME}_${TIMESTAMP}.sql"
LOCAL_BACKUP_DIR="DataBackup"

echo "=== MySQL 数据库备份脚本 ==="
echo "数据库: $DATABASE_NAME"
echo "服务器: $SERVER_IP"
echo "备份文件: $BACKUP_FILENAME"
echo ""

# 创建本地备份目录
if [ ! -d "$LOCAL_BACKUP_DIR" ]; then
    echo "创建备份目录: $LOCAL_BACKUP_DIR"
    mkdir -p "$LOCAL_BACKUP_DIR"
fi

# 步骤1: 检查服务器连接
echo "步骤1: 检查服务器连接..."
ssh -o ConnectTimeout=10 root@$SERVER_IP "echo '服务器连接成功'" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到服务器 $SERVER_IP"
    exit 1
fi

# 步骤2: 检查数据库是否存在
echo "步骤2: 检查数据库..."
DB_CHECK=$(ssh root@$SERVER_IP "mysql -u root -p -e 'SHOW DATABASES LIKE \"$DATABASE_NAME\";' 2>/dev/null | grep '$DATABASE_NAME'")
if [ -z "$DB_CHECK" ]; then
    echo "错误: 数据库 '$DATABASE_NAME' 不存在"
    echo "可用的数据库:"
    ssh root@$SERVER_IP "mysql -u root -p -e 'SHOW DATABASES;' 2>/dev/null"
    exit 1
fi
echo "数据库 '$DATABASE_NAME' 存在"

# 步骤3: 执行备份
echo "步骤3: 执行数据库备份..."
echo "正在备份，请输入 MySQL 密码..."

# 使用更详细的错误检测
ssh root@$SERVER_IP "
set -e
echo '开始备份...'
mysqldump -u root -p --single-transaction --routines --triggers $DATABASE_NAME > /root/$BACKUP_FILENAME 2>/tmp/backup_error.log
if [ \$? -eq 0 ]; then
    FILE_SIZE=\$(stat -c%s /root/$BACKUP_FILENAME)
    if [ \$FILE_SIZE -gt 0 ]; then
        echo \"备份成功，文件大小: \$(ls -lh /root/$BACKUP_FILENAME | awk '{print \$5}')\"
        echo \"备份文件路径: /root/$BACKUP_FILENAME\"
    else
        echo \"错误: 备份文件为空\"
        cat /tmp/backup_error.log
        exit 1
    fi
else
    echo \"错误: mysqldump 执行失败\"
    cat /tmp/backup_error.log
    exit 1
fi
"

if [ $? -ne 0 ]; then
    echo "备份失败，请检查上面的错误信息"
    exit 1
fi

# 步骤4: 验证远程文件
echo "步骤4: 验证远程备份文件..."
REMOTE_FILE_INFO=$(ssh root@$SERVER_IP "ls -lh /root/$BACKUP_FILENAME 2>/dev/null")
if [ -z "$REMOTE_FILE_INFO" ]; then
    echo "错误: 远程备份文件不存在"
    exit 1
fi
echo "远程文件信息: $REMOTE_FILE_INFO"

# 步骤5: 下载文件
echo "步骤5: 下载备份文件..."
scp root@$SERVER_IP:/root/$BACKUP_FILENAME "$LOCAL_BACKUP_DIR/"

if [ $? -ne 0 ]; then
    echo "错误: 文件下载失败"
    exit 1
fi

# 步骤6: 验证本地文件
LOCAL_FILE_PATH="$LOCAL_BACKUP_DIR/$BACKUP_FILENAME"
if [ -f "$LOCAL_FILE_PATH" ]; then
    LOCAL_FILE_SIZE=$(stat -c%s "$LOCAL_FILE_PATH" 2>/dev/null || stat -f%z "$LOCAL_FILE_PATH" 2>/dev/null)
    if [ "$LOCAL_FILE_SIZE" -gt 0 ]; then
        echo "本地文件验证成功:"
        ls -lh "$LOCAL_FILE_PATH"
        
        # 显示文件前几行以确认内容
        echo ""
        echo "备份文件内容预览:"
        head -5 "$LOCAL_FILE_PATH"
    else
        echo "错误: 本地文件为空 (大小: $LOCAL_FILE_SIZE 字节)"
        exit 1
    fi
else
    echo "错误: 本地文件不存在"
    exit 1
fi

# 步骤7: 清理远程文件
echo ""
read -p "是否删除服务器上的备份文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    ssh root@$SERVER_IP "rm -f /root/$BACKUP_FILENAME"
    echo "远程备份文件已删除"
else
    echo "远程备份文件保留在: /root/$BACKUP_FILENAME"
fi

echo ""
echo "=== 备份完成 ==="
echo "本地文件: $LOCAL_FILE_PATH"
echo "文件大小: $(ls -lh "$LOCAL_FILE_PATH" | awk '{print $5}')"

# 显示最近的备份文件
echo ""
echo "=== 最近的备份文件 ==="
ls -lt "$LOCAL_BACKUP_DIR"/*.sql 2>/dev/null | head -5
