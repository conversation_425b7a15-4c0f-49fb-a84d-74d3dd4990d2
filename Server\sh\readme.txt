│ >   📜 便捷启动脚本                                                                                                             │
│                                                                                                                                 │
│     - start-backend.sh - 后端服务启动                                                                                           │
│     - start-frontend.sh - 前端应用启动                                                                                          │
│     - start-admin-web.sh - 管理端启动                                                                                           │
│     - start-all.sh - 一键启动所有服务                                                                                           │
│     - stop-all.sh - 一键停止所有服务                                                                                            │
│     - check-status.sh - 服务状态检查  
│     - restart-all.sh - 快速重启脚本                                                                                            │
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘