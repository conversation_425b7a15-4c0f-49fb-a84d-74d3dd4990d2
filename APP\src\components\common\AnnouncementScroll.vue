<template>
  <div class="bg-white mx-4 rounded-2xl p-3 shadow-sm mb-2">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center">
        <iconify-icon icon="material-symbols:campaign" class="text-orange-500 mr-2 text-lg"></iconify-icon>
        <span class="text-sm font-medium text-gray-700">中奖公告</span>
      </div>
      <div class="text-xs text-gray-400">实时更新</div>
    </div>
    
    <!-- 滚动公告区域 -->
    <div class="announcement-container h-8 overflow-hidden relative">
      <div 
        v-if="announcements.length > 0"
        class="announcement-scroll absolute top-0 left-0 w-full"
        :style="{ transform: `translateY(-${currentIndex * 32}px)` }"
      >
        <div 
          v-for="(announcement, index) in announcements" 
          :key="announcement.id"
          class="announcement-item h-8 flex items-center text-sm text-gray-600 leading-8"
        >
          <span class="announcement-text">
            恭喜 <span class="text-blue-600 font-medium">{{ announcement.nickname }}</span> 
            在 <span class="text-green-600">{{ announcement.productName }}</span> 
            中 <span :class="announcement.isWin ? 'text-red-500 font-medium' : 'text-orange-500'">
              {{ announcement.isWin ? '拼中' : '未拼中' }}
            </span>
            ，获得 <span class="text-yellow-600 font-bold">{{ announcement.reward }}₫</span> 奖励
          </span>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else class="h-8 flex items-center justify-center text-xs text-gray-400">
        <iconify-icon icon="material-symbols:loading" class="animate-spin mr-1"></iconify-icon>
        正在加载公告...
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'AnnouncementScroll',
  setup() {
    const currentIndex = ref(0)
    const announcements = ref([])
    let scrollInterval = null
    let updateInterval = null

    // 生成随机用户昵称
    const generateNickname = () => {
      const nicknames = [
        '幸运星', '小确幸', '阳光少年', '微笑天使', '梦想家', '快乐小熊', 
        '甜心宝贝', '开心果', '小幸福', '温暖如初', '简单快乐', '晴天娃娃',
        '星空漫步', '花开半夏', '清晨阳光', '微风轻拂', '彩虹糖果', '甜蜜时光',
        '心动瞬间', '暖暖的我', '小可爱', '萌萌哒', '笑容如花', '阳光明媚',
        '温柔时光', '美好如初', '小确幸者', '快乐星球', '甜甜圈', '小幸运',
        '默***', '小***', '张***', '李***', '王***', '刘***', '陈***', '杨***'
      ]
      return nicknames[Math.floor(Math.random() * nicknames.length)]
    }

    // 生成随机商品名称
    const generateProductName = () => {
      const products = [
        'iPhone 15 Pro', 'MacBook Air', '华为Mate60', '小米14 Ultra', 
        '戴森吸尘器', '飞天茅台', 'LV手提包', '爱马仕丝巾',
        'AirPods Pro', 'iPad Pro', '索尼相机', '任天堂Switch',
        '茅台酒', '五粮液', '剑南春', '洋河蓝色经典',
        '香奈儿香水', 'Dior口红', '兰蔻面霜', '雅诗兰黛精华',
        '特斯拉充电器', '小鹏汽车周边', '理想ONE车载用品'
      ]
      return products[Math.floor(Math.random() * products.length)]
    }

    // 生成随机奖励金额
    const generateReward = (isWin) => {
      if (isWin) {
        // 拼中奖励：10-500万越南盾
        const amounts = [100000, 200000, 350000, 500000, 800000, 1000000, 1500000, 2000000, 3500000, 5000000]
        return amounts[Math.floor(Math.random() * amounts.length)].toLocaleString()
      } else {
        // 未拼中安慰奖：1-10万越南盾
        const amounts = [10000, 15000, 20000, 25000, 30000, 50000, 80000, 100000]
        return amounts[Math.floor(Math.random() * amounts.length)].toLocaleString()
      }
    }

    // 生成公告数据
    const generateAnnouncements = () => {
      const newAnnouncements = []
      for (let i = 0; i < 20; i++) {
        const isWin = Math.random() > 0.3 // 70%几率拼中，30%未拼中
        newAnnouncements.push({
          id: Date.now() + i,
          nickname: generateNickname(),
          productName: generateProductName(),
          isWin: isWin,
          reward: generateReward(isWin),
          timestamp: new Date()
        })
      }
      announcements.value = newAnnouncements
    }

    // 开始滚动
    const startScrolling = () => {
      scrollInterval = setInterval(() => {
        currentIndex.value = (currentIndex.value + 1) % announcements.value.length
      }, 3000) // 每3秒滚动一次
    }

    // 停止滚动
    const stopScrolling = () => {
      if (scrollInterval) {
        clearInterval(scrollInterval)
        scrollInterval = null
      }
    }

    // 定期更新公告数据
    const updateAnnouncements = () => {
      updateInterval = setInterval(() => {
        // 添加新公告到开头
        const isWin = Math.random() > 0.3
        const newAnnouncement = {
          id: Date.now(),
          nickname: generateNickname(),
          productName: generateProductName(),
          isWin: isWin,
          reward: generateReward(isWin),
          timestamp: new Date()
        }
        
        announcements.value.unshift(newAnnouncement)
        
        // 保持最多50条公告
        if (announcements.value.length > 50) {
          announcements.value = announcements.value.slice(0, 50)
        }
      }, 30000) // 每30秒添加新公告
    }

    onMounted(() => {
      console.log('🎺 公告滚动组件已挂载')
      generateAnnouncements()
      console.log('📢 生成公告数据:', announcements.value.length, '条')
      startScrolling()
      updateAnnouncements()
    })

    onUnmounted(() => {
      console.log('🎺 公告滚动组件正在卸载，清理定时器')
      stopScrolling()
      // 清理更新定时器
      if (updateInterval) {
        clearInterval(updateInterval)
        updateInterval = null
      }
    })

    return {
      currentIndex,
      announcements
    }
  }
}
</script>

<style lang="scss" scoped>
.announcement-container {
  position: relative;
  overflow: hidden;
}

.announcement-scroll {
  transition: transform 0.8s ease-in-out;
}

.announcement-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 4px;
}

.announcement-text {
  display: inline-block;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(10px); }
  to { opacity: 1; transform: translateX(0); }
}

/* 手机端适配 */
@media (max-width: 640px) {
  .announcement-item {
    font-size: 13px;
  }
}
</style>