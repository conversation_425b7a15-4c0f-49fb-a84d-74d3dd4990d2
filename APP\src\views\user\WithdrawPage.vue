<template>
  <div class="bg-gray-50 min-h-screen pb-20">
    <!-- 顶部导航栏 -->
    <div class="bg-white border-b border-gray-100 px-4 py-3 flex items-center sticky top-0 z-10">
      <button @click="goToProfile" class="mr-3">
        <iconify-icon icon="material-symbols:arrow-back" class="text-xl text-gray-600"></iconify-icon>
      </button>
      <h1 class="text-lg font-semibold text-gray-800 flex-1">提现</h1>
      <button @click="goToWithdrawHistory" class="text-blue-500 text-sm">
        <iconify-icon icon="material-symbols:history" class="mr-1"></iconify-icon>
        记录
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !authError" class="flex items-center justify-center py-20">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
        <p class="text-gray-500 text-sm">正在加载...</p>
      </div>
    </div>

    <!-- 认证错误状态 -->
    <div v-else-if="authError" class="flex flex-col items-center justify-center min-h-screen px-4">
      <div class="bg-white rounded-2xl p-8 shadow-sm text-center max-w-sm mx-auto">
        <iconify-icon icon="material-symbols:error" class="text-red-500 text-6xl mb-4"></iconify-icon>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">认证失败</h3>
        <p class="text-gray-600 text-sm mb-6">
          登录状态已过期，请重新登录
        </p>
        <div class="space-y-3">
          <button 
            @click="retryAuthentication"
            :disabled="retryCount >= maxRetries"
            class="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium text-sm hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ retryCount >= maxRetries ? '重试次数已用完' : `重试 (${retryCount}/${maxRetries})` }}
          </button>
          <button 
            @click="$router.push('/login')"
            class="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium text-sm hover:bg-gray-300 transition-colors"
          >
            重新登录
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="px-4 py-4 space-y-4">
      <!-- 钱包信息卡片 -->
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <iconify-icon icon="material-symbols:account-balance-wallet" class="text-2xl mr-3"></iconify-icon>
            <span class="text-lg font-medium">钱包余额</span>
          </div>
          <button @click="refreshBalance" class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors">
            <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
          </button>
        </div>
        <div class="text-3xl font-bold mb-2">{{ formatCurrency(walletBalance) }}</div>
        <div class="text-sm opacity-90">可提现余额</div>
      </div>

      <!-- 提现表单 -->
      <div class="bg-white rounded-2xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-gray-800 mb-6">提现信息</h2>
        
        <!-- 提现金额 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">提现金额</label>
          <div class="relative">
            <input
              v-model="withdrawForm.amount"
              type="number"
              placeholder="请输入提现金额"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :class="{
                'border-red-300 focus:ring-red-500': errors.amount
              }"
            />
            <button 
              @click="setAllBalance"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 text-sm px-2 py-1 rounded hover:bg-blue-50 transition-colors"
            >
              全部
            </button>
          </div>
          <div v-if="errors.amount" class="text-red-500 text-xs mt-1">{{ errors.amount }}</div>
          <div v-if="withdrawForm.amount" class="text-gray-500 text-xs mt-1">
            手续费：{{ formatCurrency(calculateFee(withdrawForm.amount)) }}，实际到账：{{ formatCurrency(calculateActualAmount(withdrawForm.amount)) }}
          </div>
        </div>

        <!-- 银行信息 -->
        <div class="space-y-4">
          <!-- 银行名称 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">银行名称</label>
            <input
              v-model="withdrawForm.bankName"
              type="text"
              placeholder="请输入银行名称，如：越南银行"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :class="{
                'border-red-300 focus:ring-red-500': errors.bankName
              }"
            />
            <div v-if="errors.bankName" class="text-red-500 text-xs mt-1">{{ errors.bankName }}</div>
          </div>

          <!-- 银行账号 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">银行账号</label>
            <input
              v-model="withdrawForm.bankAccount"
              type="text"
              placeholder="请输入银行账号"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :class="{
                'border-red-300 focus:ring-red-500': errors.bankAccount
              }"
            />
            <div v-if="errors.bankAccount" class="text-red-500 text-xs mt-1">{{ errors.bankAccount }}</div>
          </div>

          <!-- 账户姓名 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">账户姓名</label>
            <input
              v-model="withdrawForm.accountHolder"
              type="text"
              placeholder="请输入银行账户姓名"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              :class="{
                'border-red-300 focus:ring-red-500': errors.accountHolder
              }"
            />
            <div v-if="errors.accountHolder" class="text-red-500 text-xs mt-1">{{ errors.accountHolder }}</div>
          </div>
        </div>

        <!-- 快捷银行选择 -->
        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">常用银行</label>
          <div class="grid grid-cols-2 gap-3">
            <button
              v-for="bank in popularBanks"
              :key="bank"
              @click="selectBank(bank)"
              class="p-3 border border-gray-200 rounded-lg text-center text-sm hover:border-blue-300 hover:bg-blue-50 transition-colors"
              :class="{
                'border-blue-500 bg-blue-50 text-blue-600': withdrawForm.bankName === bank
              }"
            >
              {{ bank }}
            </button>
          </div>
        </div>
      </div>

      <!-- 提现说明 -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
          <iconify-icon icon="material-symbols:info" class="text-yellow-600 mt-0.5 mr-2"></iconify-icon>
          <div class="text-sm text-yellow-800">
            <p class="font-medium mb-1">提现说明：</p>
            <ul class="space-y-1 text-xs">
              <li>• 单次提现金额：最低100₫，最高100,000₫</li>
              <li>• 提现手续费：{{ (feeRate * 100).toFixed(1) }}%，最低{{ formatCurrency(minFee) }}</li>
              <li>• 工作日提现，1-3个工作日内到账</li>
              <li>• 请确保银行信息准确，错误信息可能导致提现失败</li>
              <li>• 每日提现次数限制：{{ maxDailyWithdraws }}次</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="pt-4">
        <button
          @click="submitWithdraw"
          :disabled="!canSubmit || submitting"
          class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold py-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:transform-none disabled:cursor-not-allowed"
        >
          <div v-if="submitting" class="flex items-center justify-center">
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            提交中...
          </div>
          <div v-else class="flex items-center justify-center">
            <iconify-icon icon="material-symbols:send" class="mr-2"></iconify-icon>
            确认提现
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError, showLoading, hideLoading } from '@/utils/message.js'
import { StandardApiAdapter } from '@/api/standardAdapter.js'
import { useQuickAuthCheck } from '@/composables/useAuthGuard'

export default {
  name: 'WithdrawPage',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 快速登录检查
    const { isAuthenticated, requireAuth } = useQuickAuthCheck()
    
    // 响应式数据
    const loading = ref(false)
    const submitting = ref(false)
    const walletBalance = ref(0)
    const authError = ref(false)
    const retryCount = ref(0)
    const maxRetries = ref(3)
    
    // API服务
    let apiAdapter = null
    
    // 提现表单
    const withdrawForm = ref({
      amount: '',
      bankName: '',
      bankAccount: '',
      accountHolder: ''
    })
    
    // 表单验证错误
    const errors = ref({})
    
    // 常用银行列表
    const popularBanks = ref([
      '越南银行',
      'Vietcombank',
      'Techcombank',
      'BIDV',
      'Agribank',
      'VPBank'
    ])
    
    // 提现配置
    const feeRate = ref(0.01) // 1% 手续费
    const minFee = ref(1000) // 最低手续费 1000₫
    const minAmount = ref(10000) // 最低提现 10000₫
    const maxAmount = ref(********) // 最高提现 10,000,000₫
    const maxDailyWithdraws = ref(3) // 每日最大提现次数
    
    // 初始化API服务
    const initApiService = async () => {
      try {
        apiAdapter = new StandardApiAdapter()
        console.log('✅ 提现页面API服务初始化成功')
      } catch (err) {
        console.error('❌ 提现页面API服务初始化失败:', err)
        showError('API服务初始化失败')
      }
    }
    
    // 检查认证状态和token有效性
    const checkAuthenticationStatus = async () => {
      try {
        console.log('🔍 检查认证状态...')
        
        // 1. 检查基本登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录或无token')
          authError.value = true
          return false
        }
        
        // 2. 检查token格式
        const token = authStore.token
        if (!token || typeof token !== 'string' || token.length < 10) {
          console.warn('❌ Token格式无效')
          authError.value = true
          await handleAuthFailure('Token格式无效')
          return false
        }
        
        // 3. 验证token有效性（通过API调用）
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
        
        // 使用轻量级API验证token
        const response = await fetch('/api/v1/wallet', {
          method: 'GET',
          headers: headers,
          mode: 'cors',
          credentials: 'omit'
        })
        
        if (!response.ok) {
          const result = await response.json()
          if (result.code === 30007 || result.code === 401 || result.code === 403) {
            console.warn('❌ Token已失效:', result.code)
            authError.value = true
            await handleAuthFailure('登录已过期')
            return false
          }
        }
        
        console.log('✅ 认证状态检查通过')
        authError.value = false
        retryCount.value = 0
        return true
        
      } catch (error) {
        console.error('❌ 认证状态检查异常:', error)
        authError.value = true
        
        // 网络错误不立即跳转，允许重试
        if (retryCount.value < maxRetries.value) {
          retryCount.value++
          console.log(`🔄 认证检查重试 ${retryCount.value}/${maxRetries.value}`)
          return false
        } else {
          await handleAuthFailure('网络异常，请检查网络连接')
          return false
        }
      }
    }
    
    // 处理认证失败
    const handleAuthFailure = async (reason = '认证失败') => {
      try {
        console.error('🔐 认证失败处理:', reason)
        
        // 1. 清除认证状态
        await authStore.logout()
        
        // 2. 设置错误状态
        authError.value = true
        
        // 3. 显示错误提示
        showError(`${reason}，请重新登录`)
        
        // 4. 延迟跳转到登录页面
        setTimeout(() => {
          router.push({
            name: 'Login',
            query: { redirect: '/user/withdraw' }
          })
        }, 2000)
        
      } catch (error) {
        console.error('❌ 认证失败处理异常:', error)
        // 强制跳转
        router.push('/login')
      }
    }
    
    // 重试认证
    const retryAuthentication = async () => {
      try {
        console.log('🔄 重试认证...')
        retryCount.value = 0
        authError.value = false
        await initializeData()
      } catch (error) {
        console.error('❌ 重试认证失败:', error)
        showError('重试失败，请重新登录')
      }
    }
    
    // 计算属性
    const canSubmit = computed(() => {
      return withdrawForm.value.amount && 
             withdrawForm.value.bankName && 
             withdrawForm.value.bankAccount && 
             withdrawForm.value.accountHolder &&
             Object.keys(errors.value).length === 0 &&
             !authError.value
    })
    
    // 方法
    const formatCurrency = (amount) => {
      if (!amount) return '0₫'
      return `${parseFloat(amount).toLocaleString()}₫`
    }
    
    const calculateFee = (amount) => {
      if (!amount) return 0
      const fee = parseFloat(amount) * feeRate.value
      return Math.max(fee, minFee.value)
    }
    
    const calculateActualAmount = (amount) => {
      if (!amount) return 0
      return parseFloat(amount) - calculateFee(amount)
    }
    
    const setAllBalance = () => {
      withdrawForm.value.amount = walletBalance.value.toString()
      validateAmount()
    }
    
    const selectBank = (bankName) => {
      withdrawForm.value.bankName = bankName
      if (errors.value.bankName) {
        delete errors.value.bankName
      }
    }
    
    const validateAmount = () => {
      const amount = parseFloat(withdrawForm.value.amount)
      
      if (!withdrawForm.value.amount) {
        errors.value.amount = '请输入提现金额'
        return false
      }
      
      if (isNaN(amount) || amount <= 0) {
        errors.value.amount = '请输入有效的金额'
        return false
      }
      
      if (amount < minAmount.value) {
        errors.value.amount = `最低提现金额为 ${formatCurrency(minAmount.value)}`
        return false
      }
      
      if (amount > maxAmount.value) {
        errors.value.amount = `最高提现金额为 ${formatCurrency(maxAmount.value)}`
        return false
      }
      
      if (amount > walletBalance.value) {
        errors.value.amount = '提现金额不能超过可用余额'
        return false
      }
      
      delete errors.value.amount
      return true
    }
    
    const validateForm = () => {
      errors.value = {}
      
      // 验证金额
      if (!validateAmount()) {
        return false
      }
      
      // 验证银行名称
      if (!withdrawForm.value.bankName.trim()) {
        errors.value.bankName = '请输入银行名称'
      }
      
      // 验证银行账号
      if (!withdrawForm.value.bankAccount.trim()) {
        errors.value.bankAccount = '请输入银行账号'
      } else if (!/^\d+$/.test(withdrawForm.value.bankAccount.trim())) {
        errors.value.bankAccount = '银行账号只能包含数字'
      }
      
      // 验证账户姓名
      if (!withdrawForm.value.accountHolder.trim()) {
        errors.value.accountHolder = '请输入账户姓名'
      }
      
      return Object.keys(errors.value).length === 0
    }
    
    const refreshBalance = async () => {
      try {
        loading.value = true
        await loadWalletBalance()
        showSuccess('余额已刷新')
      } catch (error) {
        console.error('刷新余额失败:', error)
        showError('刷新失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    const loadWalletBalance = async () => {
      try {
        if (!apiAdapter) {
          await initApiService()
        }
        
        // 1. 检查认证状态
        const isAuthenticated = await checkAuthenticationStatus()
        if (!isAuthenticated) {
          console.warn('❌ 认证检查失败，跳过钱包信息获取')
          walletBalance.value = 0
          return
        }
        
        console.log('🔄 获取钱包余额...')
        
        // 2. 构建请求头
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        }
        
        // 3. 构建URL，添加时间戳防止缓存
        const timestamp = new Date().getTime()
        const url = `/api/v1/wallet?_t=${timestamp}`
        
        // 4. 调用钱包API
        const response = await fetch(url, {
          method: 'GET',
          headers: headers,
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-store'
        })
        
        const result = await response.json()
        console.log('💰 提现页面钱包API响应:', result)
        
        if (response.ok && (result.code === 0 || result.code === 200)) {
          const data = result.data || {}
          walletBalance.value = data.balance?.balance || data.balance?.totalBalance || 0
          console.log('✅ 钱包余额获取成功:', walletBalance.value)
        } else if (result.code === 30007 || result.code === 401 || result.code === 403) {
          // 处理认证失败
          console.error('🔐 钱包API认证失败:', result)
          await handleAuthFailure('登录已过期')
        } else {
          throw new Error(result.message || result.msg || '获取余额失败')
        }
      } catch (error) {
        console.error('❌ 获取钱包余额失败:', error)
        
        // 处理网络异常
        if (error.message.includes('401') || error.message.includes('403')) {
          await handleAuthFailure('认证失败')
        } else {
          showError('获取余额失败，请重试')
          walletBalance.value = 0
        }
      }
    }
    
    // 检查实名认证状态
    const checkIdentityVerification = async () => {
      try {
        if (!apiAdapter) {
          await initApiService()
        }
        
        console.log('🔍 调用实名认证检查API...')
        const response = await apiAdapter.checkIdentityVerification()
        
        console.log('📝 实名认证检查响应:', response)
        
        if (response.code === 200 || response.code === 0) {
          const data = response.data || {}
          const isVerified = data.isVerified || data.verified || false
          const status = data.status || 'not_verified'
          
          console.log('✅ 实名认证状态:', { isVerified, status })
          
          if (!isVerified || status === 'not_verified' || status === 'pending') {
            // 用户未完成实名认证，跳转到实名认证页面
            console.log('⚠️ 用户未完成实名认证，跳转到实名认证页面')
            
            showError('提现前需要完成实名认证')
            
            // 跳转到实名认证页面，并传递来源参数
            await router.push({
              path: '/user/identity-verification',
              query: { from: 'withdraw' }
            })
            
            return false
          }
          
          if (status === 'rejected') {
            showError('实名认证被拒绝，请重新提交认证资料')
            await router.push({
              path: '/user/identity-verification',
              query: { from: 'withdraw' }
            })
            return false
          }
          
          // 实名认证通过，可以继续提现
          console.log('✅ 实名认证已通过，可以继续提现')
          return true
        } else {
          throw new Error(response.message || '检查实名认证状态失败')
        }
      } catch (error) {
        console.error('❌ 检查实名认证状态失败:', error)
        
        // 如果API调用失败，假设用户未认证，跳转到实名认证页面
        showError('无法确认实名认证状态，请先完成实名认证')
        await router.push({
          path: '/user/identity-verification',
          query: { from: 'withdraw' }
        })
        
        return false
      }
    }
    
    const submitWithdraw = async () => {
      try {
        // 1. 首先检查实名认证状态
        console.log('🔍 检查实名认证状态...')
        const isVerified = await checkIdentityVerification()
        
        // 2. 如果实名认证检查未通过，直接返回（checkIdentityVerification已经处理了跳转）
        if (!isVerified) {
          return
        }
        
        // 3. 实名认证通过，继续提现流程
        if (!validateForm()) {
          showError('请检查表单信息')
          return
        }
        
        // 检查认证状态
        const isAuthenticated = await checkAuthenticationStatus()
        if (!isAuthenticated) {
          showError('登录状态已过期，请重新登录')
          return
        }
        
        submitting.value = true
        showLoading('正在提交提现申请...')
        
        const withdrawData = {
          amount: withdrawForm.value.amount.toString(), // 确保amount为字符串类型
          bankName: withdrawForm.value.bankName.trim(),
          bankAccount: withdrawForm.value.bankAccount.trim(),
          accountHolder: withdrawForm.value.accountHolder.trim()
        }
        
        console.log('🔄 提交提现申请:', withdrawData)
        
        const response = await fetch('/api/v1/withdrawalsApply', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          },
          body: JSON.stringify(withdrawData)
        })
        
        const result = await response.json()
        
        // 记录完整的API响应用于调试
        console.log('📋 API响应状态:', response.status, response.statusText)
        console.log('📋 API响应数据:', result)
        
        if (response.ok && result.code === 0) {
          console.log('✅ 提现申请提交成功')
          hideLoading()
          showSuccess('提现申请已提交，请等待审核')
          
          // 清空表单
          withdrawForm.value = {
            amount: '',
            bankName: '',
            bankAccount: '',
            accountHolder: ''
          }
          
          // 刷新余额
          await loadWalletBalance()
          
          // 延迟跳转到提现记录页面
          setTimeout(() => {
            goToWithdrawHistory()
          }, 2000)
          
        } else if (result.code === 30007 || result.code === 401 || result.code === 403) {
          // 处理认证失败
          console.error('🔐 提现申请认证失败:', result)
          hideLoading()
          await handleAuthFailure('登录已过期')
        } else {
          // 记录详细的错误信息
          console.error('❌ 提现申请失败详情:', {
            status: response.status,
            statusText: response.statusText,
            result: result,
            code: result.code,
            message: result.message,
            msg: result.msg
          })
          throw new Error(result.message || result.msg || `提现申请失败 (错误代码: ${result.code})`)
        }
        
      } catch (error) {
        console.error('❌ 提现申请失败:', error)
        hideLoading()
        
        if (error.message.includes('401') || error.message.includes('403')) {
          await handleAuthFailure('认证失败')
        } else {
          showError(error.message || '提现申请失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }
    
    const goToWithdrawHistory = () => {
      router.push('/user/withdraw/history')
    }
    
    const goToProfile = () => {
      router.push('/user')
    }
    
    // 初始化数据
    const initializeData = async () => {
      try {
        loading.value = true
        authError.value = false
        
        console.log('🚀 提现页面初始化开始')
        
        // 1. 检查基本登录状态
        if (!authStore.isLoggedIn || !authStore.token) {
          console.warn('❌ 未登录状态')
          authError.value = true
          
          // 延迟跳转到登录页面
          setTimeout(() => {
            router.push({
              name: 'Login',
              query: { redirect: '/user/withdraw' }
            })
          }, 1000)
          
          return
        }
        
        // 2. 初始化API服务
        await initApiService()
        
        // 3. 获取钱包信息（包含认证检查）
        await loadWalletBalance()
        
        console.log('✅ 提现页面初始化完成')
        
      } catch (error) {
        console.error('❌ 提现页面初始化失败:', error)
        authError.value = true
        showError('页面初始化失败，请重试')
        
      } finally {
        loading.value = false
      }
    }
    
    // 生命周期
    onMounted(async () => {
      console.log('🚀 提现页面挂载')
      await initializeData()
    })
    
    return {
      // 响应式数据
      loading,
      submitting,
      walletBalance,
      withdrawForm,
      errors,
      popularBanks,
      feeRate,
      minFee,
      maxDailyWithdraws,
      authError,
      retryCount,
      maxRetries,
      
      // 计算属性
      canSubmit,
      
      // 方法
      formatCurrency,
      calculateFee,
      calculateActualAmount,
      setAllBalance,
      selectBank,
      validateAmount,
      refreshBalance,
      submitWithdraw,
      goToWithdrawHistory,
      goToProfile,
      retryAuthentication
    }
  }
}
</script>

<style scoped>
/* 输入框聚焦效果 */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 表单验证错误样式 */
.border-red-300 {
  border-color: #fca5a5;
}

.focus\:ring-red-500:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
</style> 