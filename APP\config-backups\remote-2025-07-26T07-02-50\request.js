import { showError } from '@/utils/message.js'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'
const REQUEST_TIMEOUT = 10000 // 10秒超时

/**
 * 获取存储的token
 * 支持多种token存储方式的兼容性
 */
function getToken() {
  // 优先使用access_token（新的存储方式）
  const accessToken = localStorage.getItem('access_token') || sessionStorage.getItem('access_token')
  if (accessToken) {
    return accessToken
  }
  
  // 兼容旧的token存储方式
  const token = localStorage.getItem('token')
  if (token) {
    return token
  }
  
  return null
}

/**
 * 获取刷新token
 */
function getRefreshToken() {
  return localStorage.getItem('refresh_token')
}

/**
 * 设置token
 */
function setToken(token, refreshToken) {
  localStorage.setItem('token', token)
  if (refreshToken) {
    localStorage.setItem('refresh_token', refreshToken)
  }
}

/**
 * 清除token
 */
function clearToken() {
  // 清除所有可能的token存储方式
  localStorage.removeItem('token')
  localStorage.removeItem('access_token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('user_info')
  sessionStorage.removeItem('access_token')
  sessionStorage.removeItem('refresh_token')
}

/**
 * 刷新token
 */
async function refreshToken() {
  const refreshToken = getRefreshToken()
  if (!refreshToken) {
    throw new Error('No refresh token available')
  }

  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${refreshToken}`
      }
    })

    const data = await response.json()
    
    if (data.code === 200) {
      setToken(data.data.token, data.data.refresh_token)
      return data.data.token
    } else {
      throw new Error(data.message || 'Token refresh failed')
    }
  } catch (error) {
    clearToken()
    // 跳转到登录页面
    window.location.href = '/login'
    throw error
  }
}

/**
 * 统一的API请求函数
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {Object} options.headers - 请求头
 * @param {boolean} options.requireAuth - 是否需要认证
 * @param {number} options.timeout - 请求超时时间
 * @returns {Promise<Object>} 响应数据
 */
export async function apiRequest(url, options = {}) {
  const {
    method = 'GET',
    data = null,
    headers = {},
    requireAuth = true,
    timeout = REQUEST_TIMEOUT
  } = options

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`

  // 构建请求头
  const requestHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers
  }

  // 添加认证头
  if (requireAuth) {
    const token = getToken()
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`
    }
  }

  // 构建请求配置
  const requestConfig = {
    method,
    headers: requestHeaders,
    signal: AbortSignal.timeout(timeout)
  }

  // 添加请求体
  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestConfig.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(fullUrl, requestConfig)
    
    // 检查响应是否为空或无效
    if (!response) {
      throw new Error('服务器无响应')
    }
    
    let responseData
    try {
      responseData = await response.json()
    } catch (jsonError) {
      // 如果JSON解析失败，返回错误信息
      throw new Error('服务器响应格式错误')
    }

    // 处理HTTP状态码
    if (!response.ok) {
      // 401 未授权，尝试刷新token
      if (response.status === 401 && requireAuth) {
        try {
          const newToken = await refreshToken()
          // 重新发起请求
          requestHeaders['Authorization'] = `Bearer ${newToken}`
          const retryResponse = await fetch(fullUrl, {
            ...requestConfig,
            headers: requestHeaders
          })
          const retryData = await retryResponse.json()
          
          if (!retryResponse.ok) {
            throw new Error(retryData.message || `HTTP ${retryResponse.status}`)
          }
          
          return retryData
        } catch (refreshError) {
          // 刷新token失败，跳转到登录页面
          clearToken()
          window.location.href = '/login'
          throw new Error('登录已过期，请重新登录')
        }
      }

      // 其他HTTP错误
      throw new Error(responseData.message || `HTTP ${response.status}`)
    }

    // 处理业务错误码
    // 注意：API成功响应的code是0，不是200
    if (responseData.code !== undefined && responseData.code !== 0) {
      throw new Error(responseData.message || responseData.msg || '请求失败')
    }

    return responseData
  } catch (error) {
    console.warn(`API请求失败: ${fullUrl}`, error)
    
    // 网络错误或超时
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接')
    }
    
    if (error.name === 'TypeError' && (error.message.includes('fetch') || error.message.includes('NetworkError'))) {
      throw new Error('网络连接失败，API服务暂时不可用')
    }
    
    // ECONNRESET 或连接重置错误
    if (error.message.includes('ECONNRESET') || error.message.includes('Connection was reset')) {
      throw new Error('服务器连接中断，请稍后重试')
    }

    // 重新抛出其他错误
    throw error
  }
}

/**
 * GET请求 - 自动重试版本
 */
export function get(url, params = {}, options = {}) {
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  const requestFn = () => apiRequest(fullUrl, {
    method: 'GET',
    ...options
  })
  
  // 自动重试网络请求
  return retryRequest(requestFn, 3, 1500)
}

/**
 * POST请求 - 自动重试版本
 */
export function post(url, data = {}, options = {}) {
  const requestFn = () => apiRequest(url, {
    method: 'POST',
    data,
    ...options
  })
  
  // 自动重试网络请求
  return retryRequest(requestFn, 3, 1500)
}

/**
 * PUT请求
 */
export function put(url, data = {}, options = {}) {
  return apiRequest(url, {
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
export function del(url, options = {}) {
  return apiRequest(url, {
    method: 'DELETE',
    ...options
  })
}

/**
 * PATCH请求
 */
export function patch(url, data = {}, options = {}) {
  return apiRequest(url, {
    method: 'PATCH',
    data,
    ...options
  })
}

/**
 * 上传文件
 */
export async function uploadFile(url, file, options = {}) {
  const {
    onProgress,
    headers = {},
    timeout = 30000
  } = options

  const token = getToken()
  const requestHeaders = {
    'Authorization': `Bearer ${token}`,
    ...headers
  }

  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    const formData = new FormData()
    
    formData.append('file', file)

    // 监听上传进度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentComplete = (e.loaded / e.total) * 100
          onProgress(percentComplete)
        }
      })
    }

    // 监听响应
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          resolve(response)
        } catch (error) {
          reject(new Error('响应数据格式错误'))
        }
      } else {
        reject(new Error(`上传失败: HTTP ${xhr.status}`))
      }
    })

    // 监听错误
    xhr.addEventListener('error', () => {
      reject(new Error('上传失败'))
    })

    // 监听超时
    xhr.addEventListener('timeout', () => {
      reject(new Error('上传超时'))
    })

    // 设置超时时间
    xhr.timeout = timeout

    // 设置请求头
    Object.keys(requestHeaders).forEach(key => {
      xhr.setRequestHeader(key, requestHeaders[key])
    })

    // 发送请求
    xhr.open('POST', fullUrl)
    xhr.send(formData)
  })
}

/**
 * 检查网络状态
 */
export function checkNetworkStatus() {
  if (!navigator.onLine) {
    showError('网络连接不可用，请检查网络设置')
    return false
  }
  return true
}

/**
 * 重试请求 - 增强版，处理网络不稳定问题
 */
export async function retryRequest(requestFn, maxRetries = 5, delay = 1000) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await requestFn()
      if (i > 0) {
        console.log(`✅ 请求重试第${i}次成功`)
      }
      return result
    } catch (error) {
      const isNetworkError = error.message.includes('ECONNREFUSED') || 
                           error.message.includes('ECONNRESET') || 
                           error.message.includes('NetworkError') ||
                           error.message.includes('网络连接失败')
      
      if (i === maxRetries - 1) {
        if (isNetworkError) {
          throw new Error(`网络连接不稳定，已重试${maxRetries}次失败。请检查网络或稍后重试。`)
        }
        throw error
      }
      
      if (isNetworkError) {
        const retryDelay = delay * Math.pow(2, i) // 指数退避
        console.warn(`⚠️ 网络连接失败，${retryDelay}ms后进行第${i + 1}次重试...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      } else {
        // 非网络错误直接抛出
        throw error
      }
    }
  }
}

// 导出token管理函数
export {
  getToken,
  getRefreshToken,
  setToken,
  clearToken,
  refreshToken
}

// 默认导出对象，包含所有主要方法
const request = {
  get,
  post,
  put,
  del,
  patch,
  uploadFile,
  apiRequest,
  checkNetworkStatus,
  retryRequest,
  getToken,
  setToken,
  clearToken
}

export default request