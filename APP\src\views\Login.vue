<template>
  <div class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <!-- 顶部导航 -->
    <div class="px-4 py-3 flex items-center justify-between">
      <iconify-icon 
        icon="material-symbols:arrow-back" 
        class="text-2xl text-gray-700 cursor-pointer"
        @click="handleBack"
      ></iconify-icon>
      <span class="text-sm text-gray-600">帮助</span>
    </div>

    <div class="px-5 py-6">
      <!-- Logo和标题 -->
      <div class="text-center mb-8">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mx-auto mb-3 flex items-center justify-center">
          <iconify-icon icon="material-symbols:group" class="text-white text-2xl"></iconify-icon>
        </div>
        <h1 class="text-xl font-bold text-gray-800 mb-2">社交拼团</h1>
        <p class="text-sm text-gray-600">和好友一起买，更便宜更有趣</p>
      </div>

      <!-- Tab切换 -->
      <div class="flex bg-gray-100 rounded-full p-1 mb-6">
        <button 
          class="flex-1 py-2 text-center rounded-full font-medium shadow-sm transition-all text-sm"
          :class="activeTab === 'login' ? 'bg-white text-blue-600' : 'text-gray-600'"
          @click="switchTab('login')"
        >
          登录
        </button>
        <button 
          class="flex-1 py-2 text-center rounded-full transition-all text-sm"
          :class="activeTab === 'register' ? 'bg-white text-blue-600 shadow-sm font-medium' : 'text-gray-600'"
          @click="switchTab('register')"
        >
          注册
        </button>
      </div>

      <!-- 登录表单 -->
      <div v-show="activeTab === 'login'">
        <form @submit.prevent="handleLogin">
          <div class="space-y-3 mb-5">
            <!-- 手机号输入 -->
            <div class="relative">
              <input 
                type="tel" 
                placeholder="请输入手机号" 
                class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="loginForm.phone"
                :class="{ 'border-red-300': loginErrors.phone }"
                @input="clearError('phone')"
              >
              <div v-if="loginErrors.phone" class="text-red-500 text-xs mt-1 ml-3">
                {{ loginErrors.phone }}
              </div>
            </div>

            <!-- 密码输入 -->
            <div class="relative">
              <input 
                :type="showLoginPassword ? 'text' : 'password'" 
                placeholder="请输入登录密码" 
                class="w-full pl-3 pr-10 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="loginForm.password"
                :class="{ 'border-red-300': loginErrors.password }"
                @input="clearError('password')"
              >
              <iconify-icon 
                :icon="showLoginPassword ? 'material-symbols:visibility' : 'material-symbols:visibility-off'" 
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer text-lg"
                @click="showLoginPassword = !showLoginPassword"
              ></iconify-icon>
              <div v-if="loginErrors.password" class="text-red-500 text-xs mt-1 ml-3">
                {{ loginErrors.password }}
              </div>
            </div>

            <!-- 验证码输入 -->
            <div class="flex space-x-2 relative">
              <div class="relative" style="width: calc(100% - 140px);">
                <input 
                  type="text" 
                  placeholder="请输入验证码" 
                  class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                  v-model="loginForm.captchaCode"
                  :class="{ 'border-red-300': loginErrors.captchaCode }"
                  @input="clearError('captchaCode')"
                  maxlength="4"
                >
                <div v-if="loginErrors.captchaCode" class="text-red-500 text-xs mt-1 ml-3">
                  {{ loginErrors.captchaCode }}
                </div>
              </div>
              <!-- 验证码图片 -->
              <div class="absolute right-0 top-0 w-34 h-12 border border-gray-200 rounded-lg overflow-hidden cursor-pointer flex items-center justify-center" style="width: 136px; height: 48px; margin-left: -40px;" @click="loadCaptcha">
                <img 
                  v-if="captchaImage" 
                  :src="captchaImage" 
                  alt="验证码" 
                  class="object-contain"
                  style="transform: scale(1.0); max-width: none; max-height: none;"
                >
                <div v-else class="w-full h-full bg-gray-100 flex items-center justify-center text-xs text-gray-500">
                  点击获取
                </div>
              </div>
            </div>
          </div>

          <!-- 登录按钮 -->
          <button 
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-medium mb-4 text-sm disabled:opacity-50"
            :disabled="loginLoading"
          >
            {{ loginLoading ? '登录中...' : '登录' }}
          </button>
        </form>

        <!-- 忘记密码 -->
        <div class="text-center mb-5">
          <button class="text-blue-600 text-xs" @click="showForgotPasswordModal = true">
            忘记密码？
          </button>
        </div>
      </div>

      <!-- 注册表单 -->
      <div v-show="activeTab === 'register'">
        <form @submit.prevent="handleRegister">
          <div class="space-y-3 mb-5">
            <!-- 手机号输入 -->
            <div class="relative">
              <input 
                type="tel" 
                placeholder="请输入手机号" 
                class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="registerForm.phone"
                :class="{ 'border-red-300': registerErrors.phone }"
                @input="clearRegisterError('phone')"
              >
              <div v-if="registerErrors.phone" class="text-red-500 text-xs mt-1 ml-3">
                {{ registerErrors.phone }}
              </div>
            </div>

            <!-- 设置密码 -->
            <div class="relative">
              <input 
                :type="showRegisterPassword ? 'text' : 'password'" 
                placeholder="设置登录密码" 
                class="w-full pl-3 pr-10 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="registerForm.password"
                :class="{ 'border-red-300': registerErrors.password }"
                @input="clearRegisterError('password')"
              >
              <iconify-icon 
                :icon="showRegisterPassword ? 'material-symbols:visibility' : 'material-symbols:visibility-off'" 
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer text-lg"
                @click="showRegisterPassword = !showRegisterPassword"
              ></iconify-icon>
              <div v-if="registerErrors.password" class="text-red-500 text-xs mt-1 ml-3">
                {{ registerErrors.password }}
              </div>
            </div>

            <!-- 确认密码 -->
            <div class="relative">
              <input 
                :type="showConfirmPassword ? 'text' : 'password'" 
                placeholder="确认登录密码" 
                class="w-full pl-3 pr-10 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="registerForm.confirmPassword"
                :class="{ 'border-red-300': registerErrors.confirmPassword }"
                @input="clearRegisterError('confirmPassword')"
              >
              <iconify-icon 
                :icon="showConfirmPassword ? 'material-symbols:visibility' : 'material-symbols:visibility-off'" 
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer text-lg"
                @click="showConfirmPassword = !showConfirmPassword"
              ></iconify-icon>
              <div v-if="registerErrors.confirmPassword" class="text-red-500 text-xs mt-1 ml-3">
                {{ registerErrors.confirmPassword }}
              </div>
            </div>

            <!-- 验证码输入 -->
            <div class="flex space-x-2 relative">
              <div class="relative" style="width: calc(100% - 140px);">
                <input 
                  type="text" 
                  placeholder="请输入验证码" 
                  class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                  v-model="registerForm.captchaCode"
                  :class="{ 'border-red-300': registerErrors.captchaCode }"
                  @input="clearRegisterError('captchaCode')"
                  maxlength="4"
                >
                <div v-if="registerErrors.captchaCode" class="text-red-500 text-xs mt-1 ml-3">
                  {{ registerErrors.captchaCode }}
                </div>
              </div>
              <!-- 验证码图片 -->
              <div class="absolute right-0 top-0 w-34 h-12 border border-gray-200 rounded-lg overflow-hidden cursor-pointer flex items-center justify-center" style="width: 136px; height: 48px; margin-left: -40px;" @click="loadCaptcha">
                <img 
                  v-if="captchaImage" 
                  :src="captchaImage" 
                  alt="验证码" 
                  class="object-contain"
                  style="transform: scale(1.17); max-width: none; max-height: none;"
                >
                <div v-else class="w-full h-full bg-gray-100 flex items-center justify-center text-xs text-gray-500">
                  点击获取
                </div>
              </div>
            </div>

            <!-- 邀请码（可选） -->
            <div class="relative" v-if="showInviteCode">
              <input 
                type="text" 
                placeholder="邀请码（可选）" 
                class="w-full pl-3 pr-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-colors"
                v-model="registerForm.inviteCode"
              >
            </div>
          </div>

          <!-- 用户协议 -->
          <div class="flex items-start mb-5">
            <input 
              type="checkbox" 
              id="agreement" 
              v-model="registerForm.agreedToTerms"
              class="mt-1 mr-2"
            >
            <label for="agreement" class="text-xs text-gray-600 leading-relaxed">
              我已阅读并同意
              <span class="text-blue-600 cursor-pointer" @click="showUserAgreement">《用户协议》</span>
              和
              <span class="text-blue-600 cursor-pointer" @click="showPrivacyPolicy">《隐私政策》</span>
            </label>
          </div>

          <!-- 注册按钮 -->
          <button 
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-medium mb-4 text-sm disabled:opacity-50"
            :disabled="registerLoading || !registerForm.agreedToTerms"
          >
            {{ registerLoading ? '注册中...' : '立即注册' }}
          </button>
        </form>
      </div>

      <!-- 第三方登录 -->
      <div class="text-center">
        <div class="text-xs text-gray-500 mb-4">或使用以下方式快速登录</div>
        <div class="flex justify-center space-x-6 mb-6">
          <button 
            class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center"
            @click="handleOAuthLogin('wechat')"
            :disabled="oauthLoading"
          >
            <iconify-icon icon="simple-icons:wechat" class="text-white text-lg"></iconify-icon>
          </button>
          <button 
            class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center"
            @click="handleOAuthLogin('alipay')"
            :disabled="oauthLoading"
          >
            <iconify-icon icon="simple-icons:alipay" class="text-white text-lg"></iconify-icon>
          </button>
        </div>
        
        <!-- 随便逛逛按钮 -->
        <div class="mt-6">
          <button 
            @click="goToHome"
            class="w-full border-2 border-gray-300 text-gray-600 py-3 px-4 rounded-xl font-medium text-sm hover:border-gray-400 hover:text-gray-700 transition-colors flex items-center justify-center"
          >
            <iconify-icon icon="material-symbols:explore" class="mr-2 text-lg"></iconify-icon>
            随便逛逛
          </button>
          <p class="text-xs text-gray-500 mt-2">无需登录，浏览精选商品</p>
        </div>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <div v-if="showForgotPasswordModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl w-full max-w-sm p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium">重置密码</h3>
          <iconify-icon 
            icon="material-symbols:close" 
            class="text-xl text-gray-400 cursor-pointer"
            @click="showForgotPasswordModal = false"
          ></iconify-icon>
        </div>
        
        <form @submit.prevent="handleForgotPassword">
          <div class="space-y-3 mb-5">
            <!-- 手机号 -->
            <div class="relative">
              <input 
                type="tel" 
                placeholder="请输入手机号" 
                class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500"
                v-model="forgotPasswordForm.phone"
              >
            </div>
            
            <!-- 验证码 -->
            <div class="relative">
              <input 
                type="text" 
                placeholder="请输入验证码" 
                class="w-full pl-3 pr-20 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500"
                v-model="forgotPasswordForm.verificationCode"
                maxlength="6"
              >
              <button 
                type="button"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 text-xs px-2 py-1 rounded"
                :disabled="forgotPasswordCountdown > 0"
                @click="sendForgotPasswordCode"
              >
                {{ forgotPasswordCountdown > 0 ? `${forgotPasswordCountdown}s` : '获取验证码' }}
              </button>
            </div>
            
            <!-- 新密码 -->
            <div class="relative">
              <input 
                type="password" 
                placeholder="请输入新密码" 
                class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500"
                v-model="forgotPasswordForm.newPassword"
              >
            </div>
            
            <!-- 确认新密码 -->
            <div class="relative">
              <input 
                type="password" 
                placeholder="确认新密码" 
                class="w-full px-3 py-3 text-sm border border-gray-200 rounded-xl focus:outline-none focus:border-blue-500"
                v-model="forgotPasswordForm.confirmNewPassword"
              >
            </div>
          </div>
          
          <button 
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-medium text-sm disabled:opacity-50"
            :disabled="forgotPasswordLoading"
          >
            {{ forgotPasswordLoading ? '重置中...' : '重置密码' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { standardApi } from '@/api/standardAdapter'
import { showSuccess, showError, showInfo } from '@/utils/message'
import { handleLoginRedirect } from '@/utils/loginRedirect'
import { getSavedInviteCode, clearSavedInviteCode } from '@/utils/share'

// 路由
const router = useRouter()
const route = useRoute()

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('login')
const loginLoading = ref(false)
const registerLoading = ref(false)
const oauthLoading = ref(false)
const forgotPasswordLoading = ref(false)

// 验证码相关
const captchaImage = ref('')
const captchaUuid = ref('')

// 密码显示控制
const showLoginPassword = ref(false)
const showRegisterPassword = ref(false)
const showConfirmPassword = ref(false)

// 表单数据
const loginForm = reactive({
  phone: '',
  password: '',
  captchaCode: ''
})

const registerForm = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
  captchaCode: '',
  inviteCode: '',
  agreedToTerms: false
})

const forgotPasswordForm = reactive({
  phone: '',
  verificationCode: '',
  newPassword: '',
  confirmNewPassword: ''
})

// 错误信息
const loginErrors = reactive({})
const registerErrors = reactive({})

// 弹窗控制
const showForgotPasswordModal = ref(false)
const showInviteCode = ref(false)

// 倒计时相关
const forgotPasswordCountdown = ref(0)
let forgotPasswordTimer = null

// 方法
const switchTab = (tab) => {
  activeTab.value = tab
  clearAllErrors()
}

const handleBack = () => {
  router.back()
}

// 随便逛逛 - 跳转到首页
const goToHome = () => {
  console.log('🏠 用户选择随便逛逛，跳转到首页')
  router.push({
    path: '/home',
    query: { from: 'login_browse' }
  })
}

// 加载验证码
const loadCaptcha = async () => {
  try {
    const response = await standardApi.getCaptcha()
    // 根据后端接口文档，成功状态码是 0，不是 200
    if (response.code === 0 && response.ok) {
      captchaImage.value = response.data.captchaBase64Image
      captchaUuid.value = response.data.captchaUuid
      console.log('验证码获取成功，有效期:', response.data.expireSeconds, '秒')
    } else {
      console.error('获取验证码失败:', response.msg)
      showError(response.msg || '获取验证码失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    showError('获取验证码失败，请检查网络连接')
  }
}

// 表单验证
const validateLoginForm = () => {
  clearAllErrors()
  let isValid = true

  if (!loginForm.phone) {
    loginErrors.phone = '请输入手机号'
    isValid = false
  }

  if (!loginForm.password) {
    loginErrors.password = '请输入密码'
    isValid = false
  }

  if (!loginForm.captchaCode) {
    loginErrors.captchaCode = '请输入验证码'
    isValid = false
  }

  return isValid
}

const validateRegisterForm = () => {
  clearAllErrors()
  let isValid = true

  if (!registerForm.phone) {
    registerErrors.phone = '请输入手机号'
    isValid = false
  }

  if (!registerForm.password) {
    registerErrors.password = '请输入密码'
    isValid = false
  } else if (registerForm.password.length < 6) {
    registerErrors.password = '密码长度至少6位'
    isValid = false
  }

  if (!registerForm.confirmPassword) {
    registerErrors.confirmPassword = '请确认密码'
    isValid = false
  } else if (registerForm.password !== registerForm.confirmPassword) {
    registerErrors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  if (!registerForm.captchaCode) {
    registerErrors.captchaCode = '请输入验证码'
    isValid = false
  }

  return isValid
}

// 清除错误信息
const clearError = (field) => {
  delete loginErrors[field]
}

const clearRegisterError = (field) => {
  delete registerErrors[field]
}

const clearAllErrors = () => {
  Object.keys(loginErrors).forEach(key => delete loginErrors[key])
  Object.keys(registerErrors).forEach(key => delete registerErrors[key])
}

// 处理登录 - 修正：适配后端接口格式
const handleLogin = async () => {
  if (!validateLoginForm()) return

  if (!captchaUuid.value) {
    showError('请先获取验证码')
    return
  }

  loginLoading.value = true
  
  try {
    const response = await authStore.login({
      phone: loginForm.phone,
      password: loginForm.password,
      captchaCode: loginForm.captchaCode,
      captchaUuid: captchaUuid.value
    })

    showSuccess('登录成功')
    
    // 使用专用的跳转处理工具
    try {
      await handleLoginRedirect(router, route, authStore, '/user')
      console.log('✅ 登录跳转处理完成')
    } catch (error) {
      console.error('❌ 登录跳转处理失败:', error)
      showError('页面跳转失败，请刷新页面重试')
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    showError(error.message || '登录失败，请稍后重试')
    // 登录失败后重新获取验证码
    loadCaptcha()
  } finally {
    loginLoading.value = false
  }
}

// 处理注册 - 修正：适配后端接口格式
const handleRegister = async () => {
  if (!validateRegisterForm()) return

  if (!captchaUuid.value) {
    showError('请先获取验证码')
    return
  }

  registerLoading.value = true
  
  try {
    const response = await authStore.register({
      phone: registerForm.phone,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword,
      captchaCode: registerForm.captchaCode,
      captchaUuid: captchaUuid.value,
      agreed_to_terms: registerForm.agreedToTerms,
      inviteCode: registerForm.inviteCode || ''  // 修复：后端期望的字段名是inviteCode
    })

    // 处理注册成功的情况
    if (response.data && response.data.autoLoginDisabled) {
      // 新的处理方式：注册成功但需要手动登录
      showSuccess('注册成功！请使用刚注册的账号登录')
      
      // 清除保存的邀请码
      clearSavedInviteCode()
      console.log('✅ 注册成功，已清除本地保存的邀请码')
      
      // 显示体验金奖励
      if (response.data.experience) {
        setTimeout(() => {
          showSuccess(`恭喜获得体验金 ${response.data.experience}₫`)
        }, 1000)
      }
      
      // 自动切换到登录标签页并填充账号和密码
      setTimeout(() => {
        activeTab.value = 'login'
        loginForm.phone = response.data.loginHelp?.phone || registerForm.phone
        loginForm.password = response.data.loginHelp?.password || registerForm.password  // 自动填充密码
        loginForm.captchaCode = ''  // 清空验证码输入框，需要用户输入新验证码
        
        // 如果有新验证码，更新验证码
        if (response.data.loginHelp?.newCaptcha) {
          captchaImage.value = response.data.loginHelp.newCaptcha.captchaBase64Image
          captchaUuid.value = response.data.loginHelp.newCaptcha.captchaUuid
        }
        
        showInfo('请输入验证码完成登录')
        console.log('📝 已自动填充登录信息 - 手机号:', loginForm.phone, '密码已填充，请输入新验证码')
      }, 2000)
      
    } else if (response.data && response.data.token) {
      // 老的自动登录逻辑（保留兼容性）
      showSuccess('注册成功！已自动为您登录')
      
      clearSavedInviteCode()
      
      if (response.data.experience) {
        setTimeout(() => {
          showSuccess(`恭喜获得体验金 ${response.data.experience}₫`)
        }, 1000)
      }
      
      setTimeout(() => {
        const redirectPath = route.query.redirect || '/home'
        router.push({
          path: redirectPath,
          query: { from: 'register' }
        })
      }, 1500)
      
    } else {
      // 普通注册成功
      showSuccess('注册成功')
      clearSavedInviteCode()
      
      if (response.data && response.data.experience) {
        setTimeout(() => {
          showSuccess(`恭喜获得体验金 ${response.data.experience}₫`)
        }, 1000)
      }
    }
    
  } catch (error) {
    console.error('注册失败:', error)
    showError(error.message || '注册失败，请稍后重试')
    // 注册失败后重新获取验证码
    loadCaptcha()
  } finally {
    registerLoading.value = false
  }
}

// 处理第三方登录
const handleOAuthLogin = async (provider) => {
  oauthLoading.value = true
  
  try {
    // 这里需要根据实际的第三方登录SDK来实现
    showInfo(`${provider} 登录功能开发中`)
  } catch (error) {
    console.error('第三方登录失败:', error)
    showError(error.message || '登录失败，请稍后重试')
  } finally {
    oauthLoading.value = false
  }
}

// 发送忘记密码验证码
const sendForgotPasswordCode = async () => {
  if (!forgotPasswordForm.phone) {
    showError('请输入手机号')
    return
  }

  try {
    // 这里需要实现发送验证码的API
    showInfo('验证码发送功能开发中')
    
    // 开始倒计时
    forgotPasswordCountdown.value = 60
    forgotPasswordTimer = setInterval(() => {
      forgotPasswordCountdown.value--
      if (forgotPasswordCountdown.value <= 0) {
        clearInterval(forgotPasswordTimer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    showError(error.message || '发送验证码失败，请稍后重试')
  }
}

// 处理忘记密码
const handleForgotPassword = async () => {
  // 这里需要实现忘记密码的逻辑
  showInfo('忘记密码功能开发中')
}

// 显示用户协议
const showUserAgreement = () => {
  showInfo('用户协议内容')
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  showInfo('隐私政策内容')
}

// 生命周期
onMounted(() => {
  // 自动获取验证码
  loadCaptcha()
  
  // 设置最后一次登录成功的账号作为默认值
  const lastLoginAccount = authStore.getLastLoginAccount()
  if (lastLoginAccount) {
    loginForm.phone = lastLoginAccount
    console.log('📱 设置最后登录账号为默认值:', lastLoginAccount)
  }
  
  // 检查URL参数中的邀请码
  if (route.query.invite_code) {
    registerForm.inviteCode = route.query.invite_code
    showInviteCode.value = true
    activeTab.value = 'register'
    console.log('🔗 从URL参数获取邀请码:', route.query.invite_code)
  } else {
    // 如果URL中没有邀请码，尝试从本地存储获取
    const savedInviteCode = getSavedInviteCode()
    if (savedInviteCode) {
      registerForm.inviteCode = savedInviteCode
      showInviteCode.value = true
      console.log('💾 从本地存储获取邀请码:', savedInviteCode)
    }
  }
  
  if (route.query.tab === 'register') {
    activeTab.value = 'register'
  }
})

onUnmounted(() => {
  if (forgotPasswordTimer) {
    clearInterval(forgotPasswordTimer)
  }
})
</script>

<style scoped>
/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.login-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>