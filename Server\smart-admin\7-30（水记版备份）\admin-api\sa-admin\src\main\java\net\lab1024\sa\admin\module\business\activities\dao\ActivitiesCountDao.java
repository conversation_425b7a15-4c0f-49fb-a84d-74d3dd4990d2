package net.lab1024.sa.admin.module.business.activities.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesCountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

@Mapper
public interface ActivitiesCountDao extends BaseMapper<ActivitiesCountEntity> {
    @Update("UPDATE t_activities_count SET novice_count=novice_count+1, novice=novice+#{amount} WHERE user_id=#{userId}")
    void incrementNovice(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE t_activities_count SET low_price_count=low_price_count+1, low_price=low_price+#{amount} WHERE user_id=#{userId}")
    void incrementLowPrice(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE t_activities_count SET high_price_count=high_price_count+1, high_price=high_price+#{amount} WHERE user_id=#{userId}")
    void incrementHighPrice(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

}
