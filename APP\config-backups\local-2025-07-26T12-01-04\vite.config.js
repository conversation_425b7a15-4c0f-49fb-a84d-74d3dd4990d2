import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 将 iconify-icon 视为自定义元素
          isCustomElement: (tag) => tag === 'iconify-icon'
        }
      }
    })
  ],
  
  // 解决HTML内联CSS构建问题
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    open: false, // 不自动打开浏览器，由启动脚本控制
    host: true, // 允许外部访问
    cors: true,
    proxy: {
      // 图片资源代理
      '/upload': {
        target: 'http://localhost:8686',
        changeOrigin: true,
        secure: false,
        timeout: 10000,
        proxyTimeout: 10000,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🖼️ 代理图片请求到本地后端:', req.method, req.url, '-> https://pp.oripicks.com' + req.url);
            
            proxyReq.setHeader('Host', 'pp.oripicks.com');
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            proxyReq.setTimeout(10000);
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ 图片响应:', proxyRes.statusCode, req.url);
            res.setHeader('Access-Control-Allow-Origin', '*');
          });
          
          proxy.on('error', (err, req, res) => {
            console.error('❌ 图片代理错误:', err.message);
            console.error('请求URL:', req.url);
            
            if (!res.headersSent) {
              res.writeHead(404, { 'Content-Type': 'text/plain' });
              res.end('Image not found');
            }
          });
        }
      },
      
      // 图片文件代理 - 处理/file路径
      '/file': {
        target: 'http://localhost:8686',
        changeOrigin: true,
        secure: false,
        timeout: 10000,
        proxyTimeout: 10000,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🖼️ 代理文件请求:', req.method, req.url, '-> https://pp.oripicks.com' + req.url);
            
            proxyReq.setHeader('Host', 'pp.oripicks.com');
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            proxyReq.setTimeout(10000);
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ 文件响应:', proxyRes.statusCode, req.url);
            res.setHeader('Access-Control-Allow-Origin', '*');
          });
          
          proxy.on('error', (err, req, res) => {
            console.error('❌ 文件代理错误:', err.message);
            if (!res.headersSent) {
              res.writeHead(404, {
                'Content-Type': 'text/plain',
                'Access-Control-Allow-Origin': '*'
              });
              res.end('File not found');
            }
          });
        }
      },
      
      // API请求代理
      '/api': {
        target: 'http://localhost:8686',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '/app'),
        timeout: 10000,
        proxyTimeout: 10000,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            const rewrittenUrl = req.url.replace(/^\/api/, '/app');
            console.log('🔄 代理请求:', req.method, req.url, '-> http://localhost:8686' + rewrittenUrl);
            
            // 本地后端请求头配置
            proxyReq.setHeader('Host', 'pp.oripicks.com');
            proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            proxyReq.setHeader('Accept', 'application/json, text/plain, */*');
            proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
            proxyReq.setHeader('Connection', 'close');
            
            proxyReq.setTimeout(10000);
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ 远程后端响应:', proxyRes.statusCode, req.url);
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
          });
          
          proxy.on('error', (err, req, res) => {
            console.error('❌ 本地后端连接错误:', err.message);
            console.error('请求URL:', req.url);
            console.error('确保本地后端服务正在 http://localhost:8686 运行');
            
            if (!res.headersSent) {
              res.writeHead(502, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              });
              res.end(JSON.stringify({
                error: '无法连接到本地API服务器',
                message: `连接 https://pp.oripicks.com 失败: ${err.message}`,
                code: 'LOCAL_CONNECTION_FAILED',
                solution: '请确保本地后端服务正在运行 (D:/start-tgw-server.bat)'
              }));
            }
          });
        }
      }
    }
  },
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@api': resolve(__dirname, 'src/api'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@store': resolve(__dirname, 'src/store'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // CSS配置
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer
      ]
    }
  },
  
  // 环境变量配置
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    // 🚫 Mock功能已完全禁用
    'import.meta.env.VITE_USE_MOCK': JSON.stringify('false') // 强制禁用Mock
    // 移除 VITE_API_BASE_URL 的强制定义，允许环境变量动态设置
  }
}) 
