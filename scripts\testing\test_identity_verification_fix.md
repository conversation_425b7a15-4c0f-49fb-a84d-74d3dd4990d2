# 实名认证状态修复测试计划

## 问题描述
- 用户实名认证在后台管理系统中已审核通过，但前端"我的"页面仍显示"未认证"
- 根本原因：t_identity_verification 表状态为 approved，但 t_employee 表的 has_real 字段为 0

## 修复方案

### 1. 历史数据修复
- ✅ 执行 SQL 脚本修复已通过认证但 has_real=0 的历史数据
- ✅ 用户73的数据已修复：has_real 从 0 更新为 1

### 2. 前端API增强
- ✅ 在 ProfileService.wallet() 方法中添加 hasReal 字段返回
- ✅ 在 EmployeeService 中添加 getHasRealByUserId() 方法

### 3. 后端审核流程修复
- ✅ 在 IdentityVerificationServiceImpl.auditVerification() 方法中添加自动更新逻辑
- ✅ 审核通过时：has_real = true
- ✅ 审核拒绝时：has_real = false
- ✅ 在 EmployeeService 中添加 updateHasRealStatus() 方法

## 测试步骤

### 测试1：验证历史数据修复
```sql
-- 查询所有已通过认证的用户状态
SELECT 
    e.employee_id,
    e.actual_name,
    e.has_real,
    iv.status,
    iv.audit_time
FROM t_employee e
INNER JOIN t_identity_verification iv ON e.employee_id = iv.user_id
WHERE iv.status = 'approved' AND iv.deleted_flag = 0;
```

### 测试2：验证前端API
```bash
# 获取用户钱包信息，检查是否包含 hasReal 字段
curl -H "Authorization: Bearer [TOKEN]" "http://localhost:8686/app/v1/wallet"
```
预期结果：返回的 balance 对象应包含 `"hasReal": true`

### 测试3：验证新认证流程
1. 创建新用户并提交实名认证申请
2. 在后台管理系统审核通过
3. 检查数据库 t_employee 表的 has_real 字段是否自动更新为 1
4. 在前端"我的"页面验证是否显示"已完成实名认证"

### 测试4：验证认证拒绝流程
1. 提交实名认证申请
2. 在后台管理系统审核拒绝
3. 检查数据库 t_employee 表的 has_real 字段是否为 0
4. 在前端验证是否显示"未完成实名认证，提现需要先认证"

## 预期结果
- ✅ 用户73在前端"我的"页面应显示"已完成实名认证"
- ✅ /app/v1/wallet API 返回 hasReal: true
- ✅ 新的实名认证审核流程会自动同步 has_real 状态
- ✅ 数据一致性问题彻底解决

## 部署检查清单
- ✅ 数据库脚本已执行
- ✅ 后端代码已修改
- ✅ 代码编译通过
- [ ] 重启后端服务
- [ ] 前端测试验证