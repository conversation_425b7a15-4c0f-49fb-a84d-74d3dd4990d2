# 真实API模式使用说明

## 🔧 模式切换

### 当前状态
✅ **已切换到真实API模式**

### 配置说明
- `VITE_USE_MOCK=false` - 使用真实API
- `VITE_API_BASE_URL=http://pp.kongzhongkouan.com` - 真实API服务器
- API路径: `/app/v1/` - 后端实际路径

## 🚀 启动方式

### 方法1：直接启动
```bash
cd APP
npm run dev
```

### 方法2：使用快速脚本
- **真实API模式**: 双击 `switch-to-real-api.bat`
- **Mock模式**: 双击 `switch-to-mock-api.bat`

## 📋 真实API接口测试

### 1. 验证码接口
```bash
GET http://pp.kongzhongkouan.com/app/v1/captcha
```

### 2. 登录接口
```bash
POST http://pp.kongzhongkouan.com/app/v1/auth
Content-Type: application/json

{
  "type": "login",
  "phone": "13833883388",
  "password": "123456",
  "captchaCode": "验证码",
  "captchaUuid": "验证码UUID"
}
```

### 3. 注册接口
```bash
POST http://pp.kongzhongkouan.com/app/v1/auth
Content-Type: application/json

{
  "type": "register",
  "phone": "13833883389",
  "password": "123456",
  "confirmPassword": "123456",
  "captchaCode": "验证码",
  "captchaUuid": "验证码UUID",
  "agreed_to_terms": true,
  "invite_code": ""
}
```

## 🔍 调试说明

### 网络请求监控
开发服务器会显示代理请求日志：
```
代理请求: POST /app/v1/auth
代理响应: 200 /app/v1/auth
```

### 浏览器调试
1. 打开开发者工具 (F12)
2. 查看 Network 标签页
3. 观察API请求和响应

### 控制台日志
应用会显示：
```
🔧 StandardApiAdapter initialized: {
  baseURL: '/app/v1', 
  isMock: false, 
  useProxy: true
}
```

## ⚠️ 注意事项

### 1. CORS处理
真实API请求通过Vite代理处理，避免跨域问题：
- 前端请求: `http://localhost:3000/app/v1/auth`
- 代理转发: `http://pp.kongzhongkouan.com/app/v1/auth`

### 2. 网络要求
- 确保能访问 `http://pp.kongzhongkouan.com`
- 检查防火墙和网络设置

### 3. 错误处理
- 网络错误会显示具体错误信息
- 查看控制台获取详细日志

## 🔄 模式对比

| 功能 | Mock模式 | 真实API模式 |
|------|----------|-------------|
| 数据来源 | 本地Mock数据 | 真实后端服务器 |
| 网络要求 | 无 | 需要网络连接 |
| 响应速度 | 快 | 取决于网络 |
| 数据一致性 | 模拟数据 | 真实数据 |
| 调试便利性 | 高 | 中等 |

## 🛠️ 故障排除

### 问题1: API请求404
**原因**: 接口路径不正确
**解决**: 检查后端接口文档，确认路径

### 问题2: 网络连接失败
**原因**: 无法访问后端服务器
**解决**: 
1. 检查网络连接
2. 确认服务器地址正确
3. 检查防火墙设置

### 问题3: CORS错误
**原因**: 跨域请求被阻止
**解决**: 确保Vite代理配置正确（已配置）

### 问题4: 验证码无法获取
**原因**: 验证码接口不可用
**解决**: 
1. 检查接口地址
2. 确认后端服务正常
3. 查看网络请求日志

## 📝 测试建议

1. **接口连通性测试**:
   - 先测试验证码接口
   - 再测试登录注册功能

2. **功能完整性测试**:
   - 验证码获取和刷新
   - 登录流程完整性
   - 注册流程完整性
   - 错误处理机制

3. **性能测试**:
   - 接口响应时间
   - 网络超时处理
   - 重试机制

---

**配置完成时间**: 2024-12-15
**文档版本**: v1.0
**维护人员**: AI Assistant 