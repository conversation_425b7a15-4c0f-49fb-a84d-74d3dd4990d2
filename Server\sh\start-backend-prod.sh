#!/bin/bash

echo "🏭 启动生产环境后端服务..."

PROJECT_ROOT="/mnt/d/Dev/团购网"
LOG_DIR="$PROJECT_ROOT/logs/prod"
mkdir -p "$LOG_DIR"

# 检查服务状态
echo "📋 检查服务状态..."
if ! systemctl is-active --quiet mysql; then
    echo "❌ MySQL服务未运行，正在启动..."
    sudo systemctl start mysql
fi

if ! systemctl is-active --quiet redis-server; then
    echo "❌ Redis服务未运行，正在启动..."
    sudo systemctl start redis-server
fi

# 进入后端目录
cd "$PROJECT_ROOT/Server/smart-admin/admin-api/sa-admin"

# 检查JAR文件是否存在
if [ ! -f "target/tgw-pp.jar" ]; then
    echo "📦 编译生产版本..."
    cd ../..
    mvn clean package -Pprod -DskipTests
    cd admin-api/sa-admin
fi

# 检查是否已有运行的实例
PID=$(ps -ef | grep tgw-pp.jar | grep -v grep | awk '{ print $2 }')
if [ ! -z "$PID" ]; then
    echo "⚠️  发现已运行的后端服务 (PID: $PID)，正在停止..."
    kill -9 $PID
    sleep 3
fi

# 启动生产环境服务
echo "🌟 启动生产环境后端服务..."
echo "🔧 使用配置: prod"
nohup java -jar target/tgw-pp.jar --spring.profiles.active=prod > "$LOG_DIR/backend-prod.log" 2>&1 &
BACKEND_PID=$!

echo "✅ 生产环境后端服务已启动 (PID: $BACKEND_PID)"
echo $BACKEND_PID > "$LOG_DIR/backend-prod.pid"

echo ""
echo "📍 访问地址："
echo "   后端API:  http://localhost:8686"
echo "   API文档:  http://localhost:8686/doc.html"
echo "   健康检查: http://localhost:8686/actuator/health"
echo ""
echo "📝 生产日志: $LOG_DIR/backend-prod.log"
echo ""
echo "💡 查看实时日志: tail -f $LOG_DIR/backend-prod.log"
echo "🛑 停止服务请运行: ./stop-backend-prod.sh"