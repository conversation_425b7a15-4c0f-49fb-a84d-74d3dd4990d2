import { computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showError, showSuccess, showLoading, hideLoading } from '@/utils/message'

/**
 * 全局登录状态管理组合式函数
 * 为所有页面提供统一的登录状态访问和操作
 */
export function useAuth() {
  const authStore = useAuthStore()
  const router = useRouter()

  // 登录状态相关计算属性
  const isLoggedIn = computed(() => authStore.isLoggedIn)
  const user = computed(() => authStore.user)
  const token = computed(() => authStore.token)
  const userInfo = computed(() => ({
    id: authStore.user?.id,
    username: authStore.user?.username || authStore.user?.loginName,
    nickname: authStore.user?.nickname || authStore.user?.actualName,
    phone: authStore.user?.phone,
    avatar: authStore.user?.avatar || '',
    walletBalance: authStore.walletBalance,
    points: authStore.userPoints,
    level: authStore.userLevel,
    role: authStore.userRole,
    teamInfo: authStore.teamInfo,
    stats: authStore.userStats
  }))

  // 用户权限检查
  const hasPermission = (permission) => {
    if (!isLoggedIn.value) return false
    const userRole = authStore.userRole
    // 根据用户角色和权限进行判断
    return true // 简化实现，可根据实际需求扩展
  }

  // 检查是否需要登录
  const requireAuth = (showMessage = true) => {
    if (!isLoggedIn.value) {
      if (showMessage) {
        showError('请先登录')
      }
      return false
    }
    return true
  }

  // 登录函数
  const login = async (loginData) => {
    try {
      showLoading('登录中...')
      const result = await authStore.login(loginData)
      hideLoading()
      
      if (result.code === 200) {
        showSuccess('登录成功')
        console.log('🔑 用户登录成功:', authStore.user)
        return { success: true, data: result.data }
      } else {
        showError(result.message || '登录失败')
        return { success: false, error: result.message }
      }
    } catch (error) {
      hideLoading()
      console.error('❌ 登录失败:', error)
      showError(error.message || '登录失败，请重试')
      return { success: false, error: error.message }
    }
  }

  // 注册函数
  const register = async (registerData) => {
    try {
      showLoading('注册中...')
      const result = await authStore.register(registerData)
      hideLoading()
      
      if (result.code === 200) {
        showSuccess('注册成功')
        console.log('📝 用户注册成功:', result.data)
        return { success: true, data: result.data }
      } else {
        showError(result.message || '注册失败')
        return { success: false, error: result.message }
      }
    } catch (error) {
      hideLoading()
      console.error('❌ 注册失败:', error)
      showError(error.message || '注册失败，请重试')
      return { success: false, error: error.message }
    }
  }

  // 登出函数
  const logout = async (showMessage = true) => {
    try {
      await authStore.logout()
      if (showMessage) {
        showSuccess('已退出登录')
      }
      console.log('🚪 用户已退出登录')
      
      // 跳转到登录页面
      await nextTick()
      router.push('/login')
      return { success: true }
    } catch (error) {
      console.error('❌ 退出登录失败:', error)
      if (showMessage) {
        showError('退出登录失败')
      }
      return { success: false, error: error.message }
    }
  }

  // 刷新用户信息
  const refreshUserInfo = async () => {
    try {
      const userInfo = await authStore.getCurrentUser()
      console.log('🔄 用户信息已刷新:', userInfo)
      return { success: true, data: userInfo }
    } catch (error) {
      console.error('❌ 刷新用户信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 检查登录状态
  const checkAuthStatus = async () => {
    try {
      const isValid = await authStore.checkAuthStatus()
      if (!isValid && isLoggedIn.value) {
        // 如果token失效，自动退出登录
        await logout(false)
      }
      return isValid
    } catch (error) {
      console.error('❌ 检查登录状态失败:', error)
      return false
    }
  }

  // 更新用户信息
  const updateUserInfo = (updates) => {
    authStore.updateUser(updates)
    console.log('👤 用户信息已更新:', updates)
  }

  // 更新钱包余额
  const updateWallet = (balance) => {
    authStore.updateWalletBalance(balance)
    console.log('💰 钱包余额已更新:', balance)
  }

  // 更新积分
  const updatePoints = (points) => {
    authStore.updatePoints(points)
    console.log('⭐ 积分已更新:', points)
  }

  // 导航守卫：需要登录的页面
  const navigateWithAuth = async (path, options = {}) => {
    if (!requireAuth()) {
      router.push({
        path: '/login',
        query: { redirect: path }
      })
      return false
    }
    
    router.push(path, options)
    return true
  }

  // 监听登录状态变化
  const watchAuthStatus = (callback) => {
    return watch(
      () => authStore.isLoggedIn,
      (newStatus, oldStatus) => {
        console.log('🔄 登录状态变化:', { oldStatus, newStatus })
        if (callback) {
          callback(newStatus, oldStatus)
        }
      },
      { immediate: true }
    )
  }

  return {
    // 状态
    isLoggedIn,
    user,
    token,
    userInfo,
    
    // 权限检查
    hasPermission,
    requireAuth,
    
    // 认证操作
    login,
    register,
    logout,
    refreshUserInfo,
    checkAuthStatus,
    
    // 用户信息更新
    updateUserInfo,
    updateWallet,
    updatePoints,
    
    // 导航相关
    navigateWithAuth,
    
    // 监听器
    watchAuthStatus
  }
}

/**
 * 全局登录状态初始化
 * 在应用启动时调用，检查登录状态
 */
export async function initAuth() {
  const authStore = useAuthStore()
  
  console.log('🔍 初始化登录状态检查...')
  
  // 检查本地存储的token和用户信息
  const token = localStorage.getItem('token')
  const userInfo = localStorage.getItem('user_info')
  
  if (token && userInfo) {
    try {
      // 验证token有效性
      const isValid = await authStore.checkAuthStatus()
      if (isValid) {
        console.log('✅ 登录状态有效，用户已登录')
        return true
      } else {
        console.log('❌ 登录状态失效，清除本地数据')
        authStore.logout()
        return false
      }
    } catch (error) {
      console.error('❌ 登录状态检查失败:', error)
      authStore.logout()
      return false
    }
  } else {
    console.log('ℹ️ 未找到登录信息，用户未登录')
    return false
  }
}

/**
 * 路由守卫：自动检查登录状态
 */
export function createAuthGuard() {
  return async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查是否需要登录
    if (to.meta.requiresAuth) {
      console.log('🔍 检查页面登录状态:', to.path)
      console.log('🔍 当前token:', authStore.token ? '存在' : '不存在')
      console.log('🔍 登录状态:', authStore.isLoggedIn)
      
      // 1. 基本检查：token 和 store 状态
      console.log('🔍 [AuthGuard] 详细认证状态:', {
        hasToken: !!authStore.token,
        token: authStore.token ? authStore.token.substring(0, 10) + '...' : 'null',
        hasUser: !!authStore.user,
        isLoggedIn: authStore.isLoggedIn,
        path: to.path
      })
      
      if (!authStore.token || !authStore.isLoggedIn) {
        // 如果是从登录页面跳转过来的，给更多时间让状态更新
        if (from.path === '/login' && to.query?.from === 'login') {
          console.log('⏳ 检测到登录跳转，等待状态同步...')
          
          // 等待状态更新，最多重试3次
          for (let i = 0; i < 3; i++) {
            await new Promise(resolve => setTimeout(resolve, 300))
            
            if (authStore.token && authStore.isLoggedIn) {
              console.log('✅ 状态同步成功，继续导航')
              break
            }
            
            console.log(`⏳ 重试状态检查 ${i + 1}/3`)
          }
          
          // 最终检查
          if (!authStore.token || !authStore.isLoggedIn) {
            console.log('🔒 状态同步失败，重定向到登录页面')
            next({
              path: '/login',
              query: { redirect: to.fullPath }
            })
            return
          }
        } else {
          console.log('🔒 未登录，跳转到登录页面')
          console.log('🚀 [AuthGuard] 强制跳转:', {
            from: to.fullPath,
            to: '/login'
          })
          
          // 立即跳转，不等待
          next({
            path: '/login',
            query: { redirect: to.fullPath },
            replace: true
          })
          return
        }
      }
      
      // 2. 验证 token 有效性
      try {
        const isValid = await authStore.checkAuthStatus()
        
        if (!isValid) {
          console.log('🔒 登录已过期，跳转到登录页面')
          showError('登录已过期，请重新登录')
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
        
        console.log('✅ 登录状态验证通过')
      } catch (error) {
        console.error('❌ 登录状态验证失败:', error)
        showError('登录验证失败，请重新登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    next()
  }
}

/**
 * 全局错误处理：自动处理认证错误
 */
export function handleAuthError(error) {
  if (error.message?.includes('401') || 
      error.message?.includes('登录') || 
      error.message?.includes('token')) {
    const authStore = useAuthStore()
    authStore.logout()
    showError('登录已过期，请重新登录')
    return true
  }
  return false
}

/**
 * 页面级别的登录状态检查装饰器
 */
export function withAuth(component) {
  return {
    ...component,
    async beforeRouteEnter(to, from, next) {
      const authStore = useAuthStore()
      
      if (to.meta.requiresAuth) {
        const isValid = await authStore.checkAuthStatus()
        if (!isValid) {
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }
      
      next()
    }
  }
} 