# 团购网技术架构文档

## 1. 整体架构概述

### 1.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动端APP     │    │   管理后台      │    │   第三方服务    │
│   (Vue 3)       │    │   (Vue 3)       │    │   (支付/短信)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │   (Nginx)       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端API服务   │
                    │   (Spring Boot) │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据库存储    │    │   缓存层        │    │   文件存储      │
│   (MySQL)       │    │   (Redis)       │    │   (OSS/本地)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 技术栈选型

#### 前端技术栈
| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 框架 | Vue 3 | 3.4.27 | 响应式前端框架 |
| 构建工具 | Vite | 5.2.12 | 快速构建工具 |
| 状态管理 | Pinia | 2.1.7 | Vue 3状态管理 |
| 路由 | Vue Router | 4.3.2 | 前端路由管理 |
| UI组件库 | Vant (移动端) | 4.x | 移动端UI组件 |
| UI组件库 | Ant Design Vue (管理端) | 4.2.5 | PC端UI组件 |
| HTTP客户端 | Axios | 1.8.2 | HTTP请求库 |
| CSS预处理 | SCSS/Less | - | 样式预处理 |
| 工具库 | Lodash, Day.js | - | 工具函数库 |

#### 后端技术栈
| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 框架 | Spring Boot | 3.3.1 | Java Web框架 |
| JDK版本 | OpenJDK | 17 | Java运行环境 |
| 认证框架 | SA-Token | 1.38.0 | 权限认证框架 |
| ORM框架 | MyBatis Plus | 3.5.5 | 数据访问层 |
| 数据库 | MySQL | 8.0 | 关系型数据库 |
| 缓存 | Redis | 7.0 | 内存数据库 |
| 连接池 | Druid | 1.2.20 | 数据库连接池 |
| JSON处理 | Jackson | 2.15.2 | JSON序列化 |
| 日志框架 | Logback | - | 日志处理 |
| 监控 | Spring Boot Actuator | - | 应用监控 |

#### 基础设施
| 组件 | 技术选型 | 说明 |
|------|----------|------|
| Web服务器 | Nginx | 反向代理和负载均衡 |
| 应用服务器 | Tomcat | 嵌入式Web容器 |
| 消息队列 | RabbitMQ | 异步消息处理 |
| 文件存储 | 阿里云OSS | 对象存储服务 |
| CDN | 阿里云CDN | 内容分发网络 |
| 监控 | Prometheus + Grafana | 系统监控 |
| 日志 | ELK Stack | 日志收集分析 |

### 1.3 架构特点

#### 微服务化设计
- **前后端分离**: 前端和后端独立开发、部署
- **服务拆分**: 按业务模块拆分微服务
- **API优先**: 基于RESTful API的服务间通信
- **数据隔离**: 每个服务拥有独立的数据存储

#### 高可用性
- **负载均衡**: Nginx实现请求分发
- **服务冗余**: 多实例部署避免单点故障
- **数据备份**: 主从数据库和定期备份
- **故障转移**: 自动故障检测和切换

#### 可扩展性
- **水平扩展**: 支持服务实例的水平扩展
- **垂直扩展**: 支持硬件资源的垂直扩展
- **缓存策略**: 多级缓存提升性能
- **异步处理**: 消息队列处理高并发请求

## 2. 移动端APP架构

### 2.1 技术架构
```
┌─────────────────────────────────────────────────────────┐
│                    Vue 3 + Composition API              │
├─────────────────────────────────────────────────────────┤
│  状态管理(Pinia) │ 路由管理(Vue Router) │ UI组件(Vant)  │
├─────────────────────────────────────────────────────────┤
│  网络层(Axios)   │ 工具库(Lodash)      │ 样式(SCSS)    │
├─────────────────────────────────────────────────────────┤
│  构建工具(Vite)  │ 开发工具(ESLint)    │ 打包优化      │
└─────────────────────────────────────────────────────────┘
```

### 2.2 目录结构
```
src/
├── api/                    # API接口定义
│   ├── index.js           # 统一导出
│   ├── standardAdapter.js # API适配器
│   └── real/              # 真实API实现
├── components/            # 公共组件
│   ├── common/           # 通用组件
│   └── popups/           # 弹窗组件
├── views/                # 页面组件
│   ├── home/             # 首页相关
│   ├── product/          # 商品相关
│   ├── order/            # 订单相关
│   ├── user/             # 用户中心
│   └── group/            # 团购相关
├── store/                # 状态管理
│   ├── auth.js           # 认证状态
│   └── modules/          # 功能模块状态
├── router/               # 路由配置
├── utils/                # 工具函数
├── assets/               # 静态资源
└── config/               # 配置文件
```

### 2.3 核心特性

#### 响应式设计
- **移动优先**: 针对移动端优化的UI设计
- **适配多屏**: 支持不同尺寸的移动设备
- **触摸友好**: 按钮和交互区域适合触摸操作
- **性能优化**: 懒加载和虚拟滚动优化

#### 状态管理
```javascript
// Pinia状态管理示例
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isLoggedIn: false
  }),
  
  actions: {
    async login(credentials) {
      const response = await authApi.login(credentials)
      this.user = response.data.user
      this.token = response.data.token
      this.isLoggedIn = true
      
      // 持久化存储
      localStorage.setItem('token', this.token)
    },
    
    logout() {
      this.user = null
      this.token = null
      this.isLoggedIn = false
      localStorage.removeItem('token')
    }
  }
})
```

#### 路由管理
```javascript
// 路由配置示例
const routes = [
  {
    path: '/home',
    name: 'HomePage',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '首页', requiresAuth: false }
  },
  {
    path: '/user/profile',
    name: 'ProfilePage',
    component: () => import('@/views/user/ProfilePage.vue'),
    meta: { title: '个人中心', requiresAuth: true }
  }
]

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isLoggedIn()) {
    next('/login')
  } else {
    next()
  }
})
```

#### API集成
```javascript
// API适配器模式
class StandardAdapter {
  constructor(apiImpl) {
    this.api = apiImpl
  }
  
  async get(url, params) {
    try {
      const response = await this.api.get(url, { params })
      return this.handleResponse(response)
    } catch (error) {
      return this.handleError(error)
    }
  }
  
  handleResponse(response) {
    return {
      success: true,
      data: response.data,
      message: response.message
    }
  }
  
  handleError(error) {
    return {
      success: false,
      error: error.message,
      code: error.code
    }
  }
}
```

### 2.4 性能优化

#### 代码分割
```javascript
// 路由级别的代码分割
const routes = [
  {
    path: '/product/:id',
    component: () => import(
      /* webpackChunkName: "product" */ 
      '@/views/product/ProductDetailPage.vue'
    )
  }
]
```

#### 资源优化
- **图片懒加载**: 使用Intersection Observer API
- **组件懒加载**: 动态导入非关键组件
- **资源压缩**: Gzip压缩和图片优化
- **缓存策略**: HTTP缓存和Service Worker

#### 渲染优化
- **虚拟滚动**: 长列表使用虚拟滚动
- **防抖节流**: 用户输入和滚动事件优化
- **内存管理**: 组件销毁时清理定时器和事件监听

## 3. 管理后台架构

### 3.1 技术架构
```
┌─────────────────────────────────────────────────────────┐
│                Vue 3 + Ant Design Vue                   │
├─────────────────────────────────────────────────────────┤
│  权限管理(RBAC) │ 动态路由      │ 富文本编辑器           │
├─────────────────────────────────────────────────────────┤
│  表格组件       │ 表单验证      │ 图表组件(ECharts)      │
├─────────────────────────────────────────────────────────┤
│  主题定制       │ 国际化(i18n)  │ 响应式布局             │
└─────────────────────────────────────────────────────────┘
```

### 2.2 权限系统设计

#### RBAC权限模型
```
用户(User) → 角色(Role) → 权限(Permission) → 资源(Resource)
```

#### 权限控制实现
```javascript
// 权限指令
app.directive('privilege', {
  mounted(el, binding) {
    const { value } = binding
    const privileges = userStore.privileges
    
    if (!privileges.includes(value)) {
      el.style.display = 'none'
      // 或者 el.parentNode.removeChild(el)
    }
  }
})

// 使用示例
<a-button v-privilege="'goods:add'">新增商品</a-button>
```

#### 动态路由
```javascript
// 根据权限动态生成路由
const generateRoutes = (menuList) => {
  const routes = []
  
  menuList.forEach(menu => {
    if (menu.type === 'CATALOG') {
      // 目录类型，递归处理子菜单
      const children = generateRoutes(menu.children)
      if (children.length > 0) {
        routes.push({
          path: menu.path,
          component: Layout,
          children: children
        })
      }
    } else if (menu.type === 'MENU') {
      // 菜单类型，添加路由
      routes.push({
        path: menu.path,
        name: menu.name,
        component: () => import(`@/views${menu.component}`),
        meta: {
          title: menu.title,
          privileges: menu.privileges
        }
      })
    }
  })
  
  return routes
}
```

### 3.3 组件设计

#### 通用表格组件
```vue
<template>
  <div class="smart-table">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record, index }">
        <slot :name="column.dataIndex" :record="record" :index="index">
          {{ record[column.dataIndex] }}
        </slot>
      </template>
    </a-table>
  </div>
</template>

<script setup>
// 通用表格逻辑
const props = defineProps({
  api: Function,
  columns: Array,
  queryParams: Object
})

const { data, loading, pagination, refresh } = useTable(props.api)
</script>
```

#### 通用表单组件
```vue
<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
  >
    <slot :form-data="formData" />
    
    <div class="form-actions">
      <a-button @click="handleReset">重置</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </a-form>
</template>
```

### 3.4 业务模块设计

#### 商品管理模块
```
goods/
├── GoodsList.vue          # 商品列表页
├── GoodsForm.vue          # 商品表单组件
├── GoodsDetail.vue        # 商品详情页
├── components/            # 模块组件
│   ├── GoodsStatusTag.vue # 商品状态标签
│   ├── GoodsImageUpload.vue # 商品图片上传
│   └── GoodsSpecsEditor.vue # 商品规格编辑
└── api/                   # 模块API
    └── goods-api.js
```

#### 订单管理模块
```javascript
// 订单状态机
const OrderStateMachine = {
  PENDING_PAYMENT: {
    canTransitionTo: ['PAID', 'CANCELLED', 'EXPIRED'],
    actions: ['pay', 'cancel']
  },
  WON_PENDING_ACTION: {
    canTransitionTo: ['COMPLETED', 'CANCELLED'],
    actions: ['takeGoods', 'withdraw', 'payBalance']
  },
  COMPLETED: {
    canTransitionTo: [],
    actions: ['view']
  }
}
```

## 4. 后端API架构

### 4.1 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    Controller层                         │
│              (接收请求、参数验证、响应格式化)             │
├─────────────────────────────────────────────────────────┤
│                    Service层                            │
│              (业务逻辑处理、事务管理)                     │
├─────────────────────────────────────────────────────────┤
│                    Manager层                            │
│              (领域服务、复杂业务编排)                     │
├─────────────────────────────────────────────────────────┤
│                    DAO层                                │
│              (数据访问、SQL操作)                         │
├─────────────────────────────────────────────────────────┤
│                    Entity/VO/DTO                        │
│              (数据模型、值对象、传输对象)                 │
└─────────────────────────────────────────────────────────┘
```

### 4.2 目录结构
```
src/main/java/net/lab1024/sa/admin/
├── common/                # 公共模块
│   ├── config/           # 配置类
│   ├── constant/         # 常量定义
│   ├── exception/        # 异常处理
│   └── util/            # 工具类
├── framework/            # 框架模块
│   ├── security/        # 安全配置
│   ├── web/             # Web配置
│   └── database/        # 数据库配置
├── module/              # 业务模块
│   ├── business/        # 业务功能
│   │   ├── goods/       # 商品管理
│   │   ├── orders/      # 订单管理
│   │   ├── wallets/     # 钱包管理
│   │   └── activities/  # 活动管理
│   └── system/          # 系统功能
│       ├── employee/    # 员工管理
│       ├── role/        # 角色管理
│       └── menu/        # 菜单管理
└── SmartAdminApplication.java # 启动类
```

### 4.3 分层设计详解

#### Controller层
```java
@RestController
@RequestMapping("/api/v1/goods")
@Validated
@Slf4j
public class GoodsController {
    
    @Autowired
    private GoodsService goodsService;
    
    @PostMapping("/list")
    @Operation(summary = "商品列表查询")
    public ResponseDTO<PageResult<GoodsVO>> queryPage(
            @RequestBody @Valid GoodsQueryForm queryForm) {
        
        PageResult<GoodsVO> pageResult = goodsService.queryPage(queryForm);
        return ResponseDTO.ok(pageResult);
    }
    
    @PostMapping("/add")
    @Operation(summary = "添加商品")
    @SaCheckPermission("goods:add")
    public ResponseDTO<String> add(
            @RequestBody @Valid GoodsAddForm addForm) {
        
        return goodsService.add(addForm);
    }
}
```

#### Service层
```java
@Service
@Slf4j
public class GoodsService {
    
    @Autowired
    private GoodsDao goodsDao;
    
    @Autowired
    private GoodsManager goodsManager;
    
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(GoodsAddForm addForm) {
        // 1. 参数验证
        if (goodsDao.existsByName(addForm.getName())) {
            return ResponseDTO.userErrorParam("商品名称已存在");
        }
        
        // 2. 数据转换
        GoodsEntity goodsEntity = SmartBeanUtil.copy(addForm, GoodsEntity.class);
        goodsEntity.setStatus(GoodsStatusEnum.DRAFT);
        goodsEntity.setCreateTime(LocalDateTime.now());
        
        // 3. 保存数据
        goodsDao.insert(goodsEntity);
        
        // 4. 处理规格信息
        if (CollectionUtils.isNotEmpty(addForm.getSkuList())) {
            goodsManager.saveSkuList(goodsEntity.getId(), addForm.getSkuList());
        }
        
        return ResponseDTO.ok();
    }
}
```

#### Manager层
```java
@Component
@Slf4j
public class GoodsManager {
    
    @Autowired
    private GoodsSkuDao goodsSkuDao;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 保存商品SKU信息
     */
    public void saveSkuList(Long goodsId, List<GoodsSkuAddForm> skuList) {
        List<GoodsSkuEntity> skuEntityList = skuList.stream()
                .map(sku -> {
                    GoodsSkuEntity entity = SmartBeanUtil.copy(sku, GoodsSkuEntity.class);
                    entity.setGoodsId(goodsId);
                    entity.setCreateTime(LocalDateTime.now());
                    return entity;
                })
                .collect(Collectors.toList());
        
        goodsSkuDao.insertBatch(skuEntityList);
        
        // 清除相关缓存
        clearGoodsCache(goodsId);
    }
    
    private void clearGoodsCache(Long goodsId) {
        String cacheKey = "goods:detail:" + goodsId;
        redisTemplate.delete(cacheKey);
    }
}
```

#### DAO层
```java
@Mapper
public interface GoodsDao extends BaseMapper<GoodsEntity> {
    
    /**
     * 分页查询商品
     */
    List<GoodsVO> queryPage(@Param("queryForm") GoodsQueryForm queryForm);
    
    /**
     * 根据名称查询是否存在
     */
    boolean existsByName(@Param("name") String name);
    
    /**
     * 批量更新商品状态
     */
    int batchUpdateStatus(@Param("idList") List<Long> idList, 
                         @Param("status") GoodsStatusEnum status);
}
```

### 4.4 核心组件设计

#### 认证授权(SA-Token)
```java
@Configuration
public class SaTokenConfig {
    
    /**
     * 注册Sa-Token全局过滤器
     */
    @Bean
    public SaServletFilter getSaServletFilter() {
        return new SaServletFilter()
                .addInclude("/**")
                .addExclude("/api/v1/auth/login", "/api/v1/auth/register")
                .setAuth(obj -> {
                    // 检查登录状态
                    SaRouter.match("/**", () -> StpUtil.checkLogin());
                })
                .setError(e -> {
                    return ResponseDTO.userErrorParam("认证失败：" + e.getMessage());
                });
    }
    
    /**
     * Sa-Token配置
     */
    @Bean
    @Primary
    public SaTokenConfig getSaTokenConfigPrimary() {
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName("Authorization");
        config.setTimeout(30 * 24 * 60 * 60); // 30天过期
        config.setActiveTimeout(-1);
        config.setIsConcurrent(false);
        config.setIsShare(false);
        config.setTokenStyle("uuid");
        return config;
    }
}
```

#### 异步处理
```java
@Component
@Slf4j
public class OrderDrawService {
    
    @Autowired
    private OrdersDao ordersDao;
    
    @Autowired
    private WalletService walletService;
    
    @Async("orderTaskExecutor")
    public CompletableFuture<Void> processOrderDraw(Long activityId) {
        try {
            // 1. 获取参与用户
            List<OrdersEntity> participants = ordersDao.getParticipants(activityId);
            
            // 2. 执行抽奖算法
            List<Long> winners = drawLotteryAlgorithm(participants);
            
            // 3. 更新订单状态
            ordersDao.batchUpdateStatus(winners, OrderStatusEnum.WON_PENDING_ACTION);
            ordersDao.batchUpdateStatus(getLoserIds(participants, winners), OrderStatusEnum.LOST);
            
            // 4. 处理资金
            processWinnerRewards(winners);
            processLoserRefunds(getLoserIds(participants, winners));
            
            log.info("活动{}抽奖完成，中奖人数：{}", activityId, winners.size());
            
        } catch (Exception e) {
            log.error("抽奖处理失败，活动ID：{}", activityId, e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
}
```

#### 缓存设计
```java
@Component
@Slf4j
public class GoodsCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private GoodsDao goodsDao;
    
    private static final String GOODS_DETAIL_KEY = "goods:detail:";
    private static final String GOODS_LIST_KEY = "goods:list:";
    private static final long CACHE_TIMEOUT = 30 * 60; // 30分钟
    
    @Cacheable(value = "goods", key = "#goodsId")
    public GoodsDetailVO getGoodsDetail(Long goodsId) {
        String cacheKey = GOODS_DETAIL_KEY + goodsId;
        
        // 尝试从缓存获取
        GoodsDetailVO cached = (GoodsDetailVO) redisTemplate.opsForValue()
                .get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        GoodsDetailVO goodsDetail = goodsDao.getDetailById(goodsId);
        if (goodsDetail != null) {
            // 放入缓存
            redisTemplate.opsForValue().set(cacheKey, goodsDetail, 
                    CACHE_TIMEOUT, TimeUnit.SECONDS);
        }
        
        return goodsDetail;
    }
    
    @CacheEvict(value = "goods", key = "#goodsId")
    public void evictGoodsCache(Long goodsId) {
        String cacheKey = GOODS_DETAIL_KEY + goodsId;
        redisTemplate.delete(cacheKey);
    }
}
```

### 4.5 数据库设计

#### 连接池配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:123456}
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      filters: stat,wall
```

#### MyBatis Plus配置
```java
@Configuration
@MapperScan("net.lab1024.sa.admin.module.*.dao")
public class MybatisPlusConfig {
    
    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
    
    /**
     * 自动填充字段
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            }
            
            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            }
        };
    }
}
```

### 4.6 安全设计

#### 数据加密
```java
@Component
public class CryptoUtil {
    
    private static final String AES_KEY = "SmartAdmin2024Key";
    
    /**
     * AES加密
     */
    public static String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }
    
    /**
     * AES解密
     */
    public static String decrypt(String encryptedText) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decrypted);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
}
```

#### 参数验证
```java
@Validated
@RestController
public class OrdersController {
    
    @PostMapping("/create")
    public ResponseDTO<String> createOrder(
            @RequestBody @Valid OrderCreateForm form) {
        // 业务逻辑
    }
}

// 表单验证
@Data
public class OrderCreateForm {
    
    @NotNull(message = "商品ID不能为空")
    private Long goodsId;
    
    @NotNull(message = "SKU ID不能为空")
    private Long skuId;
    
    @NotNull(message = "收货地址ID不能为空")
    private Long addressId;
    
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    private BigDecimal amount;
    
    @Pattern(regexp = "^(BALANCE|EXPERIENCE|MIXED)$", message = "支付方式无效")
    private String paymentMethod;
}
```

### 4.7 监控和日志

#### 应用监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/smart-admin.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/smart-admin.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>
    
    <root level="INFO">
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

## 5. 数据库设计

### 5.1 数据库架构
```
┌─────────────────────────────────────────────────────────┐
│                    应用层                               │
├─────────────────────────────────────────────────────────┤
│                    连接池层                             │
│                   (Druid)                               │
├─────────────────────────────────────────────────────────┤
│   主库(Master)        │        从库(Slave)             │
│   (读写操作)          │        (只读操作)               │
├─────────────────────────────────────────────────────────┤
│                    MySQL 8.0                           │
│               (InnoDB存储引擎)                          │
└─────────────────────────────────────────────────────────┘
```

### 5.2 核心表设计

#### 用户相关表
```sql
-- 用户基本信息表
CREATE TABLE t_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(200) COMMENT '头像URL',
    invite_code VARCHAR(20) NOT NULL UNIQUE COMMENT '邀请码',
    inviter_id BIGINT COMMENT '邀请者ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1正常 0禁用',
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    KEY idx_phone (phone),
    KEY idx_invite_code (invite_code),
    KEY idx_inviter_id (inviter_id)
) COMMENT '用户表';

-- 用户实名认证表
CREATE TABLE t_user_identities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    id_number VARCHAR(20) NOT NULL COMMENT '身份证号',
    id_front_image VARCHAR(200) COMMENT '身份证正面照',
    id_back_image VARCHAR(200) COMMENT '身份证反面照',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '认证状态',
    audit_time DATETIME COMMENT '审核时间',
    audit_remark VARCHAR(200) COMMENT '审核备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    UNIQUE KEY uk_id_number (id_number)
) COMMENT '用户身份认证表';
```

#### 商品相关表
```sql
-- 商品表
CREATE TABLE t_goods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    main_image VARCHAR(200) COMMENT '主图片',
    images JSON COMMENT '商品图片列表',
    category_id BIGINT COMMENT '分类ID',
    market_price DECIMAL(10,2) COMMENT '市场价',
    group_price DECIMAL(10,2) COMMENT '团购价',
    win_price DECIMAL(10,2) COMMENT '中奖价',
    stock INT DEFAULT 0 COMMENT '库存数量',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    KEY idx_category_id (category_id),
    KEY idx_status (status),
    KEY idx_sort_order (sort_order)
) COMMENT '商品表';

-- 商品SKU表
CREATE TABLE t_goods_sku (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'SKU ID',
    goods_id BIGINT NOT NULL COMMENT '商品ID',
    sku_name VARCHAR(100) COMMENT 'SKU名称',
    sku_attrs JSON COMMENT 'SKU属性',
    price DECIMAL(10,2) COMMENT 'SKU价格',
    stock INT DEFAULT 0 COMMENT 'SKU库存',
    status TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    KEY idx_goods_id (goods_id)
) COMMENT '商品SKU表';
```

#### 订单相关表
```sql
-- 订单表
CREATE TABLE t_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_sn VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    activity_id BIGINT COMMENT '活动ID',
    goods_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT COMMENT 'SKU ID',
    status VARCHAR(20) NOT NULL COMMENT '订单状态',
    draw_result VARCHAR(20) COMMENT '抽奖结果：WIN中奖 LOSE未中奖',
    win_option VARCHAR(20) COMMENT '中奖选项',
    amount_paid DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    experience_paid DECIMAL(10,2) DEFAULT 0 COMMENT '体验金支付',
    subsidy_paid DECIMAL(10,2) DEFAULT 0 COMMENT '补贴金额',
    points_paid BIGINT DEFAULT 0 COMMENT '积分支付',
    shipping_address_id BIGINT COMMENT '收货地址ID',
    payment_method VARCHAR(20) COMMENT '支付方式',
    payment_time DATETIME COMMENT '支付时间',
    draw_time DATETIME COMMENT '抽奖时间',
    complete_time DATETIME COMMENT '完成时间',
    settle_flag TINYINT DEFAULT 0 COMMENT '结算标志',
    deleted_flag TINYINT DEFAULT 0 COMMENT '删除标志',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    KEY idx_order_sn (order_sn),
    KEY idx_user_id (user_id),
    KEY idx_status (status),
    KEY idx_activity_id (activity_id),
    KEY idx_create_time (create_time)
) COMMENT '订单表';
```

#### 钱包相关表
```sql
-- 用户钱包表
CREATE TABLE t_wallets (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    balance DECIMAL(10,2) DEFAULT 0 COMMENT '账户余额',
    experience_balance DECIMAL(10,2) DEFAULT 0 COMMENT '体验金余额',
    points BIGINT DEFAULT 0 COMMENT '用户积分',
    total_recharge DECIMAL(10,2) DEFAULT 0 COMMENT '累计充值',
    total_withdraw DECIMAL(10,2) DEFAULT 0 COMMENT '累计提现',
    status INT DEFAULT 1 COMMENT '钱包状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '用户钱包表';

-- 钱包交易流水表
CREATE TABLE t_wallet_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流水ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    wallet_type INT NOT NULL COMMENT '钱包类型：0余额 1体验金 2积分',
    amount DECIMAL(10,2) NOT NULL COMMENT '交易金额',
    balance_before DECIMAL(10,2) NOT NULL COMMENT '交易前余额',
    balance_after DECIMAL(10,2) NOT NULL COMMENT '交易后余额',
    transaction_type VARCHAR(20) NOT NULL COMMENT '交易类型',
    related_id BIGINT COMMENT '关联业务ID',
    remark VARCHAR(200) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    KEY idx_user_id (user_id),
    KEY idx_transaction_type (transaction_type),
    KEY idx_create_time (create_time)
) COMMENT '钱包交易流水表';
```

### 5.3 索引设计

#### 主要索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: 用户手机号、订单号等唯一字段
- **普通索引**: 查询频繁的字段如用户ID、状态等
- **复合索引**: 多字段联合查询的组合索引
- **覆盖索引**: 包含查询所需所有字段的索引

#### 索引优化原则
```sql
-- 示例：订单查询优化
-- 原查询：SELECT * FROM t_orders WHERE user_id = ? AND status = ? ORDER BY create_time DESC
-- 优化索引：
CREATE INDEX idx_user_status_time ON t_orders(user_id, status, create_time DESC);

-- 示例：钱包流水查询优化
-- 原查询：SELECT * FROM t_wallet_transactions WHERE user_id = ? AND transaction_type = ?
-- 优化索引：
CREATE INDEX idx_user_transaction_type ON t_wallet_transactions(user_id, transaction_type);
```

### 5.4 数据库优化

#### 读写分离
```yaml
spring:
  datasource:
    master:
      url: ***************************************
      username: root
      password: password
    slave:
      url: **************************************
      username: readonly
      password: password
```

#### 分表策略
```sql
-- 订单表按月分表
CREATE TABLE t_orders_202501 LIKE t_orders;
CREATE TABLE t_orders_202502 LIKE t_orders;

-- 交易流水表按月分表
CREATE TABLE t_wallet_transactions_202501 LIKE t_wallet_transactions;
CREATE TABLE t_wallet_transactions_202502 LIKE t_wallet_transactions;
```

## 6. 缓存架构

### 6.1 缓存层次
```
┌─────────────────────────────────────────────────────────┐
│                  浏览器缓存                             │
├─────────────────────────────────────────────────────────┤
│                  CDN缓存                                │
├─────────────────────────────────────────────────────────┤
│                  Nginx缓存                              │
├─────────────────────────────────────────────────────────┤
│                  应用缓存(Redis)                        │
├─────────────────────────────────────────────────────────┤
│                  数据库缓存                             │
└─────────────────────────────────────────────────────────┘
```

### 6.2 Redis缓存设计

#### 缓存策略
```java
@Service
public class CacheService {
    
    // 缓存键命名规范
    private static final String USER_CACHE = "user:info:";
    private static final String GOODS_CACHE = "goods:detail:";
    private static final String MENU_CACHE = "menu:user:";
    
    // 缓存过期时间
    private static final long USER_CACHE_TTL = 30 * 60; // 30分钟
    private static final long GOODS_CACHE_TTL = 60 * 60; // 1小时
    private static final long MENU_CACHE_TTL = 2 * 60 * 60; // 2小时
    
    /**
     * 缓存用户信息
     */
    public void cacheUserInfo(Long userId, UserVO userInfo) {
        String key = USER_CACHE + userId;
        redisTemplate.opsForValue().set(key, userInfo, USER_CACHE_TTL, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存的用户信息
     */
    public UserVO getCachedUserInfo(Long userId) {
        String key = USER_CACHE + userId;
        return (UserVO) redisTemplate.opsForValue().get(key);
    }
}
```

#### 缓存更新策略
- **Cache Aside**: 应用程序负责缓存的读写
- **Write Through**: 写数据时同时更新缓存
- **Write Behind**: 异步更新缓存
- **Refresh Ahead**: 预加载即将过期的缓存

#### 缓存穿透和雪崩防护
```java
@Component
public class CacheProtection {
    
    private final BloomFilter<String> bloomFilter;
    
    /**
     * 防止缓存穿透
     */
    public Object getCacheWithBloomFilter(String key) {
        // 1. 布隆过滤器检查
        if (!bloomFilter.mightContain(key)) {
            return null;
        }
        
        // 2. 查询缓存
        Object cached = redisTemplate.opsForValue().get(key);
        if (cached != null) {
            return cached;
        }
        
        // 3. 查询数据库
        Object data = queryFromDatabase(key);
        if (data != null) {
            // 4. 放入缓存
            redisTemplate.opsForValue().set(key, data, getRandomTTL(), TimeUnit.SECONDS);
        } else {
            // 5. 缓存空值防止穿透
            redisTemplate.opsForValue().set(key, "", 60, TimeUnit.SECONDS);
        }
        
        return data;
    }
    
    /**
     * 随机TTL防止雪崩
     */
    private long getRandomTTL() {
        return 1800 + new Random().nextInt(600); // 30-40分钟随机
    }
}
```

## 7. 安全架构

### 7.1 安全防护体系
```
┌─────────────────────────────────────────────────────────┐
│                    网络层安全                           │
│              (防火墙、DDoS防护)                         │
├─────────────────────────────────────────────────────────┤
│                    应用层安全                           │
│         (认证授权、输入验证、SQL注入防护)               │
├─────────────────────────────────────────────────────────┤
│                    数据层安全                           │
│              (数据加密、访问控制)                       │
├─────────────────────────────────────────────────────────┤
│                    业务层安全                           │
│              (风控系统、反作弊)                         │
└─────────────────────────────────────────────────────────┘
```

### 7.2 认证授权机制

#### JWT Token设计
```java
@Component
public class JwtTokenProvider {
    
    private final String SECRET = "SmartAdminSecretKey2024";
    private final long TOKEN_VALIDITY = 24 * 60 * 60 * 1000; // 24小时
    
    public String generateToken(UserPrincipal userPrincipal) {
        Date expiryDate = new Date(System.currentTimeMillis() + TOKEN_VALIDITY);
        
        return Jwts.builder()
                .setSubject(userPrincipal.getId().toString())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .claim("phone", userPrincipal.getPhone())
                .claim("roles", userPrincipal.getRoles())
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .compact();
    }
    
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(SECRET).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 7.3 数据安全

#### 敏感数据加密
```java
@Component
public class SensitiveDataEncryption {
    
    private final AESUtil aesUtil;
    
    /**
     * 身份证号加密存储
     */
    public String encryptIdNumber(String idNumber) {
        return aesUtil.encrypt(idNumber);
    }
    
    /**
     * 手机号脱敏显示
     */
    public String maskPhone(String phone) {
        if (StringUtils.isEmpty(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    
    /**
     * 身份证号脱敏显示
     */
    public String maskIdNumber(String idNumber) {
        if (StringUtils.isEmpty(idNumber) || idNumber.length() < 8) {
            return idNumber;
        }
        return idNumber.substring(0, 4) + "**********" + idNumber.substring(14);
    }
}
```

### 7.4 风控系统

#### 反作弊机制
```java
@Component
public class AntiCheatService {
    
    /**
     * 检查用户行为异常
     */
    public boolean checkUserBehavior(Long userId, String action) {
        // 1. 检查操作频率
        if (checkOperationFrequency(userId, action)) {
            return false;
        }
        
        // 2. 检查设备指纹
        if (checkDeviceFingerprint(userId)) {
            return false;
        }
        
        // 3. 检查IP地址
        if (checkIpAddress(userId)) {
            return false;
        }
        
        return true;
    }
    
    private boolean checkOperationFrequency(Long userId, String action) {
        String key = "rate_limit:" + action + ":" + userId;
        Long count = redisTemplate.opsForValue().increment(key);
        
        if (count == 1) {
            redisTemplate.expire(key, 60, TimeUnit.SECONDS);
        }
        
        // 每分钟最多10次操作
        return count > 10;
    }
}
```

## 8. 性能优化

### 8.1 前端性能优化

#### 资源优化
```javascript
// Vite配置优化
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['vant', 'ant-design-vue'],
          utils: ['lodash', 'dayjs', 'axios']
        }
      }
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  // 开发服务器优化
  server: {
    hmr: {
      overlay: false
    }
  }
})
```

#### 组件懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/product/:id',
    component: () => import(
      /* webpackChunkName: "product" */
      '@/views/product/ProductDetailPage.vue'
    )
  }
]

// 组件懒加载
export default {
  components: {
    LazyComponent: () => import('@/components/LazyComponent.vue')
  }
}
```

### 8.2 后端性能优化

#### 数据库优化
```java
@Service
public class OptimizedService {
    
    /**
     * 批量操作优化
     */
    @Transactional
    public void batchUpdateOrders(List<Long> orderIds, String status) {
        // 使用批量更新而不是循环单个更新
        ordersDao.batchUpdateStatus(orderIds, status);
    }
    
    /**
     * 分页查询优化
     */
    public PageResult<OrderVO> queryOrdersOptimized(OrderQueryForm form) {
        // 1. 先查询ID列表（覆盖索引）
        List<Long> orderIds = ordersDao.queryOrderIds(form);
        
        if (CollectionUtils.isEmpty(orderIds)) {
            return PageResult.empty();
        }
        
        // 2. 根据ID批量查询详细信息
        List<OrderVO> orders = ordersDao.queryOrdersByIds(orderIds);
        
        return PageResult.of(orders, form.getPage(), form.getSize(), orderIds.size());
    }
}
```

#### 缓存优化
```java
@Service
public class CacheOptimizedService {
    
    /**
     * 多级缓存
     */
    public GoodsDetailVO getGoodsDetail(Long goodsId) {
        // 1. 本地缓存
        GoodsDetailVO localCached = localCache.get(goodsId);
        if (localCached != null) {
            return localCached;
        }
        
        // 2. Redis缓存
        GoodsDetailVO redisCached = redisTemplate.opsForValue()
                .get("goods:detail:" + goodsId);
        if (redisCached != null) {
            localCache.put(goodsId, redisCached);
            return redisCached;
        }
        
        // 3. 数据库查询
        GoodsDetailVO goods = goodsDao.getDetailById(goodsId);
        if (goods != null) {
            // 更新缓存
            redisTemplate.opsForValue().set("goods:detail:" + goodsId, goods, 
                    30, TimeUnit.MINUTES);
            localCache.put(goodsId, goods);
        }
        
        return goods;
    }
}
```

### 8.3 系统监控

#### 性能监控指标
```yaml
management:
  metrics:
    tags:
      application: smart-admin
    export:
      prometheus:
        enabled: true
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
```

#### 自定义监控
```java
@Component
public class CustomMetrics {
    
    private final Counter orderCreatedCounter;
    private final Timer orderProcessingTimer;
    private final Gauge activeUsersGauge;
    
    public CustomMetrics(MeterRegistry meterRegistry) {
        this.orderCreatedCounter = Counter.builder("orders.created")
                .description("Number of orders created")
                .register(meterRegistry);
                
        this.orderProcessingTimer = Timer.builder("order.processing.time")
                .description("Order processing time")
                .register(meterRegistry);
                
        this.activeUsersGauge = Gauge.builder("users.active")
                .description("Number of active users")
                .register(meterRegistry, this, CustomMetrics::getActiveUserCount);
    }
    
    public void incrementOrderCreated() {
        orderCreatedCounter.increment();
    }
    
    public Timer.Sample startOrderProcessingTimer() {
        return Timer.start(orderProcessingTimer);
    }
    
    private double getActiveUserCount() {
        // 获取活跃用户数量的逻辑
        return userService.getActiveUserCount();
    }
}
```

## 9. 总结

团购网技术架构采用现代化的微服务架构设计，具有以下特点：

### 9.1 技术先进性
- **前端**: Vue 3 + Vite构建的现代化前端应用
- **后端**: Spring Boot 3 + Java 17的高性能后端服务
- **数据库**: MySQL 8.0 + Redis的高效数据存储
- **基础设施**: 完善的监控、日志、缓存体系

### 9.2 架构优势
- **高可用**: 多实例部署、读写分离、故障转移
- **高性能**: 多级缓存、数据库优化、CDN加速
- **高安全**: 多层安全防护、数据加密、风控系统
- **可扩展**: 微服务架构、水平扩展、模块化设计

### 9.3 业务支撑
- **完整流程**: 支持用户注册到订单完成的完整业务流程
- **营销系统**: 强大的推荐奖励和营销活动系统
- **管理功能**: 完善的后台管理和运营功能
- **数据分析**: 丰富的数据统计和分析功能

该架构能够很好地支撑团购网的业务发展，具备良好的扩展性和维护性，为未来的功能迭代和业务扩展提供了坚实的技术基础。

---

**文档版本**: V1.0  
**创建日期**: 2025年1月  
**维护团队**: 技术架构组  
**审核状态**: 已审核