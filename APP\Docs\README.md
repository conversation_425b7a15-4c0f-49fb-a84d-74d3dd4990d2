# 社交拼团APP - Vue 3项目

基于Vue 3 + Vite + Pinia + Vant UI构建的移动端社交拼团应用。

## 项目结构

```
src/
├── api/              # API接口模块
├── assets/           # 静态资源
├── components/       # 组件
│   ├── common/      # 通用组件
│   └── popups/      # 弹窗组件
├── router/          # 路由配置
├── store/           # 状态管理
│   └── modules/     # 状态模块
├── utils/           # 工具函数
├── views/           # 页面组件
│   ├── home/       # 首页模块
│   ├── product/    # 商品模块
│   ├── order/      # 订单模块
│   ├── user/       # 用户中心
│   └── auth/       # 认证模块
├── App.vue         # 根组件
└── main.js         # 入口文件
```

## 第一阶段完成功能

### ✅ 项目基础搭建
- [x] Vue 3 + Vite项目初始化
- [x] Pinia状态管理配置
- [x] Vue Router路由配置
- [x] Vant UI组件库集成
- [x] SCSS样式系统
- [x] ESLint + Prettier代码规范

### ✅ 核心组件开发
- [x] 首页组件 (HomePage.vue)
- [x] 商品卡片组件 (ProductCard.vue)
- [x] 底部导航组件 (BottomNav.vue)
- [x] 全局弹窗组件 (GlobalPopups.vue)

### ✅ 状态管理
- [x] 用户状态管理 (userStore)
- [x] 全局UI状态管理 (uiStore)
- [x] 弹窗联动逻辑

### ✅ API层设计
- [x] HTTP请求封装 (axios)
- [x] 用户API接口定义
- [x] 商品API接口定义
- [x] Mock数据服务

### ✅ 样式系统
- [x] 基于原型2的样式迁移
- [x] CSS变量系统
- [x] 响应式布局
- [x] 动画效果

## 启动开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI组件**: Vant UI
- **样式**: SCSS
- **HTTP**: Axios
- **代码规范**: ESLint + Prettier

## 下一阶段计划

1. **Week 2**: 活动专区和商品详情页开发
2. **Week 3**: 交易流程（订单确认、支付、等待开奖）
3. **Week 4**: 个人中心功能
4. **Week 5**: 裂变功能和收尾优化

## 特色功能

- 📱 **移动端优先**: 专为移动设备设计的响应式界面
- 🎨 **原型还原**: 100%还原原型2的视觉设计
- ⚡ **高性能**: 基于Vue 3和Vite的现代化开发体验
- 🔄 **状态管理**: 完善的全局状态管理和弹窗联动
- 🛠 **开发友好**: 完整的开发工具链和代码规范 