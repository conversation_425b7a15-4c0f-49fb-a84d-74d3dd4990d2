package net.lab1024.sa.admin.module.business.orders.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 订单表 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-29 16:03:41
 * @Copyright -
 */

@Data
@TableName("t_orders")
public class OrdersEntity {

    /**
     * 订单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 开奖结果
     */
    private Integer drawResult;

    /**
     * 返奖金额
     */
    private BigDecimal rewardAmount;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * SkuID
     */
    private Long skuId;

    /**
     * 单独购买标记
     */
    private Boolean aloneFlag;

    /**
     * 中签后选择
     */
    private String winOption;

    /**
     * 用户实付金额
     */
    private BigDecimal amountPaid;

    /**
     * 使用体验金
     */
    private BigDecimal experiencePaid;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyPaid;

    /**
     * 使用积分
     */
    private Integer pointsPaid;

    /**
     * 应付额
     */
    private BigDecimal payableAmount;

    /**
     * 收货地址ID
     */
    private Long shippingAddressId;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 开奖时间
     */
    private LocalDateTime drawTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 结算标记
     */
    private Integer settleFlag;

    /**
     * 删除标记
     */
    private Integer deletedFlag;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 退款原因
     */
    private String refundReason;

}
