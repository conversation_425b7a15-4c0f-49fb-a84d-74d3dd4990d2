package net.lab1024.sa.admin.module.business.oa.popups.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.oa.popups.domain.entity.PopupsEntity;
import net.lab1024.sa.admin.module.business.oa.popups.domain.form.PopupsQueryForm;
import net.lab1024.sa.admin.module.business.oa.popups.domain.vo.PopupsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 弹窗管理 Dao
 *
 * <AUTHOR>
 * @Date 2025-07-01 13:19:39
 * @Copyright -
 */

@Mapper
public interface PopupsDao extends BaseMapper<PopupsEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PopupsVO> queryPage(Page page, @Param("queryForm") PopupsQueryForm queryForm);

}
