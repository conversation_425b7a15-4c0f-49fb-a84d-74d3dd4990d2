● 地址列表
/api/v1/address
GET请求:
返回:
{
    "code": 0,
    "msg": "success",
    "ok": true,
    "data": {
        "pageNum": 1,
        "pageSize": 30,
        "total": 3,
        "pages": 1,
        "list": [
            {
                "id": 8,
                "userId": 81,
                "userName": "13833883388",
                "recipientName": "张二",
                "phoneNumber": "865542141",
                "province": "广西区",
                "city": "南宁市",
                "district": "青秀区",
                "addressLine": "唠三叨四涌腾泉在哪里",
                "isDefault": 1,
                "createTime": "2025-07-06 16:25:36"
            },
            {
                "id": 9,
                "userId": 81,
                "userName": "13833883388",
                "recipientName": "张一",
                "phoneNumber": "865542141",
                "province": "广西区",
                "city": "南宁市",
                "district": "青秀区",
                "addressLine": "唠三叨四涌腾泉在哪里",
                "isDefault": 0,
                "createTime": "2025-07-06 16:25:39"
            }
        ],
        "emptyFlag": false
    },
    "dataType": 1
}

● 新增地址
/api/v1/address/add
POST提交:
{
  "recipientName": "张一",
  "phoneNumber": "865542141",
  "addressLine": "唠三叨四涌腾泉在哪里",
  "province": "广西区",	//非必填
  "city": "南宁市", 		//非必填
  "district": "青秀区",	//非必填
  "isDefault": 0		//是否默认地址1是0否
}
返回:
{
    "code": 0,  //0为成功，其它有错误
    "msg": "success",
    "ok": true,
    "data": "7", //地址ID
    "dataType": 1
}

● 修改地址
/api/v1/address/update
POST提交:
{
  "id": 6,
  "recipientName": "张二",
  "phoneNumber": "865542141",
  "addressLine": "唠三叨四涌腾泉在哪里",
  "isDefault": 0
}
返回:
{
    "code": 0, //0为成功，其它有错误
    "msg": "success",
    "ok": true,
    "dataType": 1
}

● 删除地址
/api/v1/address/delete/地址ID
GET请求:
返回:
{
    "code": 0,  //0为成功，其它有错误
    "msg": "success",
    "ok": true,
    "dataType": 1
}