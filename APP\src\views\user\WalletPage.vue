<template>
  <div class="page-container bg-gray-100">
    <!-- 顶部导航 -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-b">
      <iconify-icon 
        icon="material-symbols:arrow-back" 
        class="text-2xl text-gray-700 cursor-pointer" 
        @click="goBack"
      />
      <h1 class="text-lg font-bold text-gray-800">交易记录</h1>
      <div class="w-6 h-6"></div>
    </div>

    <!-- 筛选选项 -->
    <div class="filter-tabs">
      <div class="tab-container">
        <div 
          v-for="filter in transactionFilters"
          :key="filter.key"
          class="tab-item"
          :class="{ 'active': activeFilter === filter.key }"
          @click="setActiveFilter(filter.key)"
        >
          {{ filter.label }}
        </div>
      </div>
    </div>

    <!-- 交易记录列表 -->
    <div class="transaction-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="text-gray-500 text-sm mt-2">正在加载交易记录...</p>
      </div>
      
      <!-- 交易记录列表 -->
      <div v-else-if="filteredTransactions.length > 0" class="transaction-list">
        <div 
          v-for="transaction in filteredTransactions" 
          :key="transaction.id"
          class="transaction-item"
        >
          <div class="transaction-icon" :class="getTransactionIconClass(transaction.type)">
            <iconify-icon :icon="getTransactionIcon(transaction.type)" class="text-lg" />
          </div>
          <div class="transaction-details">
            <div class="transaction-title">{{ transaction.description || getTransactionTitle(transaction.type) }}</div>
            <div class="transaction-meta">
              <span class="transaction-time">{{ formatDate(transaction.createTime) }}</span>
              <span v-if="transaction.relatedId" class="transaction-id">订单号: {{ transaction.relatedId }}</span>
            </div>
            <div v-if="transaction.balanceAfter" class="balance-after">
              余额: {{ formatPrice(transaction.balanceAfter) }}
            </div>
          </div>
          <div class="transaction-amount" :class="getTransactionAmountClass(transaction.amount)">
            {{ formatTransactionAmount(transaction.amount) }}
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-container">
        <iconify-icon icon="material-symbols:receipt-long" class="text-gray-300 text-6xl mb-4" />
        <p class="text-gray-500 text-sm mb-6">暂无交易记录</p>
      </div>

      <!-- 分页加载 -->
      <div v-if="transactions.length > 0 && hasMore && activeFilter === 'all'" class="load-more-container">
        <button 
          @click="loadMoreTransactions"
          :disabled="loadingMore"
          class="load-more-btn"
        >
          <span v-if="loadingMore" class="flex items-center justify-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            加载中...
          </span>
          <span v-else>加载更多</span>
        </button>
      </div>

      <!-- 分页信息 -->
      <div v-if="transactions.length > 0" class="pagination-info">
        <span class="text-gray-500 text-xs">
          <span v-if="activeFilter === 'all'">
            已显示 {{ transactions.length }} 条，共 {{ totalTransactions }} 条记录
            <span v-if="!hasMore" class="text-green-600 ml-2">（已全部加载）</span>
          </span>
          <span v-else>
            筛选结果：{{ filteredTransactions.length }} 条记录
          </span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { showSuccess, showError } from '@/utils/message.js'
import { formatPrice } from '@/utils/format'
import { requireAuth } from '@/composables/usePageAuth'

// 启用页面级登录检查
requireAuth({
  errorMessage: '访问钱包页面需要先登录'
})

const router = useRouter()
const authStore = useAuthStore()

// 快速登录检查
const { isAuthenticated, requireAuth } = useQuickAuthCheck()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const transactions = ref([])
const totalTransactions = ref(0)
const currentPage = ref(1)
const pageSize = ref(30)
const hasMore = ref(true)
const activeFilter = ref('all')

// 交易类型筛选
const transactionFilters = ref([
  { key: 'all', label: '全部' },
  { key: 'PAYMENT', label: '支付' },
  { key: 'RECHARGE', label: '充值' },
  { key: 'REFUND', label: '退款' },
  { key: 'REWARD', label: '奖励' },
  { key: 'BREACH', label: '违约金' }
])

// 计算属性
const filteredTransactions = computed(() => {
  if (activeFilter.value === 'all') {
    return transactions.value
  }
  
  return transactions.value.filter(transaction => {
    // 根据描述内容判断交易类型归类
    const description = transaction.description || ''
    
    if (activeFilter.value === 'REFUND') {
      // 退款类：包括原本的退款类型 + "未中返还" + "拼团退单退款"
      return transaction.type === 'REFUND' || 
             transaction.type === 'BREACH_REFUND' ||
             description.includes('未中返还') ||
             description.includes('拼团退单退款')
    } else if (activeFilter.value === 'REWARD') {
      // 奖励类：包括原本的奖励类型 + "未中奖励"
      return transaction.type === 'REWARD' || description.includes('未中奖励')
    } else if (activeFilter.value === 'BREACH') {
      // 违约金类：包括违约金扣除
      return transaction.type === 'BREACH_FEE' || description.includes('拼团退单违约金')
    } else {
      // 其他类型保持原逻辑，但排除已被重新归类的记录
      if (transaction.type === activeFilter.value) {
        // 如果是退款或奖励类型，需要排除已被重新归类的记录
        if (transaction.type === 'REFUND' && description.includes('未中返还')) {
          return false // 这个已经在退款筛选中处理了
        }
        if (transaction.type === 'REWARD' && description.includes('未中奖励')) {
          return false // 这个已经在奖励筛选中处理了
        }
        return true
      }
      return false
    }
  })
})

// 工具函数
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const getTransactionIcon = (type) => {
  const iconMap = {
    'PAYMENT': 'material-symbols:shopping-cart',
    'RECHARGE': 'material-symbols:add-circle',
    'REFUND': 'material-symbols:undo',
    'REWARD': 'material-symbols:card-giftcard',
    'WITHDRAW': 'material-symbols:remove-circle',
    'BREACH_FEE': 'material-symbols:warning',
    'BREACH_REFUND': 'material-symbols:undo'
  }
  return iconMap[type] || 'material-symbols:receipt'
}

const getTransactionIconClass = (type) => {
  const classMap = {
    'PAYMENT': 'payment-icon',
    'RECHARGE': 'recharge-icon',
    'REFUND': 'refund-icon',
    'REWARD': 'reward-icon',
    'WITHDRAW': 'withdraw-icon',
    'BREACH_FEE': 'breach-fee-icon',
    'BREACH_REFUND': 'refund-icon'
  }
  return classMap[type] || 'default-icon'
}

const getTransactionAmountClass = (amount) => {
  return amount > 0 ? 'positive' : 'negative'
}

const formatTransactionAmount = (amount) => {
  const absAmount = Math.abs(amount)
  const prefix = amount > 0 ? '+' : '-'
  return `${prefix}${formatPrice(absAmount)}`
}

const getTransactionTitle = (type) => {
  const titleMap = {
    'PAYMENT': '购物支付',
    'RECHARGE': '账户充值',
    'REFUND': '退款',
    'REWARD': '奖励收入',
    'WITHDRAW': '提现',
    'BREACH_FEE': '拼团退单违约金',
    'BREACH_REFUND': '拼团退单退款'
  }
  return titleMap[type] || '交易记录'
}

// 加载交易记录数据
const loadTransactions = async (page = 1, resetList = true) => {
  try {
    if (resetList) {
      loading.value = true
    } else {
      loadingMore.value = true
    }
    
    console.log('🔄 开始加载交易记录...', { page, pageSize: pageSize.value })
    
    // 验证登录状态
    if (!authStore.isLoggedIn) {
      console.log('❌ 用户未登录，直接跳转到登录页面')
      router.push('/login')
      return
    }
    
    // 构建请求头和URL参数
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${authStore.token}`
    }
    
    // 添加分页参数到URL（注意：API的pageNum从0开始）
    const apiUrl = `/api/v1/wallet?pageNum=${page - 1}&pageSize=${pageSize.value}`
    console.log(`🔗 请求第${page}页数据: ${apiUrl}`)
    
    // 调用钱包API获取交易记录
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: headers,
      mode: 'cors',
      credentials: 'omit'
    })
    
    const result = await response.json()
    console.log('💰 钱包API响应:', result)
    
    if (response.ok && result.code === 0) {
      const transactionsData = result.data.transactions || {}
      const transactionList = transactionsData.list || []
      
      console.log('📊 分页数据详情:', {
        currentAPIPage: page - 1,
        actualPage: page,
        pageSize: pageSize.value,
        receivedCount: transactionList.length,
        totalFromAPI: transactionsData.total,
        pagesFromAPI: transactionsData.pages
      })
      
      if (resetList) {
        transactions.value = transactionList
        currentPage.value = 1
      } else {
        // 直接添加新数据，不做重复检查（API应该保证数据不重复）
        if (transactionList.length > 0) {
          // 先更新总记录数信息
          totalTransactions.value = transactionsData.total || 0
          
          // 计算还需要多少条记录来达到总数
          const remainingNeeded = Math.max(0, totalTransactions.value - transactions.value.length)
          
          // 只添加需要的记录数，防止超过总数
          const recordsToAdd = transactionList.slice(0, remainingNeeded)
          
          if (recordsToAdd.length > 0) {
            transactions.value.push(...recordsToAdd)
            currentPage.value = page
            console.log(`📈 添加了 ${recordsToAdd.length} 条新记录（从 ${transactionList.length} 条中取用），总计 ${transactions.value.length} 条`)
          }
        } else {
          console.log('📋 本页没有数据，可能已经到底了')
        }
      }
      
      // 更新分页信息
      totalTransactions.value = transactionsData.total || 0
      const totalPages = transactionsData.pages || Math.ceil(totalTransactions.value / pageSize.value)
      
      // 重新计算 hasMore：检查是否已经加载了所有记录
      hasMore.value = transactions.value.length < totalTransactions.value && currentPage.value < totalPages
      
      console.log('✅ 交易记录加载成功:', {
        currentLoaded: transactions.value.length,
        totalRecords: totalTransactions.value,
        currentPage: currentPage.value,
        totalPages: totalPages,
        hasMore: hasMore.value,
        thisPageCount: transactionList.length
      })
      
    } else if (result.code === 30007 || result.code === 401 || result.code === 403) {
      console.error('🔐 认证失败:', result)
      showError('登录已过期，请重新登录')
      authStore.logout()
      router.push('/login')
    } else {
      console.warn('⚠️ 交易记录获取失败:', result.message || result.msg)
      showError(result.message || result.msg || '获取交易记录失败')
    }
    
  } catch (error) {
    console.error('❌ 加载交易记录失败:', error)
    showError('网络异常，请重试')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 设置筛选器
const setActiveFilter = (filterKey) => {
  activeFilter.value = filterKey
  console.log('🔍 切换交易类型筛选:', filterKey, '筛选后结果数量:', filteredTransactions.value.length)
}

// 加载更多交易记录
const loadMoreTransactions = async () => {
  if (hasMore.value && !loadingMore.value) {
    const nextPage = currentPage.value + 1
    console.log(`🔄 点击加载更多，将加载第${nextPage}页`)
    await loadTransactions(nextPage, false)
  } else {
    console.log('🚫 无法加载更多:', { hasMore: hasMore.value, loadingMore: loadingMore.value })
  }
}

// 事件处理
const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  console.log('🎯 TransactionHistoryPage mounted')
  
  if (!authStore.isLoggedIn) {
    console.log('❌ 用户未登录，直接跳转到登录页面')
    router.push('/login')
    return
  }
  
  loadTransactions()
})
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  padding-bottom: 20px;
  background-color: #f5f7fa;
}

/* 筛选标签 */
.filter-tabs {
  background: white;
  padding: 12px 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.tab-container {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-item {
  flex-shrink: 0;
  padding: 8px 16px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-item.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  font-weight: 500;
}

/* 交易记录内容 */
.transaction-content {
  padding: 16px;
}

.loading-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.empty-container {
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.transaction-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background-color: #f9fafb;
}

.transaction-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.transaction-icon.payment-icon {
  background: #fef2f2;
  color: #dc2626;
}

.transaction-icon.recharge-icon {
  background: #f0fdf4;
  color: #16a34a;
}

.transaction-icon.refund-icon {
  background: #eff6ff;
  color: #2563eb;
}

.transaction-icon.reward-icon {
  background: #fefbf2;
  color: #d97706;
}

.transaction-icon.withdraw-icon {
  background: #faf5ff;
  color: #9333ea;
}

.transaction-icon.breach-fee-icon {
  background: #fef2f2;
  color: #dc2626;
}

.transaction-icon.default-icon {
  background: #f3f4f6;
  color: #6b7280;
}

.transaction-details {
  flex: 1;
  min-width: 0;
}

.transaction-title {
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.4;
}

.transaction-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-time {
  font-size: 13px;
  color: #6b7280;
}

.transaction-id {
  font-size: 12px;
  color: #9ca3af;
}

.balance-after {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
  flex-shrink: 0;
  margin-left: 12px;
}

.transaction-amount.positive {
  color: #16a34a;
}

.transaction-amount.negative {
  color: #dc2626;
}

/* 加载更多 */
.load-more-container {
  margin-top: 16px;
  text-align: center;
}

.load-more-btn {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 24px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.load-more-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 分页信息 */
.pagination-info {
  margin-top: 16px;
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .transaction-content {
    padding: 12px;
  }
  
  .transaction-item {
    padding: 12px;
  }
  
  .transaction-icon {
    width: 40px;
    height: 40px;
  }
  
  .transaction-title {
    font-size: 14px;
  }
  
  .transaction-amount {
    font-size: 15px;
  }
}

/* 滚动条样式 */
.tab-container::-webkit-scrollbar {
  display: none;
}

.tab-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style> 