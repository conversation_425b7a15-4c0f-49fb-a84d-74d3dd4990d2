package net.lab1024.sa.admin.module.business.identity.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.identity.annotation.PreventDuplicateSubmit;
import net.lab1024.sa.admin.module.business.identity.dao.IdentityVerificationDao;
import net.lab1024.sa.admin.module.business.identity.domain.entity.IdentityVerificationEntity;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAddForm;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationAuditForm;
import net.lab1024.sa.admin.module.business.identity.domain.form.IdentityVerificationQueryForm;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationListVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationStatusVO;
import net.lab1024.sa.admin.module.business.identity.domain.vo.IdentityVerificationVO;
import net.lab1024.sa.admin.module.business.identity.constant.IdentityVerificationErrorCode;
import net.lab1024.sa.admin.module.business.identity.enums.IdentityVerificationStatusEnum;
import net.lab1024.sa.admin.module.business.identity.exception.IdentityVerificationException;
import net.lab1024.sa.admin.module.business.identity.service.IdentityVerificationService;
import net.lab1024.sa.admin.module.business.identity.util.DataMaskingUtil;
import net.lab1024.sa.base.module.support.file.service.FileService;
import net.lab1024.sa.base.module.support.file.constant.FileFolderTypeEnum;
import net.lab1024.sa.base.module.support.file.domain.vo.FileUploadVO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.enumeration.UserTypeEnum;
import net.lab1024.sa.admin.module.business.identity.util.IdCardValidator;
import net.lab1024.sa.admin.module.business.identity.util.BankCardValidator;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实名认证服务实现类
 *
 * <AUTHOR>
 * @Date 2025-01-27
 */
@Slf4j
@Service
public class IdentityVerificationServiceImpl implements IdentityVerificationService {

    @Resource
    private IdentityVerificationDao identityVerificationDao;

    @Resource
    private FileService fileService;

    @Resource
    private IdCardValidator idCardValidator;

    @Resource
    private BankCardValidator bankCardValidator;

    @Resource
    private DataMaskingUtil dataMaskingUtil;
    
    @Resource
    private EmployeeService employeeService;

    /**
     * 提交实名认证申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @PreventDuplicateSubmit(
        key = "identity:submit:#{#userId}",
        expireTime = 300,
        message = "请勿重复提交实名认证申请，请等待5分钟后重试"
    )
    public ResponseDTO<String> submitVerification(IdentityVerificationAddForm addForm, Long userId) {
        try {
            log.info("User {} submitting identity verification", userId);

            // 1. 验证用户是否可以提交认证
            ResponseDTO<String> validateResult = validateUserCanVerify(userId);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }

            // 2. 身份证号基本验证（只验证非空）
            if (addForm.getIdCard() == null || addForm.getIdCard().trim().isEmpty()) {
                throw IdentityVerificationException.invalidIdCard();
            }

            // 3. 验证银行卡格式
            if (!bankCardValidator.isValid(addForm.getBankCard())) {
                throw IdentityVerificationException.invalidBankCard();
            }

            // 4. 检查身份证和银行卡是否已被使用
            if (isIdCardExists(addForm.getIdCard(), userId)) {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.ID_CARD_EXISTS);
            }

            if (isBankCardExists(addForm.getBankCard(), userId)) {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.BANK_CARD_EXISTS);
            }

            // 5. 上传身份证照片
            String idCardFrontUrl = uploadFile(addForm.getIdCardFront(), "idcard_front", userId);
            String idCardBackUrl = uploadFile(addForm.getIdCardBack(), "idcard_back", userId);
            String idCardHandheldUrl = uploadFile(addForm.getIdCardHandheld(), "idcard_handheld", userId);

            // 6. 创建认证记录
            IdentityVerificationEntity entity = createVerificationEntity(addForm, userId, 
                    idCardFrontUrl, idCardBackUrl, idCardHandheldUrl);

            // 7. 保存到数据库
            identityVerificationDao.insert(entity);

            log.info("Identity verification submitted successfully, userId: {}, verificationId: {}", 
                    userId, entity.getId());

            return ResponseDTO.ok("实名认证申请提交成功，请等待审核");

        } catch (IdentityVerificationException e) {
            log.warn("Submit identity verification failed, userId: {}, error: {}", userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Submit identity verification error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 获取用户认证状态
     */
    @Override
    public ResponseDTO<IdentityVerificationStatusVO> getUserVerificationStatus(Long userId) {
        try {
            IdentityVerificationEntity entity = identityVerificationDao.queryByUserId(userId);
            IdentityVerificationStatusVO statusVO = buildStatusVO(entity, userId);
            return ResponseDTO.ok(statusVO);

        } catch (Exception e) {
            log.error("Get user verification status error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 获取用户认证详情（用户端）
     */
    @Override
    public ResponseDTO<IdentityVerificationVO> getUserVerificationDetail(Long userId) {
        try {
            IdentityVerificationEntity entity = identityVerificationDao.queryByUserId(userId);
            if (entity == null) {
                // 如果用户没有认证记录，返回默认的未认证状态
                IdentityVerificationVO vo = new IdentityVerificationVO();
                vo.setStatus("not_submitted");
                vo.setUserId(userId);
                return ResponseDTO.ok(vo);
            }

            // 转换实体到VO
            IdentityVerificationVO vo = new IdentityVerificationVO();
            SmartBeanUtil.copyProperties(entity, vo);
            
            // 数据脱敏处理
            if (vo.getIdCard() != null) {
                vo.setIdCard(dataMaskingUtil.maskIdCard(vo.getIdCard()));
            }
            if (vo.getBankCard() != null) {
                vo.setBankCard(dataMaskingUtil.maskBankCard(vo.getBankCard()));
            }

            return ResponseDTO.ok(vo);

        } catch (Exception e) {
            log.error("Get user verification detail error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 分页查询实名认证记录
     */
    @Override
    public ResponseDTO<IPage<IdentityVerificationListVO>> queryByPage(IdentityVerificationQueryForm queryForm) {
        try {
            log.info("🔍 [服务层] 开始执行分页查询");
            log.info("🔍 [服务层] 查询条件详情: {}", queryForm);
            
            Page<IdentityVerificationListVO> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
            log.info("🔍 [服务层] 创建分页对象: 页码={}, 每页大小={}", queryForm.getPageNum(), queryForm.getPageSize());
            
            long queryStartTime = System.currentTimeMillis();
            IPage<IdentityVerificationListVO> result = identityVerificationDao.queryByPage(page, queryForm);
            long queryEndTime = System.currentTimeMillis();
            
            log.info("📊 [服务层] 数据库查询完成，耗时: {}ms", queryEndTime - queryStartTime);
            log.info("📊 [服务层] 原始查询结果:");
            log.info("  - 总记录数: {}", result.getTotal());
            log.info("  - 总页数: {}", result.getPages());
            log.info("  - 当前页: {}", result.getCurrent());
            log.info("  - 每页大小: {}", result.getSize());
            log.info("  - 当前页记录数: {}", result.getRecords().size());
            
            if (!result.getRecords().isEmpty()) {
                log.info("📄 [服务层] 第一条原始记录: {}", result.getRecords().get(0));
            }
            
            // 数据脱敏处理和状态转换
            long processStartTime = System.currentTimeMillis();
            result.getRecords().forEach(record -> {
                log.debug("🔧 [服务层] 处理记录: id={}, rawStatus={}", record.getId(), record.getRawStatus());
                maskSensitiveData(record);
                // 将字符串状态转换为数字状态码，兼容数据库中的字符串状态值  
                convertStringStatusToCode(record);
                log.debug("🔧 [服务层] 处理完成: id={}, status={}, statusDesc={}", 
                    record.getId(), record.getStatus(), record.getStatusDesc());
            });
            long processEndTime = System.currentTimeMillis();
            
            log.info("🔧 [服务层] 数据处理完成，耗时: {}ms", processEndTime - processStartTime);
            
            if (!result.getRecords().isEmpty()) {
                log.info("📄 [服务层] 第一条处理后记录: {}", result.getRecords().get(0));
            }
            
            ResponseDTO<IPage<IdentityVerificationListVO>> response = ResponseDTO.ok(result);
            log.info("✅ [服务层] 查询成功，返回响应");
            
            return response;

        } catch (Exception e) {
            log.error("❌ [服务层] 分页查询失败", e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 查询认证详情
     */
    @Override
    public ResponseDTO<IdentityVerificationVO> getVerificationDetail(Long id, Long userId) {
        try {
            IdentityVerificationVO detail = identityVerificationDao.queryDetailById(id);
            
            if (detail == null) {
                throw IdentityVerificationException.verificationNotFound();
            }

            // 权限检查：普通用户只能查看自己的记录
            if (!isAdminUser(userId) && !detail.getUserId().equals(userId)) {
                throw IdentityVerificationException.accessDenied();
            }

            // 脱敏处理
            maskSensitiveData(detail);

            return ResponseDTO.ok(detail);

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Get verification detail error, id: {}, userId: {}", id, userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 审核实名认证申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @PreventDuplicateSubmit(
        key = "identity:audit:#{#auditForm.verificationId}",
        expireTime = 60,
        message = "请勿重复审核同一记录"
    )
    public ResponseDTO<String> auditVerification(IdentityVerificationAuditForm auditForm, Long auditUserId) {
        try {
            log.info("Auditing identity verification, verificationId: {}, auditUserId: {}, result: {}", 
                    auditForm.getVerificationId(), auditUserId, auditForm.getAuditResult());

            // 1. 验证审核表单
            if (!auditForm.isValid()) {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.REJECT_REASON_REQUIRED);
            }

            // 2. 查询认证记录
            IdentityVerificationEntity entity = identityVerificationDao.selectById(auditForm.getVerificationId());
            if (entity == null) {
                throw IdentityVerificationException.verificationNotFound();
            }

            // 3. 检查状态是否可以审核
            IdentityVerificationStatusEnum currentStatus = IdentityVerificationStatusEnum.getByValue(entity.getStatus());
            if (!currentStatus.canAudit()) {
                throw IdentityVerificationException.alreadyAudited();
            }

            // 4. 更新审核信息
            String newStatus = "approved".equals(auditForm.getAuditResult()) ? 
                    IdentityVerificationStatusEnum.APPROVED.getValue() : 
                    IdentityVerificationStatusEnum.REJECTED.getValue();

            String auditUserName = getAuditUserName(auditUserId);
            
            int updated = identityVerificationDao.updateAuditInfo(
                    auditForm.getVerificationId(),
                    auditForm.getAuditResult(),
                    auditUserId,
                    auditUserName,
                    LocalDateTime.now(),
                    auditForm.getRejectReason(),
                    auditForm.getAuditRemark()
            );

            // 5. 更新状态
            identityVerificationDao.updateStatus(auditForm.getVerificationId(), newStatus, auditUserId, LocalDateTime.now());

            if (updated > 0) {
                // 6. 根据审核结果更新员工表的has_real字段
                try {
                    boolean hasReal = "approved".equals(auditForm.getAuditResult());
                    employeeService.updateHasRealStatus(entity.getUserId(), hasReal);
                    log.info("Updated employee has_real status to {} for userId: {}", hasReal, entity.getUserId());
                } catch (Exception e) {
                    log.error("Failed to update employee has_real status for userId: {}", entity.getUserId(), e);
                    // 不抛出异常，避免影响主业务流程，但记录错误日志
                }
                
                log.info("Identity verification audit completed, verificationId: {}, result: {}", 
                        auditForm.getVerificationId(), auditForm.getAuditResult());
                
                String message = "approved".equals(auditForm.getAuditResult()) ? "审核通过" : "审核拒绝";
                return ResponseDTO.ok(message);
            } else {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.CONCURRENT_UPDATE_ERROR);
            }

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Audit verification error, verificationId: {}, auditUserId: {}", 
                    auditForm.getVerificationId(), auditUserId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 重新提交实名认证申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @PreventDuplicateSubmit(
        key = "identity:resubmit:#{#userId}",
        expireTime = 300,
        message = "请勿频繁重新提交认证申请"
    )
    public ResponseDTO<String> resubmitVerification(IdentityVerificationAddForm addForm, Long userId) {
        try {
            log.info("User {} resubmitting identity verification", userId);

            // 1. 查询当前认证记录
            IdentityVerificationEntity currentEntity = identityVerificationDao.queryByUserId(userId);
            if (currentEntity == null) {
                // 如果没有记录，直接提交新申请
                return submitVerification(addForm, userId);
            }

            // 2. 检查是否可以重新提交
            IdentityVerificationStatusEnum currentStatus = IdentityVerificationStatusEnum.getByValue(currentEntity.getStatus());
            if (currentStatus != IdentityVerificationStatusEnum.REJECTED) {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.STATUS_CHANGE_INVALID,
                        "只有被拒绝的认证申请才能重新提交");
            }

            // 3. 增加重试次数
            identityVerificationDao.incrementRetryCount(currentEntity.getId(), LocalDateTime.now());

            // 4. 验证和上传新文件
            String idCardFrontUrl = uploadFile(addForm.getIdCardFront(), "idcard_front", userId);
            String idCardBackUrl = uploadFile(addForm.getIdCardBack(), "idcard_back", userId);
            String idCardHandheldUrl = uploadFile(addForm.getIdCardHandheld(), "idcard_handheld", userId);

            // 5. 更新认证信息
            currentEntity.setRealName(addForm.getRealName());
            currentEntity.setIdCard(addForm.getIdCard());
            currentEntity.setIdCardFrontUrl(idCardFrontUrl);
            currentEntity.setIdCardBackUrl(idCardBackUrl);
            currentEntity.setIdCardHandheldUrl(idCardHandheldUrl);
            currentEntity.setBankCard(addForm.getBankCard());
            currentEntity.setBankName(addForm.getBankName());
            currentEntity.setBankAddress(addForm.getBankAddress());
            currentEntity.setBankPhone(addForm.getBankPhone());
            currentEntity.setStatus(IdentityVerificationStatusEnum.PENDING.getValue());
            currentEntity.setSubmitTime(LocalDateTime.now());
            // 清空审核信息
            currentEntity.setAuditTime(null);
            currentEntity.setAuditUserId(null);
            currentEntity.setAuditUserName(null);
            currentEntity.setAuditResult(null);
            currentEntity.setRejectReason(null);
            currentEntity.setAuditRemark(null);

            identityVerificationDao.updateById(currentEntity);

            log.info("Identity verification resubmitted successfully, userId: {}, verificationId: {}", 
                    userId, currentEntity.getId());

            return ResponseDTO.ok("实名认证申请重新提交成功，请等待审核");

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Resubmit verification error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 获取待审核记录列表
     */
    @Override
    public ResponseDTO<List<IdentityVerificationListVO>> getPendingList() {
        try {
            List<IdentityVerificationListVO> pendingList = identityVerificationDao.queryPendingList();
            pendingList.forEach(this::maskSensitiveData);
            return ResponseDTO.ok(pendingList);

        } catch (Exception e) {
            log.error("Get pending verification list error", e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 检查用户是否已通过实名认证
     */
    @Override
    public boolean isUserVerified(Long userId) {
        try {
            IdentityVerificationEntity entity = identityVerificationDao.queryByUserId(userId);
            return entity != null && IdentityVerificationStatusEnum.APPROVED.getValue().equals(entity.getStatus());

        } catch (Exception e) {
            log.error("Check user verified status error, userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 批量查询用户认证状态
     */
    @Override
    public ResponseDTO<List<IdentityVerificationStatusVO>> batchGetUserVerificationStatus(List<Long> userIds) {
        try {
            List<IdentityVerificationEntity> entities = identityVerificationDao.batchQueryByUserIds(userIds);
            Map<Long, IdentityVerificationEntity> entityMap = entities.stream()
                    .collect(Collectors.toMap(IdentityVerificationEntity::getUserId, e -> e));

            List<IdentityVerificationStatusVO> statusList = userIds.stream()
                    .map(userId -> buildStatusVO(entityMap.get(userId), userId))
                    .collect(Collectors.toList());

            return ResponseDTO.ok(statusList);

        } catch (Exception e) {
            log.error("Batch get user verification status error", e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 撤销实名认证申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> cancelVerification(Long userId) {
        try {
            IdentityVerificationEntity entity = identityVerificationDao.queryByUserId(userId);
            if (entity == null) {
                throw IdentityVerificationException.verificationNotFound();
            }

            IdentityVerificationStatusEnum currentStatus = IdentityVerificationStatusEnum.getByValue(entity.getStatus());
            if (currentStatus != IdentityVerificationStatusEnum.PENDING) {
                throw new IdentityVerificationException(IdentityVerificationErrorCode.STATUS_CHANGE_INVALID,
                        "只有待审核状态的申请才能撤销");
            }

            // 更新状态为未提交
            identityVerificationDao.updateStatus(entity.getId(), 
                    IdentityVerificationStatusEnum.NOT_SUBMITTED.getValue(), userId, LocalDateTime.now());

            log.info("Identity verification cancelled, userId: {}, verificationId: {}", userId, entity.getId());
            return ResponseDTO.ok("实名认证申请已撤销");

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Cancel verification error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 软删除认证记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteVerification(Long id, Long deletedBy) {
        try {
            int deleted = identityVerificationDao.softDelete(id, deletedBy);
            if (deleted > 0) {
                log.info("Identity verification soft deleted, id: {}, deletedBy: {}", id, deletedBy);
                return ResponseDTO.ok("认证记录已删除");
            } else {
                throw IdentityVerificationException.verificationNotFound();
            }

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Delete verification error, id: {}, deletedBy: {}", id, deletedBy, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 恢复已删除的认证记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> restoreVerification(Long id) {
        try {
            int restored = identityVerificationDao.restoreDeleted(id);
            if (restored > 0) {
                log.info("Identity verification restored, id: {}", id);
                return ResponseDTO.ok("认证记录已恢复");
            } else {
                throw IdentityVerificationException.verificationNotFound();
            }

        } catch (IdentityVerificationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Restore verification error, id: {}", id, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 获取认证统计信息
     */
    @Override
    public ResponseDTO<Object> getVerificationStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            statistics.put("totalCount", identityVerificationDao.countByStatus(null));
            statistics.put("pendingCount", identityVerificationDao.countByStatus(IdentityVerificationStatusEnum.PENDING.getValue()));
            statistics.put("approvedCount", identityVerificationDao.countByStatus(IdentityVerificationStatusEnum.APPROVED.getValue()));
            statistics.put("rejectedCount", identityVerificationDao.countByStatus(IdentityVerificationStatusEnum.REJECTED.getValue()));

            return ResponseDTO.ok(statistics);

        } catch (Exception e) {
            log.error("Get verification statistics error", e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 检查身份证号是否已被使用
     */
    @Override
    public boolean isIdCardExists(String idCard, Long userId) {
        try {
            return identityVerificationDao.checkIdCardExists(idCard, userId) > 0;
        } catch (Exception e) {
            log.error("Check id card exists error, idCard: {}, userId: {}", idCard, userId, e);
            return false;
        }
    }

    /**
     * 检查银行卡号是否已被使用
     */
    @Override
    public boolean isBankCardExists(String bankCard, Long userId) {
        try {
            return identityVerificationDao.checkBankCardExists(bankCard, userId) > 0;
        } catch (Exception e) {
            log.error("Check bank card exists error, bankCard: {}, userId: {}", bankCard, userId, e);
            return false;
        }
    }

    /**
     * 验证用户是否可以进行认证操作
     */
    @Override
    public ResponseDTO<String> validateUserCanVerify(Long userId) {
        try {
            // 查询用户现有的认证记录
            IdentityVerificationEntity existingEntity = identityVerificationDao.queryByUserId(userId);
            
            if (existingEntity != null) {
                IdentityVerificationStatusEnum status = IdentityVerificationStatusEnum.getByValue(existingEntity.getStatus());
                
                switch (status) {
                    case PENDING:
                        return ResponseDTO.error(IdentityVerificationErrorCode.VERIFICATION_PENDING, 
                                "您的实名认证正在审核中，请耐心等待");
                    case APPROVED:
                        return ResponseDTO.error(IdentityVerificationErrorCode.ALREADY_VERIFIED, 
                                "您已完成实名认证，无需重复提交");
                    case REJECTED:
                        // 被拒绝的可以重新提交，不阻止
                        break;
                    case NOT_SUBMITTED:
                    default:
                        // 可以提交
                        break;
                }
            }

            return ResponseDTO.ok("可以提交认证");

        } catch (Exception e) {
            log.error("Validate user can verify error, userId: {}", userId, e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    /**
     * 清理过期的认证记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Integer> cleanExpiredRecords() {
        try {
            // 清理30天前的未提交记录
            LocalDateTime expireTime = LocalDateTime.now().minusDays(30);
            int cleaned = identityVerificationDao.cleanExpiredRecords(expireTime);
            
            log.info("Clean expired verification records completed, count: {}", cleaned);
            return ResponseDTO.ok(cleaned);

        } catch (Exception e) {
            log.error("Clean expired records error", e);
            throw IdentityVerificationException.systemError(e);
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 上传文件 - 使用Smart-Admin的FileService
     */
    private String uploadFile(MultipartFile file, String fileType, Long userId) {
        try {
            // 构造RequestUser对象 - 创建匿名实现类
            RequestUser requestUser = new RequestUser() {
                @Override
                public Long getUserId() {
                    return userId;
                }

                @Override
                public String getUserName() {
                    return "User_" + userId;
                }

                @Override
                public UserTypeEnum getUserType() {
                    return UserTypeEnum.ADMIN_EMPLOYEE;
                }

                @Override
                public String getIp() {
                    return "127.0.0.1";
                }

                @Override
                public String getUserAgent() {
                    return "Identity-Verification-Service";
                }
            };
            
            // 使用Smart-Admin的FileService上传文件
            ResponseDTO<FileUploadVO> uploadResult = fileService.fileUpload(
                file, 
                FileFolderTypeEnum.IDENTITY_VERIFICATION.getValue(), 
                requestUser
            );
            
            if (!uploadResult.isSuccess()) {
                log.error("File upload failed, fileType: {}, userId: {}, error: {}", 
                    fileType, userId, uploadResult.getMsg());
                throw IdentityVerificationException.fileUploadFailed();
            }
            
            FileUploadVO uploadVO = uploadResult.getData();
            // 返回文件访问URL或文件Key，根据业务需要选择
            return uploadVO.getFileUrl();
            
        } catch (Exception e) {
            log.error("Upload file error, fileType: {}, userId: {}", fileType, userId, e);
            throw IdentityVerificationException.fileUploadFailed();
        }
    }

    /**
     * 创建认证实体
     */
    private IdentityVerificationEntity createVerificationEntity(IdentityVerificationAddForm addForm, Long userId,
                                                               String idCardFrontUrl, String idCardBackUrl, String idCardHandheldUrl) {
        IdentityVerificationEntity entity = SmartBeanUtil.copy(addForm, IdentityVerificationEntity.class);
        entity.setUserId(userId);
        entity.setIdCardFrontUrl(idCardFrontUrl);
        entity.setIdCardBackUrl(idCardBackUrl);
        entity.setIdCardHandheldUrl(idCardHandheldUrl);
        entity.setStatus(IdentityVerificationStatusEnum.PENDING.getValue());
        entity.setSubmitTime(LocalDateTime.now());
        entity.setRetryCount(0);
        entity.setDeletedFlag(false);
        
        return entity;
    }

    /**
     * 构建状态VO
     */
    private IdentityVerificationStatusVO buildStatusVO(IdentityVerificationEntity entity, Long userId) {
        IdentityVerificationStatusVO statusVO = new IdentityVerificationStatusVO();
        statusVO.setUserId(userId);

        if (entity == null) {
            // 未提交状态
            statusVO.setStatus(IdentityVerificationStatusEnum.NOT_SUBMITTED.getStatusCode());
            statusVO.setStatusDesc(IdentityVerificationStatusEnum.NOT_SUBMITTED.getDesc());
            statusVO.setHasSubmitted(false);
            statusVO.setIsPending(false);
            statusVO.setIsApproved(false);
            statusVO.setIsRejected(false);
            statusVO.setCanResubmit(false);
            statusVO.setRetryCount(0);
        } else {
            IdentityVerificationStatusEnum status = IdentityVerificationStatusEnum.getByValue(entity.getStatus());
            statusVO.setStatus(status.getStatusCode());
            statusVO.setStatusDesc(status.getDesc());
            statusVO.setHasSubmitted(true);
            statusVO.setIsPending(status == IdentityVerificationStatusEnum.PENDING);
            statusVO.setIsApproved(status == IdentityVerificationStatusEnum.APPROVED);
            statusVO.setIsRejected(status == IdentityVerificationStatusEnum.REJECTED);
            statusVO.setCanResubmit(status == IdentityVerificationStatusEnum.REJECTED);
            statusVO.setRejectReason(entity.getRejectReason());
            statusVO.setRetryCount(entity.getRetryCount());

            // 如果已通过认证，显示基本信息
            if (status == IdentityVerificationStatusEnum.APPROVED) {
                statusVO.setRealName(entity.getRealName());
                statusVO.setIdCard(dataMaskingUtil.maskIdCard(entity.getIdCard()));
                statusVO.setBankCard(dataMaskingUtil.maskBankCard(entity.getBankCard()));
                statusVO.setBankName(entity.getBankName());
            }
        }

        return statusVO;
    }

    /**
     * 脱敏处理
     */
    private void maskSensitiveData(IdentityVerificationListVO vo) {
        if (vo != null) {
            vo.setIdCard(dataMaskingUtil.maskIdCard(vo.getIdCard()));
            vo.setBankCard(dataMaskingUtil.maskBankCard(vo.getBankCard()));
        }
    }

    private void maskSensitiveData(IdentityVerificationVO vo) {
        if (vo != null) {
            vo.setIdCard(dataMaskingUtil.maskIdCard(vo.getIdCard()));
            vo.setBankCard(dataMaskingUtil.maskBankCard(vo.getBankCard()));
            vo.setBankPhone(dataMaskingUtil.maskPhone(vo.getBankPhone()));
        }
    }

    /**
     * 获取审核人姓名
     */
    private String getAuditUserName(Long auditUserId) {
        // TODO: 根据实际的用户服务获取用户姓名
        // 暂时返回默认值
        return "管理员";
    }

    /**
     * 检查是否为管理员用户
     */
    private boolean isAdminUser(Long userId) {
        // TODO: 根据实际的权限服务判断是否为管理员
        // 暂时返回false
        return false;
    }

    /**
     * 将字符串状态转换为数字状态码供前端使用
     * 这是一个临时方法，因为数据库存储字符串状态，但前端期望数字状态
     */
    private void convertStringStatusToCode(IdentityVerificationListVO vo) {
        if (vo != null) {
            try {
                // 从rawStatus字段获取数据库中的字符串状态值
                String stringStatus = vo.getRawStatus();
                if (stringStatus == null) {
                    stringStatus = "not_submitted";
                }
                
                Integer statusCode = IdentityVerificationStatusEnum.getStatusCode(stringStatus);
                vo.setStatus(statusCode);
                
                // 也设置状态描述
                IdentityVerificationStatusEnum statusEnum = IdentityVerificationStatusEnum.getByValue(stringStatus);
                if (statusEnum != null) {
                    vo.setStatusDesc(statusEnum.getDesc());
                }
            } catch (Exception e) {
                log.warn("Failed to convert status for record id: {}, rawStatus: {}", vo.getId(), vo.getRawStatus());
                vo.setStatus(-1); // 设置为未知状态
                vo.setStatusDesc("未知状态");
            }
        }
    }

}