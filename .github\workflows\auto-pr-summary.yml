name: Auto PR Summary Generator

on:
  pull_request:
    types: [opened, synchronize]
  workflow_dispatch:

jobs:
  generate-summary:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install dependencies
      run: |
        npm install -g github-slugger
        npm install

    - name: Generate PR Summary
      id: generate-summary
      run: |
        # 确保脚本有执行权限
        chmod +x scripts/generate-pr-summary.js
        
        # 生成摘要
        node scripts/generate-pr-summary.js > pr-summary.md
        
        # 读取摘要内容
        SUMMARY=$(cat pr-summary.md)
        
        # 设置输出变量
        echo "summary<<EOF" >> $GITHUB_OUTPUT
        echo "$SUMMARY" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Update PR Description
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const summary = `${{ steps.generate-summary.outputs.summary }}`;
          
          // 获取当前PR信息
          const { data: pr } = await github.rest.pulls.get({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number
          });

          // 检查是否已经有自动生成的摘要
          const existingSummary = pr.body || '';
          const summaryMarker = '<!-- AUTO-GENERATED-SUMMARY -->';
          
          let newBody;
          if (existingSummary.includes(summaryMarker)) {
            // 替换现有摘要
            const beforeSummary = existingSummary.split(summaryMarker)[0];
            newBody = beforeSummary + summaryMarker + '\n\n' + summary;
          } else {
            // 添加新摘要
            newBody = (existingSummary ? existingSummary + '\n\n' : '') + 
                     summaryMarker + '\n\n' + summary;
          }

          // 更新PR描述
          await github.rest.pulls.update({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number,
            body: newBody
          });

    - name: Add PR Comment
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const summary = `${{ steps.generate-summary.outputs.summary }}`;
          
          // 创建评论
          await github.rest.issues.createComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
            body: `🤖 **自动生成的PR摘要已更新**\n\n${summary}\n\n---\n*此摘要由GitHub Actions自动生成*`
          });

  analyze-changes:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Analyze Code Changes
      id: analyze
      run: |
        # 获取变更统计
        CHANGES=$(git diff --name-only ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }})
        STATS=$(git diff --stat ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }})
        
        # 分析变更类型
        FRONTEND_CHANGES=$(echo "$CHANGES" | grep -E '\.(vue|js|ts|css|scss)$' | wc -l)
        CONFIG_CHANGES=$(echo "$CHANGES" | grep -E '\.(json|yml|yaml|config)$' | wc -l)
        DOC_CHANGES=$(echo "$CHANGES" | grep -E '\.(md|txt)$' | wc -l)
        
        # 生成分析报告
        ANALYSIS="📊 **代码变更分析**\n\n"
        ANALYSIS+="- 前端文件变更: $FRONTEND_CHANGES 个\n"
        ANALYSIS+="- 配置文件变更: $CONFIG_CHANGES 个\n"
        ANALYSIS+="- 文档文件变更: $DOC_CHANGES 个\n\n"
        ANALYSIS+="📈 **详细统计**\n\`\`\`\n$STATS\n\`\`\`"
        
        echo "analysis<<EOF" >> $GITHUB_OUTPUT
        echo "$ANALYSIS" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Add Analysis Comment
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const analysis = `${{ steps.analyze.outputs.analysis }}`;
          
          await github.rest.issues.createComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
            body: analysis
          });