#!/bin/bash

# 快速数据库恢复脚本 - 简化版
# 使用方法: ./quick_restore.sh [备份文件] [数据库名]

BACKUP_DIR="DataBackup"

# 显示可用备份文件
show_backups() {
    echo "可用的备份文件:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -lt "$BACKUP_DIR"/*.sql 2>/dev/null | nl -w2 -s'. ' | head -10
    else
        echo "备份目录 $BACKUP_DIR 不存在"
        return 1
    fi
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    echo "快速数据库恢复脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [备份文件] [数据库名]"
    echo ""
    show_backups
    echo ""
    echo "示例:"
    echo "  $0 1                          # 恢复编号为1的备份文件"
    echo "  $0 backup.sql                 # 恢复指定备份文件"
    echo "  $0 backup.sql mydb            # 恢复到指定数据库"
    exit 0
fi

# 处理参数
BACKUP_INPUT="$1"
TARGET_DB="$2"

# 如果输入的是数字，按编号选择文件
if [[ "$BACKUP_INPUT" =~ ^[0-9]+$ ]]; then
    BACKUP_FILE=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | sed -n "${BACKUP_INPUT}p")
    if [ -z "$BACKUP_FILE" ]; then
        echo "错误: 编号 $BACKUP_INPUT 对应的备份文件不存在"
        show_backups
        exit 1
    fi
else
    # 处理文件名
    if [[ "$BACKUP_INPUT" == *.sql ]]; then
        if [ -f "$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_INPUT"
        elif [ -f "$BACKUP_DIR/$BACKUP_INPUT" ]; then
            BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        else
            echo "错误: 备份文件不存在: $BACKUP_INPUT"
            exit 1
        fi
    else
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_INPUT"
        if [ ! -f "$BACKUP_FILE" ]; then
            echo "错误: 备份文件不存在: $BACKUP_FILE"
            exit 1
        fi
    fi
fi

# 如果没有指定数据库名，从文件名推断
if [ -z "$TARGET_DB" ]; then
    BASENAME=$(basename "$BACKUP_FILE" .sql)
    TARGET_DB=$(echo "$BASENAME" | sed 's/_[0-9]\{8\}_[0-9]\{6\}$//' | sed 's/_backup$//')
fi

echo "恢复配置:"
echo "备份文件: $BACKUP_FILE"
echo "目标数据库: $TARGET_DB"
echo "文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"
echo ""

# 确认操作
read -p "确认恢复数据库吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 检查 MySQL 连接
echo "检查 MySQL 连接..."
mysql -u root -p -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到 MySQL"
    exit 1
fi

# 检查数据库是否存在
DB_EXISTS=$(mysql -u root -p -e "SHOW DATABASES LIKE '$TARGET_DB';" 2>/dev/null | grep "$TARGET_DB")
if [ -n "$DB_EXISTS" ]; then
    echo "警告: 数据库 '$TARGET_DB' 已存在"
    read -p "是否覆盖现有数据库？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
fi

# 创建/重建数据库
echo "准备数据库..."
mysql -u root -p -e "DROP DATABASE IF EXISTS \`$TARGET_DB\`; CREATE DATABASE \`$TARGET_DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
    echo "错误: 创建数据库失败"
    exit 1
fi

# 恢复数据
echo "正在恢复数据..."
mysql -u root -p "$TARGET_DB" < "$BACKUP_FILE"

if [ $? -ne 0 ]; then
    echo "错误: 数据恢复失败"
    exit 1
fi

# 验证结果
TABLE_COUNT=$(mysql -u root -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null | wc -l)
TABLE_COUNT=$((TABLE_COUNT - 1))

echo ""
echo "恢复完成！"
echo "数据库: $TARGET_DB"
echo "表数量: $TABLE_COUNT"

if [ $TABLE_COUNT -gt 0 ]; then
    echo ""
    echo "数据库表列表:"
    mysql -u root -p -e "USE \`$TARGET_DB\`; SHOW TABLES;" 2>/dev/null
fi
