
使用方法

  自动调用

  Claude Code 会根据上下文自动选择合适的子代理。

  显式调用

  "使用 code-reviewer 检查我的代码"
  "让 security-auditor 扫描安全漏洞"
  "用 performance-engineer 优化这个性能瓶颈"


● 根据已安装的 Claude Code Subagents，这里是全部 44 个可用的子代理：

  📋 完整的子代理列表

  🏗️ 开发与架构 (5个)

1. backend-architect - 设计 RESTful API、微服务边界和数据库架构
2. frontend-developer - 构建 React 组件、实现响应式布局、处理客户端状态管理
3. mobile-developer - 开发 React Native 或 Flutter 应用，集成原生功能
4. graphql-architect - 设计 GraphQL 架构、解析器和联合查询
5. architect-reviewer - 审查代码变更的架构一致性和模式

  💻 编程语言专家 (7个)

6. python-pro - 编写惯用的 Python 代码，使用高级特性和优化
7. golang-pro - 编写惯用的 Go 代码，使用 goroutines、channels 和接口
8. rust-pro - 编写惯用的 Rust 代码，掌握所有权模式、生命周期和 trait 实现
9. c-pro - 编写高效的 C 代码，进行内存管理和系统调用
10. cpp-pro - 编写惯用的 C++ 代码，使用现代特性、RAII、智能指针和 STL 算法
11. javascript-pro - 掌握现代 JavaScript ES6+、异步模式和 Node.js API
12. sql-pro - 编写复杂 SQL 查询、优化执行计划、设计规范化架构

  🛠️ 基础设施与运维 (10个)

13. devops-troubleshooter - 调试生产问题、分析日志、修复部署故障
14. deployment-engineer - 配置 CI/CD 流水线、Docker 容器和云部署
15. cloud-architect - 设计 AWS/Azure/GCP 基础设施并优化云成本
16. database-optimizer - 优化 SQL 查询、设计高效索引、处理数据库迁移
17. database-admin - 管理数据库操作、备份、复制和监控
18. terraform-specialist - 编写高级 Terraform 模块、管理状态文件、实施 IaC 最佳实践
19. incident-responder - 紧急且精准地处理生产事故
20. network-engineer - 调试网络连接、配置负载均衡器、分析流量模式
21. dx-optimizer - 开发者体验专家，改进工具、设置和工作流程
22. legacy-modernizer - 重构遗留代码库并实施渐进式现代化

  🔍 质量与安全 (6个)

23. code-reviewer - 专业代码审查，关注质量、安全性和可维护性
24. security-auditor - 审查代码漏洞并确保 OWASP 合规性
25. test-automator - 创建全面的测试套件，包括单元、集成和端到端测试
26. performance-engineer - 分析应用性能、优化瓶颈、实施缓存策略
27. debugger - 调试专家，处理错误、测试失败和意外行为
28. error-detective - 在日志和代码库中搜索错误模式、堆栈跟踪和异常

  📊 数据与 AI (7个)

29. data-scientist - 数据分析专家，处理 SQL 查询、BigQuery 操作和数据洞察
30. data-engineer - 构建 ETL 流水线、数据仓库和流式架构
31. ai-engineer - 构建 LLM 应用、RAG 系统和提示流水线
32. ml-engineer - 实施 ML 流水线、模型服务和特征工程
33. mlops-engineer - 构建 ML 流水线、实验跟踪和模型注册
34. prompt-engineer - 为 LLM 和 AI 系统优化提示
35. search-specialist - 专业网络研究员，使用高级搜索技术和综合分析

  🔧 专业领域 (4个)

36. api-documenter - 创建 OpenAPI/Swagger 规范并编写开发者文档
37. payment-integration - 集成 Stripe、PayPal 和支付处理器
38. quant-analyst - 构建金融模型、回测交易策略、分析市场数据
39. context-manager - 管理多个代理和长期运行任务的上下文

  💼 商业与营销 (5个)

40. business-analyst - 分析指标、创建报告、跟踪 KPI
41. content-marketer - 撰写博客文章、社交媒体内容和电子邮件通讯
42. sales-automator - 起草开发信、跟进邮件和提案模板
43. customer-support - 处理支持工单、FAQ 回复和客户邮件
44. risk-manager - 监控投资组合风险、R-倍数和仓位限制

  🎯 使用示

  显式调用

# 开发任务

  "用 backend-architect 设计用户认证 API"
  "让 frontend-developer 创建响应式仪表板布局"

# 运维任务

  "用 devops-troubleshooter 分析这些生产日志"
  "让 cloud-architect 设计可扩展的 AWS 架构"

# 质量保证

  "用 code-reviewer 检查这个组件的最佳实践"
  "让 security-auditor 扫描 OWASP 合规性问题"

# 数据和 AI

  "用 data-scientist 分析这个客户行为数据集"
  "让 ai-engineer 构建文档搜索的 RAG 系统"

  这些子代理现在已经安装并可以使用，它们会根据您的请求自动激活或通过显式调用来使用。
