package net.lab1024.sa.admin.module.business.activities.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.constant.OrdersConst;
import net.lab1024.sa.admin.module.business.activities.dao.ActivitiesCountDao;
import net.lab1024.sa.admin.module.business.activities.dao.ActivitiesDao;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesCountEntity;
import net.lab1024.sa.admin.module.business.activities.domain.entity.ActivitiesEntity;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesAddForm;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesQueryForm;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesUpdateForm;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

/**
 * 抽奖活动表 Service
 *
 * <AUTHOR>
 * @Date 2025-06-28 19:40:05
 * @Copyright -
 */
@Slf4j
@Service
public class ActivitiesService {

    @Resource
    private ActivitiesDao activitiesDao;

    @Resource
    private ActivitiesCountDao activitiesCountDao;

    /**
     * 分页查询
     */
    public PageResult<ActivitiesVO> query(ActivitiesQueryForm queryForm) {
        queryForm.setDeletedFlag(0);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ActivitiesVO> list = activitiesDao.query(page, queryForm);
        log.info("list ={}", list);
        //log.info("list ={}", activitiesDao.selectById(2));
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ActivitiesAddForm addForm) {
        ActivitiesEntity activitiesEntity = SmartBeanUtil.copy(addForm, ActivitiesEntity.class);
        activitiesEntity.setStatus(1);
        activitiesDao.insert(activitiesEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     */
    @CacheEvict(value = {"Activities"}, allEntries = true)
    public ResponseDTO<String> update(ActivitiesUpdateForm updateForm) {
        ActivitiesEntity activitiesEntity = SmartBeanUtil.copy(updateForm, ActivitiesEntity.class);
        activitiesDao.updateById(activitiesEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     */
    @CacheEvict(value = {"Activities"}, allEntries = true)
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        activitiesDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    @CacheEvict(value = {"Activities"}, allEntries = true)
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        activitiesDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    @Cacheable(value = {"Activities"}, key = "'list'")
    public List<ActivitiesVO> listAll() {
        List<ActivitiesVO> avList = new ArrayList<>();
        QueryWrapper<ActivitiesEntity> Q1 = new QueryWrapper<>();
        Q1.eq("deleted_flag", 0).eq("status", 1).select("id,name,type");
        List<ActivitiesEntity> rsList = activitiesDao.selectList(Q1);
        for(ActivitiesEntity activitiesEntity : rsList){
            avList.add(SmartBeanUtil.copy(activitiesEntity, ActivitiesVO.class));
        }
        return avList;
    }

    @Cacheable(value = "Activities", key = "#activityId")
    public ActivitiesEntity selectById(Long activityId) {
        return activitiesDao.selectById(activityId);
    }

    @Cacheable(value = "UserActivitiesCount", key = "#userId")
    public ActivitiesCountEntity getUserActivitiesCount(Long userId) {
        ActivitiesCountEntity ace = activitiesCountDao.selectById(userId);
        if(null == ace){
            ace = new ActivitiesCountEntity();
            ace.setUserId(userId);
            activitiesCountDao.insert(ace);
        }
        return ace;
    }

    /**
     * 消费累计
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "UserActivitiesCount", key = "#userId")
    public void incrementCount(Long userId, String activitiesType, BigDecimal amount) {
        this.getUserActivitiesCount(userId);
        if (Objects.equals(activitiesType, OrdersConst.ActivitiesType.LOW_PRICE)){
            activitiesCountDao.incrementLowPrice(userId, amount);
        } else if (Objects.equals(activitiesType, OrdersConst.ActivitiesType.HIGH_PRICE)) {
            activitiesCountDao.incrementHighPrice(userId, amount);
        } else {
            activitiesCountDao.incrementNovice(userId, amount);
        }
    }
}
