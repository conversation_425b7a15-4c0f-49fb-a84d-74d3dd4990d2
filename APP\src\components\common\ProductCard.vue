<template>
  <div class="product-card" @click="$emit('click')">
    <!-- 商品图片 -->
    <div class="product-image-wrapper">
      <img
        :src="getImageUrl(product.image || product.imageUrl || product.mainImage || product.thumbnail)"
        :alt="product.goodsName || product.name || product.title"
        class="product-image"
        @load="onImageLoad"
        @error="onImageError"
      />
    </div>

    <!-- 商品信息区域 -->
    <div class="product-info">
      <!-- 商品标题 -->
      <h3 class="product-title">
        {{ product.goodsName || product.name || product.title || '商品名称' }}
      </h3>
      
      <!-- 价格显示区域 -->
      <div class="price-section">
        <!-- 现价（拼团价） -->
        <div class="current-price">
          {{ formatPrice(getDisplayPrice(product)) }}
        </div>
        
        <!-- 原价划线 -->
        <div class="original-price">
          原价 {{ formatPrice(getOriginalPrice(product)) }}
        </div>
      </div>
      
      <!-- 促销信息标签 -->
      <div class="promotion-tags">
        <!-- 保底机制 -->
        <div class="guarantee-tag">
          支持货到付款
        </div>
      </div>
      
      <!-- 双按钮操作区域 -->
      <div class="action-buttons-row">
        <!-- 直接购买按钮 - 根据aloneFlag字段判断是否显示 -->
        <button 
          v-if="canDirectBuy(product)"
          class="direct-buy-button" 
          @click.stop="handleDirectBuy"
        >
          直接购买
          <span class="button-price">{{ formatPrice(product.alonePrice || 0) }}</span>
        </button>
        
        <!-- 拼团按钮 - 根据是否有直接购买按钮调整样式 -->
        <button 
          :class="[
            'group-buy-button',
            { 'full-width': !canDirectBuy(product) }
          ]"
          @click.stop="handleGroupBuy"
        >
          {{ getGroupSize(product) }}人拼团
          <span class="button-price">{{ formatPrice(getDisplayPrice(product)) }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getImageUrl } from '@/config/image'
import { formatPrice, getGroupSize } from '@/utils/format'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click', 'directBuy', 'groupBuy'])

// 图片加载成功
const onImageLoad = (event) => {
  console.log('图片加载成功:', event.target.src)
}

// 图片加载失败
const onImageError = (event) => {
  console.log('图片加载失败:', event.target.src)
  // 设置默认图片
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTMwQzEwNS41MjMgMTMwIDExMCAxMjUuNTIzIDExMCAxMjBDMTEwIDExNC40NzcgMTA1LjUyMyAxMTAgMTAwIDExMEM5NC40NzcgMTEwIDkwIDExNC40NzcgOTAgMTIwQzkwIDEyNS41MjMgOTQuNDc3IDEzMCAxMDAgMTMwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDcwSDMwVjE3MEgxNzBWNzBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTUwIDEwMEwxMzAgMTIwTDEwMCA5MEw3MCA5MEw1MCA5MEw3MCA3MEwxMDAgNzBMMTMwIDcwTDE1MCA3MEwxNzAgNzBWMTcwSDMwVjcwSDUwTDcwIDkwTDEwMCA5MEwxMzAgMTIwTDE1MCA5MEwxNzAgNzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
}

// 格式化价格 - 使用统一的越南盾格式化函数

// 图片URL处理已通过导入的getImageUrl函数统一处理

// 获取显示价格（拼团价格）
const getDisplayPrice = (product) => {
  return product.price || product.salePrice || product.currentPrice || product.participationPrice || 0
}

// 获取原价（直接购买价格）
const getOriginalPrice = (product) => {
  // 使用originalPrice字段作为原价（划线价）
  return product.originalPrice || 0
}


// 获取返现金额
const getCashbackAmount = (product) => {
  const price = getDisplayPrice(product)
  // 红包金额为拼团购买价格的5%
  return (price * 0.05).toFixed(2) || '0.00'
}

// 判断是否可以直接购买
const canDirectBuy = (product) => {
  // 根据aloneFlag字段判断，1表示可以直接购买，0表示不可以
  return product.aloneFlag === 1 || product.aloneFlag === true
}

// 处理直接购买点击
const handleDirectBuy = (event) => {
  event.stopPropagation()
  console.log('直接购买:', props.product)
  // 触发直接购买事件
  emit('directBuy', props.product)
}

// 处理拼团购买点击 - 修复缓存问题
const handleGroupBuy = (event) => {
  event.stopPropagation()
  console.log('拼团购买:', props.product)
  // 触发拼团购买事件
  emit('groupBuy', props.product)
}
</script>

<style lang="scss" scoped>
.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .product-image-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
    flex-shrink: 0;
    overflow: hidden;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 12px;

    .product-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      border: none;
      outline: none;
      border-radius: 8px;
    }
  }

  .product-info {
    padding: 16px 16px 16px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .product-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .price-section {
      margin-bottom: 8px;
      
      .current-price {
        font-size: 20px;
        font-weight: bold;
        color: #ff4444;
        margin-bottom: 4px;
      }
      
      .original-price {
        font-size: 13px;
        color: #000;
        text-decoration: line-through;
      }
    }

    .promotion-tags {
      margin-bottom: 12px;

      .guarantee-tag {
        display: inline-block;
        padding: 3px 6px;
        border: 1px solid #ff6b35;
        border-radius: 10px;
        font-size: 11px;
        color: #ff6b35;
        background: #fff9f7;
      }
    }

    .action-buttons-row {
      display: flex;
      gap: 8px;
      margin-top: 8px;

      .direct-buy-button {
        flex: 1;
        padding: 6px 8px;
        background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 36px;
        line-height: 1.1;

        .button-price {
          font-size: 14px;
          font-weight: bold;
          margin-top: 1px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .group-buy-button {
        flex: 1;
        padding: 6px 8px;
        background: linear-gradient(135deg, #ff4444 0%, #e73c3c 100%);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 36px;
        line-height: 1.1;

        // 当没有直接购买按钮时，占满全宽
        &.full-width {
          flex: 1;
          width: 100%;
        }

        .button-price {
          font-size: 14px;
          font-weight: bold;
          margin-top: 1px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .product-card {
    .product-image-wrapper {
      width: 130px;
      height: 130px;
      margin: 10px;
    }

    .product-info {
      padding: 10px 10px 10px 0;

      .product-title {
        font-size: 15px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .price-section {
        margin-bottom: 6px;
        
        .current-price {
          font-size: 18px;
          margin-bottom: 3px;
        }
        
        .original-price {
          font-size: 12px;
        }
      }

      .promotion-tags {
        margin-bottom: 8px;

        .guarantee-tag {
          font-size: 10px;
          padding: 2px 5px;
          margin-bottom: 3px;
        }
      }

      .action-buttons-row {
        gap: 6px;
        margin-top: 6px;

        .direct-buy-button,
        .group-buy-button {
          padding: 4px 6px;
          font-size: 12px;
          min-height: 32px;
          border-radius: 12px;
          line-height: 1.0;

          .button-price {
            font-size: 12px;
            margin-top: 0.5px;
          }
        }
      }
    }
  }
}
</style> 