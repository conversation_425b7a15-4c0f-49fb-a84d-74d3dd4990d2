package net.lab1024.sa.admin.module.app.data;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.activities.domain.form.ActivitiesQueryForm;
import net.lab1024.sa.admin.module.business.activities.domain.vo.ActivitiesVO;
import net.lab1024.sa.admin.module.business.activities.service.ActivitiesService;
import net.lab1024.sa.admin.module.business.category.domain.form.CategoryTreeQueryForm;
import net.lab1024.sa.admin.module.business.category.domain.vo.CategoryTreeVO;
import net.lab1024.sa.admin.module.business.category.service.CategoryService;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsQueryForm;
import net.lab1024.sa.admin.module.business.goods.domain.form.GoodsSkusQueryForm;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsSkusVO;
import net.lab1024.sa.admin.module.business.goods.domain.vo.GoodsVO;
import net.lab1024.sa.admin.module.business.goods.service.GoodsService;
import net.lab1024.sa.admin.module.business.oa.banners.domain.form.BannersQueryForm;
import net.lab1024.sa.admin.module.business.oa.banners.domain.vo.BannersVO;
import net.lab1024.sa.admin.module.business.oa.banners.service.BannersService;
import net.lab1024.sa.admin.module.business.wallets.dao.WalletTransactionsDao;
import net.lab1024.sa.admin.module.system.employee.domain.form.EmployeeQueryForm;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.admin.module.system.employee.domain.vo.TeamVO;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.module.support.file.service.IFileStorageService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class DataService {
    @Resource
    private BannersService bannersService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private ActivitiesService activitiesService;

    @Resource
    private IFileStorageService fileStorageService;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private WalletTransactionsDao walletTransactionsDao;

    /**
     * 首页内容
     */
    public Map<String, Object> home() {
        //Banners
        BannersQueryForm bannersQueryForm = new BannersQueryForm();
        bannersQueryForm.setStatus(1);
        bannersQueryForm.setPageNum(0L);
        bannersQueryForm.setPageSize(30L);
        List<BannersVO> banners = bannersService.queryPage(bannersQueryForm).getList();

        //分类
        CategoryTreeQueryForm categoryTreeQueryForm = new CategoryTreeQueryForm();
        categoryTreeQueryForm.setParentId(0L);
        categoryTreeQueryForm.setCategoryType(1);
        List<CategoryTreeVO> categories = categoryService.queryTree(categoryTreeQueryForm).getData();

        //商品
        GoodsSkusQueryForm goodsSkusQueryForm = new GoodsSkusQueryForm();
        goodsSkusQueryForm.setDeletedFlag(false);
        goodsSkusQueryForm.setPageNum(1L);
        goodsSkusQueryForm.setPageSize(30L);
        PageResult<GoodsSkusVO> initialProducts = goodsService.skusQuery(goodsSkusQueryForm).getData();

        Map<String, Object> data = new LinkedHashMap<>();
        //data.put("userStatus", employeeService.getHomeUserStatus());
        data.put("banners", banners);
        data.put("categories", categories);
        data.put("initialProducts", initialProducts);

        return data;
    }

    /**
     * 商品搜索
     */
    public Map<String, Object> search(String q, String type, String category, Integer priceMin, Integer priceMax, String sort, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);

        long startDoTime = System.currentTimeMillis();

        PageResult<GoodsVO> products = new PageResult<>();
        PageResult<GoodsVO> brands = new PageResult<>();
        PageResult<ActivitiesVO> activities = new PageResult<>();

        if (Objects.equals(type, "all") || Objects.equals(type, "product")) {
            //商品
            GoodsQueryForm goodsQueryForm = SmartBeanUtil.copy(pageParam, GoodsQueryForm.class);
            if (q != null) {
                goodsQueryForm.setSearchWord(q);
            }
            if (category != null) {
                goodsQueryForm.setCategoryName(category);
            }
            if ((priceMin != null && priceMin == 1) || Objects.equals(sort, "price_desc")) {
                PageParam.SortItem si = new PageParam.SortItem();
                si.setColumn("price");
                si.setIsAsc(false);
                goodsQueryForm.getSortItemList().add(si);
            }
            if ((priceMax != null && priceMax == 1) || Objects.equals(sort, "price_asc")) {
                PageParam.SortItem si = new PageParam.SortItem();
                si.setColumn("price");
                si.setIsAsc(true);
                goodsQueryForm.getSortItemList().add(si);
            }
            goodsQueryForm.setDeletedFlag(false);
            products = goodsService.query(goodsQueryForm).getData();
            for(GoodsVO goodsVO: products.getList()){
                for(Map<String, Object> m : goodsVO.getImages() ){
                    String url = m.get("url").toString();
                    ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                    if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                        m.put("url", getFileUrl.getData());
                    }
                }
                for(Map<String, Object> m : goodsVO.getDetailImages() ){
                    String url = m.get("url").toString();
                    ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                    if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                        m.put("url", getFileUrl.getData());
                    }
                }
            }
        }
        if (Objects.equals(type, "all") || Objects.equals(type, "brand")) {
            //品牌
            GoodsQueryForm goodsQueryForm = SmartBeanUtil.copy(pageParam, GoodsQueryForm.class);
            goodsQueryForm.setSortItemList(new ArrayList<>());
            if (q != null) {
                goodsQueryForm.setCategoryName(q);
            }
            if (category != null) {
                goodsQueryForm.setCategoryName(category);
            }
            if ((priceMin != null && priceMin == 1) || Objects.equals(sort, "price_desc")) {
                PageParam.SortItem si = new PageParam.SortItem();
                si.setColumn("price");
                si.setIsAsc(false);
                goodsQueryForm.getSortItemList().add(si);
            }
            if ((priceMax != null && priceMax == 1) || Objects.equals(sort, "price_asc")) {
                PageParam.SortItem si = new PageParam.SortItem();
                si.setColumn("price");
                si.setIsAsc(true);
                goodsQueryForm.getSortItemList().add(si);
            }
            goodsQueryForm.setDeletedFlag(false);
            brands = goodsService.query(goodsQueryForm).getData();
            for(GoodsVO goodsVO: brands.getList()){
                if(goodsVO.getImages() != null) {
                    for (Map<String, Object> m : goodsVO.getImages()) {
                        String url = m.get("url").toString();
                        ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                        if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                            m.put("url", getFileUrl.getData());
                        }
                    }
                }
                if(goodsVO.getDetailImages() != null) {
                    for (Map<String, Object> m : goodsVO.getDetailImages()) {
                        String url = m.get("url").toString();
                        ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(url);
                        if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                            m.put("url", getFileUrl.getData());
                        }
                    }
                }
            }
        }
        if (Objects.equals(type, "all") || Objects.equals(type, "activities")) {
            //活动
            ActivitiesQueryForm activitiesQueryForm = SmartBeanUtil.copy(pageParam, ActivitiesQueryForm.class);
            if (q != null) {
                activitiesQueryForm.setName(q);
            }
            activities = activitiesService.query(activitiesQueryForm);
        }

        int totalProducts = 0;
        int totalBrands = 0;
        int totalActivities = 0;
        if(products.getTotal() != null){
            totalProducts = products.getList().size();
        }
        if(brands.getTotal() != null){
            totalBrands = brands.getList().size();
        }
        if(activities.getTotal() != null){
            totalActivities = activities.getList().size();
        }

        float searchTime = (float) (System.currentTimeMillis() - startDoTime) /1000;

        Map<String, Object> searchStats = new LinkedHashMap<>();
        searchStats.put("totalProducts", totalProducts);
        searchStats.put("totalBrands", totalBrands);
        searchStats.put("totalActivities", totalActivities);
        searchStats.put("searchTime", searchTime);

        Map<String, Object> data = new LinkedHashMap<>();
        data.put("products", products);
        data.put("brands", brands);
        data.put("activities", activities);
        data.put("suggestions", new ArrayList<String>(){});
        data.put("searchStats", searchStats);

        return data;
    }


    /**
     * 分类列表
     */
    public Object categories(Integer categoryType, Long parentId) {

        if(parentId == null){ parentId = 0L; }
        if(categoryType == null){ categoryType = 1; }

        //分类
        CategoryTreeQueryForm categoryTreeQueryForm = new CategoryTreeQueryForm();
        categoryTreeQueryForm.setParentId(parentId);
        categoryTreeQueryForm.setCategoryType(categoryType);

        return categoryService.queryTree(categoryTreeQueryForm).getData();
    }

    /**
     * 活动列表
     */
    public Object activities(String name, String type, Long pageNum, Long pageSize) {
        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        ActivitiesQueryForm activitiesQueryForm = new ActivitiesQueryForm();
        activitiesQueryForm.setPageNum(pageNum);
        activitiesQueryForm.setPageSize(pageSize);
        if(name!=null){ activitiesQueryForm.setName(name); }
        if(type!=null){ activitiesQueryForm.setType(type); }
        return activitiesService.query(activitiesQueryForm);
    }

    /**
     * 我的团队
     */
    public Object myTeam(Long pageNum, Long pageSize) {
        Long userId = AdminRequestUtil.getRequestUserId();

        if(pageNum == null || pageNum == 0L){ pageNum = 1L; }
        if(pageSize == null){ pageSize = 30L; }
        if(pageSize > 30L){ pageSize = 30L; }

        EmployeeQueryForm employeeQueryForm = new EmployeeQueryForm();
        employeeQueryForm.setSortItemList(new ArrayList<>());
        employeeQueryForm.setInviterId(userId);
        employeeQueryForm.setPageNum(pageNum);
        employeeQueryForm.setPageSize(pageSize);
        PageParam.SortItem si = new PageParam.SortItem();
        si.setColumn("contribution");
        si.setIsAsc(false);
        employeeQueryForm.getSortItemList().add(si);
        si = new PageParam.SortItem();
        si.setColumn("id");
        si.setIsAsc(false);
        employeeQueryForm.getSortItemList().add(si);

        BigDecimal totalTeamReward = walletTransactionsDao.getUserReward(userId) ;

        Integer weekCount = employeeService.getIncreasedCountByThisWeek(userId);

        PageResult<TeamVO> page = employeeService.queryMyTeam(employeeQueryForm).getData();
//        for(TeamVO teamVO : page.getList()){
//            teamVO.setContribution(walletTransactionsDao.getUserContribution(teamVO.getId()));
//            teamVO.setConsume(walletTransactionsDao.getUserConsume(teamVO.getId()));
//        }

        Map<String, Object> data = new LinkedHashMap<>();
        data.put("list", page.getList());
        data.put("total", page.getTotal());
        data.put("totalTeamReward", totalTeamReward);
        data.put("weekCount", weekCount);
        return data;
    }
}
