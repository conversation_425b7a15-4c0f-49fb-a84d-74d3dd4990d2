# 团购网功能规格说明书

## 1. 系统概述

### 1.1 系统架构
团购网采用前后端分离的微服务架构，包含以下主要模块：
- **移动端APP**: Vue 3 + Vite构建的PWA应用
- **管理后台**: Vue 3 + Ant Design Vue的Web管理系统
- **后端API**: Spring Boot 3.3.1 + SA-Token的REST API服务
- **数据库**: MySQL 8.0存储业务数据，Redis做缓存

### 1.2 核心业务流程
```
用户注册 → 实名认证 → 浏览商品 → 参与团购 → 支付订单 → 等待抽奖 → 中奖处理 → 订单完成
    ↓
邀请好友 → 获得奖励 → 钱包管理 → 提现到账
```

## 2. 详细功能规格

### 2.1 用户认证系统

#### 2.1.1 用户注册
**功能描述**: 用户通过手机号码注册账户
**输入参数**:
- 手机号码（必填，11位数字）
- 验证码（必填，6位数字）
- 邀请码（可选，8位字符串）
- 用户协议确认（必填，布尔值）

**业务规则**:
- 手机号码格式验证（中国大陆号码）
- 验证码60秒内有效，同一手机号5分钟内最多发送3次
- 邀请码验证，建立邀请关系
- 注册成功自动登录，生成JWT Token

**输出结果**:
- 成功：返回用户信息和Token
- 失败：返回错误码和错误信息

**相关接口**: `/api/v1/auth/register`

#### 2.1.2 用户登录
**功能描述**: 用户通过手机号+验证码登录
**输入参数**:
- 手机号码（必填）
- 验证码（必填）

**业务规则**:
- 验证码验证
- 用户状态检查（是否被禁用）
- Token生成和刷新机制
- 登录记录和异常检测

**输出结果**:
- 成功：用户信息、Token、权限信息
- 失败：错误提示

**相关接口**: `/api/v1/auth/login`

#### 2.1.3 实名认证
**功能描述**: 用户提交身份证信息进行实名认证
**输入参数**:
- 真实姓名（必填）
- 身份证号码（必填）
- 身份证正面照片（必填）
- 身份证反面照片（必填）

**业务规则**:
- 身份证号码格式验证
- 年龄限制检查（18-70岁）
- 同一身份证只能认证一个账户
- 认证后不可修改

**认证状态**:
- 未认证：可以浏览商品，不能下单
- 认证中：已提交材料，等待审核
- 已认证：通过审核，可以正常使用所有功能
- 审核失败：需要重新提交材料

**相关接口**: `/api/v1/user/identity/verify`

### 2.2 商品管理系统

#### 2.2.1 商品展示
**功能描述**: 在APP首页展示商品列表
**展示信息**:
- 商品图片（主图+轮播图）
- 商品名称和简介
- 市场价和团购价
- 已参与人数
- 商品状态（预约中/售卖中/售罄）

**商品状态定义**:
- **预约中**: 可以预约，不能立即购买
- **售卖中**: 正在销售，可以参与团购
- **售罄**: 库存为0，不能购买
- **下架**: 管理员下架，用户不可见

**排序规则**:
- 默认：按照权重和创建时间排序
- 价格：按价格升序/降序
- 人气：按参与人数排序

**相关接口**: `/api/v1/goods/list`

#### 2.2.2 商品详情
**功能描述**: 显示商品的详细信息
**详情内容**:
- 商品基本信息（名称、价格、规格）
- 商品详情图文描述
- 团购规则说明
- 商品评价（预留功能）
- 相关推荐商品

**交互功能**:
- 商品图片放大预览
- 规格选择（颜色、尺寸等）
- 收藏/取消收藏
- 分享商品

**相关接口**: `/api/v1/goods/detail/{id}`

### 2.3 团购订单系统

#### 2.3.1 下单流程
**第一步：商品选择**
- 选择商品规格（颜色、尺寸等）
- 确认商品信息和价格
- 检查库存状态

**第二步：订单确认**
- 选择收货地址
- 确认订单金额
- 选择支付方式（余额/体验金/组合支付）
- 阅读团购规则

**第三步：支付处理**
- 余额充足性检查
- 创建订单记录
- 扣减用户资金
- 更新商品库存

**订单字段设计**:
```sql
t_orders (
    id BIGINT PRIMARY KEY,
    order_sn VARCHAR(32) UNIQUE,        -- 订单号
    user_id BIGINT,                     -- 用户ID
    activity_id BIGINT,                 -- 活动ID
    goods_id BIGINT,                    -- 商品ID
    sku_id BIGINT,                      -- SKU ID
    status VARCHAR(20),                 -- 订单状态
    draw_result VARCHAR(20),            -- 抽奖结果
    win_option VARCHAR(20),             -- 中奖选项
    amount_paid DECIMAL(10,2),          -- 支付金额
    experience_paid DECIMAL(10,2),      -- 体验金支付
    subsidy_paid DECIMAL(10,2),         -- 补贴金额
    points_paid BIGINT,                 -- 积分支付
    shipping_address_id BIGINT,         -- 收货地址
    payment_method VARCHAR(20),         -- 支付方式
    payment_time DATETIME,              -- 支付时间
    draw_time DATETIME,                 -- 抽奖时间
    complete_time DATETIME,             -- 完成时间
    create_time DATETIME,               -- 创建时间
    update_time DATETIME                -- 更新时间
)
```

**相关接口**: `/api/v1/orders/create`

#### 2.3.2 订单状态管理
**状态流转图**:
```
创建订单 → 待付款 → 已付款 → 抽奖中 → 中奖待付/未中奖 → 已完成/已取消
```

**状态详细说明**:

1. **PENDING_PAYMENT（待付款）**
   - 订单已创建，等待用户付款
   - 超时时间：15分钟
   - 可操作：付款、取消订单

2. **WON_PENDING_ACTION（中奖待付）**
   - 用户中奖，等待选择后续操作
   - 可选操作：
     - 取货：支付尾款后发货
     - 提现：将中奖金额提现到钱包
     - 支付尾款：继续完成购买流程
     - 没收定金：放弃购买，定金不退

3. **LOST（未中奖）**
   - 抽奖未中奖
   - 系统自动退款或发放补贴
   - 订单结束

4. **COMPLETED（已完成）**
   - 订单正常完成
   - 中奖用户取货或提现完成
   - 或未中奖退款完成

5. **CANCELLED（已取消）**
   - 用户主动取消或系统取消
   - 已付款的进行退款处理

6. **EXPIRED（已过期）**
   - 超时未付款的订单
   - 系统自动设置为过期状态

**相关接口**: `/api/v1/orders/status/{orderId}`

#### 2.3.3 抽奖机制
**抽奖时机**: 团购活动结束后24小时内进行抽奖

**抽奖规则**:
- 每个活动设定固定的中奖名额
- 中奖概率根据商品价值和活动类型动态调整
- 新用户有额外的中奖概率加成
- VIP用户享受更高的中奖概率

**抽奖算法**:
```javascript
// 伪代码
function drawLottery(activityId) {
    const participants = getParticipants(activityId);
    const winnerCount = getWinnerCount(activityId);
    const winners = [];
    
    // 按权重随机选择中奖用户
    for (let i = 0; i < winnerCount; i++) {
        const winner = weightedRandom(participants);
        winners.push(winner);
        participants.remove(winner);
    }
    
    // 更新订单状态
    updateOrderStatus(winners, 'WON_PENDING_ACTION');
    updateOrderStatus(participants, 'LOST');
    
    // 发送通知
    sendNotification(winners, 'WIN_NOTIFICATION');
    sendNotification(participants, 'LOSE_NOTIFICATION');
}
```

**相关接口**: `/api/v1/orders/draw/{activityId}`

### 2.4 钱包支付系统

#### 2.4.1 钱包结构
**钱包字段设计**:
```sql
t_wallets (
    user_id BIGINT PRIMARY KEY,         -- 用户ID
    balance DECIMAL(10,2),              -- 可提现余额
    experience_balance DECIMAL(10,2),   -- 体验金余额
    points BIGINT,                      -- 积分（预留）
    total_recharge DECIMAL(10,2),       -- 累计充值
    total_withdraw DECIMAL(10,2),       -- 累计提现
    status INT,                         -- 钱包状态
    create_time DATETIME,
    update_time DATETIME
)
```

**资金类型说明**:
- **余额(balance)**: 可提现的真实资金
- **体验金(experience_balance)**: 仅用于购买，不可提现
- **积分(points)**: 用于兑换商品或优惠券（预留功能）

#### 2.4.2 充值功能
**支持的充值方式**:
- 微信支付
- 支付宝
- 银联支付
- Apple Pay（iOS）

**充值流程**:
1. 用户选择充值金额
2. 选择支付方式
3. 调用第三方支付接口
4. 支付成功后更新钱包余额
5. 记录交易流水

**充值规则**:
- 最低充值金额：10元
- 最高单次充值：10000元
- 充值无手续费
- 充值实时到账

**相关接口**: `/api/v1/wallet/recharge`

#### 2.4.3 提现功能
**提现条件**:
- 用户必须完成实名认证
- 钱包状态正常
- 余额充足（大于等于提现金额+手续费）

**提现流程**:
1. 用户输入提现金额
2. 系统计算手续费
3. 确认提现信息
4. 创建提现申请
5. 管理员审核
6. 财务处理到账

**提现规则**:
- 最低提现金额：50元
- 提现手续费：5%（可配置）
- 每日提现次数限制：3次
- 工作日1-3个工作日到账

**提现状态**:
- 申请中：等待审核
- 审核通过：等待财务处理
- 处理中：正在转账
- 已完成：提现成功
- 已拒绝：审核未通过
- 已取消：用户取消申请

**相关接口**: `/api/v1/wallet/withdraw`

#### 2.4.4 交易流水
**流水类型定义**:
- **RECHARGE**: 用户充值
- **WITHDRAWAL**: 用户提现
- **PAYMENT**: 购买商品支付
- **REFUND_WIN**: 中奖奖励
- **REFUND_LOSS**: 未中奖退款
- **SUBSIDY**: 平台补贴
- **COMMISSION**: 推荐佣金
- **FIRST_REWARD**: 首单奖励
- **TEAM_REWARD**: 团队奖金
- **MANUAL_ADJUST**: 手动调整
- **EXPERIENCE_GIFT**: 体验金赠送

**流水记录设计**:
```sql
t_wallet_transactions (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,                     -- 用户ID
    wallet_type INT,                    -- 钱包类型(0余额1体验金2积分)
    amount DECIMAL(10,2),               -- 交易金额
    balance_before DECIMAL(10,2),       -- 交易前余额
    balance_after DECIMAL(10,2),        -- 交易后余额
    transaction_type VARCHAR(20),       -- 交易类型
    related_id BIGINT,                  -- 关联订单ID
    remark VARCHAR(200),                -- 备注说明
    create_time DATETIME
)
```

**相关接口**: `/api/v1/wallet/transactions`

### 2.5 推荐奖励系统

#### 2.5.1 邀请关系管理
**邀请码生成规则**:
- 8位字符串，包含数字和字母
- 全局唯一，不重复
- 用户注册时自动生成

**邀请关系建立**:
- 新用户注册时输入邀请码
- 系统验证邀请码有效性
- 建立邀请者和被邀请者的关系
- 记录邀请时间和状态

**关系数据设计**:
```sql
t_user_invitations (
    id BIGINT PRIMARY KEY,
    inviter_id BIGINT,                  -- 邀请者ID
    invitee_id BIGINT,                  -- 被邀请者ID
    invite_code VARCHAR(20),            -- 邀请码
    invite_time DATETIME,               -- 邀请时间
    first_order_time DATETIME,          -- 首单时间
    status VARCHAR(20),                 -- 关系状态
    total_orders INT DEFAULT 0,         -- 下单总数
    total_amount DECIMAL(10,2) DEFAULT 0 -- 下单总金额
)
```

#### 2.5.2 奖励计算规则
**直接邀请奖励**:
- 成功邀请1人注册：5元体验金
- 被邀请人首次下单：10元现金奖励
- 被邀请人每次下单：订单金额1%的佣金

**团队奖励机制**:
- **一级团队**: 直接邀请的用户
- **二级团队**: 一级团队邀请的用户
- **奖励比例**: 
  - 一级团队订单：1%佣金
  - 二级团队订单：0.5%佣金

**等级奖励体系**:
- **普通用户**: 无特殊奖励
- **银牌推广员**: 直接邀请10人，团队奖励+20%
- **金牌推广员**: 直接邀请50人，团队奖励+50%
- **钻石推广员**: 直接邀请100人，团队奖励+100%

**相关接口**: 
- `/api/v1/user/invitation/my-team`
- `/api/v1/user/invitation/rewards`

### 2.6 地址管理系统

#### 2.6.1 收货地址
**地址信息字段**:
```sql
t_user_addresses (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,                     -- 用户ID
    consignee_name VARCHAR(50),         -- 收货人姓名
    phone VARCHAR(20),                  -- 联系电话
    province VARCHAR(50),               -- 省份
    city VARCHAR(50),                   -- 城市
    district VARCHAR(50),               -- 区县
    detail_address VARCHAR(200),        -- 详细地址
    postal_code VARCHAR(10),            -- 邮政编码
    is_default TINYINT DEFAULT 0,       -- 是否默认地址
    create_time DATETIME,
    update_time DATETIME
)
```

**业务规则**:
- 每个用户最多保存20个地址
- 必须设置一个默认地址
- 删除默认地址时自动设置其他地址为默认
- 地址信息格式验证

**相关接口**: `/api/v1/user/addresses`

### 2.7 系统通知

#### 2.7.1 消息类型
**订单相关通知**:
- 订单支付成功
- 抽奖结果通知
- 发货通知
- 订单完成通知

**资金相关通知**:
- 充值成功通知
- 提现申请通知
- 提现到账通知
- 奖励发放通知

**活动相关通知**:
- 新商品上架
- 限时活动开始
- 优惠券发放

#### 2.7.2 通知渠道
- **APP内通知**: 消息中心显示
- **短信通知**: 重要操作短信提醒
- **推送通知**: APP推送消息
- **邮件通知**: 邮件确认（可选）

## 3. 管理后台功能规格

### 3.1 商品管理
**商品列表**: 支持多条件查询、批量操作、状态管理
**商品编辑**: 富文本编辑器、图片上传、规格管理
**分类管理**: 商品分类的增删改查
**库存管理**: 库存预警、库存调整记录

### 3.2 订单管理
**订单列表**: 按状态、时间、用户等维度查询
**订单详情**: 完整的订单信息和操作记录
**批量操作**: 批量发货、批量退款
**数据导出**: 订单数据Excel导出

### 3.3 用户管理
**用户列表**: 用户基本信息、注册时间、状态等
**实名审核**: 身份证审核和认证管理
**钱包管理**: 用户资金查看和调整
**邀请关系**: 用户邀请关系树形展示

### 3.4 财务管理
**提现审核**: 用户提现申请的审核和处理
**交易流水**: 所有资金流水的查询和统计
**财务报表**: 收入、支出、利润等财务报表
**对账功能**: 与第三方支付平台的对账

### 3.5 营销管理
**活动管理**: 团购活动的创建和管理
**横幅管理**: 首页轮播图和广告横幅
**优惠券管理**: 优惠券的发放和使用统计
**推广统计**: 邀请推广效果统计

### 3.6 系统管理
**员工管理**: 管理员账户和权限管理
**角色权限**: 基于RBAC的权限控制
**系统配置**: 系统参数和业务规则配置
**操作日志**: 管理员操作记录和审计

## 4. API接口规范

### 4.1 接口设计原则
- **RESTful风格**: 使用HTTP方法表示操作类型
- **统一响应格式**: 所有接口返回统一的JSON格式
- **错误码规范**: 标准化的错误码和错误信息
- **版本控制**: 通过URL路径进行版本控制

### 4.2 响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": 1672531200000
}
```

### 4.3 错误码定义
- **200**: 操作成功
- **400**: 请求参数错误
- **401**: 未授权，需要登录
- **403**: 无权限访问
- **404**: 资源不存在
- **500**: 服务器内部错误

### 4.4 接口安全
- **JWT Token**: 基于Token的身份认证
- **签名验证**: 关键接口增加签名验证
- **频率限制**: API接口调用频率限制
- **数据加密**: 敏感数据传输加密

## 5. 数据库设计

### 5.1 核心数据表
- **t_user**: 用户基本信息表
- **t_user_identities**: 用户实名认证信息表
- **t_goods**: 商品信息表
- **t_activities**: 团购活动表
- **t_orders**: 订单表
- **t_wallets**: 用户钱包表
- **t_wallet_transactions**: 钱包交易流水表
- **t_user_invitations**: 用户邀请关系表
- **t_user_addresses**: 用户地址表

### 5.2 数据库优化
- **索引设计**: 基于查询频率设计索引
- **分表策略**: 大表采用分表存储
- **读写分离**: 主从数据库读写分离
- **缓存策略**: Redis缓存热点数据

### 5.3 数据安全
- **敏感数据加密**: 身份证号、银行卡号等敏感信息加密存储
- **数据备份**: 定期数据备份和恢复测试
- **访问控制**: 数据库访问权限控制
- **审计日志**: 数据变更审计日志

## 6. 性能要求

### 6.1 响应时间
- **页面加载**: 首屏加载时间 ≤ 3秒
- **API响应**: 接口响应时间 ≤ 1秒
- **数据库查询**: 单次查询 ≤ 500ms
- **缓存命中**: 缓存响应时间 ≤ 10ms

### 6.2 并发能力
- **在线用户**: 支持1000人同时在线
- **并发请求**: 支持500 QPS
- **数据库连接**: 最大连接数200
- **缓存容量**: Redis内存使用 ≤ 2GB

### 6.3 可用性
- **系统可用性**: 99.9%的服务可用性
- **故障恢复**: 故障恢复时间 ≤ 30分钟
- **数据一致性**: 强一致性要求的业务保证数据一致
- **容灾备份**: 异地容灾备份机制

## 7. 安全规范

### 7.1 数据安全
- **传输加密**: HTTPS加密传输
- **存储加密**: 敏感数据AES加密存储
- **密码安全**: 密码hash存储，支持密码强度检查
- **数据脱敏**: 日志和报表中的敏感数据脱敏

### 7.2 访问安全
- **身份认证**: JWT Token + 刷新Token机制
- **权限控制**: RBAC权限模型
- **防刷机制**: API接口防刷和频率限制
- **异常检测**: 异常登录和操作检测

### 7.3 业务安全
- **资金安全**: 资金操作双重验证
- **反作弊**: 防刷单、防薅羊毛机制
- **风控系统**: 实时风险监控和预警
- **合规要求**: 符合相关法律法规要求

## 8. 测试规范

### 8.1 测试策略
- **单元测试**: 覆盖率 ≥ 80%
- **集成测试**: 主要业务流程集成测试
- **性能测试**: 压力测试和负载测试
- **安全测试**: 安全漏洞扫描和渗透测试

### 8.2 测试环境
- **开发环境**: 开发人员本地测试
- **测试环境**: QA团队功能测试
- **预发环境**: 生产环境的完整模拟
- **生产环境**: 线上正式环境

### 8.3 测试数据
- **测试数据准备**: 覆盖各种业务场景的测试数据
- **数据隔离**: 测试数据与生产数据隔离
- **数据清理**: 测试完成后及时清理测试数据
- **敏感数据保护**: 生产数据脱敏后用于测试

## 9. 部署运维

### 9.1 部署架构
- **负载均衡**: Nginx反向代理和负载均衡
- **应用服务**: 多实例部署，支持水平扩展
- **数据库**: MySQL主从架构，读写分离
- **缓存**: Redis集群部署

### 9.2 监控报警
- **应用监控**: 应用性能和错误监控
- **服务器监控**: CPU、内存、磁盘、网络监控
- **业务监控**: 关键业务指标监控
- **报警通知**: 邮件、短信、微信报警

### 9.3 日志管理
- **应用日志**: 应用运行日志和错误日志
- **访问日志**: Web服务器访问日志
- **操作日志**: 管理员操作审计日志
- **日志分析**: ELK或类似的日志分析系统

## 10. 版本发布

### 10.1 发布流程
1. **代码审查**: Code Review和质量检查
2. **自动化测试**: 单元测试和集成测试
3. **预发验证**: 预发环境功能验证
4. **生产发布**: 灰度发布和全量发布
5. **发布验证**: 发布后功能验证

### 10.2 回滚机制
- **快速回滚**: 发现问题可快速回滚到上一版本
- **数据库回滚**: 数据库变更的回滚方案
- **缓存清理**: 发布后相关缓存的清理
- **服务重启**: 必要时的服务重启策略

### 10.3 发布通知
- **发布公告**: 用户端发布公告和功能介绍
- **技术通知**: 技术团队发布通知
- **客服培训**: 客服团队新功能培训
- **运营配合**: 运营活动配合新功能推广

---

**文档版本**: V1.0
**最后更新**: 2025年1月
**维护人员**: 技术团队
**审核状态**: 已审核