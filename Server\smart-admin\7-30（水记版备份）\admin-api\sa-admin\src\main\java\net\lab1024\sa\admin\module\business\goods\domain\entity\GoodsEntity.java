package net.lab1024.sa.admin.module.business.goods.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商品 实体类
 *
 * <AUTHOR> 胡克
 * @Date 2021-10-25 20:26:54
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@TableName(value = "t_goods", autoResultMap = true)
public class GoodsEntity {

    @TableId(type = IdType.AUTO)
    private Long goodsId;

    /**
     *  商品状态:[1:预约中,2:售卖中,3:售罄]
     */
    private Integer goodsStatus;

    /**
     * 商品分类
     */
    private Long categoryId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private String goodsType;

    /**
     * 货币类型
     */
    private String goodsCurrency;

    /**
     * 产地
     */
    private String place;

    /**
     * 商品价格
     */
    private String price;

    /**
     * 单独购买标记
     */
    private Boolean aloneFlag;

    /**
     * 单独购买价格
     */
    private String alonePrice;

    /**
     * 付款模式
     */
    private Integer payMode;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品图片数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> images;

    /**
     * 详情图片数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> detailImages;

    /**
     * 上架状态
     */
    private Boolean shelvesFlag;

    /**
     * 删除状态
     */
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}
